import { Router } from 'express';
import { customFormController } from '../controllers/custom-form.controller.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { createCustomFormSchema, updateCustomFormSchema } from '../schemas/custom-form.schema.js';

const router = Router();

/**
 * @route GET /api/custom-forms
 * @desc الحصول على قائمة النماذج المخصصة
 * @access خاص (مصادقة مطلوبة)
 */
router.get('/', authMiddleware, customFormController.listCustomForms);

/**
 * @route GET /api/custom-forms/:id
 * @desc الحصول على نموذج مخصص محدد
 * @access خاص (مصادقة مطلوبة)
 */
router.get('/:id', authMiddleware, customFormController.getCustomForm);

/**
 * @route POST /api/custom-forms
 * @desc إنشاء نموذج مخصص جديد
 * @access خاص (مصادقة مطلوبة)
 */
router.post(
  '/',
  authMiddleware,
  validateRequest(createCustomFormSchema),
  customFormController.createCustomForm
);

/**
 * @route PUT /api/custom-forms/:id
 * @desc تحديث نموذج مخصص
 * @access خاص (مصادقة مطلوبة)
 */
router.put(
  '/:id',
  authMiddleware,
  validateRequest(updateCustomFormSchema),
  customFormController.updateCustomForm
);

/**
 * @route DELETE /api/custom-forms/:id
 * @desc حذف نموذج مخصص
 * @access خاص (مصادقة مطلوبة)
 */
router.delete('/:id', authMiddleware, customFormController.deleteCustomForm);

export default router;
