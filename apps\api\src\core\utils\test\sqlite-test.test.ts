import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { SQLiteTestSetup } from './sqlite-setup.js';

describe('SQLite Test Database', () => {
  let prisma: PrismaClient;
  let setup: SQLiteTestSetup;

  beforeAll(async () => {
    setup = SQLiteTestSetup.getInstance();
    prisma = await setup.setupDatabase();
    await setup.seedTestData();
  });

  afterAll(async () => {
    await setup.cleanupDatabase();
  });

  describe('Database Connection', () => {
    it('should connect to SQLite database', async () => {
      expect(prisma).toBeDefined();

      // اختبار الاتصال
      const result = await prisma.$queryRaw`SELECT 1 as test`;
      expect(result).toBeDefined();
    });

    it('should have test data seeded', async () => {
      // التحقق من وجود المستخدم التجريبي
      const users = await prisma.user.findMany();
      expect(users.length).toBeGreaterThan(0);

      const testUser = await prisma.user.findUnique({
        where: { username: 'testuser' }
      });
      expect(testUser).toBeDefined();
      expect(testUser?.email).toBe('<EMAIL>');
      expect(testUser?.name).toBe('Test User');
    });

    it('should have test client data', async () => {
      const clients = await prisma.client.findMany();
      expect(clients.length).toBeGreaterThan(0);

      const testClient = await prisma.client.findUnique({
        where: { taxNumber: '*********' }
      });
      expect(testClient).toBeDefined();
      expect(testClient?.name).toBe('شركة الاختبار');
    });

    it('should have test declaration data', async () => {
      const declarations = await prisma.declaration.findMany();
      expect(declarations.length).toBeGreaterThan(0);

      const testDeclaration = await prisma.declaration.findUnique({
        where: { declarationNumber: 'D001' }
      });
      expect(testDeclaration).toBeDefined();
      expect(testDeclaration?.declarationType).toBe('IMPORT');
      expect(testDeclaration?.goodsType).toBe('MEDICAL_SUPPLIES');
    });

    it('should have test item movement data', async () => {
      const itemMovements = await prisma.itemMovement.findMany();
      expect(itemMovements.length).toBeGreaterThan(0);

      const testMovement = itemMovements[0];
      expect(testMovement).toBeDefined();
      expect(testMovement?.itemName).toBe('أدوية طبية');
      expect(testMovement?.quantity).toBe(100);
    });

    it('should have test authorization data', async () => {
      const authorizations = await prisma.authorization.findMany();
      expect(authorizations.length).toBeGreaterThan(0);

      const testAuth = authorizations.find(auth => auth.idNumber === '**********');
      expect(testAuth).toBeDefined();
      expect(testAuth?.authorizationType).toBe('FULL');
      expect(testAuth?.authorizedPerson).toBe('أحمد محمد');
    });
  });

  describe('Database Operations', () => {
    it('should create new user', async () => {
      const newUser = await prisma.user.create({
        data: {
          username: 'newuser',
          email: '<EMAIL>',
          password: 'hashedpassword',
          name: 'New User',
          role: 'USER'
        }
      });

      expect(newUser).toBeDefined();
      expect(newUser.username).toBe('newuser');
      expect(newUser.email).toBe('<EMAIL>');
    });

    it('should create new client', async () => {
      const newClient = await prisma.client.create({
        data: {
          taxNumber: '*********',
          name: 'عميل جديد',
          phone: '+966501234568',
          email: '<EMAIL>'
        }
      });

      expect(newClient).toBeDefined();
      expect(newClient.taxNumber).toBe('*********');
      expect(newClient.name).toBe('عميل جديد');
    });

    it('should update existing data', async () => {
      const testUser = await prisma.user.findUnique({
        where: { username: 'testuser' }
      });

      if (testUser) {
        const updatedUser = await prisma.user.update({
          where: { id: testUser.id },
          data: { name: 'Updated Test User' }
        });

        expect(updatedUser.name).toBe('Updated Test User');
      }
    });

    it('should delete data', async () => {
      // إنشاء بيانات مؤقتة للحذف
      const tempClient = await prisma.client.create({
        data: {
          taxNumber: '*********',
          name: 'عميل مؤقت'
        }
      });

      // حذف البيانات
      await prisma.client.delete({
        where: { id: tempClient.id }
      });

      // التحقق من الحذف
      const deletedClient = await prisma.client.findUnique({
        where: { id: tempClient.id }
      });

      expect(deletedClient).toBeNull();
    });
  });

  describe('Database Relationships', () => {
    it('should handle user-declaration relationship', async () => {
      const userWithDeclarations = await prisma.user.findUnique({
        where: { username: 'testuser' },
        include: { declarations: true }
      });

      expect(userWithDeclarations).toBeDefined();
      expect(userWithDeclarations?.declarations).toBeDefined();
      expect(userWithDeclarations?.declarations.length).toBeGreaterThan(0);
    });

    it('should handle client-declaration relationship', async () => {
      const clientWithDeclarations = await prisma.client.findUnique({
        where: { taxNumber: '*********' },
        include: { declarations: true }
      });

      expect(clientWithDeclarations).toBeDefined();
      expect(clientWithDeclarations?.declarations).toBeDefined();
      expect(clientWithDeclarations?.declarations.length).toBeGreaterThan(0);
    });

    it('should handle declaration-itemMovement relationship', async () => {
      const declarationWithItems = await prisma.declaration.findUnique({
        where: { declarationNumber: 'D001' },
        include: { itemMovements: true }
      });

      expect(declarationWithItems).toBeDefined();
      expect(declarationWithItems?.itemMovements).toBeDefined();
      expect(declarationWithItems?.itemMovements.length).toBeGreaterThan(0);
    });
  });
});
