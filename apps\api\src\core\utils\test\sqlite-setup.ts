import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import { existsSync, unlinkSync } from 'fs';
import path from 'path';

// إنشاء Prisma Client مخصص للاختبارات مع SQLite
const createTestPrismaClient = () => {
  return new PrismaClient({
    datasources: {
      db: {
        url: 'file:./prisma/test.db'
      }
    },
    log: ['error']
  });
};

// إعداد قاعدة بيانات SQLite للاختبارات
export class SQLiteTestSetup {
  private static instance: SQLiteTestSetup;
  private prisma: PrismaClient | null = null;
  private dbPath: string;

  private constructor() {
    this.dbPath = path.join(process.cwd(), 'prisma', 'test.db');
  }

  static getInstance(): SQLiteTestSetup {
    if (!SQLiteTestSetup.instance) {
      SQLiteTestSetup.instance = new SQLiteTestSetup();
    }
    return SQLiteTestSetup.instance;
  }

  async setupDatabase(): Promise<PrismaClient> {
    try {
      console.log('🔧 إعداد قاعدة بيانات SQLite للاختبارات...');

      // حذف قاعدة البيانات السابقة إن وجدت
      if (existsSync(this.dbPath)) {
        unlinkSync(this.dbPath);
        console.log('🗑️ تم حذف قاعدة البيانات السابقة');
      }

      // تطبيق المخطط باستخدام schema.test.prisma
      console.log('📋 تطبيق مخطط قاعدة البيانات...');
      execSync('npx prisma db push --schema=./prisma/schema.test.prisma --force-reset', {
        stdio: 'inherit',
        cwd: process.cwd()
      });

      // إنشاء اتصال جديد
      this.prisma = createTestPrismaClient();

      await this.prisma.$connect();
      console.log('✅ تم إعداد قاعدة بيانات SQLite بنجاح');

      return this.prisma;
    } catch (error) {
      console.error('❌ خطأ في إعداد قاعدة البيانات:', error);
      throw error;
    }
  }

  async seedTestData(): Promise<void> {
    if (!this.prisma) {
      throw new Error('قاعدة البيانات غير مهيأة');
    }

    try {
      console.log('🌱 إضافة بيانات تجريبية...');

      // إنشاء مستخدم تجريبي
      const testUser = await this.prisma.user.create({
        data: {
          username: 'testuser',
          email: '<EMAIL>',
          password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uDfm', // password123
          name: 'Test User',
          role: 'USER',
          isActive: true
        }
      });

      // إنشاء عميل تجريبي
      const testClient = await this.prisma.client.create({
        data: {
          name: 'شركة الاختبار',
          taxNumber: '*********',
          phone: '+966501234567',
          email: '<EMAIL>',
          address: 'الرياض، المملكة العربية السعودية'
        }
      });

      // إنشاء بيان تجريبي
      const testDeclaration = await this.prisma.declaration.create({
        data: {
          declarationNumber: 'D001',
          taxNumber: '*********',
          clientName: 'شركة الاختبار',
          companyName: 'Test Company',
          gatewayEntryNumber: 'G001',
          declarationType: 'IMPORT',
          declarationDate: new Date(),
          count: 10,
          weight: 100.5,
          goodsType: 'MEDICAL_SUPPLIES',
          itemsCount: 5,
          clientId: testClient.id,
          userId: testUser.id
        }
      });

      // إنشاء حركة صنف تجريبية
      await this.prisma.itemMovement.create({
        data: {
          itemName: 'أدوية طبية',
          quantity: 100,
          unit: 'قطعة',
          movementDate: new Date(),
          movementType: 'دخول',
          notes: 'حركة تجريبية',
          declarationId: testDeclaration.id
        }
      });

      // إنشاء تفويض تجريبي
      await this.prisma.authorization.create({
        data: {
          authorizedPerson: 'أحمد محمد',
          idNumber: '*********0',
          authorizationType: 'FULL',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 يوم من الآن
          declarationId: testDeclaration.id
        }
      });

      console.log('✅ تم إضافة البيانات التجريبية بنجاح');
    } catch (error) {
      console.error('❌ خطأ في إضافة البيانات التجريبية:', error);
      throw error;
    }
  }

  async cleanupDatabase(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect();
      this.prisma = null;
    }

    if (existsSync(this.dbPath)) {
      unlinkSync(this.dbPath);
      console.log('🧹 تم تنظيف قاعدة البيانات');
    }
  }

  getPrismaClient(): PrismaClient {
    if (!this.prisma) {
      throw new Error('قاعدة البيانات غير مهيأة');
    }
    return this.prisma;
  }
}

// إعداد عام للاختبارات
export const setupTestDatabase = async (): Promise<PrismaClient> => {
  const setup = SQLiteTestSetup.getInstance();
  const prisma = await setup.setupDatabase();
  await setup.seedTestData();
  return prisma;
};

export const cleanupTestDatabase = async (): Promise<void> => {
  const setup = SQLiteTestSetup.getInstance();
  await setup.cleanupDatabase();
};

// إعداد Jest
beforeAll(async () => {
  await setupTestDatabase();
});

afterAll(async () => {
  await cleanupTestDatabase();
});

// تم إزالة beforeEach لتجنب التعارض مع الاختبارات الموجودة
