import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getPermits,
  getPermit,
  createPermit,
  updatePermit,
  deletePermit,
  PermitSearchParams,
  CreatePermitRequest,
  UpdatePermitRequest,
} from '../api/permits.api';
import { useToast } from '@lib/hooks/useToast';
import { useTranslation } from 'react-i18next';

// خطاف للحصول على قائمة التصاريح
export const usePermits = (params: PermitSearchParams = {}) => {
  return useQuery({
    queryKey: ['permits', params],
    queryFn: () => getPermits(params),
  });
};

// خطاف للحصول على تصريح محدد
export const usePermit = (id: string) => {
  return useQuery({
    queryKey: ['permit', id],
    queryFn: () => getPermit(id),
    enabled: !!id,
  });
};

// خطاف لإنشاء تصريح جديد
export const useCreatePermit = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ data, file }: { data: CreatePermitRequest; file?: File }) =>
      createPermit(data, file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permits'] });
      showSuccess(t('permits.createSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

// خطاف لتحديث تصريح
export const useUpdatePermit = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, data, file }: { id: string; data: UpdatePermitRequest; file?: File }) =>
      updatePermit(id, data, file),
    onSuccess: (_: any, variables: { id: string; data: UpdatePermitRequest; file?: File }) => {
      queryClient.invalidateQueries({ queryKey: ['permits'] });
      queryClient.invalidateQueries({ queryKey: ['permit', variables.id] });
      showSuccess(t('permits.updateSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

// خطاف لحذف تصريح
export const useDeletePermit = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => deletePermit(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permits'] });
      showSuccess(t('permits.deleteSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

// خطاف لتحميل ملف PDF للتصريح
export const useDownloadPermitPdf = () => {
  const { showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => {
      window.open(`/api/permits/pdf/${id}`, '_blank');
      return Promise.resolve();
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};
