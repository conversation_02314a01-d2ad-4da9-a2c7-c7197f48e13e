#!/bin/bash

echo "🔧 إعداد قاعدة البيانات التجريبية..."

# الانتقال إلى مجلد API
cd "$(dirname "$0")/.."

# تعيين متغير البيئة لقاعدة البيانات
export DATABASE_URL="file:./prisma/test.db"
export NODE_ENV="test"

echo "✅ متغيرات البيئة جاهزة"

# حذف قاعدة البيانات القديمة إن وجدت
if [ -f "prisma/test.db" ]; then
    rm prisma/test.db
    echo "🗑️ تم حذف قاعدة البيانات القديمة"
fi

# إنشاء قاعدة البيانات الجديدة
echo "🔧 إنشاء قاعدة البيانات الجديدة..."

# تشغيل migrations
npx prisma migrate dev --name init --skip-seed

if [ $? -eq 0 ]; then
    echo "✅ تم إنشاء قاعدة البيانات بنجاح"
else
    echo "❌ فشل في إنشاء قاعدة البيانات"
    exit 1
fi

# توليد Prisma Client
echo "🔧 توليد Prisma Client..."

npx prisma generate

if [ $? -eq 0 ]; then
    echo "✅ تم توليد Prisma Client بنجاح"
else
    echo "❌ فشل في توليد Prisma Client"
    exit 1
fi

echo "🎉 تم إعداد قاعدة البيانات التجريبية بنجاح!"
echo "📍 مسار قاعدة البيانات: ./prisma/test.db"
echo "🧪 يمكنك الآن تشغيل الاختبارات باستخدام: npm test"
