import { Router } from 'express';
import multer from 'multer';
import { documentController } from '../controllers/document.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import {
  createDocumentSchema,
  updateDocumentSchema,
  getDocumentSchema,
  deleteDocumentSchema,
  listDocumentsSchema,
} from '../schemas/document.schema.js';
import { config } from '../../../core/config/app.config.js';

export const documentRoutes = Router();

// إعداد Multer لرفع الملفات
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: config.upload.maxFileSize,
  },
  fileFilter: (req, file, cb) => {
    // قبول ملفات PDF فقط
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم. يرجى رفع ملف PDF فقط.'));
    }
  },
});

// مسارات المستندات
documentRoutes.get(
  '/',
  authMiddleware,
  validateRequest(listDocumentsSchema),
  documentController.listDocuments
);

documentRoutes.post(
  '/',
  authMiddleware,
  upload.single('file'),
  validateRequest(createDocumentSchema),
  documentController.createDocument
);

documentRoutes.get(
  '/:id',
  authMiddleware,
  validateRequest(getDocumentSchema),
  documentController.getDocument
);

documentRoutes.put(
  '/:id',
  authMiddleware,
  upload.single('file'),
  validateRequest(updateDocumentSchema),
  documentController.updateDocument
);

documentRoutes.delete(
  '/:id',
  authMiddleware,
  validateRequest(deleteDocumentSchema),
  documentController.deleteDocument
);
