import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { server, http, HttpResponse } from '../../../test/integration-setup';
import { configureStore } from '@reduxjs/toolkit';
import { authReducer } from '../../auth/store/authSlice';
import DeclarationsPage from '../pages/DeclarationsPage';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';

// Mock useMediaQuery hook
vi.mock('@mui/material/useMediaQuery', () => ({
  __esModule: true,
  default: vi.fn(),
}));

// Create a theme for testing
const theme = createTheme({
  direction: 'rtl',
});

// إنشاء متجر وهمي
const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
    preloadedState: {
      auth: {
        isAuthenticated: true,
        user: {
          id: 'user-1',
          username: 'test_user',
          name: 'مستخدم اختبار',
          email: '<EMAIL>',
          role: 'ADMIN' as any,
          createdAt: new Date('2023-01-01T00:00:00.000Z') as any,
          updatedAt: new Date('2023-01-01T00:00:00.000Z') as any,
        },
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
        loading: false,
        error: null,
      },
    },
  });
};

// إنشاء عميل استعلام وهمي
const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  });
};

// مكون الاختبار مع المزودين
const TestComponent = ({ isMobile = false }) => {
  const store = createTestStore();
  const queryClient = createTestQueryClient();

  // Mock the useMediaQuery hook
  (useMediaQuery as any).mockReturnValue(isMobile);

  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider theme={theme}>
          <BrowserRouter>
            <DeclarationsPage />
          </BrowserRouter>
        </ThemeProvider>
      </QueryClientProvider>
    </Provider>
  );
};

describe('DeclarationList Integration Tests', () => {
  beforeEach(() => {
    // إعادة تعيين المعالجات الوهمية قبل كل اختبار
    server.resetHandlers();
    vi.clearAllMocks();
  });

  describe('عرض سطح المكتب', () => {
    it('يجب أن يعرض قائمة البيانات بنجاح في عرض الجدول', async () => {
      // تعريف معالج وهمي لجلب البيانات
      server.use(
        http.get('http://localhost:3001/api/declarations', () => {
          return HttpResponse.json({
            success: true,
            data: [
              {
                id: 'declaration-1',
                declarationNumber: 1001,
                taxNumber: '*********',
                clientName: 'عميل اختبار',
                declarationType: 'IMPORT',
                declarationDate: '2023-01-15T00:00:00.000Z',
                createdAt: '2023-01-15T00:00:00.000Z',
                updatedAt: '2023-01-15T00:00:00.000Z',
              },
              {
                id: 'declaration-2',
                declarationNumber: 1002,
                taxNumber: '*********',
                clientName: 'عميل اختبار آخر',
                declarationType: 'EXPORT',
                declarationDate: '2023-02-15T00:00:00.000Z',
                createdAt: '2023-02-15T00:00:00.000Z',
                updatedAt: '2023-02-15T00:00:00.000Z',
              },
            ],
            pagination: {
              page: 1,
              limit: 10,
              totalItems: 2,
              totalPages: 1,
            },
          }, { status: 200 });
        })
      );

      render(<TestComponent isMobile={false} />);

      // التحقق من عرض البيانات في شكل جدول
      await waitFor(() => {
        expect(screen.getByText('1001')).toBeInTheDocument();
        expect(screen.getByText('1002')).toBeInTheDocument();
        expect(screen.getByText('عميل اختبار')).toBeInTheDocument();
        expect(screen.getByText('عميل اختبار آخر')).toBeInTheDocument();
      });

      // التحقق من عرض رؤوس الجدول
      expect(screen.getByText(/رقم البيان/i)).toBeInTheDocument();
      expect(screen.getByText(/الرقم الضريبي/i)).toBeInTheDocument();
      expect(screen.getByText(/اسم العميل/i)).toBeInTheDocument();
      expect(screen.getByText(/نوع البيان/i)).toBeInTheDocument();

      // التحقق من عرض عمود الإجراءات
      expect(screen.getByText(/الإجراءات/i)).toBeInTheDocument();
    });
  });

  describe('عرض الجوال', () => {
    it('يجب أن يعرض قائمة البيانات بنجاح في عرض البطاقات', async () => {
      // تعريف معالج وهمي لجلب البيانات
      server.use(
        http.get('http://localhost:3001/api/declarations', () => {
          return HttpResponse.json({
            success: true,
            data: [
              {
                id: 'declaration-1',
                declarationNumber: 1001,
                taxNumber: '*********',
                clientName: 'عميل اختبار',
                declarationType: 'IMPORT',
                declarationDate: '2023-01-15T00:00:00.000Z',
                createdAt: '2023-01-15T00:00:00.000Z',
                updatedAt: '2023-01-15T00:00:00.000Z',
              },
              {
                id: 'declaration-2',
                declarationNumber: 1002,
                taxNumber: '*********',
                clientName: 'عميل اختبار آخر',
                declarationType: 'EXPORT',
                declarationDate: '2023-02-15T00:00:00.000Z',
                createdAt: '2023-02-15T00:00:00.000Z',
                updatedAt: '2023-02-15T00:00:00.000Z',
              },
            ],
            pagination: {
              page: 1,
              limit: 10,
              totalItems: 2,
              totalPages: 1,
            },
          }, { status: 200 });
        })
      );

      render(<TestComponent isMobile={true} />);

      // التحقق من عرض البيانات في شكل بطاقات
      await waitFor(() => {
        expect(screen.getByText('1001')).toBeInTheDocument();
        expect(screen.getByText('1002')).toBeInTheDocument();
        expect(screen.getByText('عميل اختبار')).toBeInTheDocument();
        expect(screen.getByText('عميل اختبار آخر')).toBeInTheDocument();
      });

      // التحقق من عرض البيانات في شكل بطاقات
      const cards = screen.getAllByRole('article');
      expect(cards.length).toBe(2);

      // التحقق من عرض الحقول في البطاقة الأولى
      const firstCard = cards[0];
      expect(within(firstCard).getByText('1001')).toBeInTheDocument();
      expect(within(firstCard).getByText('عميل اختبار')).toBeInTheDocument();

      // التحقق من وجود أزرار الإجراءات في البطاقة
      expect(within(firstCard).getByLabelText(/عرض/i)).toBeInTheDocument();
      expect(within(firstCard).getByLabelText(/تعديل/i)).toBeInTheDocument();
      expect(within(firstCard).getByLabelText(/حذف/i)).toBeInTheDocument();
    });
  });

  it('يجب أن يعرض رسالة عند عدم وجود بيانات', async () => {
    // تعريف معالج وهمي لجلب البيانات فارغة
    server.use(
      http.get('http://localhost:3001/api/declarations', () => {
        return HttpResponse.json({
          success: true,
          data: [],
          pagination: {
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
          },
        }, { status: 200 });
      })
    );

    render(<TestComponent />);

    // التحقق من عرض رسالة عدم وجود بيانات
    await waitFor(() => {
      expect(screen.getByText(/لا توجد بيانات/i)).toBeInTheDocument();
    });
  });

  it('يجب أن يعرض رسالة خطأ عند فشل جلب البيانات', async () => {
    // تعريف معالج وهمي لفشل جلب البيانات
    server.use(
      http.get('http://localhost:3001/api/declarations', () => {
        return HttpResponse.json({
          success: false,
          error: 'حدث خطأ أثناء جلب البيانات',
        }, { status: 500 });
      })
    );

    render(<TestComponent />);

    // التحقق من عرض رسالة الخطأ
    await waitFor(() => {
      expect(screen.getByText(/حدث خطأ/i)).toBeInTheDocument();
    });
  });

  it('يجب أن يقوم بتصفية البيانات حسب نوع البيان', async () => {
    // تعريف معالج وهمي لجلب البيانات
    server.use(
      http.get('http://localhost:3001/api/declarations', ({ request }) => {
        const url = new URL(request.url);
        const declarationType = url.searchParams.get('declarationType');

        if (declarationType === 'IMPORT') {
          return HttpResponse.json({
            success: true,
            data: [
              {
                id: 'declaration-1',
                declarationNumber: 1001,
                taxNumber: '*********',
                clientName: 'عميل اختبار',
                declarationType: 'IMPORT',
                declarationDate: '2023-01-15T00:00:00.000Z',
                createdAt: '2023-01-15T00:00:00.000Z',
                updatedAt: '2023-01-15T00:00:00.000Z',
              },
            ],
            pagination: {
              page: 1,
              limit: 10,
              totalItems: 1,
              totalPages: 1,
            },
          }, { status: 200 });
        } else {
          return HttpResponse.json({
            success: true,
            data: [
              {
                id: 'declaration-1',
                declarationNumber: 1001,
                taxNumber: '*********',
                clientName: 'عميل اختبار',
                declarationType: 'IMPORT',
                declarationDate: '2023-01-15T00:00:00.000Z',
                createdAt: '2023-01-15T00:00:00.000Z',
                updatedAt: '2023-01-15T00:00:00.000Z',
              },
              {
                id: 'declaration-2',
                declarationNumber: 1002,
                taxNumber: '*********',
                clientName: 'عميل اختبار آخر',
                declarationType: 'EXPORT',
                declarationDate: '2023-02-15T00:00:00.000Z',
                createdAt: '2023-02-15T00:00:00.000Z',
                updatedAt: '2023-02-15T00:00:00.000Z',
              },
            ],
            pagination: {
              page: 1,
              limit: 10,
              totalItems: 2,
              totalPages: 1,
            },
          }, { status: 200 });
        }
      })
    );

    render(<TestComponent />);

    // التحقق من عرض جميع البيانات في البداية
    await waitFor(() => {
      expect(screen.getByText('1001')).toBeInTheDocument();
      expect(screen.getByText('1002')).toBeInTheDocument();
    });

    // اختيار تصفية حسب نوع البيان (استيراد)
    const filterSelect = screen.getByLabelText(/نوع البيان/i);
    await userEvent.selectOptions(filterSelect, 'IMPORT');

    // التحقق من عرض البيانات المصفاة
    await waitFor(() => {
      expect(screen.getByText('1001')).toBeInTheDocument();
      expect(screen.queryByText('1002')).not.toBeInTheDocument();
    });
  });
});
