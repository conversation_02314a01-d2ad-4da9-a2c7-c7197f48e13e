import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Divider,
  Tab,
  Tabs,
  Typography,
  Paper,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Visibility as VisibilityIcon } from '@mui/icons-material';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { ResponsiveTable } from '@components/ResponsiveTable';
import { AdvancedSearchResults } from '../api/advanced-search.api';
import { SearchType } from '../api/advanced-search.api';

interface SearchResultsProps {
  results: AdvancedSearchResults;
  searchType: SearchType;
}

/**
 * مكون عرض نتائج البحث المتقدم
 */
export const SearchResults = ({ results, searchType }: SearchResultsProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // التعامل مع عرض تفاصيل العنصر
  const handleViewDetails = (type: string, id: string) => {
    switch (type) {
      case 'declaration':
        navigate(`/declarations/${id}`);
        break;
      case 'itemMovement':
        navigate(`/item-movements/${id}`);
        break;
      case 'authorization':
        navigate(`/authorizations/${id}`);
        break;
      case 'release':
        navigate(`/releases/${id}`);
        break;
      case 'permit':
        navigate(`/permits/${id}`);
        break;
      case 'guarantee':
        navigate(`/guarantees/${id}`);
        break;
      case 'receipt':
        navigate(`/receipts/${id}`);
        break;
      case 'client':
        navigate(`/clients/${id}`);
        break;
      case 'document':
        navigate(`/documents/${id}`);
        break;
      default:
        break;
    }
  };

  // تنسيق التاريخ
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    return format(new Date(dateString), 'PPP', { locale: arSA });
  };

  // عرض نتائج البيانات
  const renderDeclarations = () => {
    if (!results.declarations || results.declarations.length === 0) {
      return (
        <Typography variant="body2" color="textSecondary" align="center" py={2}>
          {t('advancedSearch.noResults')}
        </Typography>
      );
    }

    const columns = [
      {
        id: 'declarationNumber',
        label: t('declarations.fields.declarationNumber'),
        align: 'right' as const,
        showInCard: true,
      },
      {
        id: 'taxNumber',
        label: t('declarations.fields.taxNumber'),
        align: 'right' as const,
      },
      {
        id: 'clientName',
        label: t('declarations.fields.clientName'),
        align: 'right' as const,
        render: (row: any) => row.clientName || '-',
        showInCard: true,
      },
      {
        id: 'declarationType',
        label: t('declarations.fields.declarationType'),
        align: 'right' as const,
        render: (row: any) => t(`declarations.types.${row.declarationType}`),
      },
      {
        id: 'declarationDate',
        label: t('declarations.fields.declarationDate'),
        align: 'right' as const,
        render: (row: any) => formatDate(row.declarationDate),
      },
    ];

    return (
      <ResponsiveTable
        columns={columns}
        data={results.declarations}
        totalCount={results.declarations.length}
        page={0}
        rowsPerPage={10}
        onPageChange={() => {}}
        onRowsPerPageChange={() => {}}
        onView={(row) => handleViewDetails('declaration', row.id)}
        emptyMessage={t('advancedSearch.noResults')}
        primaryColumn="declarationNumber"
        secondaryColumn="clientName"
      />
    );
  };

  // عرض نتائج حركة الأصناف
  const renderItemMovements = () => {
    if (!results.itemMovements || results.itemMovements.length === 0) {
      return (
        <Typography variant="body2" color="textSecondary" align="center" py={2}>
          {t('advancedSearch.noResults')}
        </Typography>
      );
    }

    const columns = [
      {
        id: 'movementNumber',
        label: t('itemMovements.fields.movementNumber'),
        align: 'right' as const,
        showInCard: true,
      },
      {
        id: 'declarationNumber',
        label: t('itemMovements.fields.declarationNumber'),
        align: 'right' as const,
        showInCard: true,
      },
      {
        id: 'itemName',
        label: t('itemMovements.fields.itemName'),
        align: 'right' as const,
        render: (row: any) => row.itemName || '-',
      },
      {
        id: 'goodsType',
        label: t('itemMovements.fields.goodsType'),
        align: 'right' as const,
        render: (row: any) => row.goodsType ? t(`itemMovements.goodsTypes.${row.goodsType}`) : '-',
      },
      {
        id: 'movementDate',
        label: t('itemMovements.fields.movementDate'),
        align: 'right' as const,
        render: (row: any) => formatDate(row.movementDate),
      },
    ];

    return (
      <ResponsiveTable
        columns={columns}
        data={results.itemMovements}
        totalCount={results.itemMovements.length}
        page={0}
        rowsPerPage={10}
        onPageChange={() => {}}
        onRowsPerPageChange={() => {}}
        onView={(row) => handleViewDetails('itemMovement', row.id)}
        emptyMessage={t('advancedSearch.noResults')}
        primaryColumn="movementNumber"
        secondaryColumn="declarationNumber"
      />
    );
  };

  // عرض النتائج حسب نوع البحث
  const renderResults = () => {
    if (searchType === SearchType.ALL) {
      return (
        <Box>
          {results.declarations && results.declarations.length > 0 && (
            <Box mb={4}>
              <Typography variant="h6" gutterBottom>
                {t('advancedSearch.searchTypes.DECLARATIONS')}
              </Typography>
              {renderDeclarations()}
            </Box>
          )}

          {results.itemMovements && results.itemMovements.length > 0 && (
            <Box mb={4}>
              <Typography variant="h6" gutterBottom>
                {t('advancedSearch.searchTypes.ITEM_MOVEMENTS')}
              </Typography>
              {renderItemMovements()}
            </Box>
          )}

          {/* يمكن إضافة المزيد من أنواع النتائج هنا */}

          {!results.declarations?.length &&
           !results.itemMovements?.length &&
           !results.authorizations?.length &&
           !results.releases?.length &&
           !results.permits?.length &&
           !results.guarantees?.length &&
           !results.receipts?.length &&
           !results.clients?.length &&
           !results.documents?.length && (
            <Typography variant="body1" color="textSecondary" align="center" py={4}>
              {t('advancedSearch.noResults')}
            </Typography>
          )}
        </Box>
      );
    }

    switch (searchType) {
      case SearchType.DECLARATIONS:
        return renderDeclarations();
      case SearchType.ITEM_MOVEMENTS:
        return renderItemMovements();
      // يمكن إضافة المزيد من أنواع النتائج هنا
      default:
        return (
          <Typography variant="body1" color="textSecondary" align="center" py={4}>
            {t('advancedSearch.noResults')}
          </Typography>
        );
    }
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('advancedSearch.resultsTitle')}
        </Typography>
        <Divider sx={{ mb: 2 }} />

        {renderResults()}
      </CardContent>
    </Card>
  );
};
