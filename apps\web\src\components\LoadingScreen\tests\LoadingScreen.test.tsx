import React from 'react';
import { render, screen } from '@testing-library/react';
import { LoadingScreen } from '../LoadingScreen';

describe('LoadingScreen Component', () => {
  test('renders with default message', () => {
    render(<LoadingScreen />);
    expect(screen.getByText('جاري التحميل...')).toBeInTheDocument();
  });

  test('renders with custom message', () => {
    render(<LoadingScreen message="يتم تحميل البيانات" />);
    expect(screen.getByText('يتم تحميل البيانات')).toBeInTheDocument();
  });

  test('does not show message when showMessage is false', () => {
    render(<LoadingScreen showMessage={false} />);
    expect(screen.queryByText('جاري التحميل...')).not.toBeInTheDocument();
  });

  test('renders without background when withBackground is false', () => {
    const { container } = render(<LoadingScreen withBackground={false} />);
    // في حالة عدم وجود خلفية، لن يكون هناك عنصر Paper
    expect(container.querySelector('.MuiPaper-root')).not.toBeInTheDocument();
  });
});
