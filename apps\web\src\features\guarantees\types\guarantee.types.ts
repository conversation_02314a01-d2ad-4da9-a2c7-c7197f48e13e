/**
 * أنواع الضمانات
 */
export enum GuaranteeType {
  RETURNABLE = 'RETURNABLE',
  NON_RETURNABLE = 'NON_RETURNABLE',
}

/**
 * طبيعة الضمانات
 */
export enum GuaranteeNature {
  DOCUMENTS = 'DOCUMENTS',
  FINANCIAL = 'FINANCIAL',
}

/**
 * عملات الضمانات
 */
export enum GuaranteeCurrency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  SAR = 'SAR',
}

/**
 * واجهة الضمان
 */
export interface Guarantee {
  id: string;
  guaranteeType: GuaranteeType;
  guaranteeNature: GuaranteeNature;
  guaranteeNumber: string;
  issueDate: string;
  expiryDate?: string;
  amount: number;
  currency: GuaranteeCurrency;
  notes?: string;
  pdfFile?: string;
  declarationId: string;
  declarationNumber?: number;
  clientName?: string;
  isReturned: boolean;
  returnDate?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * واجهة إنشاء ضمان جديد
 */
export interface CreateGuaranteeDto {
  declarationId: string;
  guaranteeType: GuaranteeType;
  guaranteeNature: GuaranteeNature;
  guaranteeNumber: string;
  issueDate: string;
  expiryDate?: string;
  amount: number;
  currency: GuaranteeCurrency;
  notes?: string;
}

/**
 * واجهة تحديث ضمان
 */
export interface UpdateGuaranteeDto {
  declarationId?: string;
  guaranteeType?: GuaranteeType;
  guaranteeNature?: GuaranteeNature;
  guaranteeNumber?: string;
  issueDate?: string;
  expiryDate?: string;
  amount?: number;
  currency?: GuaranteeCurrency;
  notes?: string;
  isReturned?: boolean;
  returnDate?: string;
}

/**
 * واجهة إنشاء ضمان مع ملف
 */
export interface CreateGuaranteeWithFileDto {
  data: CreateGuaranteeDto;
  file?: File;
}

/**
 * واجهة تحديث ضمان مع ملف
 */
export interface UpdateGuaranteeWithFileDto {
  id: string;
  data: UpdateGuaranteeDto;
  file?: File;
}

/**
 * واجهة معلمات البحث عن الضمانات
 */
export interface GuaranteeSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationId?: string;
  guaranteeType?: GuaranteeType;
  guaranteeNature?: GuaranteeNature;
  fromDate?: string;
  toDate?: string;
  isReturned?: boolean;
  isActive?: boolean;
}

/**
 * واجهة قيم نموذج الضمان
 */
export interface GuaranteeFormValues {
  declarationId: string;
  guaranteeType: GuaranteeType;
  guaranteeNature: GuaranteeNature;
  guaranteeNumber: string;
  issueDate: Date | null;
  expiryDate: Date | null;
  amount: number;
  currency: GuaranteeCurrency;
  notes: string;
  file: File | null;
  isReturned?: boolean;
  returnDate?: Date | null;
}
