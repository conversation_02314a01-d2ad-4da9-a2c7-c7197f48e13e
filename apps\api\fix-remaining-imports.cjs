#!/usr/bin/env node

/**
 * سكريبت لإصلاح مسارات الاستيراد المتبقية بناءً على أخطاء البناء
 */

const fs = require('fs');
const path = require('path');

// قائمة بالإصلاحات المطلوبة
const fixes = [
  // Custom Forms
  {
    file: 'src/modules/custom-forms/controllers/custom-form.controller.ts',
    fixes: [
      { from: '../services/custom-form.service', to: '../services/custom-form.service.js' },
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  },
  {
    file: 'src/modules/custom-forms/services/custom-form.service.ts',
    fixes: [
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  },
  
  // Declarations
  {
    file: 'src/modules/declarations/routes/declaration.routes.ts',
    fixes: [
      { from: '../controllers/declaration.controller', to: '../controllers/declaration.controller.js' },
      { from: '../../../core/middleware/validation.middleware', to: '../../../core/middleware/validation.middleware.js' },
      { from: '../../../core/middleware/auth.middleware', to: '../../../core/middleware/auth.middleware.js' },
      { from: '../schemas/declaration.schema', to: '../schemas/declaration.schema.js' },
      { from: '../../../core/config/app.config', to: '../../../core/config/app.config.js' }
    ]
  },
  {
    file: 'src/modules/declarations/tests/declaration.integration.test.ts',
    fixes: [
      { from: '../../../core/config/app.config', to: '../../../core/config/app.config.js' }
    ]
  },
  
  // Documents
  {
    file: 'src/modules/documents/controllers/document.controller.ts',
    fixes: [
      { from: '../services/document.service', to: '../services/document.service.js' },
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  },
  {
    file: 'src/modules/documents/routes/document.routes.ts',
    fixes: [
      { from: '../controllers/document.controller', to: '../controllers/document.controller.js' },
      { from: '../../../core/middleware/validation.middleware', to: '../../../core/middleware/validation.middleware.js' },
      { from: '../../../core/middleware/auth.middleware', to: '../../../core/middleware/auth.middleware.js' },
      { from: '../schemas/document.schema', to: '../schemas/document.schema.js' },
      { from: '../../../core/config/app.config', to: '../../../core/config/app.config.js' }
    ]
  },
  
  // Guarantees
  {
    file: 'src/modules/guarantees/controllers/guarantee.controller.ts',
    fixes: [
      { from: '../services/guarantee.service', to: '../services/guarantee.service.js' },
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  },
  {
    file: 'src/modules/guarantees/routes/guarantee.routes.ts',
    fixes: [
      { from: '../controllers/guarantee.controller', to: '../controllers/guarantee.controller.js' },
      { from: '../../../core/middleware/validation.middleware', to: '../../../core/middleware/validation.middleware.js' },
      { from: '../../../core/middleware/auth.middleware', to: '../../../core/middleware/auth.middleware.js' },
      { from: '../schemas/guarantee.schema', to: '../schemas/guarantee.schema.js' },
      { from: '../../../core/config/app.config', to: '../../../core/config/app.config.js' }
    ]
  },
  {
    file: 'src/modules/guarantees/services/guarantee.service.ts',
    fixes: [
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  },
  
  // Health
  {
    file: 'src/modules/health/routes/health.routes.ts',
    fixes: [
      { from: '../controllers/health.controller', to: '../controllers/health.controller.js' }
    ]
  },
  
  // Permits
  {
    file: 'src/modules/permits/controllers/permit.controller.ts',
    fixes: [
      { from: '../services/permit.service', to: '../services/permit.service.js' },
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  },
  {
    file: 'src/modules/permits/routes/permit.routes.ts',
    fixes: [
      { from: '../controllers/permit.controller', to: '../controllers/permit.controller.js' },
      { from: '../../../core/middleware/validation.middleware', to: '../../../core/middleware/validation.middleware.js' },
      { from: '../../../core/middleware/auth.middleware', to: '../../../core/middleware/auth.middleware.js' },
      { from: '../schemas/permit.schema', to: '../schemas/permit.schema.js' },
      { from: '../../../core/config/app.config', to: '../../../core/config/app.config.js' }
    ]
  },
  {
    file: 'src/modules/permits/services/permit.service.ts',
    fixes: [
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  },
  
  // Receipts
  {
    file: 'src/modules/receipts/controllers/receipt.controller.ts',
    fixes: [
      { from: '../services/receipt.service', to: '../services/receipt.service.js' },
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  },
  {
    file: 'src/modules/receipts/routes/receipt.routes.ts',
    fixes: [
      { from: '../controllers/receipt.controller', to: '../controllers/receipt.controller.js' },
      { from: '../../../core/middleware/validation.middleware', to: '../../../core/middleware/validation.middleware.js' },
      { from: '../../../core/middleware/auth.middleware', to: '../../../core/middleware/auth.middleware.js' },
      { from: '../schemas/receipt.schema', to: '../schemas/receipt.schema.js' },
      { from: '../../../core/config/app.config', to: '../../../core/config/app.config.js' }
    ]
  },
  
  // Releases
  {
    file: 'src/modules/releases/controllers/release.controller.ts',
    fixes: [
      { from: '../services/release.service', to: '../services/release.service.js' },
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  },
  {
    file: 'src/modules/releases/routes/release.routes.ts',
    fixes: [
      { from: '../controllers/release.controller', to: '../controllers/release.controller.js' },
      { from: '../../../core/middleware/validation.middleware', to: '../../../core/middleware/validation.middleware.js' },
      { from: '../../../core/middleware/auth.middleware', to: '../../../core/middleware/auth.middleware.js' },
      { from: '../schemas/release.schema', to: '../schemas/release.schema.js' },
      { from: '../../../core/config/app.config', to: '../../../core/config/app.config.js' }
    ]
  },
  
  // Reports
  {
    file: 'src/modules/reports/controllers/report-template.controller.ts',
    fixes: [
      { from: '../services/report-template.service', to: '../services/report-template.service.js' },
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  },
  
  // Settings
  {
    file: 'src/modules/settings/controllers/settings.controller.ts',
    fixes: [
      { from: '../services/settings.service', to: '../services/settings.service.js' },
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' },
      { from: '../../../core/config/app.config', to: '../../../core/config/app.config.js' }
    ]
  },
  {
    file: 'src/modules/settings/services/settings.service.ts',
    fixes: [
      { from: '../../../core/middleware/error.middleware', to: '../../../core/middleware/error.middleware.js' }
    ]
  }
];

/**
 * يطبق الإصلاحات على ملف واحد
 */
function fixFile(filePath, fileFixes) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  الملف غير موجود: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    for (const fix of fileFixes) {
      const regex = new RegExp(`from\\s+['"]${fix.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g');
      if (regex.test(content)) {
        content = content.replace(regex, `from '${fix.to}'`);
        hasChanges = true;
        console.log(`  ${fix.from} -> ${fix.to}`);
      }
    }

    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    return false;
  } catch (error) {
    console.error(`خطأ في معالجة الملف ${filePath}:`, error.message);
    return false;
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔧 بدء إصلاح مسارات الاستيراد المتبقية...\n');
  
  let totalFixed = 0;
  
  for (const item of fixes) {
    const fullPath = path.resolve(item.file);
    console.log(`📁 معالجة: ${item.file}`);
    
    if (fixFile(fullPath, item.fixes)) {
      totalFixed++;
    }
  }
  
  console.log(`\n🎉 انتهى! تم إصلاح ${totalFixed} ملف.`);
  
  if (totalFixed > 0) {
    console.log('\n📝 يُنصح بتشغيل:');
    console.log('npm run build  # للتحقق من الإصلاحات');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}
