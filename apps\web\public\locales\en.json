{"app": {"name": "Alnoor Archive System", "description": "Integrated system for managing and archiving data and documents"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "confirm": "Confirm", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "print": "Print", "view": "View", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "close": "Close", "yes": "Yes", "no": "No", "all": "All", "none": "None", "required": "Required", "optional": "Optional", "actions": "Actions", "details": "Details", "more": "More", "less": "Less", "date": "Date", "time": "Time", "from": "From", "to": "To", "status": "Status", "type": "Type", "name": "Name", "description": "Description", "notes": "Notes", "number": "Number", "value": "Value", "amount": "Amount", "total": "Total", "count": "Count", "weight": "Weight", "file": "File", "files": "Files", "upload": "Upload", "download": "Download", "select": "Select", "selectAll": "Select All", "selectNone": "Select None", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "id": "ID", "createdAt": "Created At", "updatedAt": "Updated At", "createdBy": "Created By", "updatedBy": "Updated By", "noData": "No data", "noResults": "No results", "noMatches": "No matches", "noOptions": "No options", "noItems": "No items", "noFiles": "No files", "noPermission": "No permission", "notFound": "Not found", "notAvailable": "Not available", "notApplicable": "Not applicable", "notSpecified": "Not specified", "unknown": "Unknown", "other": "Other"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginError": "Invalid username or password", "loginSuccess": "Logged in successfully", "logoutSuccess": "Logged out successfully", "sessionExpired": "Session expired, please login again", "unauthorized": "You are not authorized to access this page", "accessDenied": "Access denied"}, "navigation": {"dashboard": "Dashboard", "declarations": "Declarations", "itemsMovement": "Items Movement", "authorizations": "Authorizations", "releases": "Releases", "permits": "Permits", "guarantees": "Guarantees", "receipts": "Receipts", "clients": "Clients", "documents": "Office Documents", "advancedSearch": "Advanced Search", "reports": "Reports", "customForms": "Custom Forms", "database": "Database", "apiTest": "API Connection Test", "settings": "Settings", "users": "Users", "profile": "Profile", "help": "Help", "about": "About"}, "notifications": {"title": "Notifications", "unread": "unread", "markRead": "<PERSON> as read", "markAllRead": "Mark all as read", "clearAll": "Clear all", "noNotifications": "No notifications", "newNotification": "New notification", "types": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Info"}}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to Alnoor Archive System", "overview": "Overview", "statistics": "Statistics", "recentActivity": "Recent Activity", "recentDeclarations": "Recent Declarations", "declarationsByType": "Declarations by Type", "percentage": "Percentage", "viewAll": "View All", "noData": "No data available", "loading": "Loading...", "error": "Error loading data"}, "declarations": {"title": "Declarations", "addDeclaration": "Add New Declaration", "editDeclaration": "Edit Declaration", "viewDeclaration": "View Declaration", "deleteDeclaration": "Delete Declaration", "declarationDetails": "Declaration Details", "declarationNumber": "Declaration Number", "taxNumber": "Tax Number", "clientName": "Client Name", "companyName": "Company Name", "policyNumber": "Policy Number", "invoiceNumber": "Invoice Number", "gatewayEntryNumber": "Gateway Entry Number", "declarationType": "Declaration Type", "declarationDate": "Declaration Date", "count": "Count", "weight": "Weight", "goodsType": "Goods Type", "itemsCount": "Items Count", "entryDate": "Entry Date", "exitDate": "Exit Date", "pdfFile": "PDF File", "drivers": "Drivers", "addDriver": "Add Driver", "editDriver": "Edit Driver", "deleteDriver": "Delete Driver", "driverName": "Driver Name", "truckNumber": "Truck Number", "trailerNumber": "Trailer Number", "driverPhone": "Driver Phone", "import": "Import", "export": "Export", "deleteConfirmation": "Are you sure you want to delete this declaration?", "deleteSuccess": "Declaration deleted successfully", "saveSuccess": "Declaration saved successfully", "updateSuccess": "Declaration updated successfully", "validationError": "Please check the entered data", "requiredField": "This field is required", "invalidNumber": "Please enter a valid number", "invalidDate": "Please enter a valid date", "pdfRequired": "Please attach a PDF file", "duplicateNumber": "Declaration number already exists"}, "itemsMovement": {"title": "Items Movement", "addMovement": "Add New Movement", "editMovement": "Edit Movement", "viewMovement": "View Movement", "deleteMovement": "Delete Movement", "movementDetails": "Movement Details", "movementNumber": "Movement Number", "movementDate": "Movement Date", "declarationNumber": "Declaration Number", "itemNumber": "Item Number", "invoiceNumber": "Invoice Number", "packingListNumber": "Packing List Number", "identificationClause": "Identification Clause", "itemName": "Item Name", "count": "Count", "packageType": "Package Type", "goodsType": "Goods Type", "countryOfOrigin": "Country of Origin", "itemValue": "Item Value", "currency": "<PERSON><PERSON><PERSON><PERSON>", "totalValue": "Total Value", "pdfFile": "PDF File", "pallet": "<PERSON><PERSON><PERSON>", "carton": "<PERSON><PERSON>", "barrel": "Barrel", "humanMedicine": "Human Medicine", "laboratoryMaterials": "Laboratory Materials", "medicalSupplies": "Medical Supplies", "sugarStrips": "Sugar Strips", "medicalDevices": "Medical Devices", "miscellaneous": "Miscellaneous", "usd": "USD", "eur": "EUR", "gbp": "GBP", "deleteConfirmation": "Are you sure you want to delete this movement?", "deleteSuccess": "Movement deleted successfully", "saveSuccess": "Movement saved successfully", "updateSuccess": "Movement updated successfully"}, "authorizations": {"title": "Authorizations", "addNew": "Add New Authorization", "create": "Create New Authorization", "edit": "Edit Authorization", "view": "View Authorization", "delete": "Delete Authorization", "details": "Authorization Details", "number": "Authorization Number", "clientName": "Client Name", "taxNumber": "Tax Number", "type": "Authorization Type", "startDate": "Start Date", "endDate": "End Date", "pdfFile": "PDF File", "searchPlaceholder": "Search by name or tax number", "confirmDelete": "Are you sure you want to delete this authorization?", "deleteSuccess": "Authorization deleted successfully", "saveSuccess": "Authorization saved successfully", "updateSuccess": "Authorization updated successfully", "types": {"FOLLOW_UP": "Follow Up", "CLEARANCE": "Clearance", "RECEIPT": "Receipt", "FULL": "Full Authorization"}}, "settings": {"title": "System Settings", "companyInfo": "Company Information", "companyName": "Company Name", "companyAddress": "Company Address", "companyPhone": "Company Phone", "companyEmail": "Company Email", "companyWebsite": "Company Website", "companyLogo": "Company Logo", "systemSettings": "System Settings", "defaultLanguage": "Default Language", "updateSuccess": "Settings updated successfully"}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "security": "Security", "name": "Name", "email": "Email", "username": "Username", "role": "Role", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "changePassword": "Change Password", "updateSuccess": "Profile updated successfully", "passwordChangeSuccess": "Password changed successfully"}, "advancedSearch": {"title": "Advanced Search", "description": "Search across all system modules using multiple criteria", "formTitle": "Search Criteria", "resultsTitle": "Search Results", "noResults": "No results match your search criteria", "searching": "Searching...", "searchType": "Search Type", "keyword": "Keyword", "taxNumber": "Tax Number", "client": "Client", "declarationNumber": "Declaration Number", "invoiceNumber": "Invoice Number", "declarationType": "Declaration Type", "goodsType": "Goods Type", "fromDate": "From Date", "toDate": "To Date", "searchTypes": {"ALL": "All Modules", "DECLARATIONS": "Declarations", "ITEM_MOVEMENTS": "Item Movements", "AUTHORIZATIONS": "Authorizations", "RELEASES": "Releases", "PERMITS": "Permits", "GUARANTEES": "Guarantees", "RECEIPTS": "Receipts", "CLIENTS": "Clients", "DOCUMENTS": "Office Documents"}}, "database": {"title": "Database Management", "adminOnlyFeature": "This feature is only available to system administrators", "backups": "Backups", "backupsDescription": "Manage database backups", "backupsList": "Backups List", "createBackup": "Create Backup", "createBackupDescription": "Create a new backup of the database", "restore": "Rest<PERSON>", "download": "Download", "fileName": "File Name", "size": "Size", "createdAt": "Created At", "noBackups": "No backups available", "backupCreated": "Backup created successfully", "backupCreateError": "Failed to create backup", "backupRestored": "Database restored successfully", "backupRestoreError": "Failed to restore database", "backupDeleted": "Backup deleted successfully", "backupDeleteError": "Failed to delete backup", "confirmDeleteBackup": "Confirm Backup Deletion", "confirmDeleteBackupMessage": "Are you sure you want to delete this backup? This action cannot be undone.", "confirmRestoreBackup": "Confirm Database Restore", "confirmRestoreBackupMessage": "Are you sure you want to restore the database from this backup? All current data will be replaced.", "export": "Export", "exportDatabase": "Export Database", "exportDescription": "Export database to SQL file", "exportDatabaseDescription": "Export the current database to an SQL file that can be used to restore the database later", "dangerZone": "Danger Zone", "dangerZoneDescription": "The following operations are dangerous and may result in data loss", "initialize": "Initialize", "initializeDatabase": "Initialize Database", "initializeDatabaseDescription": "Reset the database to its initial state", "initializeDatabaseWarning": "Warning: This will delete all current data and reset the database. A backup will be automatically created before initialization.", "confirmInitializeDatabase": "Confirm Database Initialization", "confirmInitializeDatabaseMessage": "Are you sure you want to initialize the database? All current data will be deleted. A backup will be automatically created before initialization.", "databaseInitialized": "Database initialized successfully", "databaseInitializeError": "Failed to initialize database"}}