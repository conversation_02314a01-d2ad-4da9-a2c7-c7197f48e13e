import { ReactNode, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { CircularProgress, Box } from '@mui/material';

interface ProtectedRouteProps {
  children: ReactNode;
  redirectTo?: string;
}

/**
 * مكون المسار المحمي
 * يتحقق من حالة المصادقة قبل عرض المحتوى
 * إذا لم يكن المستخدم مصادقًا، يتم توجيهه إلى صفحة تسجيل الدخول
 */
const ProtectedRoute = ({ children, redirectTo = '/login' }: ProtectedRouteProps) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  // إذا كان التحميل جاريًا، عرض مؤشر التحميل
  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // إذا لم يكن المستخدم مصادقًا، توجيهه إلى صفحة تسجيل الدخول
  if (!isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // إذا كان المستخدم مصادقًا، عرض المحتوى
  return <>{children}</>;
};

export default ProtectedRoute;
