import { z } from 'zod';

// مخطط تسجيل الدخول
export const loginSchema = z.object({
  body: z.object({
    username: z.string({
      required_error: 'اسم المستخدم مطلوب',
    }),
    password: z.string({
      required_error: 'كلمة المرور مطلوبة',
    }),
  }),
});

// مخطط تجديد التوكن
export const refreshTokenSchema = z.object({
  body: z.object({
    refreshToken: z.string({
      required_error: 'توكن التجديد مطلوب',
    }),
  }),
});

// مخطط تسجيل مستخدم جديد
export const registerSchema = z.object({
  body: z.object({
    username: z.string({
      required_error: 'اسم المستخدم مطلوب',
    }).min(3, 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل'),
    password: z.string({
      required_error: 'كلمة المرور مطلوبة',
    }).min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
    name: z.string({
      required_error: 'الاسم مطلوب',
    }),
    email: z.string({
      required_error: 'البريد الإلكتروني مطلوب',
    }).email('البريد الإلكتروني غير صالح'),
    role: z.enum(['ADMIN', 'USER'], {
      required_error: 'الدور مطلوب',
    }),
  }),
});

// مخطط تغيير كلمة المرور
export const changePasswordSchema = z.object({
  body: z.object({
    currentPassword: z.string({
      required_error: 'كلمة المرور الحالية مطلوبة',
    }),
    newPassword: z.string({
      required_error: 'كلمة المرور الجديدة مطلوبة',
    }).min(6, 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل'),
  }),
});
