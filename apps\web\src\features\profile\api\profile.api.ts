import axios from 'axios';
import { User } from '../types/profile.types';

// واجهة طلب تحديث الملف الشخصي
export interface UpdateProfileRequest {
  name?: string;
  email?: string;
}

// واجهة طلب تغيير كلمة المرور
export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

/**
 * الحصول على معلومات الملف الشخصي
 */
export const getProfile = async (): Promise<User> => {
  const response = await axios.get('/api/users/profile');
  return response.data;
};

/**
 * تحديث معلومات الملف الشخصي
 */
export const updateProfile = async (data: UpdateProfileRequest): Promise<User> => {
  const response = await axios.put('/api/users/profile', data);
  return response.data;
};

/**
 * تغيير كلمة المرور
 */
export const changePassword = async (data: ChangePasswordRequest): Promise<{ success: boolean }> => {
  const response = await axios.put('/api/users/change-password', data);
  return response.data;
};
