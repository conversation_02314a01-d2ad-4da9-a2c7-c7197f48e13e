import { Routes, Route, Navigate } from 'react-router-dom';
import ProtectedRoute from '@features/auth/components/ProtectedRoute';
import { LazyLoad } from '../../components';

// Layouts
import AuthLayout from '../layouts/AuthLayout';
import MainLayout from '../layouts/MainLayout';
import DashboardLayout from '../layouts/DashboardLayout';

// استيراد المكونات الكسولة
import {
  // صفحات المصادقة
  LazyLoginPage,
  LazyForgotPasswordPage,
  LazyResetPasswordPage,

  // صفحة لوحة التحكم
  LazyDashboardPage,

  // صفحات البيانات
  LazyDeclarationsPage,
  LazyDeclarationDetailsPage,
  LazyDeclarationFormPage,

  // صفحات حركة الأصناف
  LazyItemMovementsPage,
  LazyItemMovementDetailsPage,
  LazyItemMovementFormPage,

  // صفحات التفويضات
  LazyAuthorizationsPage,
  LazyAuthorizationDetailsPage,
  LazyAuthorizationFormPage,

  // صفحات الإفراجات
  LazyReleasesPage,
  LazyReleaseDetailsPage,
  LazyReleaseFormPage,

  // صفحات التصاريح
  LazyPermitsPage,
  LazyPermitDetailsPage,
  LazyPermitFormPage,

  // صفحات الضمانات
  LazyGuaranteesPage,
  LazyGuaranteeDetailsPage,
  LazyGuaranteeFormPage,

  // صفحات الاستلامات
  LazyReceiptsPage,
  LazyReceiptDetailsPage,
  LazyReceiptFormPage,

  // صفحات العملاء
  LazyClientsPage,
  LazyClientDetailsPage,
  LazyClientFormPage,

  // صفحات المستندات
  LazyDocumentsPage,
  LazyDocumentDetailsPage,
  LazyDocumentFormPage,

  // صفحة البحث المتقدم
  LazyAdvancedSearchPage,

  // صفحات النماذج المخصصة
  LazyCustomFormsPage,
  LazyCustomFormBuilderPage,

  // صفحات التقارير
  LazyReportsPage,
  LazyReportBuilderPage,

  // صفحة قاعدة البيانات
  LazyDatabasePage,

  // صفحة اختبار API
  LazyApiTestPage,

  // صفحة الإعدادات
  LazySettingsPage,

  // صفحة الملف الشخصي
  LazyProfilePage,

  // صفحات الخطأ
  LazyNotFoundPage,
} from '../routes/lazyRoutes';

const AppRouter = () => {
  return (
    <Routes>
      {/* Auth Routes */}
      <Route element={<AuthLayout />}>
        <Route path="/login" element={<LazyLoad component={LazyLoginPage} loadingMessage="جاري تحميل صفحة تسجيل الدخول..." />} />
        <Route path="/forgot-password" element={<LazyLoad component={LazyForgotPasswordPage} loadingMessage="جاري تحميل صفحة استعادة كلمة المرور..." />} />
        <Route path="/reset-password" element={<LazyLoad component={LazyResetPasswordPage} loadingMessage="جاري تحميل صفحة إعادة تعيين كلمة المرور..." />} />
      </Route>

      {/* Main Routes */}
      <Route
        element={
          <ProtectedRoute>
            <MainLayout />
          </ProtectedRoute>
        }
      >
        <Route path="/" element={<Navigate to="/dashboard" replace />} />

        {/* Dashboard Routes */}
        <Route element={<DashboardLayout />}>
          <Route path="/dashboard" element={<LazyLoad component={LazyDashboardPage} loadingMessage="جاري تحميل لوحة التحكم..." />} />
        </Route>

        {/* Declarations Routes */}
        <Route path="/declarations" element={<LazyLoad component={LazyDeclarationsPage} loadingMessage="جاري تحميل صفحة البيانات..." />} />
        <Route path="/declarations/new" element={<LazyLoad component={LazyDeclarationFormPage} loadingMessage="جاري تحميل نموذج البيان..." />} />
        <Route path="/declarations/:id" element={<LazyLoad component={LazyDeclarationDetailsPage} loadingMessage="جاري تحميل تفاصيل البيان..." />} />
        <Route path="/declarations/:id/edit" element={<LazyLoad component={LazyDeclarationFormPage} loadingMessage="جاري تحميل نموذج البيان..." />} />

        {/* Items Movement Routes */}
        <Route path="/items-movement" element={<LazyLoad component={LazyItemMovementsPage} loadingMessage="جاري تحميل صفحة حركة الأصناف..." />} />
        <Route path="/items-movement/new" element={<LazyLoad component={LazyItemMovementFormPage} loadingMessage="جاري تحميل نموذج حركة الصنف..." />} />
        <Route path="/items-movement/:id" element={<LazyLoad component={LazyItemMovementDetailsPage} loadingMessage="جاري تحميل تفاصيل حركة الصنف..." />} />
        <Route path="/items-movement/:id/edit" element={<LazyLoad component={LazyItemMovementFormPage} loadingMessage="جاري تحميل نموذج حركة الصنف..." />} />

        {/* Authorizations Routes */}
        <Route path="/authorizations" element={<LazyLoad component={LazyAuthorizationsPage} loadingMessage="جاري تحميل صفحة التفويضات..." />} />
        <Route path="/authorizations/new" element={<LazyLoad component={LazyAuthorizationFormPage} loadingMessage="جاري تحميل نموذج التفويض..." />} />
        <Route path="/authorizations/:id" element={<LazyLoad component={LazyAuthorizationDetailsPage} loadingMessage="جاري تحميل تفاصيل التفويض..." />} />
        <Route path="/authorizations/:id/edit" element={<LazyLoad component={LazyAuthorizationFormPage} loadingMessage="جاري تحميل نموذج التفويض..." />} />

        {/* Releases Routes */}
        <Route path="/releases" element={<LazyLoad component={LazyReleasesPage} loadingMessage="جاري تحميل صفحة الإفراجات..." />} />
        <Route path="/releases/new" element={<LazyLoad component={LazyReleaseFormPage} loadingMessage="جاري تحميل نموذج الإفراج..." />} />
        <Route path="/releases/:id" element={<LazyLoad component={LazyReleaseDetailsPage} loadingMessage="جاري تحميل تفاصيل الإفراج..." />} />
        <Route path="/releases/:id/edit" element={<LazyLoad component={LazyReleaseFormPage} loadingMessage="جاري تحميل نموذج الإفراج..." />} />

        {/* Permits Routes */}
        <Route path="/permits" element={<LazyLoad component={LazyPermitsPage} loadingMessage="جاري تحميل صفحة التصاريح..." />} />
        <Route path="/permits/new" element={<LazyLoad component={LazyPermitFormPage} loadingMessage="جاري تحميل نموذج التصريح..." />} />
        <Route path="/permits/:id" element={<LazyLoad component={LazyPermitDetailsPage} loadingMessage="جاري تحميل تفاصيل التصريح..." />} />
        <Route path="/permits/:id/edit" element={<LazyLoad component={LazyPermitFormPage} loadingMessage="جاري تحميل نموذج التصريح..." />} />

        {/* Clients Routes */}
        <Route path="/clients" element={<LazyLoad component={LazyClientsPage} loadingMessage="جاري تحميل صفحة العملاء..." />} />
        <Route path="/clients/new" element={<LazyLoad component={LazyClientFormPage} loadingMessage="جاري تحميل نموذج العميل..." />} />
        <Route path="/clients/:id" element={<LazyLoad component={LazyClientDetailsPage} loadingMessage="جاري تحميل تفاصيل العميل..." />} />
        <Route path="/clients/:id/edit" element={<LazyLoad component={LazyClientFormPage} loadingMessage="جاري تحميل نموذج العميل..." />} />

        {/* Settings Routes */}
        <Route path="/settings" element={<LazyLoad component={LazySettingsPage} loadingMessage="جاري تحميل صفحة الإعدادات..." />} />

        {/* Profile Routes */}
        <Route path="/profile" element={<LazyLoad component={LazyProfilePage} loadingMessage="جاري تحميل صفحة الملف الشخصي..." />} />

        {/* Documents Routes */}
        <Route path="/documents" element={<LazyLoad component={LazyDocumentsPage} loadingMessage="جاري تحميل صفحة المستندات..." />} />
        <Route path="/documents/new" element={<LazyLoad component={LazyDocumentFormPage} loadingMessage="جاري تحميل نموذج المستند..." />} />
        <Route path="/documents/:id" element={<LazyLoad component={LazyDocumentDetailsPage} loadingMessage="جاري تحميل تفاصيل المستند..." />} />
        <Route path="/documents/:id/edit" element={<LazyLoad component={LazyDocumentFormPage} loadingMessage="جاري تحميل نموذج المستند..." />} />

        {/* Guarantees Routes */}
        <Route path="/guarantees" element={<LazyLoad component={LazyGuaranteesPage} loadingMessage="جاري تحميل صفحة الضمانات..." />} />
        <Route path="/guarantees/new" element={<LazyLoad component={LazyGuaranteeFormPage} loadingMessage="جاري تحميل نموذج الضمان..." />} />
        <Route path="/guarantees/:id" element={<LazyLoad component={LazyGuaranteeDetailsPage} loadingMessage="جاري تحميل تفاصيل الضمان..." />} />
        <Route path="/guarantees/:id/edit" element={<LazyLoad component={LazyGuaranteeFormPage} loadingMessage="جاري تحميل نموذج الضمان..." />} />

        {/* Receipts Routes */}
        <Route path="/receipts" element={<LazyLoad component={LazyReceiptsPage} loadingMessage="جاري تحميل صفحة الاستلامات..." />} />
        <Route path="/receipts/new" element={<LazyLoad component={LazyReceiptFormPage} loadingMessage="جاري تحميل نموذج الاستلام..." />} />
        <Route path="/receipts/:id" element={<LazyLoad component={LazyReceiptDetailsPage} loadingMessage="جاري تحميل تفاصيل الاستلام..." />} />
        <Route path="/receipts/:id/edit" element={<LazyLoad component={LazyReceiptFormPage} loadingMessage="جاري تحميل نموذج الاستلام..." />} />

        {/* Advanced Search Routes */}
        <Route path="/advanced-search" element={<LazyLoad component={LazyAdvancedSearchPage} loadingMessage="جاري تحميل صفحة البحث المتقدم..." />} />

        {/* Custom Forms Routes */}
        <Route path="/custom-forms" element={<LazyLoad component={LazyCustomFormsPage} loadingMessage="جاري تحميل صفحة النماذج المخصصة..." />} />
        <Route path="/custom-forms/new" element={<LazyLoad component={LazyCustomFormBuilderPage} loadingMessage="جاري تحميل منشئ النماذج..." />} />
        <Route path="/custom-forms/:id" element={<LazyLoad component={LazyCustomFormsPage} loadingMessage="جاري تحميل تفاصيل النموذج..." />} />
        <Route path="/custom-forms/edit/:id" element={<LazyLoad component={LazyCustomFormBuilderPage} loadingMessage="جاري تحميل منشئ النماذج..." />} />
        <Route path="/custom-forms/copy/:id" element={<LazyLoad component={LazyCustomFormBuilderPage} loadingMessage="جاري تحميل منشئ النماذج..." />} />

        {/* Database Management Route */}
        <Route path="/database" element={<LazyLoad component={LazyDatabasePage} loadingMessage="جاري تحميل صفحة قاعدة البيانات..." />} />

        {/* API Test Route */}
        <Route path="/api-test" element={<LazyLoad component={LazyApiTestPage} loadingMessage="جاري تحميل صفحة اختبار API..." />} />

        {/* Reports Routes */}
        <Route path="/reports" element={<LazyLoad component={LazyReportsPage} loadingMessage="جاري تحميل صفحة التقارير..." />} />
        <Route path="/reports/templates" element={<LazyLoad component={LazyReportBuilderPage} loadingMessage="جاري تحميل منشئ التقارير..." />} />
      </Route>

      {/* Not Found Route */}
      <Route path="*" element={<LazyLoad component={LazyNotFoundPage} loadingMessage="جاري تحميل الصفحة..." />} />
    </Routes>
  );
};

export default AppRouter;
