import { Router } from 'express';
import { reportController } from '../controllers/report.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import { generateReportSchema } from '../schemas/report.schema.js';

export const reportRoutes = Router();

/**
 * مسار إنشاء تقرير
 * يتيح إنشاء تقارير بأنواع وصيغ مختلفة
 */
reportRoutes.get(
  '/generate',
  authMiddleware,
  validateRequest(generateReportSchema),
  reportController.generateReport
);

/**
 * مسار تحميل تقرير
 * يتيح تحميل ملف التقرير المطلوب
 */
reportRoutes.get(
  '/download/:fileName',
  authMiddleware,
  reportController.downloadReport
);
