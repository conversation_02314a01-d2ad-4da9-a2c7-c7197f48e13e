// Mock محسن لخدمة المصادقة للاختبارات
import { generateToken } from '../jwt.js';

/**
 * Mock لخدمة المصادقة
 */
export const mockAuthService = {
  /**
   * تسجيل دخول وهمي للاختبارات
   */
  login: async (username: string, password: string, request: any) => {
    // محاكاة مستخدم نشط للاختبارات
    const mockUser = {
      id: 'test-user-mock',
      username: username,
      name: 'مستخدم اختبار',
      email: '<EMAIL>',
      role: 'ADMIN',
      isActive: true,
    };

    // محاكاة التحقق من كلمة المرور
    if (password !== 'Test@123') {
      throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
    }

    // إنشاء رموز وهمية
    const accessToken = generateToken(
      {
        id: mockUser.id,
        userId: mockUser.id,
        username: mockUser.username,
        role: mockUser.role,
        isActive: true,
      },
      'access',
      '24h'
    );

    const refreshToken = generateToken(
      {
        id: mockUser.id,
        userId: mockUser.id,
        username: mockUser.username,
        role: mockUser.role,
        isActive: true,
      },
      'refresh',
      '7d'
    );

    return {
      user: mockUser,
      accessToken,
      refreshToken,
    };
  },

  /**
   * تحديث الرمز المميز الوهمي
   */
  refreshToken: async (refreshToken: string) => {
    if (!refreshToken || refreshToken === 'invalid_refresh_token') {
      throw new Error('رمز التحديث غير صالح');
    }

    const mockUser = {
      id: 'test-user-mock',
      username: 'test_admin',
      name: 'مستخدم اختبار',
      email: '<EMAIL>',
      role: 'ADMIN',
      isActive: true,
    };

    const newAccessToken = generateToken(
      {
        id: mockUser.id,
        userId: mockUser.id,
        username: mockUser.username,
        role: mockUser.role,
        isActive: true,
      },
      'access',
      '24h'
    );

    return {
      user: mockUser,
      accessToken: newAccessToken,
    };
  },

  /**
   * تسجيل مستخدم جديد وهمي
   */
  register: async (userData: any, adminUser: any) => {
    const mockNewUser = {
      id: `test-user-${Date.now()}`,
      username: userData.username,
      name: userData.name,
      email: userData.email,
      role: userData.role || 'USER',
      isActive: true,
    };

    return mockNewUser;
  },

  /**
   * تغيير كلمة المرور الوهمية
   */
  changePassword: async (userId: string, currentPassword: string, newPassword: string) => {
    if (currentPassword !== 'Test@123') {
      throw new Error('كلمة المرور الحالية غير صحيحة');
    }

    return {
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح',
    };
  },

  /**
   * تسجيل الخروج الوهمي
   */
  logout: async (userId: string, token: string) => {
    return {
      success: true,
      message: 'تم تسجيل الخروج بنجاح',
    };
  },
};

// تصدير افتراضي
export default mockAuthService;
