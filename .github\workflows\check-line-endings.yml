name: Check Line Endings

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  check-line-endings:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Check for CRLF line endings
        run: |
          # البحث عن الملفات التي تحتوي على CRLF
          CRLF_FILES=$(find . -type f -not -path "*/node_modules/*" -not -path "*/dist/*" -not -path "*/.git/*" -exec grep -l $'\r' {} \;)
          
          if [ -n "$CRLF_FILES" ]; then
            echo "الملفات التالية تحتوي على نهايات أسطر CRLF:"
            echo "$CRLF_FILES"
            echo "يرجى تحويل نهايات الأسطر إلى LF باستخدام أداة مثل dos2unix أو إعداد .gitattributes بشكل صحيح."
            exit 1
          else
            echo "جميع الملفات تستخدم نهايات أسطر LF. جيد!"
          fi
