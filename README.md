# نظام النور للأرشفة

نظام متكامل لإدارة وأرشفة البيانات والمستندات الخاصة بالمكتب، يتضمن إدارة البيانات والتفويضات والإفراجات والتصاريح والضمانات وغيرها من الوظائف.

## المميزات الرئيسية

- إدارة البيانات والمستندات
- إدارة حركة الأصناف
- إدارة التفويضات والإفراجات والتصاريح
- إدارة الضمانات (المسترجعة وغير المسترجعة)
- إدارة العملاء
- نظام بحث متقدم
- تقارير شاملة
- لوحة تحكم متكاملة
- ميزات متقدمة:
  - واجهات مخصصة (Custom UI Builder)
  - إدارة قواعد البيانات (نسخ احتياطي، استعادة، تصدير، تهيئة)
  - تخصيص إعدادات النظام (الألوان، الخطوط، الشعار، اسم النظام)
  - واجهة تقارير مخصصة

## متطلبات النظام

- Node.js (الإصدار 18 أو أحدث)
- PNPM (الإصدار 8.6.0 أو أحدث)
- PostgreSQL (الإصدار 16 أو أحدث)

## البدء السريع

### تثبيت التبعيات

```bash
pnpm install
```

### إعداد قاعدة البيانات

```bash
# إنشاء قاعدة البيانات
"C:\Program Files\PostgreSQL\16\bin\psql.exe" -U postgres -c "CREATE DATABASE alnoor_db;"

# تنفيذ ترحيلات قاعدة البيانات
npx prisma migrate dev --schema=./database/schema.prisma

# إضافة البيانات الأولية
npx ts-node database/seeds/seed_combined.ts
```

لمزيد من التفاصيل حول إعداد قاعدة البيانات، راجع [توثيق إعداد قاعدة البيانات](./docs/database-setup.md).

### تشغيل البيئة المحلية

```bash
pnpm dev
```

### بناء المشروع للإنتاج

```bash
pnpm build
```

### تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
pnpm test

# تشغيل اختبارات الوحدات فقط
pnpm test:unit

# تشغيل اختبارات التكامل فقط
pnpm test:integration

# تشغيل اختبارات مع تقرير التغطية
pnpm test:coverage
```

## هيكل المشروع

المشروع مبني باستخدام هيكلية Monorepo باستخدام Turborepo:

- `apps/web`: تطبيق الواجهة الأمامية (React + Vite)
- `apps/api`: خدمة الواجهة الخلفية (Node.js + Express)
- `packages/shared-types`: الأنواع المشتركة بين التطبيقات
- `packages/ui-library`: مكتبة المكونات المشتركة
- `packages/configs`: إعدادات مشتركة (ESLint, Prettier, TypeScript)

## التوثيق

يمكن العثور على التوثيق الشامل للمشروع في مجلد `docs`:

- [هيكل المشروع](./docs/project-structure.md)
- [هيكل المشروع المفصل](./docs/detailed-project-structure.md)
- [خطة التنفيذ](./docs/implementation-plan.md)
- [تقدم التنفيذ](./docs/implementation-progress.md)
- [دليل النشر](./docs/deployment-guide.md)
- [إعداد قاعدة البيانات](./docs/database-setup.md)
  - [إصلاح مشكلة تكرار ملفات الترحيل والبذور](./database/README.md)
- [متغيرات البيئة](./docs/environment-variables.md)
- [دعم اللغة العربية في PDF](./docs/pdf-arabic-support.md)
- [تثبيت الخطوط](./docs/fonts-installation.md)
- [اختبارات التكامل](./docs/integration-tests.md)
- [الميزات المتقدمة](./docs/advanced-features-readme.md)
- [الميزات المخصصة](./docs/custom-features.md)

## المساهمة

يرجى قراءة [دليل المساهمة](./docs/CONTRIBUTING.md) للحصول على تفاصيل حول عملية تقديم التغييرات.

## إرشادات التطوير

### نهايات الأسطر

يستخدم هذا المشروع نهايات أسطر LF (Unix-style) لجميع الملفات النصية. لضمان اتساق نهايات الأسطر عبر جميع أنظمة التشغيل، يرجى اتباع الإرشادات التالية:

1. **إعداد Git على جهازك**:
   ```bash
   # للمستخدمين على نظام Linux/macOS
   git config --global core.autocrlf input

   # للمستخدمين على نظام Windows
   git config --global core.autocrlf true
   ```

2. **استخدام أدوات التحقق قبل الالتزام**:
   ```bash
   # تثبيت pre-commit
   pip install pre-commit

   # تثبيت hooks
   pre-commit install
   ```

3. **تحويل الملفات الموجودة**:
   إذا كنت تعمل على نظام Windows وتواجه مشاكل مع نهايات الأسطر، يمكنك تحويل الملفات باستخدام:
   ```bash
   # تثبيت dos2unix (على Windows يمكن استخدام Git Bash أو WSL)
   # تحويل ملف واحد
   dos2unix path/to/file

   # تحويل جميع الملفات في المشروع
   find . -type f -not -path "*/node_modules/*" -not -path "*/dist/*" -not -path "*/.git/*" -exec dos2unix {} \;
   ```

## الترخيص

هذا المشروع مرخص بموجب [ترخيص MIT](./LICENSE).
