import { Router } from 'express';
import { databaseController } from '../controllers/database.controller.js';
import { authMiddleware, adminMiddleware } from '../../../core/middleware/auth.middleware.js';

export const databaseRoutes = Router();

/**
 * @route GET /api/database/backups
 * @desc الحصول على قائمة النسخ الاحتياطية
 * @access خاص (مصادقة مطلوبة + مدير النظام فقط)
 */
databaseRoutes.get(
  '/backups',
  authMiddleware,
  adminMiddleware,
  databaseController.getBackups
);

/**
 * @route POST /api/database/backups
 * @desc إنشاء نسخة احتياطية جديدة
 * @access خاص (مصادقة مطلوبة + مدير النظام فقط)
 */
databaseRoutes.post(
  '/backups',
  authMiddleware,
  adminMiddleware,
  databaseController.createBackup
);

/**
 * @route POST /api/database/restore/:fileName
 * @desc استعادة قاعدة البيانات من نسخة احتياطية
 * @access خاص (مصادقة مطلوبة + مدير النظام فقط)
 */
databaseRoutes.post(
  '/restore/:fileName',
  authMiddleware,
  adminMiddleware,
  databaseController.restoreBackup
);

/**
 * @route DELETE /api/database/backups/:fileName
 * @desc حذف نسخة احتياطية
 * @access خاص (مصادقة مطلوبة + مدير النظام فقط)
 */
databaseRoutes.delete(
  '/backups/:fileName',
  authMiddleware,
  adminMiddleware,
  databaseController.deleteBackup
);

/**
 * @route GET /api/database/download/:fileName
 * @desc تنزيل نسخة احتياطية
 * @access خاص (مصادقة مطلوبة + مدير النظام فقط)
 */
databaseRoutes.get(
  '/download/:fileName',
  authMiddleware,
  adminMiddleware,
  databaseController.downloadBackup
);

/**
 * @route POST /api/database/export
 * @desc تصدير قاعدة البيانات
 * @access خاص (مصادقة مطلوبة + مدير النظام فقط)
 */
databaseRoutes.post(
  '/export',
  authMiddleware,
  adminMiddleware,
  databaseController.exportDatabase
);

/**
 * @route POST /api/database/initialize
 * @desc تهيئة قاعدة البيانات
 * @access خاص (مصادقة مطلوبة + مدير النظام فقط)
 */
databaseRoutes.post(
  '/initialize',
  authMiddleware,
  adminMiddleware,
  databaseController.initializeDatabase
);
