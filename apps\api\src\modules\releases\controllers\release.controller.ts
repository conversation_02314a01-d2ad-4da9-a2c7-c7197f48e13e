import { Request, Response, NextFunction } from 'express';
import { releaseService } from '../services/release.service.js';
import { successResponse, paginatedResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const releaseController = {
  /**
   * إنشاء إفراج جديد
   */
  createRelease: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على بيانات الإفراج من الطلب
      const releaseData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // إنشاء الإفراج
      const release = await releaseService.createRelease(
        releaseData,
        req.user.id,
        file
      );

      return res.status(201).json(successResponse(release, 'تم إنشاء الإفراج بنجاح', 201));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث إفراج
   */
  updateRelease: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف الإفراج من المعلمات
      const { id } = req.params;

      // الحصول على بيانات الإفراج من الطلب
      const releaseData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // تحديث الإفراج
      const release = await releaseService.updateRelease(
        id,
        releaseData,
        req.user.id,
        file
      );

      return res.status(200).json(successResponse(release, 'تم تحديث الإفراج بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على إفراج محدد
   */
  getRelease: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معرف الإفراج من المعلمات
      const { id } = req.params;

      // الحصول على الإفراج
      const release = await releaseService.getRelease(id);

      return res.status(200).json(successResponse(release));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف إفراج
   */
  deleteRelease: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف الإفراج من المعلمات
      const { id } = req.params;

      // حذف الإفراج
      await releaseService.deleteRelease(id, req.user.id);

      return res.status(200).json(successResponse(null, 'تم حذف الإفراج بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة الإفراجات
   */
  listReleases: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معلمات البحث
      const { page, limit, sort, order, search, declarationId, fromDate, toDate, isActive } =
        req.query as any;

      // الحصول على قائمة الإفراجات
      const result = await releaseService.listReleases({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        search,
        declarationId,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined,
        isActive,
      });

      return res.status(200).json(paginatedResponse(
        result.data,
        result.pagination.page,
        result.pagination.limit,
        result.pagination.total
      ));
    } catch (error) {
      next(error);
    }
  },
};
