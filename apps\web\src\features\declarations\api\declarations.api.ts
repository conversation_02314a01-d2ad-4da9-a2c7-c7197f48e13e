import axios from 'axios';
import { Declaration, DeclarationType } from '../types/declaration.types';

// واجهة طلب إنشاء البيان
export interface CreateDeclarationRequest {
  taxNumber: string;
  clientName?: string;
  companyName?: string;
  policyNumber?: number;
  invoiceNumber?: number;
  gatewayEntryNumber: number;
  declarationType: DeclarationType;
  declarationDate?: string;
  count?: number;
  weight?: string;
  goodsType?: string;
  itemsCount?: number;
  entryDate?: string;
  exitDate?: string;
  clientId?: string;
  drivers?: {
    name: string;
    truckNumber?: string;
    trailerNumber?: string;
    phoneNumber?: string;
  }[];
}

// واجهة طلب تحديث البيان
export interface UpdateDeclarationRequest {
  taxNumber?: string;
  clientName?: string;
  companyName?: string;
  policyNumber?: number;
  invoiceNumber?: number;
  gatewayEntryNumber?: number;
  declarationType?: DeclarationType;
  declarationDate?: string;
  count?: number;
  weight?: string;
  goodsType?: string;
  itemsCount?: number;
  entryDate?: string;
  exitDate?: string;
  clientId?: string;
  drivers?: {
    id?: string;
    name: string;
    truckNumber?: string;
    trailerNumber?: string;
    phoneNumber?: string;
  }[];
}

// واجهة معلمات البحث
export interface DeclarationSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationType?: DeclarationType;
  fromDate?: string;
  toDate?: string;
  clientId?: string;
}

// واجهة استجابة قائمة البيانات
export interface DeclarationsResponse {
  data: Declaration[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// واجهة استجابة البيان
export interface DeclarationResponse {
  data: Declaration;
}

// دالة إنشاء بيان جديد
export const createDeclaration = async (
  data: CreateDeclarationRequest,
  file?: File
): Promise<Declaration> => {
  try {
    // إنشاء FormData لإرسال البيانات والملف
    const formData = new FormData();
    formData.append('data', JSON.stringify(data));
    
    if (file) {
      formData.append('file', file);
    }

    const response = await axios.post<DeclarationResponse>('/api/declarations', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء إنشاء البيان');
    }
    throw new Error('حدث خطأ أثناء إنشاء البيان');
  }
};

// دالة تحديث بيان
export const updateDeclaration = async (
  id: string,
  data: UpdateDeclarationRequest,
  file?: File
): Promise<Declaration> => {
  try {
    // إنشاء FormData لإرسال البيانات والملف
    const formData = new FormData();
    formData.append('data', JSON.stringify(data));
    
    if (file) {
      formData.append('file', file);
    }

    const response = await axios.put<DeclarationResponse>(`/api/declarations/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء تحديث البيان');
    }
    throw new Error('حدث خطأ أثناء تحديث البيان');
  }
};

// دالة الحصول على بيان محدد
export const getDeclaration = async (id: string): Promise<Declaration> => {
  try {
    const response = await axios.get<DeclarationResponse>(`/api/declarations/${id}`);
    return response.data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء الحصول على البيان');
    }
    throw new Error('حدث خطأ أثناء الحصول على البيان');
  }
};

// دالة حذف بيان
export const deleteDeclaration = async (id: string): Promise<void> => {
  try {
    await axios.delete(`/api/declarations/${id}`);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء حذف البيان');
    }
    throw new Error('حدث خطأ أثناء حذف البيان');
  }
};

// دالة الحصول على قائمة البيانات
export const getDeclarations = async (
  params: DeclarationSearchParams = {}
): Promise<DeclarationsResponse> => {
  try {
    const response = await axios.get<DeclarationsResponse>('/api/declarations', {
      params,
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء الحصول على قائمة البيانات');
    }
    throw new Error('حدث خطأ أثناء الحصول على قائمة البيانات');
  }
};
