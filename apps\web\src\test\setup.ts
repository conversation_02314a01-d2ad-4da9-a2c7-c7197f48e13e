// إعداد بيئة الاختبار للواجهة الأمامية
import '@testing-library/jest-dom';
import { vi } from 'vitest';
import { http, HttpResponse, HttpHandler } from 'msw';
import { setupServer } from 'msw/node';

// تعيين jest كـ alias لـ vi مع الخصائص المطلوبة
globalThis.jest = Object.assign(vi, {
  Mock: vi.fn,
  requireActual: (module: string) => vi.importActual(module),
});

// إعداد خادم وهمي للاختبارات
export const server = setupServer();

// إعداد بيئة الاختبار
beforeAll(() => {
  // بدء تشغيل الخادم الوهمي
  server.listen({ onUnhandledRequest: 'error' });
});

// إعادة تعيين المعالجات بين الاختبارات
afterEach(() => {
  server.resetHandlers();
  vi.clearAllMocks();
});

// إيقاف الخادم بعد الانتهاء من جميع الاختبارات
afterAll(() => {
  server.close();
});

// تعريف معالجات وهمية أساسية
server.use(
  // معالج فحص الصحة
  http.get('http://localhost:3001/health', () => {
    return HttpResponse.json({
      status: 'ok',
    }, { status: 200 });
  })
);

// تصدير الدوال المساعدة
export { http, HttpResponse };
