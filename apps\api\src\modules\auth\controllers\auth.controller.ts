import { Request, Response, NextFunction } from 'express';
import { authService } from '../services/auth.service.js';
import { successResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const authController = {
  /**
   * متحكم تسجيل الدخول
   * يتعامل مع طلبات تسجيل الدخول ويتحقق من صحة بيانات المستخدم
   */
  login: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { username, password } = req.body;

      // التحقق من توفر اسم المستخدم وكلمة المرور
      if (!username || !password) {
        throw new HttpException(400, 'اسم المستخدم وكلمة المرور مطلوبان', 'طلب غير صالح');
      }

      // تسجيل الدخول
      const result = await authService.login(username, password, req);

      // إذا كانت محاولة تسجيل الدخول مشبوهة، إضافة رأس تحذير
      if (result.isSuspicious) {
        res.setHeader('X-Login-Warning', 'Suspicious login detected');
      }

      // إضافة معرف الجلسة إلى ملفات تعريف الارتباط
      res.cookie('sessionId', result.sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 أيام
        sameSite: 'strict',
      });

      return res.status(200).json(successResponse(
        {
          user: result.user,
          token: result.token,
          refreshToken: result.refreshToken,
        },
        'تم تسجيل الدخول بنجاح'
      ));
    } catch (error) {
      next(error);
    }
  },

  /**
   * متحكم تجديد التوكن
   * يتعامل مع طلبات تجديد توكن المصادقة عند انتهاء صلاحيته
   */
  refreshToken: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        throw new HttpException(400, 'توكن التجديد مطلوب', 'طلب غير صالح');
      }

      const result = await authService.refreshToken(refreshToken, req);

      // تحديث ملف تعريف الارتباط للجلسة
      if (result.sessionId) {
        res.cookie('sessionId', result.sessionId, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          maxAge: 7 * 24 * 60 * 60 * 1000, // 7 أيام
          sameSite: 'strict',
        });
      }

      return res.status(200).json(successResponse(
        {
          user: result.user,
          token: result.token,
          refreshToken: result.refreshToken,
        },
        'تم تجديد التوكن بنجاح'
      ));
    } catch (error) {
      next(error);
    }
  },

  /**
   * متحكم تسجيل الخروج
   * يتعامل مع طلبات تسجيل الخروج وإبطال التوكنات
   */
  logout: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على التوكن من الرأس
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new HttpException(401, 'لم يتم توفير التوكن', 'غير مصرح');
      }

      // استخراج التوكن من رأس المصادقة
      const token = authHeader.split(' ')[1];

      // الحصول على توكن التجديد من الجسم إذا كان موجوداً
      const { refreshToken, endAllSessions } = req.body;

      // الحصول على معرف المستخدم من الطلب إذا كان متاحاً
      const userId = req.user?.id;

      // الحصول على معرف الجلسة من ملفات تعريف الارتباط
      const sessionId = req.cookies?.sessionId;

      // إبطال التوكنات وإنهاء الجلسة
      await authService.logout(token, refreshToken, sessionId, userId, endAllSessions);

      // مسح ملف تعريف الارتباط للجلسة
      res.clearCookie('sessionId');

      return res.status(200).json(successResponse(null, 'تم تسجيل الخروج بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * متحكم الحصول على جلسات المستخدم النشطة
   * يتعامل مع طلبات الحصول على جلسات المستخدم النشطة
   */
  getUserSessions: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      const sessions = await authService.getUserSessions(req.user.id);

      return res.status(200).json(successResponse(sessions, 'تم الحصول على جلسات المستخدم بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * متحكم إنهاء جلسة محددة
   * يتعامل مع طلبات إنهاء جلسة محددة
   */
  endSession: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      const { sessionId } = req.params;

      if (!sessionId) {
        throw new HttpException(400, 'معرف الجلسة مطلوب', 'طلب غير صالح');
      }

      await authService.endSession(req.user.id, sessionId);

      return res.status(200).json(successResponse(null, 'تم إنهاء الجلسة بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * متحكم تسجيل مستخدم جديد
   * يتعامل مع طلبات تسجيل مستخدمين جدد
   */
  register: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { username, password, name, email, role } = req.body;

      // التحقق من توفر البيانات المطلوبة
      if (!username || !password || !name || !email || !role) {
        throw new HttpException(400, 'جميع البيانات مطلوبة', 'طلب غير صالح');
      }

      // تسجيل المستخدم الجديد
      const user = await authService.register({
        username,
        password,
        name,
        email,
        role,
      });

      return res.status(201).json(successResponse(user, 'تم تسجيل المستخدم بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * متحكم تغيير كلمة المرور
   * يتعامل مع طلبات تغيير كلمة المرور
   */
  changePassword: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      const { currentPassword, newPassword } = req.body;

      // التحقق من توفر البيانات المطلوبة
      if (!currentPassword || !newPassword) {
        throw new HttpException(400, 'كلمة المرور الحالية والجديدة مطلوبتان', 'طلب غير صالح');
      }

      // تغيير كلمة المرور
      const result = await authService.changePassword(req.user.id, currentPassword, newPassword);

      return res.status(200).json(successResponse(result, 'تم تغيير كلمة المرور بنجاح'));
    } catch (error) {
      next(error);
    }
  },
};
