import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Box,
  CircularProgress,
} from '@mui/material';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

interface ItemMovement {
  id: string;
  itemName: string;
  movementType: string;
  quantity: number;
  date: string;
  notes?: string;
  declarationId?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

interface ItemMovementListProps {
  movements: ItemMovement[];
  loading?: boolean;
  declarationId?: string;
}

const ItemMovementList: React.FC<ItemMovementListProps> = ({
  movements,
  loading = false,
  declarationId,
}) => {
  const getMovementTypeColor = (type: string) => {
    switch (type) {
      case 'IN': return 'success';
      case 'OUT': return 'error';
      case 'TRANSFER': return 'warning';
      case 'ADJUSTMENT': return 'info';
      default: return 'default';
    }
  };

  const getMovementTypeLabel = (type: string) => {
    switch (type) {
      case 'IN': return 'دخول';
      case 'OUT': return 'خروج';
      case 'TRANSFER': return 'نقل';
      case 'ADJUSTMENT': return 'تعديل';
      default: return type;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={3}>
        <CircularProgress />
      </Box>
    );
  }

  if (!movements || movements.length === 0) {
    return (
      <Box textAlign="center" p={3}>
        <Typography variant="h6" color="textSecondary">
          لا توجد حركات أصناف
        </Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={2}>
      {movements.map((movement) => (
        <Grid item xs={12} sm={6} md={4} key={movement.id}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {movement.itemName}
              </Typography>

              <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                <Chip
                  label={getMovementTypeLabel(movement.movementType)}
                  color={getMovementTypeColor(movement.movementType) as any}
                  size="small"
                />
                <Typography variant="body2" color="textSecondary">
                  الكمية: {movement.quantity}
                </Typography>
              </Box>

              <Typography variant="body2" color="textSecondary" gutterBottom>
                التاريخ: {format(new Date(movement.date), 'PPP', { locale: arSA })}
              </Typography>

              {movement.notes && (
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  ملاحظات: {movement.notes}
                </Typography>
              )}

              <Typography variant="caption" color="textSecondary">
                تم الإنشاء: {format(new Date(movement.createdAt), 'PPP', { locale: arSA })}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default ItemMovementList;
