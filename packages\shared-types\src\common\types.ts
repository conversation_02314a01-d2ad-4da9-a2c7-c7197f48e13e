/**
 * نوع البيانات الأساسي
 */
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * نوع المستخدم
 */
export interface User extends BaseEntity {
  username: string;
  email: string;
  name: string;
  role: UserRole;
}

/**
 * نوع العميل
 */
export interface Client extends BaseEntity {
  name: string;
  taxNumber: string;
  phone?: string;
  email?: string;
  address?: string;
}

/**
 * نوع معلمات البحث الأساسية
 */
export interface BaseSearchParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  fromDate?: Date | string;
  toDate?: Date | string;
}

/**
 * نوع استجابة الصفحة
 */
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * نوع دور المستخدم
 */
export type UserRole = 'ADMIN' | 'USER' | 'MANAGER';

/**
 * نوع استجابة المصادقة
 */
export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

/**
 * نوع طلب تسجيل الدخول
 */
export interface LoginRequest {
  username: string;
  password: string;
}

/**
 * نوع طلب تجديد الرمز المميز
 */
export interface RefreshTokenRequest {
  refreshToken: string;
}
