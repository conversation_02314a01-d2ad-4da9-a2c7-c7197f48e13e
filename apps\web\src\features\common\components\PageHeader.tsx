import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { Link } from 'react-router-dom';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

interface BackButtonProps {
  to: string;
  label: string;
}

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  backButton?: BackButtonProps | React.ReactNode;
  actions?: React.ReactNode;
}

/**
 * مكون رأس الصفحة
 * يعرض عنوان الصفحة والعنوان الفرعي وزر العودة وأزرار الإجراءات
 */
export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  backButton,
  actions,
}) => {
  return (
    <Box sx={{ mb: 4, mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Box>
          {backButton && typeof backButton === 'object' && 'to' in backButton ? (
            <Button
              component={Link}
              to={backButton.to}
              startIcon={<ArrowBackIcon />}
              variant="outlined"
              sx={{ mb: 2 }}
            >
              {backButton.label}
            </Button>
          ) : (
            backButton
          )}
          <Typography variant="h4" component="h1" gutterBottom>
            {title}
          </Typography>
          {subtitle && (
            <Typography variant="subtitle1" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
        {actions && <Box>{actions}</Box>}
      </Box>
    </Box>
  );
};
