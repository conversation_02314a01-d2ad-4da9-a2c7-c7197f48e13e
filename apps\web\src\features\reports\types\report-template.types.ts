import { ReportType } from './report-type';

/**
 * واجهة عمود التقرير
 */
export interface ReportColumn {
  field: string;
  header: string;
  width?: number;
  format?: string;
  visible?: boolean;
}

/**
 * واجهة قالب التقرير
 */
export interface ReportTemplateStructure {
  title: string;
  subtitle?: string;
  showLogo: boolean;
  showDate: boolean;
  showPageNumbers: boolean;
  orientation: 'portrait' | 'landscape';
  columns: ReportColumn[];
  filters?: string[];
  groupBy?: string[];
  sortBy?: string[];
  footer?: string;
  header?: string;
}

/**
 * واجهة قالب التقرير
 */
export interface ReportTemplate {
  id: string;
  name: string;
  description?: string;
  reportType: string;
  template: ReportTemplateStructure;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: {
    id: string;
    name: string;
  };
}

/**
 * واجهة قائمة قوالب التقارير
 */
export interface ReportTemplateListResponse {
  data: ReportTemplate[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

/**
 * واجهة إنشاء قالب تقرير
 */
export interface CreateReportTemplateRequest {
  name: string;
  description?: string;
  reportType: string;
  template: ReportTemplateStructure;
  isDefault?: boolean;
}

/**
 * واجهة تحديث قالب تقرير
 */
export interface UpdateReportTemplateRequest {
  name?: string;
  description?: string;
  reportType?: string;
  template?: ReportTemplateStructure;
  isDefault?: boolean;
}

/**
 * واجهة معلمات قائمة قوالب التقارير
 */
export interface ListReportTemplatesParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  reportType?: string;
}

/**
 * واجهة حقول التقرير المتاحة
 */
export interface ReportFields {
  [key: string]: {
    label: string;
    type: 'string' | 'number' | 'date' | 'boolean';
    group?: string;
  };
}

/**
 * حقول التقرير المتاحة حسب نوع التقرير
 */
export const REPORT_FIELDS: Record<string, ReportFields> = {
  [ReportType.DECLARATIONS]: {
    declarationNumber: { label: 'رقم البيان', type: 'number', group: 'بيانات أساسية' },
    declarationType: { label: 'نوع البيان', type: 'string', group: 'بيانات أساسية' },
    taxNumber: { label: 'الرقم الضريبي', type: 'string', group: 'بيانات العميل' },
    clientName: { label: 'اسم العميل', type: 'string', group: 'بيانات العميل' },
    companyName: { label: 'اسم الشركة', type: 'string', group: 'بيانات العميل' },
    policyNumber: { label: 'رقم البوليصة', type: 'number', group: 'بيانات أساسية' },
    invoiceNumber: { label: 'رقم الفاتورة', type: 'number', group: 'بيانات أساسية' },
    gatewayEntryNumber: { label: 'رقم دخول البوابة', type: 'number', group: 'بيانات أساسية' },
    declarationDate: { label: 'تاريخ البيان', type: 'date', group: 'تواريخ' },
    count: { label: 'العدد', type: 'number', group: 'بيانات الأصناف' },
    weight: { label: 'الوزن', type: 'string', group: 'بيانات الأصناف' },
    goodsType: { label: 'نوع البضاعة', type: 'string', group: 'بيانات الأصناف' },
    itemsCount: { label: 'عدد الأصناف', type: 'number', group: 'بيانات الأصناف' },
    entryDate: { label: 'تاريخ الدخول', type: 'date', group: 'تواريخ' },
    exitDate: { label: 'تاريخ الخروج', type: 'date', group: 'تواريخ' },
    createdAt: { label: 'تاريخ الإنشاء', type: 'date', group: 'بيانات النظام' },
    updatedAt: { label: 'تاريخ التحديث', type: 'date', group: 'بيانات النظام' },
  },
  [ReportType.ITEM_MOVEMENTS]: {
    movementNumber: { label: 'رقم الحركة', type: 'number', group: 'بيانات أساسية' },
    movementDate: { label: 'تاريخ الحركة', type: 'date', group: 'تواريخ' },
    declarationNumber: { label: 'رقم البيان', type: 'number', group: 'بيانات البيان' },
    itemNumber: { label: 'رقم الصنف', type: 'number', group: 'بيانات الصنف' },
    invoiceNumber: { label: 'رقم الفاتورة', type: 'number', group: 'بيانات أساسية' },
    packingListNumber: { label: 'رقم قائمة التعبئة', type: 'number', group: 'بيانات أساسية' },
    identificationClause: { label: 'بند التعريفة', type: 'number', group: 'بيانات أساسية' },
    itemName: { label: 'اسم الصنف', type: 'string', group: 'بيانات الصنف' },
    count: { label: 'العدد', type: 'number', group: 'بيانات الصنف' },
    packageType: { label: 'نوع العبوة', type: 'string', group: 'بيانات الصنف' },
    goodsType: { label: 'نوع البضاعة', type: 'string', group: 'بيانات الصنف' },
    countryOfOrigin: { label: 'بلد المنشأ', type: 'string', group: 'بيانات الصنف' },
    itemValue: { label: 'قيمة الصنف', type: 'number', group: 'بيانات مالية' },
    currency: { label: 'العملة', type: 'string', group: 'بيانات مالية' },
    totalValue: { label: 'القيمة الإجمالية', type: 'number', group: 'بيانات مالية' },
    createdAt: { label: 'تاريخ الإنشاء', type: 'date', group: 'بيانات النظام' },
    updatedAt: { label: 'تاريخ التحديث', type: 'date', group: 'بيانات النظام' },
  },
  // يمكن إضافة المزيد من أنواع التقارير هنا
};
