import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import { existsSync, unlinkSync } from 'fs';
import path from 'path';
import bcrypt from 'bcryptjs';

// إعداد قاعدة بيانات SQLite للاختبارات التكاملية فقط
export class SQLiteIntegrationSetup {
  private static instance: SQLiteIntegrationSetup;
  private prisma: PrismaClient | null = null;
  private dbPath: string;

  private constructor() {
    this.dbPath = path.join(process.cwd(), 'prisma', 'test.db');
  }

  static getInstance(): SQLiteIntegrationSetup {
    if (!SQLiteIntegrationSetup.instance) {
      SQLiteIntegrationSetup.instance = new SQLiteIntegrationSetup();
    }
    return SQLiteIntegrationSetup.instance;
  }

  async setupDatabase(): Promise<PrismaClient> {
    try {
      console.log('🔧 إعداد قاعدة بيانات SQLite للاختبارات التكاملية...');

      // حذف قاعدة البيانات السابقة إن وجدت
      if (existsSync(this.dbPath)) {
        unlinkSync(this.dbPath);
        console.log('🗑️ تم حذف قاعدة البيانات السابقة');
      }

      // تطبيق المخطط باستخدام schema.test.prisma
      console.log('📋 تطبيق مخطط قاعدة البيانات...');
      execSync('npx prisma db push --schema=./prisma/schema.test.prisma --force-reset', {
        stdio: 'pipe',
        cwd: process.cwd()
      });

      // إنشاء اتصال جديد
      this.prisma = new PrismaClient({
        datasources: {
          db: {
            url: `file:${this.dbPath}`
          }
        },
        log: ['error']
      });

      await this.prisma.$connect();
      console.log('✅ تم إعداد قاعدة بيانات SQLite بنجاح');

      return this.prisma;
    } catch (error) {
      console.error('❌ خطأ في إعداد قاعدة البيانات:', error);
      throw error;
    }
  }

  async seedTestData(): Promise<void> {
    if (!this.prisma) {
      throw new Error('قاعدة البيانات غير مهيأة');
    }

    try {
      console.log('🌱 إضافة بيانات تجريبية...');

      // إنشاء كلمة مرور مشفرة
      const hashedPassword = await bcrypt.hash('Test@123', 12);

      // إنشاء مستخدم تجريبي
      const testUser = await this.prisma.user.create({
        data: {
          username: 'testuser',
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Test User',
          role: 'ADMIN',
          isActive: true
        }
      });

      // إنشاء عميل تجريبي
      const testClient = await this.prisma.client.create({
        data: {
          name: 'شركة الاختبار',
          taxNumber: '*********',
          phone: '+966501234567',
          email: '<EMAIL>',
          address: 'الرياض، المملكة العربية السعودية'
        }
      });

      // إنشاء بيان تجريبي
      const testDeclaration = await this.prisma.declaration.create({
        data: {
          declarationNumber: 'D001',
          taxNumber: '*********',
          clientName: 'شركة الاختبار',
          companyName: 'Test Company',
          policyNumber: '12345',
          invoiceNumber: '67890',
          gatewayEntryNumber: '11111',
          declarationType: 'IMPORT',
          declarationDate: new Date(),
          count: 10,
          weight: 100.5,
          goodsType: 'MEDICAL_SUPPLIES',
          itemsCount: 5,
          clientId: testClient.id,
          userId: testUser.id
        }
      });

      // إنشاء حركة صنف تجريبية
      await this.prisma.itemMovement.create({
        data: {
          itemName: 'أدوية طبية',
          quantity: 100,
          unit: 'قطعة',
          movementDate: new Date(),
          movementType: 'دخول',
          notes: 'حركة تجريبية',
          declarationId: testDeclaration.id
        }
      });

      // إنشاء تفويض تجريبي
      await this.prisma.authorization.create({
        data: {
          authorizedPerson: 'أحمد محمد',
          idNumber: '**********',
          authorizationType: 'FULL',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 يوم من الآن
          declarationId: testDeclaration.id
        }
      });

      console.log('✅ تم إضافة البيانات التجريبية بنجاح');
    } catch (error) {
      console.error('❌ خطأ في إضافة البيانات التجريبية:', error);
      throw error;
    }
  }

  async cleanupDatabase(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect();
      this.prisma = null;
    }

    if (existsSync(this.dbPath)) {
      unlinkSync(this.dbPath);
      console.log('🧹 تم تنظيف قاعدة البيانات');
    }
  }

  getPrismaClient(): PrismaClient {
    if (!this.prisma) {
      throw new Error('قاعدة البيانات غير مهيأة');
    }
    return this.prisma;
  }

  async resetDatabase(): Promise<void> {
    if (!this.prisma) {
      return;
    }

    try {
      // حذف البيانات بترتيب معكوس للعلاقات
      await this.prisma.itemMovement?.deleteMany?.() || Promise.resolve();
      await this.prisma.authorization?.deleteMany?.() || Promise.resolve();
      await this.prisma.release?.deleteMany?.() || Promise.resolve();
      await this.prisma.permit?.deleteMany?.() || Promise.resolve();
      await this.prisma.guarantee?.deleteMany?.() || Promise.resolve();
      await this.prisma.receipt?.deleteMany?.() || Promise.resolve();
      await this.prisma.driver?.deleteMany?.() || Promise.resolve();
      await this.prisma.declaration?.deleteMany?.() || Promise.resolve();
      await this.prisma.client?.deleteMany?.() || Promise.resolve();
      await this.prisma.session?.deleteMany?.() || Promise.resolve();
      await this.prisma.customForm?.deleteMany?.() || Promise.resolve();
      await this.prisma.reportTemplate?.deleteMany?.() || Promise.resolve();
      await this.prisma.user?.deleteMany?.() || Promise.resolve();
      await this.prisma.loginAttempt?.deleteMany?.() || Promise.resolve();
      await this.prisma.invalidatedToken?.deleteMany?.() || Promise.resolve();

      // إعادة إضافة البيانات الأساسية
      await this.seedTestData();
    } catch (error) {
      console.warn('تحذير: خطأ في إعادة تعيين البيانات:', error);
    }
  }
}

// إعداد عام للاختبارات التكاملية
export const setupIntegrationTestDatabase = async (): Promise<PrismaClient> => {
  const setup = SQLiteIntegrationSetup.getInstance();
  const prisma = await setup.setupDatabase();
  await setup.seedTestData();
  return prisma;
};

export const cleanupIntegrationTestDatabase = async (): Promise<void> => {
  const setup = SQLiteIntegrationSetup.getInstance();
  await setup.cleanupDatabase();
};

export const resetIntegrationTestDatabase = async (): Promise<void> => {
  const setup = SQLiteIntegrationSetup.getInstance();
  await setup.resetDatabase();
};

// إعداد Jest للاختبارات التكاملية
beforeAll(async () => {
  await setupIntegrationTestDatabase();
});

afterAll(async () => {
  await cleanupIntegrationTestDatabase();
});

beforeEach(async () => {
  await resetIntegrationTestDatabase();
});
