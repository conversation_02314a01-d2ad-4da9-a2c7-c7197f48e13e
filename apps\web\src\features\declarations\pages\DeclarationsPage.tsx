import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  InputAdornment,
  MenuItem,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useDeclarations, useDeleteDeclaration } from '../hooks/useDeclarations';
import { DeclarationType } from '../types/declaration.types';
import { DeclarationSearchParams } from '../api/declarations.api';

const DeclarationsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // حالة البحث والتصفية
  const [searchParams, setSearchParams] = useState<DeclarationSearchParams>({
    page: 1,
    limit: 10,
    sort: 'declarationNumber',
    order: 'desc',
  });
  const [searchText, setSearchText] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [declarationType, setDeclarationType] = useState<DeclarationType | ''>('');
  const [fromDate, setFromDate] = useState<Dayjs | null>(null);
  const [toDate, setToDate] = useState<Dayjs | null>(null);

  // استخدام خطافات البيانات
  const { data, isLoading, isError } = useDeclarations(searchParams);
  const deleteMutation = useDeleteDeclaration();

  // التعامل مع تغيير الصفحة
  const handlePageChange = (page: number) => {
    setSearchParams((prev) => ({ ...prev, page }));
  };

  // التعامل مع البحث
  const handleSearch = () => {
    const params: DeclarationSearchParams = {
      ...searchParams,
      page: 1,
      search: searchText || undefined,
      declarationType: declarationType || undefined,
      fromDate: fromDate ? fromDate.toDate().toISOString() : undefined,
      toDate: toDate ? toDate.toDate().toISOString() : undefined,
    };
    setSearchParams(params);
  };

  // التعامل مع إعادة تعيين التصفية
  const handleResetFilters = () => {
    setSearchText('');
    setDeclarationType('');
    setFromDate(null);
    setToDate(null);
    setSearchParams({
      page: 1,
      limit: 10,
      sort: 'declarationNumber',
      order: 'desc',
    });
  };

  // التعامل مع حذف البيان
  const handleDeleteDeclaration = async (id: string) => {
    if (window.confirm(t('declarations.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id);
      } catch (error) {
        console.error('Error deleting declaration:', error);
      }
    }
  };

  // التعامل مع عرض البيان
  const handleViewDeclaration = (id: string) => {
    navigate(`/declarations/${id}`);
  };

  // التعامل مع تعديل البيان
  const handleEditDeclaration = (id: string) => {
    navigate(`/declarations/${id}`);
  };

  // التعامل مع إنشاء بيان جديد
  const handleCreateDeclaration = () => {
    navigate('/declarations/new');
  };

  // عرض حالة التحميل
  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  // عرض حالة الخطأ
  if (isError) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <Typography color="error">{t('common.errorLoading')}</Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('declarations.title')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('declarations.description')}
        </Typography>
      </Box>

      <Paper sx={{ p: 2, mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <TextField
            label={t('common.search')}
            variant="outlined"
            size="small"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={handleSearch} edge="end">
                    <SearchIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{ width: 300 }}
          />
          <Box>
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={() => setShowFilters(!showFilters)}
              sx={{ mr: 1 }}
            >
              {t('common.filters')}
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateDeclaration}
            >
              {t('declarations.create')}
            </Button>
          </Box>
        </Box>

        {showFilters && (
          <Box mb={3}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  select
                  fullWidth
                  label={t('declarations.declarationType')}
                  value={declarationType}
                  onChange={(e) => setDeclarationType(e.target.value as DeclarationType | '')}
                  size="small"
                >
                  <MenuItem value="">{t('common.all')}</MenuItem>
                  <MenuItem value={DeclarationType.IMPORT}>{t('declarations.import')}</MenuItem>
                  <MenuItem value={DeclarationType.EXPORT}>{t('declarations.export')}</MenuItem>
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('common.fromDate')}
                  value={fromDate}
                  onChange={(date) => setFromDate(date ? dayjs(date) : null)}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('common.toDate')}
                  value={toDate}
                  onChange={(date) => setToDate(date ? dayjs(date) : null)}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box display="flex" gap={1}>
                  <Button variant="contained" onClick={handleSearch} fullWidth>
                    {t('common.apply')}
                  </Button>
                  <Button variant="outlined" onClick={handleResetFilters} fullWidth>
                    {t('common.reset')}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}

        <Box>
          {data?.data.length === 0 ? (
            <Box textAlign="center" py={4}>
              <Typography variant="h6">{t('declarations.noDeclarations')}</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateDeclaration}
                sx={{ mt: 2 }}
              >
                {t('declarations.create')}
              </Button>
            </Box>
          ) : (
            <Grid container spacing={2}>
              {data?.data.map((declaration) => (
                <Grid item xs={12} sm={6} md={4} key={declaration.id}>
                  <Card>
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="h6">
                          {t('declarations.number')}: {declaration.declarationNumber}
                        </Typography>
                        <Chip
                          label={
                            declaration.declarationType === DeclarationType.IMPORT
                              ? t('declarations.import')
                              : t('declarations.export')
                          }
                          color={
                            declaration.declarationType === DeclarationType.IMPORT
                              ? 'primary'
                              : 'secondary'
                          }
                          size="small"
                        />
                      </Box>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {t('declarations.client')}: {declaration.clientName || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {t('declarations.taxNumber')}: {declaration.taxNumber}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {t('declarations.date')}:{' '}
                        {declaration.declarationDate
                          ? new Date(declaration.declarationDate).toLocaleDateString()
                          : '-'}
                      </Typography>
                      <Box display="flex" justifyContent="flex-end" mt={2}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewDeclaration(declaration.id)}
                          title={t('common.view')}
                        >
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleEditDeclaration(declaration.id)}
                          title={t('common.edit')}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteDeclaration(declaration.id)}
                          title={t('common.delete')}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>

        {data && data.pagination.total > 0 && (
          <Box display="flex" justifyContent="center" mt={4}>
            <Box display="flex" gap={1}>
              <Button
                disabled={data.pagination.page === 1}
                onClick={() => handlePageChange(data.pagination.page - 1)}
              >
                {t('common.previous')}
              </Button>
              <Box display="flex" alignItems="center" mx={2}>
                {t('common.page')} {data.pagination.page} {t('common.of')}{' '}
                {data.pagination.pages}
              </Box>
              <Button
                disabled={data.pagination.page >= data.pagination.pages}
                onClick={() => handlePageChange(data.pagination.page + 1)}
              >
                {t('common.next')}
              </Button>
            </Box>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default DeclarationsPage;
