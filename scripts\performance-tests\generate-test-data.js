/**
 * سكريبت لإنشاء بيانات اختبار كبيرة لقاعدة البيانات
 *
 * يستخدم هذا السكريبت Prisma لإنشاء بيانات اختبار كبيرة في قاعدة البيانات
 * لاستخدامها في اختبارات الأداء.
 *
 * الاستخدام:
 * node generate-test-data.js [count]
 *
 * أمثلة:
 * node generate-test-data.js 1000
 */

const path = require('path');
const fs = require('fs');

// محاكاة Prisma Client
const PrismaClient = function() {
  return {
    declaration: {
      upsert: async () => console.log('محاكاة إنشاء بيان'),
    },
    client: {
      upsert: async () => console.log('محاكاة إنشاء عميل'),
    },
    driver: {
      upsert: async () => console.log('محاكاة إنشاء سائق'),
    },
    itemMovement: {
      create: async () => console.log('محاكاة إنشاء حركة صنف'),
    },
    authorization: {
      create: async () => console.log('محاكاة إنشاء تفويض'),
    },
    release: {
      create: async () => console.log('محاكاة إنشاء إفراج'),
    },
    permit: {
      create: async () => console.log('محاكاة إنشاء تصريح'),
    },
    guarantee: {
      create: async () => console.log('محاكاة إنشاء ضمان'),
    },
    receipt: {
      create: async () => console.log('محاكاة إنشاء استلام'),
    },
    $disconnect: async () => console.log('محاكاة قطع الاتصال'),
  };
};

// إنشاء عميل Prisma
const prisma = new PrismaClient();

// الإعدادات الافتراضية
const DEFAULT_COUNT = 1000;

// دالة لإنشاء بيانات عشوائية
function generateRandomData(count) {
  const declarations = [];
  const clients = [];
  const drivers = [];
  const itemMovements = [];
  const authorizations = [];
  const releases = [];
  const permits = [];
  const guarantees = [];
  const receipts = [];

  // إنشاء عملاء
  for (let i = 0; i < Math.ceil(count / 10); i++) {
    clients.push({
      name: `عميل اختبار ${i + 1}`,
      taxNumber: `${********* + i}`,
      phone: `05${Math.floor(10000000 + Math.random() * 90000000)}`,
      email: `client${i + 1}@example.com`,
      address: `عنوان العميل ${i + 1}`,
    });
  }

  // إنشاء بيانات
  for (let i = 0; i < count; i++) {
    const declarationNumber = 10000 + i;
    const clientIndex = i % clients.length;

    declarations.push({
      declarationNumber,
      taxNumber: clients[clientIndex].taxNumber,
      clientName: clients[clientIndex].name,
      declarationType: ['IMPORT', 'EXPORT', 'TRANSIT'][i % 3],
      entryDate: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
      exitDate: new Date(Date.now() + Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
      gatewayEntryNumber: `GW${10000 + i}`,
      notes: `ملاحظات للبيان ${declarationNumber}`,
    });

    // إنشاء سائق لكل بيان
    drivers.push({
      declarationNumber,
      name: `سائق ${i + 1}`,
      idNumber: `${2000000000 + i}`,
      nationality: ['سعودي', 'مصري', 'أردني', 'يمني', 'سوداني'][i % 5],
      phoneNumber: `05${Math.floor(10000000 + Math.random() * 90000000)}`,
      truckNumber: `شاحنة ${1000 + i}`,
    });

    // إنشاء حركات أصناف
    for (let j = 0; j < Math.floor(Math.random() * 5) + 1; j++) {
      itemMovements.push({
        declarationNumber,
        itemName: `صنف ${j + 1} للبيان ${declarationNumber}`,
        itemType: ['بضائع', 'مواد غذائية', 'مواد بناء', 'أجهزة إلكترونية', 'ملابس'][j % 5],
        quantity: Math.floor(Math.random() * 100) + 1,
        unit: ['قطعة', 'كرتون', 'طن', 'كيلوجرام', 'متر'][j % 5],
        unitPrice: Math.floor(Math.random() * 1000) + 100,
        totalPrice: 0, // سيتم حسابها لاحقًا
        currency: ['SAR', 'USD', 'EUR'][j % 3],
        notes: `ملاحظات للصنف ${j + 1} في البيان ${declarationNumber}`,
      });
    }

    // إنشاء تفويضات
    if (i % 3 === 0) {
      authorizations.push({
        declarationNumber,
        authorizationNumber: `AUTH${10000 + i}`,
        authorizationType: ['تخليص', 'نقل', 'تفتيش'][i % 3],
        startDate: new Date(Date.now() - Math.floor(Math.random() * 15) * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + Math.floor(Math.random() * 45) * 24 * 60 * 60 * 1000),
        authorizedPerson: `شخص مفوض ${i + 1}`,
        authorizedPersonId: `${3000000000 + i}`,
        notes: `ملاحظات للتفويض ${i + 1}`,
      });
    }

    // إنشاء إفراجات
    if (i % 4 === 0) {
      releases.push({
        declarationNumber,
        releaseNumber: `REL${10000 + i}`,
        releaseType: ['جزئي', 'كلي'][i % 2],
        releaseDate: new Date(Date.now() - Math.floor(Math.random() * 10) * 24 * 60 * 60 * 1000),
        notes: `ملاحظات للإفراج ${i + 1}`,
      });
    }

    // إنشاء تصاريح
    if (i % 5 === 0) {
      permits.push({
        declarationNumber,
        permitNumber: `PERM${10000 + i}`,
        permitType: ['دخول', 'خروج', 'تفتيش'][i % 3],
        issueDate: new Date(Date.now() - Math.floor(Math.random() * 20) * 24 * 60 * 60 * 1000),
        expiryDate: new Date(Date.now() + Math.floor(Math.random() * 40) * 24 * 60 * 60 * 1000),
        notes: `ملاحظات للتصريح ${i + 1}`,
      });
    }

    // إنشاء ضمانات
    if (i % 6 === 0) {
      guarantees.push({
        declarationNumber,
        guaranteeNumber: `GUAR${10000 + i}`,
        guaranteeType: ['RETURNABLE', 'NON_RETURNABLE'][i % 2],
        guaranteeNature: ['FINANCIAL', 'DOCUMENTS'][i % 2],
        issueDate: new Date(Date.now() - Math.floor(Math.random() * 25) * 24 * 60 * 60 * 1000),
        expiryDate: new Date(Date.now() + Math.floor(Math.random() * 35) * 24 * 60 * 60 * 1000),
        amount: Math.floor(Math.random() * 10000) + 1000,
        currency: ['SAR', 'USD', 'EUR'][i % 3],
        isReturned: false,
        notes: `ملاحظات للضمان ${i + 1}`,
      });
    }

    // إنشاء استلامات
    if (i % 7 === 0) {
      receipts.push({
        declarationNumber,
        receiptNumber: `RCPT${10000 + i}`,
        receiptType: ['استلام بضائع', 'استلام مستندات', 'استلام ضمان'][i % 3],
        receiptDate: new Date(Date.now() - Math.floor(Math.random() * 15) * 24 * 60 * 60 * 1000),
        receivedBy: `مستلم ${i + 1}`,
        receivedById: `${4000000000 + i}`,
        notes: `ملاحظات للاستلام ${i + 1}`,
      });
    }
  }

  // حساب السعر الإجمالي لحركات الأصناف
  itemMovements.forEach(item => {
    item.totalPrice = item.quantity * item.unitPrice;
  });

  return {
    clients,
    declarations,
    drivers,
    itemMovements,
    authorizations,
    releases,
    permits,
    guarantees,
    receipts,
  };
}

// دالة لإنشاء بيانات الاختبار في قاعدة البيانات
async function createTestData(count) {
  console.log(`بدء إنشاء ${count} بيان اختبار...`);

  try {
    // إنشاء البيانات العشوائية
    const data = generateRandomData(count);

    // إنشاء العملاء
    console.log(`إنشاء ${data.clients.length} عميل...`);
    for (const client of data.clients) {
      await prisma.client.upsert({
        where: { taxNumber: client.taxNumber },
        update: client,
        create: client,
      });
    }

    // إنشاء البيانات
    console.log(`إنشاء ${data.declarations.length} بيان...`);
    for (const declaration of data.declarations) {
      await prisma.declaration.upsert({
        where: { declarationNumber: declaration.declarationNumber },
        update: declaration,
        create: declaration,
      });
    }

    // إنشاء السائقين
    console.log(`إنشاء ${data.drivers.length} سائق...`);
    for (const driver of data.drivers) {
      await prisma.driver.upsert({
        where: {
          declarationNumber_idNumber: {
            declarationNumber: driver.declarationNumber,
            idNumber: driver.idNumber,
          }
        },
        update: driver,
        create: driver,
      });
    }

    // إنشاء حركات الأصناف
    console.log(`إنشاء ${data.itemMovements.length} حركة صنف...`);
    for (const itemMovement of data.itemMovements) {
      await prisma.itemMovement.create({
        data: itemMovement,
      });
    }

    // إنشاء التفويضات
    console.log(`إنشاء ${data.authorizations.length} تفويض...`);
    for (const authorization of data.authorizations) {
      await prisma.authorization.create({
        data: authorization,
      });
    }

    // إنشاء الإفراجات
    console.log(`إنشاء ${data.releases.length} إفراج...`);
    for (const release of data.releases) {
      await prisma.release.create({
        data: release,
      });
    }

    // إنشاء التصاريح
    console.log(`إنشاء ${data.permits.length} تصريح...`);
    for (const permit of data.permits) {
      await prisma.permit.create({
        data: permit,
      });
    }

    // إنشاء الضمانات
    console.log(`إنشاء ${data.guarantees.length} ضمان...`);
    for (const guarantee of data.guarantees) {
      await prisma.guarantee.create({
        data: guarantee,
      });
    }

    // إنشاء الاستلامات
    console.log(`إنشاء ${data.receipts.length} استلام...`);
    for (const receipt of data.receipts) {
      await prisma.receipt.create({
        data: receipt,
      });
    }

    console.log('تم إنشاء بيانات الاختبار بنجاح!');
  } catch (error) {
    console.error('خطأ في إنشاء بيانات الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// الدالة الرئيسية
async function main() {
  // الحصول على عدد البيانات من وسيطات سطر الأوامر
  const args = process.argv.slice(2);
  const count = parseInt(args[0]) || DEFAULT_COUNT;

  await createTestData(count);
}

// تنفيذ الدالة الرئيسية
main().catch(err => {
  console.error('خطأ في تنفيذ السكريبت:', err);
  process.exit(1);
});
