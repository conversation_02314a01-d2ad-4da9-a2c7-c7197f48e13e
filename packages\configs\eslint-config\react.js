module.exports = {
  extends: [
    "./index.js",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended"
  ],
  rules: {
    // React specific rules
    "react/function-component-definition": [
      "error",
      {
        "namedComponents": "arrow-function",
        "unnamedComponents": "arrow-function"
      }
    ],
    "react/jsx-handler-names": [
      "error",
      {
        "eventHandlerPrefix": "handle",
        "eventHandlerPropPrefix": "on",
        "checkLocalVariables": true,
        "checkInlineFunction": true
      }
    ],
    "react/jsx-key": [
      "error",
      {
        "checkFragmentShorthand": true,
        "checkKeyMustBeforeSpread": true
      }
    ],
    "react/jsx-no-bind": [
      "error",
      {
        "allowArrowFunctions": true,
        "allowFunctions": false,
        "allowBind": false
      }
    ],
    "react/jsx-no-constructed-context-values": "error",
    "react/jsx-no-script-url": "error",
    "react/jsx-no-target-blank": [
      "error",
      {
        "allowReferrer": false,
        "enforceDynamicLinks": "always"
      }
    ],
    "react/jsx-props-no-spreading": [
      "error",
      {
        "html": "enforce",
        "custom": "enforce",
        "explicitSpread": "ignore",
        "exceptions": ["Component"]
      }
    ],
    "react/no-access-state-in-setstate": "error",
    "react/no-adjacent-inline-elements": "error",
    "react/no-children-prop": "error",
    "react/no-did-mount-set-state": "error",
    "react/no-did-update-set-state": "error",
    "react/no-direct-mutation-state": "error",
    "react/no-find-dom-node": "error",
    "react/no-namespace": "error",
    "react/no-redundant-should-component-update": "error",
    "react/no-render-return-value": "error",
    "react/no-string-refs": "error",
    "react/no-this-in-sfc": "error",
    "react/no-typos": "error",
    "react/no-unknown-property": "error",
    "react/no-unsafe": "error",
    "react/no-will-update-set-state": "error",
    "react/prefer-es6-class": ["error", "always"],
    "react/prefer-read-only-props": "error",
    "react/prefer-stateless-function": "error",
    "react/require-render-return": "error",
    "react/state-in-constructor": ["error", "always"],
    "react/static-property-placement": ["error", "static public field"],
    "react/style-prop-object": "error",
    "react/void-dom-elements-no-children": "error"
  }
};
