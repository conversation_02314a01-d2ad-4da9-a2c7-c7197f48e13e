#!/usr/bin/env node

/**
 * سكريبت تحليل وحذف المكتبات غير المستخدمة
 * يقوم بفحص جميع imports في الكود ومقارنتها بالتبعيات المثبتة
 */

const fs = require('fs');
const path = require('path');

// قائمة المكتبات المستخدمة فعلياً من تحليل الكود
const USED_LIBRARIES = {
  // مكتبات الجذر
  root: [
    '@prisma/client', 'bcryptjs', 'cookie-parser', 'cron', 'notistack', 'ua-parser-js',
    '@types/bcryptjs', '@types/cookie-parser', '@types/cron', '@types/node', '@types/ua-parser-js',
    '@commitlint/cli', '@commitlint/config-conventional', '@types/stylis',
    '@typescript-eslint/eslint-plugin', '@typescript-eslint/parser',
    'eslint', 'eslint-config-prettier', 'eslint-plugin-import', 'eslint-plugin-jsx-a11y',
    'eslint-plugin-react', 'eslint-plugin-react-hooks', 'eslint-plugin-sonarjs', 'eslint-plugin-unicorn',
    'husky', 'lint-staged', 'prettier', 'prisma', 'turbo'
  ],

  // مكتبات API
  api: [
    '@pdf-lib/fontkit', '@prisma/client', 'bcryptjs', 'compression', 'cookie-parser', 'cors',
    'cron', 'csv-writer', 'dotenv', 'exceljs', 'express', 'express-validator', 'helmet',
    'jsonwebtoken', 'morgan', 'multer', 'pdf-lib', 'pdfjs-dist', 'shared-types',
    'ua-parser-js', 'winston', 'zod',
    '@types/bcryptjs', '@types/compression', '@types/cookie-parser', '@types/cors',
    '@types/express', '@types/jest', '@types/jsonwebtoken', '@types/morgan', '@types/multer',
    '@types/node', '@types/supertest', '@types/ua-parser-js',
    '@typescript-eslint/eslint-plugin', '@typescript-eslint/parser', 'eslint',
    'jest', 'jest-mock-extended', 'nodemon', 'prisma', 'supertest', 'ts-jest', 'ts-node', 'typescript'
  ],

  // مكتبات الواجهة الأمامية
  web: [
    '@dnd-kit/core', '@dnd-kit/sortable', '@dnd-kit/utilities',
    '@emotion/react', '@emotion/styled', '@hookform/resolvers',
    '@mui/icons-material', '@mui/material', '@mui/x-data-grid', '@mui/x-date-pickers',
    '@pdf-lib/fontkit', '@reduxjs/toolkit', '@tanstack/react-query', '@tanstack/react-query-devtools',
    'axios', 'date-fns', 'dayjs', 'i18next', 'i18next-browser-languagedetector', 'i18next-http-backend',
    'jspdf', 'jspdf-autotable', 'notistack', 'pdf-lib', 'react', 'react-beautiful-dnd',
    'react-dom', 'react-dropzone', 'react-hook-form', 'react-i18next', 'react-pdf',
    'react-redux', 'react-router-dom', 'react-select', 'recharts', 'shared-types',
    'tailwindcss', 'ui-library', 'xlsx', 'zod',
    '@emotion/cache', '@testing-library/jest-dom', '@testing-library/react', '@testing-library/user-event',
    '@types/bcryptjs', '@types/react', '@types/react-dom', '@types/react-beautiful-dnd',
    '@typescript-eslint/eslint-plugin', '@typescript-eslint/parser', '@vitejs/plugin-react',
    'autoprefixer', 'eslint', 'eslint-plugin-react-hooks', 'eslint-plugin-react-refresh',
    'jsdom', 'msw', 'postcss', 'stylis', 'stylis-plugin-rtl', 'typescript', 'vite', 'vitest'
  ]
};

// مكتبات أساسية لا يجب حذفها
const ESSENTIAL_LIBRARIES = [
  'react', 'react-dom', 'express', '@prisma/client', 'typescript', 'eslint', 'prettier'
];

/**
 * قراءة package.json
 */
function readPackageJson(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`خطأ في قراءة ${filePath}:`, error.message);
    return null;
  }
}

/**
 * تحليل التبعيات غير المستخدمة
 */
function analyzeUnusedDependencies(packagePath, usedLibs, projectName) {
  const packageJson = readPackageJson(packagePath);
  if (!packageJson) return { unused: [], essential: [] };

  const allDependencies = {
    ...packageJson.dependencies || {},
    ...packageJson.devDependencies || {}
  };

  const unused = [];
  const essential = [];

  Object.keys(allDependencies).forEach(dep => {
    if (ESSENTIAL_LIBRARIES.includes(dep)) {
      essential.push(dep);
    } else if (!usedLibs.includes(dep)) {
      unused.push({
        name: dep,
        version: allDependencies[dep],
        type: packageJson.dependencies?.[dep] ? 'dependency' : 'devDependency'
      });
    }
  });

  return { unused, essential };
}

/**
 * إنشاء تقرير التحليل
 */
function generateReport(analyses) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalUnused: 0,
      projects: {}
    },
    details: {}
  };

  Object.keys(analyses).forEach(project => {
    const analysis = analyses[project];
    
    report.summary.totalUnused += analysis.unused.length;
    report.summary.projects[project] = {
      unused: analysis.unused.length
    };

    report.details[project] = {
      unused: analysis.unused,
      essential: analysis.essential
    };
  });

  return report;
}

/**
 * حفظ التقرير
 */
function saveReport(report) {
  const reportPath = path.join(__dirname, '..', 'docs', 'unused-dependencies-analysis.md');
  
  let content = `# 📦 تحليل المكتبات غير المستخدمة - مشروع AlnoorArch

**تاريخ التحليل**: ${new Date(report.timestamp).toLocaleString('ar-EG')}  
**إجمالي المكتبات غير المستخدمة**: ${report.summary.totalUnused}

---

## 📊 ملخص التحليل

`;

  Object.keys(report.summary.projects).forEach(project => {
    const proj = report.summary.projects[project];
    content += `### **${project}**
- المكتبات غير المستخدمة: ${proj.unused}

`;
  });

  content += `---

## 📋 تفاصيل المكتبات غير المستخدمة

`;

  Object.keys(report.details).forEach(project => {
    const details = report.details[project];
    content += `### **${project}**

#### المكتبات غير المستخدمة (${details.unused.length}):
`;

    if (details.unused.length === 0) {
      content += `✅ لا توجد مكتبات غير مستخدمة

`;
    } else {
      details.unused.forEach(lib => {
        content += `- **${lib.name}** v${lib.version} (${lib.type})
`;
      });
      content += `
`;
    }

    content += `#### المكتبات الأساسية المحتفظ بها (${details.essential.length}):
`;
    details.essential.forEach(lib => {
      content += `- ${lib}
`;
    });
    content += `

`;
  });

  content += `---

## 🛠️ أوامر الحذف المقترحة

`;

  Object.keys(report.details).forEach(project => {
    const details = report.details[project];
    if (details.unused.length > 0) {
      content += `### **${project}**
\`\`\`bash
cd ${project === 'root' ? '.' : `apps/${project}`}
pnpm remove ${details.unused.map(lib => lib.name).join(' ')}
\`\`\`

`;
    }
  });

  content += `---

## ⚠️ تحذيرات

1. **تأكد من الاختبار** بعد حذف أي مكتبة
2. **راجع التبعيات المتداخلة** قبل الحذف
3. **احتفظ بنسخة احتياطية** من package.json
4. **تحقق من البناء** بعد كل حذف

---

**📝 ملاحظة**: هذا التحليل مبني على فحص imports في الكود. قد تكون بعض المكتبات مستخدمة بطرق غير مباشرة.
`;

  fs.writeFileSync(reportPath, content, 'utf8');
  console.log(`✅ تم حفظ التقرير في: ${reportPath}`);
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔍 بدء تحليل المكتبات غير المستخدمة...\n');

  const analyses = {
    root: analyzeUnusedDependencies(
      path.join(__dirname, '..', 'package.json'),
      USED_LIBRARIES.root,
      'root'
    ),
    api: analyzeUnusedDependencies(
      path.join(__dirname, '..', 'apps', 'api', 'package.json'),
      USED_LIBRARIES.api,
      'api'
    ),
    web: analyzeUnusedDependencies(
      path.join(__dirname, '..', 'apps', 'web', 'package.json'),
      USED_LIBRARIES.web,
      'web'
    )
  };

  const report = generateReport(analyses);
  saveReport(report);

  // طباعة ملخص في الكونسول
  console.log('📊 ملخص التحليل:');
  console.log(`   إجمالي المكتبات غير المستخدمة: ${report.summary.totalUnused}`);
  console.log('');

  Object.keys(report.summary.projects).forEach(project => {
    const proj = report.summary.projects[project];
    console.log(`   ${project}: ${proj.unused} مكتبة`);
  });

  console.log('\n📁 راجع التقرير المفصل في: docs/unused-dependencies-analysis.md');
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = { analyzeUnusedDependencies, generateReport };
