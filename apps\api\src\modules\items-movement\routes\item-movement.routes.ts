import { Router } from 'express';
import { itemMovementController } from '../controllers/item-movement.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import {
  createItemMovementSchema,
  updateItemMovementSchema,
  getItemMovementSchema,
  deleteItemMovementSchema,
  listItemMovementsSchema,
} from '../schemas/item-movement.schema.js';

export const itemMovementRoutes = Router();

// مسارات حركة الأصناف
itemMovementRoutes.get(
  '/',
  authMiddleware,
  validateRequest(listItemMovementsSchema),
  itemMovementController.listItemMovements
);

itemMovementRoutes.post(
  '/',
  authMiddleware,
  validateRequest(createItemMovementSchema),
  itemMovementController.createItemMovement
);

itemMovementRoutes.get(
  '/:id',
  authMiddleware,
  validateRequest(getItemMovementSchema),
  itemMovementController.getItemMovement
);

itemMovementRoutes.put(
  '/:id',
  authMiddleware,
  validateRequest(updateItemMovementSchema),
  itemMovementController.updateItemMovement
);

itemMovementRoutes.delete(
  '/:id',
  authMiddleware,
  validateRequest(deleteItemMovementSchema),
  itemMovementController.deleteItemMovement
);
