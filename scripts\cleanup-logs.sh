#!/bin/bash

# سكريبت تنظيف السجلات والملفات المؤقتة
# يقوم بحذف السجلات القديمة وتنظيف الملفات المؤقتة

echo "🧹 بدء تنظيف السجلات والملفات المؤقتة..."

# متغيرات التكوين
LOG_RETENTION_DAYS=7
TURBO_LOG_RETENTION_DAYS=3
BACKUP_DIR="backups/logs"

# إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
mkdir -p "$BACKUP_DIR"

# تنظيف سجلات API
echo "📝 تنظيف سجلات API..."
if [ -d "apps/api/logs" ]; then
    # نسخ احتياطية للسجلات المهمة
    if [ -f "apps/api/logs/error.log" ] && [ -s "apps/api/logs/error.log" ]; then
        cp "apps/api/logs/error.log" "$BACKUP_DIR/error_$(date +%Y%m%d_%H%M%S).log"
        echo "✅ تم إنشاء نسخة احتياطية من سجل الأخطاء"
    fi
    
    # حذف السجلات الأقدم من المدة المحددة
    find apps/api/logs -name "*.log" -mtime +$LOG_RETENTION_DAYS -delete
    
    # تفريغ السجلات الحالية (الاحتفاظ بالملفات فارغة)
    > apps/api/logs/combined.log
    > apps/api/logs/error.log
    
    echo "✅ تم تنظيف سجلات API"
else
    echo "⚠️  مجلد سجلات API غير موجود"
fi

# تنظيف سجلات Turbo
echo "⚡ تنظيف سجلات Turbo..."
if [ -d ".turbo/daemon" ]; then
    find .turbo/daemon -name "*.log.*" -mtime +$TURBO_LOG_RETENTION_DAYS -delete
    echo "✅ تم تنظيف سجلات Turbo"
fi

# تنظيف ملفات التخزين المؤقت
echo "💾 تنظيف ملفات التخزين المؤقت..."

# تنظيف تخزين مؤقت Node.js
if [ -d "node_modules/.cache" ]; then
    rm -rf node_modules/.cache/*
    echo "✅ تم تنظيف تخزين Node.js المؤقت"
fi

# تنظيف تخزين مؤقت API
if [ -d "apps/api/node_modules/.cache" ]; then
    rm -rf apps/api/node_modules/.cache/*
    echo "✅ تم تنظيف تخزين API المؤقت"
fi

# تنظيف تخزين مؤقت Web
if [ -d "apps/web/node_modules/.cache" ]; then
    rm -rf apps/web/node_modules/.cache/*
    echo "✅ تم تنظيف تخزين Web المؤقت"
fi

# تنظيف ملفات البناء المؤقتة
echo "🔨 تنظيف ملفات البناء المؤقتة..."
find . -name "*.tmp" -delete 2>/dev/null
find . -name "*.temp" -delete 2>/dev/null
find . -name ".DS_Store" -delete 2>/dev/null
find . -name "Thumbs.db" -delete 2>/dev/null

# إحصائيات التنظيف
echo ""
echo "📊 إحصائيات التنظيف:"

# حساب المساحة المحررة (تقديري)
FREED_SPACE=$(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1 || echo "0")
echo "   - المساحة المحررة: تقديرياً 50-100 MB"
echo "   - النسخ الاحتياطية: $BACKUP_DIR"
echo "   - السجلات المحتفظ بها: آخر $LOG_RETENTION_DAYS أيام"

# تحذيرات وتوصيات
echo ""
echo "⚠️  تحذيرات:"
echo "   - تم حذف السجلات الأقدم من $LOG_RETENTION_DAYS أيام"
echo "   - النسخ الاحتياطية متوفرة في $BACKUP_DIR"
echo "   - يُنصح بتشغيل هذا السكريبت أسبوعياً"

echo ""
echo "✅ تم الانتهاء من تنظيف السجلات والملفات المؤقتة!"

# اختياري: عرض استخدام القرص الحالي
echo ""
echo "💽 استخدام القرص الحالي:"
df -h . | tail -1
