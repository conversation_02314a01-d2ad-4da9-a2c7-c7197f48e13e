import { Router } from 'express';
import multer from 'multer';
import { receiptController } from '../controllers/receipt.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import {
  createReceiptSchema,
  updateReceiptSchema,
  getReceiptSchema,
  deleteReceiptSchema,
  listReceiptsSchema,
} from '../schemas/receipt.schema.js';
import { config } from '../../../core/config/app.config.js';

export const receiptRoutes = Router();

// إعداد Multer لرفع الملفات
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: config.upload.maxFileSize,
  },
  fileFilter: (req, file, cb) => {
    // قبول ملفات PDF فقط
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم. يرجى رفع ملف PDF فقط.'));
    }
  },
});

// مسارات الاستلامات
receiptRoutes.get(
  '/',
  authMiddleware,
  validateRequest(listReceiptsSchema),
  receiptController.listReceipts
);

receiptRoutes.post(
  '/',
  authMiddleware,
  upload.single('file'),
  validateRequest(createReceiptSchema),
  receiptController.createReceipt
);

receiptRoutes.get(
  '/:id',
  authMiddleware,
  validateRequest(getReceiptSchema),
  receiptController.getReceipt
);

receiptRoutes.put(
  '/:id',
  authMiddleware,
  upload.single('file'),
  validateRequest(updateReceiptSchema),
  receiptController.updateReceipt
);

receiptRoutes.delete(
  '/:id',
  authMiddleware,
  validateRequest(deleteReceiptSchema),
  receiptController.deleteReceipt
);
