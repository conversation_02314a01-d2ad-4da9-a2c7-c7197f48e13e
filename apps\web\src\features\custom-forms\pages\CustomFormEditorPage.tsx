import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ArrowUpward as ArrowUpIcon,
  ArrowDownward as ArrowDownIcon,
} from '@mui/icons-material';
import { useCustomForm, useCreateCustomForm, useUpdateCustomForm } from '../hooks/useCustomForms';
import { CustomFormFormValues, <PERSON><PERSON>ield, FormType, FieldType, SelectOption } from '../types/custom-form.types';
import { v4 as uuidv4 } from 'uuid';

// مخطط التحقق من صحة النموذج
const formSchema = z.object({
  name: z.string().min(3, 'اسم النموذج يجب أن يكون على الأقل 3 أحرف'),
  description: z.string().optional(),
  formType: z.string(),
  isActive: z.boolean().default(true),
});

const CustomFormEditorPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id, mode } = useParams<{ id: string; mode: 'edit' | 'copy' }>();
  const isEditMode = mode === 'edit';
  const isCopyMode = mode === 'copy';
  const isNewMode = !id;
  
  // حالة الحقول
  const [fields, setFields] = useState<FormField[]>([]);
  
  // استخدام خطافات النماذج المخصصة
  const { data: customForm, isLoading: isLoadingForm } = useCustomForm(id || '');
  const createMutation = useCreateCustomForm();
  const updateMutation = useUpdateCustomForm(id || '');
  
  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<Omit<CustomFormFormValues, 'fields'>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      formType: 'declarations',
      isActive: true,
    },
  });
  
  // تحميل بيانات النموذج المخصص عند تحميل الصفحة
  useEffect(() => {
    if (customForm && (isEditMode || isCopyMode)) {
      reset({
        name: isCopyMode ? `${customForm.name} (نسخة)` : customForm.name,
        description: customForm.description || '',
        formType: customForm.formType,
        isActive: customForm.isActive,
      });
      
      setFields(customForm.fields.map(field => ({
        ...field,
        id: isCopyMode ? uuidv4() : field.id,
      })));
    }
  }, [customForm, isEditMode, isCopyMode, reset]);
  
  // إضافة حقل جديد
  const handleAddField = () => {
    const newField: FormField = {
      id: uuidv4(),
      name: `field_${fields.length + 1}`,
      label: `حقل ${fields.length + 1}`,
      type: 'text',
      required: false,
      order: fields.length,
    };
    
    setFields([...fields, newField]);
  };
  
  // حذف حقل
  const handleDeleteField = (id: string) => {
    setFields(fields.filter(field => field.id !== id));
  };
  
  // تحريك حقل للأعلى
  const handleMoveFieldUp = (index: number) => {
    if (index === 0) return;
    
    const newFields = [...fields];
    const temp = newFields[index];
    newFields[index] = newFields[index - 1];
    newFields[index - 1] = temp;
    
    // تحديث ترتيب الحقول
    newFields.forEach((field, i) => {
      field.order = i;
    });
    
    setFields(newFields);
  };
  
  // تحريك حقل للأسفل
  const handleMoveFieldDown = (index: number) => {
    if (index === fields.length - 1) return;
    
    const newFields = [...fields];
    const temp = newFields[index];
    newFields[index] = newFields[index + 1];
    newFields[index + 1] = temp;
    
    // تحديث ترتيب الحقول
    newFields.forEach((field, i) => {
      field.order = i;
    });
    
    setFields(newFields);
  };
  
  // تحديث حقل
  const handleUpdateField = (id: string, key: keyof FormField, value: any) => {
    setFields(fields.map(field => {
      if (field.id === id) {
        return { ...field, [key]: value };
      }
      return field;
    }));
  };
  
  // إضافة خيار للقائمة المنسدلة
  const handleAddOption = (fieldId: string) => {
    setFields(fields.map(field => {
      if (field.id === fieldId) {
        const options = field.options || [];
        const newOption: SelectOption = {
          value: `option_${options.length + 1}`,
          label: `خيار ${options.length + 1}`,
        };
        return { ...field, options: [...options, newOption] };
      }
      return field;
    }));
  };
  
  // حذف خيار من القائمة المنسدلة
  const handleDeleteOption = (fieldId: string, optionIndex: number) => {
    setFields(fields.map(field => {
      if (field.id === fieldId && field.options) {
        const options = [...field.options];
        options.splice(optionIndex, 1);
        return { ...field, options };
      }
      return field;
    }));
  };
  
  // تحديث خيار في القائمة المنسدلة
  const handleUpdateOption = (fieldId: string, optionIndex: number, key: keyof SelectOption, value: string) => {
    setFields(fields.map(field => {
      if (field.id === fieldId && field.options) {
        const options = [...field.options];
        options[optionIndex] = { ...options[optionIndex], [key]: value };
        return { ...field, options };
      }
      return field;
    }));
  };
  
  // معالجة حفظ النموذج
  const onSubmit = async (data: Omit<CustomFormFormValues, 'fields'>) => {
    const formData: CustomFormFormValues = {
      ...data,
      fields,
    };
    
    try {
      if (isEditMode) {
        await updateMutation.mutateAsync(formData);
      } else {
        await createMutation.mutateAsync(formData);
      }
      
      navigate('/custom-forms');
    } catch (error) {
      console.error('Error saving form:', error);
    }
  };
  
  // معالجة العودة إلى صفحة القائمة
  const handleBack = () => {
    navigate('/custom-forms');
  };
  
  if (isLoadingForm && (isEditMode || isCopyMode)) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Container maxWidth="xl">
      <Box sx={{ mb: 4 }}>
        <Button
          variant="outlined"
          color="primary"
          onClick={handleBack}
          startIcon={<ArrowBackIcon />}
          sx={{ mb: 2 }}
        >
          {t('common.back')}
        </Button>
        
        <Typography variant="h4" gutterBottom>
          {isEditMode
            ? t('customForms.editForm')
            : isCopyMode
            ? t('customForms.copyForm')
            : t('customForms.createForm')}
        </Typography>
        
        <Typography variant="body1" color="text.secondary">
          {t('customForms.editorDescription')}
        </Typography>
      </Box>
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {t('customForms.formDetails')}
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Controller
                      name="name"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label={t('customForms.name')}
                          fullWidth
                          error={!!errors.name}
                          helperText={errors.name?.message}
                        />
                      )}
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Controller
                      name="description"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label={t('customForms.description')}
                          fullWidth
                          multiline
                          rows={3}
                          error={!!errors.description}
                          helperText={errors.description?.message}
                        />
                      )}
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Controller
                      name="formType"
                      control={control}
                      render={({ field }) => (
                        <FormControl fullWidth error={!!errors.formType}>
                          <InputLabel>{t('customForms.formType')}</InputLabel>
                          <Select {...field} label={t('customForms.formType')}>
                            <MenuItem value="declarations">{t('customForms.formTypes.declarations')}</MenuItem>
                            <MenuItem value="item_movements">{t('customForms.formTypes.itemMovements')}</MenuItem>
                            <MenuItem value="authorizations">{t('customForms.formTypes.authorizations')}</MenuItem>
                            <MenuItem value="releases">{t('customForms.formTypes.releases')}</MenuItem>
                            <MenuItem value="permits">{t('customForms.formTypes.permits')}</MenuItem>
                            <MenuItem value="guarantees">{t('customForms.formTypes.guarantees')}</MenuItem>
                            <MenuItem value="receipts">{t('customForms.formTypes.receipts')}</MenuItem>
                            <MenuItem value="clients">{t('customForms.formTypes.clients')}</MenuItem>
                            <MenuItem value="documents">{t('customForms.formTypes.documents')}</MenuItem>
                          </Select>
                          {errors.formType && (
                            <FormHelperText>{errors.formType.message}</FormHelperText>
                          )}
                        </FormControl>
                      )}
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Controller
                      name="isActive"
                      control={control}
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Switch
                              checked={field.value}
                              onChange={(e) => field.onChange(e.target.checked)}
                            />
                          }
                          label={t('customForms.isActive')}
                        />
                      )}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    {t('customForms.fields')}
                  </Typography>
                  
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<AddIcon />}
                    onClick={handleAddField}
                  >
                    {t('customForms.addField')}
                  </Button>
                </Box>
                
                <Divider sx={{ mb: 2 }} />
                
                {fields.length === 0 ? (
                  <Typography align="center" color="text.secondary" sx={{ py: 4 }}>
                    {t('customForms.noFields')}
                  </Typography>
                ) : (
                  fields.map((field, index) => (
                    <Paper key={field.id} sx={{ p: 2, mb: 2, position: 'relative' }}>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            label={t('customForms.fieldName')}
                            value={field.name}
                            onChange={(e) => handleUpdateField(field.id, 'name', e.target.value)}
                            fullWidth
                            size="small"
                          />
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                          <TextField
                            label={t('customForms.fieldLabel')}
                            value={field.label}
                            onChange={(e) => handleUpdateField(field.id, 'label', e.target.value)}
                            fullWidth
                            size="small"
                          />
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                          <FormControl fullWidth size="small">
                            <InputLabel>{t('customForms.fieldType')}</InputLabel>
                            <Select
                              value={field.type}
                              onChange={(e) => handleUpdateField(field.id, 'type', e.target.value)}
                              label={t('customForms.fieldType')}
                            >
                              <MenuItem value="text">{t('customForms.fieldTypes.text')}</MenuItem>
                              <MenuItem value="number">{t('customForms.fieldTypes.number')}</MenuItem>
                              <MenuItem value="date">{t('customForms.fieldTypes.date')}</MenuItem>
                              <MenuItem value="select">{t('customForms.fieldTypes.select')}</MenuItem>
                              <MenuItem value="checkbox">{t('customForms.fieldTypes.checkbox')}</MenuItem>
                              <MenuItem value="textarea">{t('customForms.fieldTypes.textarea')}</MenuItem>
                              <MenuItem value="file">{t('customForms.fieldTypes.file')}</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={field.required}
                                onChange={(e) => handleUpdateField(field.id, 'required', e.target.checked)}
                              />
                            }
                            label={t('customForms.fieldRequired')}
                          />
                        </Grid>
                        
                        {field.type === 'select' && (
                          <Grid item xs={12}>
                            <Typography variant="subtitle2" gutterBottom>
                              {t('customForms.options')}
                            </Typography>
                            
                            {field.options?.map((option, optionIndex) => (
                              <Box key={optionIndex} sx={{ display: 'flex', mb: 1 }}>
                                <TextField
                                  label={t('customForms.optionValue')}
                                  value={option.value}
                                  onChange={(e) => handleUpdateOption(field.id, optionIndex, 'value', e.target.value)}
                                  size="small"
                                  sx={{ mr: 1, flex: 1 }}
                                />
                                
                                <TextField
                                  label={t('customForms.optionLabel')}
                                  value={option.label}
                                  onChange={(e) => handleUpdateOption(field.id, optionIndex, 'label', e.target.value)}
                                  size="small"
                                  sx={{ mr: 1, flex: 1 }}
                                />
                                
                                <IconButton
                                  color="error"
                                  onClick={() => handleDeleteOption(field.id, optionIndex)}
                                  size="small"
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Box>
                            ))}
                            
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<AddIcon />}
                              onClick={() => handleAddOption(field.id)}
                              sx={{ mt: 1 }}
                            >
                              {t('customForms.addOption')}
                            </Button>
                          </Grid>
                        )}
                        
                        <Box sx={{ position: 'absolute', top: 8, right: 8, display: 'flex' }}>
                          <Tooltip title={t('common.moveUp')}>
                            <IconButton
                              color="primary"
                              onClick={() => handleMoveFieldUp(index)}
                              disabled={index === 0}
                              size="small"
                            >
                              <ArrowUpIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          
                          <Tooltip title={t('common.moveDown')}>
                            <IconButton
                              color="primary"
                              onClick={() => handleMoveFieldDown(index)}
                              disabled={index === fields.length - 1}
                              size="small"
                            >
                              <ArrowDownIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          
                          <Tooltip title={t('common.delete')}>
                            <IconButton
                              color="error"
                              onClick={() => handleDeleteField(field.id)}
                              size="small"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Grid>
                    </Paper>
                  ))
                )}
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="outlined"
                color="secondary"
                onClick={handleBack}
                sx={{ mr: 2 }}
              >
                {t('common.cancel')}
              </Button>
              
              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                disabled={createMutation.isPending || updateMutation.isPending}
              >
                {(createMutation.isPending || updateMutation.isPending) ? (
                  <CircularProgress size={24} />
                ) : (
                  t('common.save')
                )}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Container>
  );
};

export default CustomFormEditorPage;
