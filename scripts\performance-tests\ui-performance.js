/**
 * سكريبت لاختبارات أداء واجهة المستخدم
 * 
 * يستخدم هذا السكريبت Puppeteer لقياس أداء واجهة المستخدم وإنشاء تقارير الأداء.
 * 
 * الاستخدام:
 * node ui-performance.js [page] [--auth]
 * 
 * أمثلة:
 * node ui-performance.js /declarations
 * node ui-performance.js /permits --auth
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const exec = promisify(require('child_process').exec);
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// الإعدادات الافتراضية
const DEFAULT_CONFIG = {
  baseUrl: 'http://localhost:3000', // عنوان URL الأساسي لواجهة المستخدم
  headless: true, // تشغيل المتصفح بدون واجهة رسومية
  timeout: 30000, // المهلة بالمللي ثانية
  viewport: { width: 1920, height: 1080 }, // حجم نافذة العرض
};

// تسجيل الدخول إلى التطبيق
async function login(page) {
  try {
    await page.goto(`${DEFAULT_CONFIG.baseUrl}/login`, { waitUntil: 'networkidle0' });
    
    // ملء نموذج تسجيل الدخول
    await page.type('input[name="username"]', process.env.TEST_USERNAME || 'admin');
    await page.type('input[name="password"]', process.env.TEST_PASSWORD || 'admin123');
    
    // النقر على زر تسجيل الدخول
    await Promise.all([
      page.click('button[type="submit"]'),
      page.waitForNavigation({ waitUntil: 'networkidle0' }),
    ]);
    
    return true;
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    return false;
  }
}

// قياس أداء الصفحة
async function measurePagePerformance(page, url) {
  // التنقل إلى الصفحة
  await page.goto(url, { waitUntil: 'networkidle0' });
  
  // قياس الأداء
  const performanceMetrics = await page.evaluate(() => {
    const perfEntries = performance.getEntriesByType('navigation');
    return perfEntries.length > 0 ? perfEntries[0].toJSON() : {};
  });
  
  // قياس استخدام الذاكرة
  const memoryInfo = await page.evaluate(() => {
    return performance.memory ? {
      jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
      totalJSHeapSize: performance.memory.totalJSHeapSize,
      usedJSHeapSize: performance.memory.usedJSHeapSize,
    } : {};
  });
  
  // التقاط لقطة شاشة
  const screenshotPath = path.join(__dirname, 'reports', `${url.replace(/\//g, '-').replace(/^-/, '')}-screenshot.png`);
  await page.screenshot({ path: screenshotPath, fullPage: true });
  
  return {
    url,
    performanceMetrics,
    memoryInfo,
    screenshotPath,
  };
}

// الدالة الرئيسية
async function runPerformanceTest() {
  // الحصول على وسيطات سطر الأوامر
  const args = process.argv.slice(2);
  const pagePath = args[0] || '/declarations';
  const needsAuth = args.includes('--auth');
  
  const url = `${DEFAULT_CONFIG.baseUrl}${pagePath}`;
  
  console.log(`تنفيذ اختبار أداء واجهة المستخدم لـ: ${pagePath}`);
  console.log(`المصادقة: ${needsAuth ? 'نعم' : 'لا'}`);
  
  // إنشاء مجلد للتقارير إذا لم يكن موجودًا
  const reportsDir = path.join(__dirname, 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir);
  }
  
  // إطلاق المتصفح
  const browser = await puppeteer.launch({
    headless: DEFAULT_CONFIG.headless,
    defaultViewport: DEFAULT_CONFIG.viewport,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });
  
  const page = await browser.newPage();
  
  try {
    // تسجيل الدخول إذا لزم الأمر
    if (needsAuth) {
      const loginSuccess = await login(page);
      if (!loginSuccess) {
        throw new Error('فشل تسجيل الدخول');
      }
    }
    
    // قياس أداء الصفحة
    const performanceData = await measurePagePerformance(page, url);
    
    // إنشاء اسم ملف للتقرير
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const pagePathName = pagePath.replace(/\//g, '-').replace(/^-/, '');
    const reportFile = path.join(reportsDir, `${pagePathName}-${timestamp}.json`);
    
    // حفظ النتائج
    fs.writeFileSync(reportFile, JSON.stringify(performanceData, null, 2));
    console.log(`تم حفظ التقرير في: ${reportFile}`);
    
    // عرض ملخص
    console.log('\nملخص الاختبار:');
    console.log(`وقت التحميل الكامل: ${performanceData.performanceMetrics.duration} مللي ثانية`);
    console.log(`وقت DOM Content Loaded: ${performanceData.performanceMetrics.domContentLoadedEventEnd - performanceData.performanceMetrics.domContentLoadedEventStart} مللي ثانية`);
    console.log(`حجم الصفحة: ${performanceData.performanceMetrics.transferSize} بايت`);
    
    if (performanceData.memoryInfo.usedJSHeapSize) {
      console.log(`استخدام ذاكرة JavaScript: ${Math.round(performanceData.memoryInfo.usedJSHeapSize / 1024 / 1024)} ميجابايت`);
    }
    
    console.log(`تم حفظ لقطة الشاشة في: ${performanceData.screenshotPath}`);
  } catch (error) {
    console.error('خطأ في تنفيذ اختبار الأداء:', error);
  } finally {
    // إغلاق المتصفح
    await browser.close();
  }
}

// تنفيذ الدالة الرئيسية
runPerformanceTest().catch(err => {
  console.error('خطأ في تنفيذ اختبار الأداء:', err);
  process.exit(1);
});
