import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { FileUpload } from '../FileUpload';
import { act } from 'react-dom/test-utils';

// Mock react-dropzone
jest.mock('react-dropzone', () => ({
  useDropzone: () => ({
    getRootProps: () => ({
      onClick: jest.fn(),
    }),
    getInputProps: () => ({}),
    isDragActive: false,
  }),
}));

describe('FileUpload Component', () => {
  const defaultProps = {
    onChange: jest.fn(),
    value: [],
  };

  test('renders dropzone area with default text', () => {
    render(<FileUpload {...defaultProps} />);
    expect(screen.getByText('اسحب وأفلت الملفات هنا، أو انقر للاختيار')).toBeInTheDocument();
  });

  test('renders helper text when provided', () => {
    render(<FileUpload {...defaultProps} helperText="الحد الأقصى للحجم: 10 ميجابايت" />);
    expect(screen.getByText('الحد الأقصى للحجم: 10 ميجابايت')).toBeInTheDocument();
  });

  test('renders error message when provided', () => {
    render(<FileUpload {...defaultProps} error="حجم الملف كبير جدًا" />);
    expect(screen.getByText('حجم الملف كبير جدًا')).toBeInTheDocument();
  });

  test('renders file list when files are provided', () => {
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    Object.defineProperty(file, 'size', { value: 1024 });
    
    render(<FileUpload {...defaultProps} value={[file]} />);
    
    expect(screen.getByText('test.pdf')).toBeInTheDocument();
    expect(screen.getByText('1 KB')).toBeInTheDocument();
  });

  test('calls onChange when removing a file', () => {
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    Object.defineProperty(file, 'size', { value: 1024 });
    
    render(<FileUpload {...defaultProps} value={[file]} />);
    
    const deleteButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(deleteButton);
    
    expect(defaultProps.onChange).toHaveBeenCalledWith([]);
  });

  test('disables component when disabled prop is true', () => {
    render(<FileUpload {...defaultProps} disabled={true} />);
    
    const uploadButton = screen.getByRole('button', { name: /اختر ملفات/i });
    expect(uploadButton).toBeDisabled();
  });

  test('renders accept types as chips when provided', () => {
    const acceptTypes = {
      'application/pdf': ['.pdf'],
      'image/jpeg': ['.jpg', '.jpeg'],
    };
    
    render(<FileUpload {...defaultProps} accept={acceptTypes} />);
    
    expect(screen.getByText('.pdf')).toBeInTheDocument();
    expect(screen.getByText('.jpg, .jpeg')).toBeInTheDocument();
  });

  test('formats file size correctly for different units', () => {
    // Create files with different sizes
    const smallFile = new File(['small'], 'small.txt', { type: 'text/plain' });
    Object.defineProperty(smallFile, 'size', { value: 500 }); // 500 B
    
    const mediumFile = new File(['medium'], 'medium.txt', { type: 'text/plain' });
    Object.defineProperty(mediumFile, 'size', { value: 1024 * 5 }); // 5 KB
    
    const largeFile = new File(['large'], 'large.txt', { type: 'text/plain' });
    Object.defineProperty(largeFile, 'size', { value: 1024 * 1024 * 10 }); // 10 MB
    
    render(<FileUpload {...defaultProps} value={[smallFile, mediumFile, largeFile]} />);
    
    expect(screen.getByText('500 B')).toBeInTheDocument();
    expect(screen.getByText('5.00 KB')).toBeInTheDocument();
    expect(screen.getByText('10.00 MB')).toBeInTheDocument();
  });
});
