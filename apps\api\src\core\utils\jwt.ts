import jwt from 'jsonwebtoken';
import { config } from '../config/app.config.js';

/**
 * نوع التوكن
 */
export type TokenType = 'access' | 'refresh';

/**
 * وظيفة لإنشاء توكن JWT
 * @param payload البيانات المراد تضمينها في التوكن
 * @param tokenType نوع التوكن (access أو refresh)
 * @param expiresIn مدة صلاحية التوكن (اختياري، يستخدم القيمة الافتراضية من التكوين)
 * @returns توكن JWT
 */
export const generateToken = (
  payload: Record<string, unknown>,
  tokenType: TokenType = 'access',
  expiresIn?: string
): string => {
  try {
    // تحديد المفتاح السري ومدة الصلاحية بناءً على نوع التوكن
    const secret = tokenType === 'refresh'
      ? config.jwt.refreshSecret
      : config.jwt.secret;

    // تحديد مدة الصلاحية
    const options: any = {};

    if (expiresIn) {
      options.expiresIn = expiresIn;
    } else {
      const expiry = tokenType === 'refresh'
        ? config.jwt.refreshExpiresIn
        : config.jwt.expiresIn;

      options.expiresIn = expiry;
    }

    return jwt.sign(payload, secret, options);
  } catch (error) {
    console.error('Error generating token:', error);
    throw new Error('Failed to generate token');
  }
};

/**
 * وظيفة للتحقق من صحة توكن JWT
 * @param token توكن JWT
 * @param tokenType نوع التوكن (access أو refresh)
 * @returns البيانات المضمنة في التوكن
 */
export const verifyToken = (token: string, tokenType: TokenType = 'access'): jwt.JwtPayload => {
  try {
    // تحديد المفتاح السري بناءً على نوع التوكن
    const secret = tokenType === 'refresh'
      ? config.jwt.refreshSecret
      : config.jwt.secret;

    return jwt.verify(token, secret) as jwt.JwtPayload;
  } catch (error) {
    console.error('Error verifying token:', error);
    throw error;
  }
};
