import { prismaMock } from '../../../core/utils/__mocks__/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

// Mock the document service
const mockDocumentService = {
  createDocument: async (data: any, userId: string, file?: any) => {
    // Mock implementation
    const document = {
      id: 'document-1',
      title: data.title,
      description: data.description,
      fileName: data.fileName,
      fileSize: data.fileSize,
      fileType: data.fileType,
      filePath: file ? `documents/doc_${Date.now()}.pdf` : data.filePath,
      uploadedBy: data.uploadedBy || userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return document;
  },

  updateDocument: async (id: string, data: any, userId: string, file?: any) => {
    const existingDocument = {
      id,
      title: 'مستند تجريبي',
      uploadedBy: 'user-123', // Fixed userId for testing
      filePath: 'documents/old_file.pdf',
    };

    if (existingDocument.uploadedBy !== userId) {
      throw new HttpException(403, 'غير مصرح لك بتحديث هذا المستند', 'Forbidden');
    }

    const updatedDocument = {
      ...existingDocument,
      ...data,
      filePath: file ? `documents/${id}.pdf` : existingDocument.filePath,
      updatedAt: new Date(),
    };

    return updatedDocument;
  },

  getDocument: async (id: string) => {
    const document = {
      id,
      title: 'مستند تجريبي',
      description: 'وصف المستند',
      fileName: 'document.pdf',
      fileSize: 1024000,
      fileType: 'application/pdf',
      filePath: 'documents/document.pdf',
      uploadedBy: 'user-123',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return document;
  },

  deleteDocument: async (id: string, userId: string) => {
    const document = {
      id,
      uploadedBy: 'user-123', // Fixed userId for testing
    };

    if (document.uploadedBy !== userId) {
      throw new HttpException(403, 'غير مصرح لك بحذف هذا المستند', 'Forbidden');
    }

    return { success: true };
  },

  listDocuments: async (params: any = {}) => {
    const documents = [
      {
        id: 'document-1',
        title: 'مستند تجريبي 1',
        description: 'وصف المستند الأول',
        fileName: 'document1.pdf',
        fileSize: 1024000,
        fileType: 'application/pdf',
        createdAt: new Date(),
      },
      {
        id: 'document-2',
        title: 'مستند تجريبي 2',
        description: 'وصف المستند الثاني',
        fileName: 'document2.pdf',
        fileSize: 2048000,
        fileType: 'application/pdf',
        createdAt: new Date(),
      },
    ];

    // Filter by search if provided
    let filteredDocuments = documents;
    if (params.search) {
      filteredDocuments = documents.filter(doc =>
        doc.title.includes(params.search) ||
        doc.description?.includes(params.search) ||
        doc.fileName.includes(params.search)
      );
    }

    return {
      data: filteredDocuments,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 10,
        total: filteredDocuments.length,
        pages: Math.ceil(filteredDocuments.length / (params.limit || 10)),
      },
    };
  },
};

describe('Document Service', () => {
  beforeEach(() => {
    // تنظيف المحاكيات قبل كل اختبار
  });

  describe('createDocument', () => {
    it('should create a new document successfully', async () => {
      // Arrange
      const documentData = {
        title: 'مستند تجريبي',
        description: 'وصف المستند',
        fileName: 'document.pdf',
        fileSize: 1024000,
        fileType: 'application/pdf',
        filePath: 'documents/document.pdf',
      };
      const userId = 'user-123';

      // Act
      const result = await mockDocumentService.createDocument(documentData, userId);

      // Assert
      expect(result).toHaveProperty('id');
      expect(result.title).toBe(documentData.title);
      expect(result.description).toBe(documentData.description);
      expect(result.fileName).toBe(documentData.fileName);
      expect(result.uploadedBy).toBe(userId);
    });

    it('should create document with uploaded file', async () => {
      // Arrange
      const documentData = {
        title: 'مستند مرفوع',
        fileName: 'uploaded.pdf',
        fileSize: 2048000,
        fileType: 'application/pdf',
        filePath: '',
      };
      const userId = 'user-123';
      const file = { originalname: 'uploaded.pdf', size: 2048000 };

      // Act
      const result = await mockDocumentService.createDocument(documentData, userId, file);

      // Assert
      expect(result.filePath).toContain('documents/doc_');
      expect(result.filePath).toContain('.pdf');
    });
  });

  describe('updateDocument', () => {
    it('should update document successfully', async () => {
      // Arrange
      const documentId = 'document-1';
      const updateData = {
        title: 'مستند محدث',
        description: 'وصف محدث',
      };
      const userId = 'user-123';

      // Act
      const result = await mockDocumentService.updateDocument(documentId, updateData, userId);

      // Assert
      expect(result.title).toBe(updateData.title);
      expect(result.description).toBe(updateData.description);
    });

    it('should throw error when user is not authorized', async () => {
      // Arrange
      const documentId = 'document-1';
      const updateData = { title: 'مستند محدث' };
      const userId = 'different-user';

      // Act & Assert
      await expect(
        mockDocumentService.updateDocument(documentId, updateData, userId)
      ).rejects.toThrow(HttpException);
    });

    it('should update document with new file', async () => {
      // Arrange
      const documentId = 'document-1';
      const updateData = { title: 'مستند محدث' };
      const userId = 'user-123';
      const file = { originalname: 'new_file.pdf', size: 1024000 };

      // Act
      const result = await mockDocumentService.updateDocument(documentId, updateData, userId, file);

      // Assert
      expect(result.filePath).toBe(`documents/${documentId}.pdf`);
    });
  });

  describe('getDocument', () => {
    it('should get document by id successfully', async () => {
      // Arrange
      const documentId = 'document-1';

      // Act
      const result = await mockDocumentService.getDocument(documentId);

      // Assert
      expect(result).toHaveProperty('id', documentId);
      expect(result).toHaveProperty('title');
      expect(result).toHaveProperty('fileName');
      expect(result).toHaveProperty('fileSize');
      expect(result).toHaveProperty('fileType');
    });
  });

  describe('deleteDocument', () => {
    it('should delete document successfully', async () => {
      // Arrange
      const documentId = 'document-1';
      const userId = 'user-123';

      // Act
      const result = await mockDocumentService.deleteDocument(documentId, userId);

      // Assert
      expect(result).toEqual({ success: true });
    });

    it('should throw error when user is not authorized to delete', async () => {
      // Arrange
      const documentId = 'document-1';
      const userId = 'different-user';

      // Act & Assert
      await expect(
        mockDocumentService.deleteDocument(documentId, userId)
      ).rejects.toThrow(HttpException);
    });
  });

  describe('listDocuments', () => {
    it('should list documents with pagination', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };

      // Act
      const result = await mockDocumentService.listDocuments(params);

      // Assert
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('pagination');
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination.page).toBe(params.page);
      expect(result.pagination.limit).toBe(params.limit);
    });

    it('should filter documents by search term', async () => {
      // Arrange
      const params = { search: 'تجريبي 1' };

      // Act
      const result = await mockDocumentService.listDocuments(params);

      // Assert
      expect(result.data).toHaveLength(1);
      expect(result.data[0].title).toContain('تجريبي 1');
    });

    it('should return empty results for non-matching search', async () => {
      // Arrange
      const params = { search: 'غير موجود' };

      // Act
      const result = await mockDocumentService.listDocuments(params);

      // Assert
      expect(result.data).toHaveLength(0);
    });
  });
});
