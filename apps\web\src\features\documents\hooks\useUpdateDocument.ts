import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';
import { Document } from './useDocument';

export interface UpdateDocumentRequest {
  documentNumber?: string;
  documentType?: string;
  title?: string;
  description?: string;
  documentDate?: string;
  expiryDate?: string;
  issuingAuthority?: string;
  referenceNumber?: string;
}

export const useUpdateDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data, file }: { id: string; data: UpdateDocumentRequest; file?: File | null }): Promise<Document> => {
      const formData = new FormData();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value);
        }
      });

      if (file) {
        formData.append('file', file);
      }

      const response = await api.put<Document>(`/api/documents/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      queryClient.invalidateQueries({ queryKey: ['document', id] });
    },
  });
};
