// 🔔 Hook للتأكيد - مشروع AlnoorArch

import { useState, useCallback } from 'react';

export interface ConfirmOptions {
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  confirmColor?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
}

export interface ConfirmState {
  open: boolean;
  title: string;
  message: string;
  confirmText: string;
  cancelText: string;
  confirmColor: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  onConfirm: () => void;
  onCancel: () => void;
}

/**
 * Hook لإدارة حالة التأكيد
 */
export const useConfirm = () => {
  const [confirmState, setConfirmState] = useState<ConfirmState>({
    open: false,
    title: 'تأكيد',
    message: 'هل أنت متأكد؟',
    confirmText: 'تأكيد',
    cancelText: 'إلغاء',
    confirmColor: 'primary',
    onConfirm: () => {},
    onCancel: () => {},
  });

  /**
   * إظهار حوار التأكيد
   */
  const confirm = useCallback((
    options: ConfirmOptions & { message?: string } | string,
    onConfirm?: () => void,
    additionalOptions: ConfirmOptions = {}
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      let message: string;
      let confirmOptions: ConfirmOptions;
      let confirmCallback: () => void;

      if (typeof options === 'string') {
        message = options;
        confirmOptions = additionalOptions;
        confirmCallback = onConfirm || (() => {});
      } else {
        message = options.message || 'هل أنت متأكد؟';
        confirmOptions = options;
        confirmCallback = onConfirm || (() => {});
      }
      setConfirmState({
        open: true,
        title: confirmOptions.title || 'تأكيد',
        message,
        confirmText: confirmOptions.confirmText || 'تأكيد',
        cancelText: confirmOptions.cancelText || 'إلغاء',
        confirmColor: confirmOptions.confirmColor || 'primary',
        onConfirm: () => {
          confirmCallback();
          setConfirmState(prev => ({ ...prev, open: false }));
          resolve(true);
        },
        onCancel: () => {
          setConfirmState(prev => ({ ...prev, open: false }));
          resolve(false);
        },
      });
    });
  }, []);

  /**
   * إخفاء حوار التأكيد
   */
  const hideConfirm = useCallback(() => {
    setConfirmState(prev => ({ ...prev, open: false }));
  }, []);

  /**
   * تأكيد الحذف
   */
  const confirmDelete = useCallback((
    itemName: string,
    onConfirm: () => void
  ) => {
    confirm(
      `هل أنت متأكد من حذف "${itemName}"؟ لا يمكن التراجع عن هذا الإجراء.`,
      onConfirm,
      {
        title: 'تأكيد الحذف',
        confirmText: 'حذف',
        confirmColor: 'error',
      }
    );
  }, [confirm]);

  /**
   * تأكيد الحفظ
   */
  const confirmSave = useCallback((
    onConfirm: () => void,
    message: string = 'هل تريد حفظ التغييرات؟'
  ) => {
    confirm(
      message,
      onConfirm,
      {
        title: 'تأكيد الحفظ',
        confirmText: 'حفظ',
        confirmColor: 'primary',
      }
    );
  }, [confirm]);

  /**
   * تأكيد الإلغاء
   */
  const confirmCancel = useCallback((
    onConfirm: () => void,
    message: string = 'هل تريد إلغاء التغييرات؟ ستفقد جميع التعديلات غير المحفوظة.'
  ) => {
    confirm(
      message,
      onConfirm,
      {
        title: 'تأكيد الإلغاء',
        confirmText: 'إلغاء التغييرات',
        cancelText: 'متابعة التحرير',
        confirmColor: 'warning',
      }
    );
  }, [confirm]);

  /**
   * تأكيد الإرسال
   */
  const confirmSubmit = useCallback((
    onConfirm: () => void,
    message: string = 'هل تريد إرسال البيانات؟'
  ) => {
    confirm(
      message,
      onConfirm,
      {
        title: 'تأكيد الإرسال',
        confirmText: 'إرسال',
        confirmColor: 'success',
      }
    );
  }, [confirm]);

  /**
   * تأكيد مخصص مع خيارات متقدمة
   */
  const confirmCustom = useCallback((
    options: ConfirmOptions & {
      message: string;
      onConfirm: () => void;
    }
  ) => {
    confirm(options.message, options.onConfirm, options);
  }, [confirm]);

  return {
    confirmState,
    confirm,
    confirmDelete,
    confirmSave,
    confirmCancel,
    confirmSubmit,
    confirmCustom,
    hideConfirm,
  };
};
