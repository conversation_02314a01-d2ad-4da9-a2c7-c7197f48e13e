import { Request, Response, NextFunction } from 'express';
import { reportTemplateService } from '../services/report-template.service.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { successResponse } from '../../../core/utils/api/apiResponse.js';

/**
 * متحكم قوالب التقارير
 */
export const reportTemplateController = {
  /**
   * إنشاء قالب تقرير جديد
   */
  createReportTemplate: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // إنشاء قالب تقرير جديد
      const reportTemplate = await reportTemplateService.createReportTemplate(req.body, req.user.id);

      return res.status(201).json(successResponse(reportTemplate, 'تم إنشاء قالب التقرير بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث قالب تقرير
   */
  updateReportTemplate: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف القالب
      const { id } = req.params;

      // تحديث قالب التقرير
      const reportTemplate = await reportTemplateService.updateReportTemplate(id, req.body, req.user.id);

      return res.status(200).json(successResponse(reportTemplate, 'تم تحديث قالب التقرير بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف قالب تقرير
   */
  deleteReportTemplate: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف القالب
      const { id } = req.params;

      // حذف قالب التقرير
      await reportTemplateService.deleteReportTemplate(id, req.user.id);

      return res.status(200).json(successResponse(null, 'تم حذف قالب التقرير بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قالب تقرير
   */
  getReportTemplate: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف القالب
      const { id } = req.params;

      // الحصول على قالب التقرير
      const reportTemplate = await reportTemplateService.getReportTemplate(id);

      return res.status(200).json(successResponse(reportTemplate));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة قوالب التقارير
   */
  listReportTemplates: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معلمات البحث
      const { page, limit, sort, order, search, reportType } = req.query as any;

      // الحصول على قائمة قوالب التقارير
      const result = await reportTemplateService.listReportTemplates({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        search,
        reportType,
      });

      return res.status(200).json(successResponse(result));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على القالب الافتراضي لنوع تقرير معين
   */
  getDefaultTemplate: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على نوع التقرير
      const { reportType } = req.params;

      // الحصول على القالب الافتراضي
      const defaultTemplate = await reportTemplateService.getDefaultTemplate(reportType);

      return res.status(200).json(successResponse(defaultTemplate));
    } catch (error) {
      next(error);
    }
  },
};
