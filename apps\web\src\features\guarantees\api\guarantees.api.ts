import apiService from '../../../lib/services/api.service';
import {
  Guarantee,
  CreateGuaranteeDto,
  UpdateGuaranteeDto,
  GuaranteeSearchParams
} from '../types/guarantee.types';
import { PaginatedResponse } from '@/types/api.types';

/**
 * إنشاء ضمان جديد
 */
export const createGuarantee = async (data: CreateGuaranteeDto, file?: File) => {
  const formData = new FormData();
  formData.append('data', JSON.stringify(data));

  if (file) {
    formData.append('file', file);
  }

  const response = await apiService.post<{ data: Guarantee }>('/guarantees', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response;
};

/**
 * تحديث ضمان
 */
export const updateGuarantee = async (id: string, data: UpdateGuaranteeDto, file?: File) => {
  const formData = new FormData();
  formData.append('data', JSON.stringify(data));

  if (file) {
    formData.append('file', file);
  }

  const response = await apiService.put<{ data: Guarantee }>(`/guarantees/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response;
};

/**
 * حذف ضمان
 */
export const deleteGuarantee = async (id: string) => {
  const response = await apiService.delete<{ success: boolean }>(`/guarantees/${id}`);
  return response.success;
};

/**
 * الحصول على ضمان محدد
 */
export const getGuarantee = async (id: string) => {
  const response = await apiService.get<{ data: Guarantee }>(`/guarantees/${id}`);
  return response;
};

/**
 * الحصول على قائمة الضمانات
 */
export const getGuarantees = async (params: GuaranteeSearchParams = {}) => {
  const response = await apiService.get<PaginatedResponse<Guarantee>>('/guarantees', { params });
  return response;
};

/**
 * تحميل ملف PDF للضمان
 */
export const downloadGuaranteePdf = async (id: string) => {
  const response = await apiService.get<Blob>(`/guarantees/pdf/${id}`, {
    responseType: 'blob',
  });

  return response;
};

/**
 * تحديث حالة الاسترجاع للضمان
 */
export const updateGuaranteeReturnStatus = async (id: string, isReturned: boolean, returnDate?: string) => {
  const response = await apiService.put<{ data: Guarantee }>(`/guarantees/${id}/return-status`, {
    isReturned,
    returnDate,
  });

  return response;
};
