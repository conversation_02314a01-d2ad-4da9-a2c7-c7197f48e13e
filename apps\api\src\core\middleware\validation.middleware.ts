import { Request, Response, NextFunction } from 'express';
import { AnyZodObject, ZodError } from 'zod';

/**
 * وسيط التحقق من صحة الطلب
 * يتحقق من صحة بيانات الطلب باستخدام مخطط Zod
 * @param schema مخطط Zod للتحقق من صحة البيانات
 * @returns وسيط Express للتحقق من صحة البيانات
 */
export const validateRequest = (schema: AnyZodObject) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من صحة الطلب باستخدام المخطط
      await schema.parseAsync({
        body: req.body,
        query: req.query,
        params: req.params,
      });

      next();
    } catch (error) {
      // تمرير أخطاء Zod إلى وسيط معالجة الأخطاء
      if (error instanceof ZodError) {
        next(error);
      } else {
        next(error);
      }
    }
  };
};
