import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Grid,
} from '@mui/material';

interface Authorization {
  id: string;
  authorizationNumber: string;
  status: string;
  issuedDate: string;
  expiryDate?: string;
  notes?: string;
}

interface AuthorizationsListProps {
  authorizations: Authorization[];
  loading?: boolean;
  declarationId?: string;
}

export const AuthorizationsList: React.FC<AuthorizationsListProps> = ({
  authorizations,
  loading = false,
  declarationId,
}) => {
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography>جاري التحميل...</Typography>
      </Box>
    );
  }

  if (!authorizations.length) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography color="text.secondary">
          لا توجد تصاريح
        </Typography>
      </Box>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'success';
      case 'EXPIRED':
        return 'error';
      case 'PENDING':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Grid container spacing={2}>
      {authorizations.map((authorization) => (
        <Grid item xs={12} md={6} key={authorization.id}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                <Typography variant="h6" component="h3">
                  {authorization.authorizationNumber}
                </Typography>
                <Chip
                  label={authorization.status}
                  size="small"
                  color={getStatusColor(authorization.status) as any}
                />
              </Box>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                تاريخ الإصدار: {new Date(authorization.issuedDate).toLocaleDateString('ar-SA')}
              </Typography>

              {authorization.expiryDate && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  تاريخ الانتهاء: {new Date(authorization.expiryDate).toLocaleDateString('ar-SA')}
                </Typography>
              )}

              {authorization.notes && (
                <Typography variant="body2" color="text.secondary">
                  ملاحظات: {authorization.notes}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};
