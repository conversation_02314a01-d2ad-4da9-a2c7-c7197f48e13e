import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { saveUploadedPdf } from '../../../core/utils/pdf/pdfService.js';
import { Prisma } from '@prisma/client';

interface CreateReceiptInput {
  declarationId: string;
  receiptNumber: string;
  receiptDate: Date;
  receivedBy: string;
  notes?: string;
}

interface UpdateReceiptInput {
  declarationId?: string;
  receiptNumber?: string;
  receiptDate?: Date;
  receivedBy?: string;
  notes?: string;
}

interface ListReceiptsParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationId?: string;
  fromDate?: Date;
  toDate?: Date;
}

export const receiptService = {
  /**
   * إنشاء استلام جديد
   */
  createReceipt: async (
    data: CreateReceiptInput,
    _userId: string, // تم تغيير الاسم لتجنب تحذير عدم الاستخدام
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود البيان
      const declaration = await prisma.declaration.findUnique({
        where: { id: data.declarationId },
      });

      if (!declaration) {
        throw new HttpException(404, 'البيان غير موجود', 'Not Found');
      }

      // حفظ ملف PDF إذا تم تقديمه
      let pdfFile: string | undefined;
      if (file) {
        pdfFile = saveUploadedPdf(file, 'receipts', `receipt_${data.receiptNumber}`);
      }

      // إنشاء الاستلام
      const receipt = await prisma.receipt.create({
        data: {
          receiptNumber: data.receiptNumber,
          receiptDate: data.receiptDate,
          receivedBy: data.receivedBy,
          notes: data.notes,
          pdfFile,
          declarationId: data.declarationId,
        },
      });

      return receipt;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(400, 'رقم الاستلام موجود بالفعل', 'Bad Request');
        }
      }
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء إنشاء الاستلام', 'Internal Server Error');
    }
  },

  /**
   * تحديث استلام
   */
  updateReceipt: async (
    id: string,
    data: UpdateReceiptInput,
    _userId: string, // تم تغيير الاسم لتجنب تحذير عدم الاستخدام
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود الاستلام
      const existingReceipt = await prisma.receipt.findUnique({
        where: { id },
      });

      if (!existingReceipt) {
        throw new HttpException(404, 'الاستلام غير موجود', 'Not Found');
      }

      // تم إزالة التحقق من صلاحية المستخدم لأن نموذج الاستلام لا يحتوي على حقل userId

      // التحقق من وجود البيان إذا تم تغييره
      if (data.declarationId) {
        const declaration = await prisma.declaration.findUnique({
          where: { id: data.declarationId },
        });

        if (!declaration) {
          throw new HttpException(404, 'البيان غير موجود', 'Not Found');
        }
      }

      // حفظ ملف PDF إذا تم تقديمه
      let pdfFile = existingReceipt.pdfFile;
      if (file) {
        pdfFile = saveUploadedPdf(file, 'receipts', id);
      }

      // تحديث الاستلام
      const receipt = await prisma.receipt.update({
        where: { id },
        data: {
          receiptNumber: data.receiptNumber,
          receiptDate: data.receiptDate,
          receivedBy: data.receivedBy,
          notes: data.notes,
          pdfFile,
          declarationId: data.declarationId,
        },
      });

      return receipt;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء تحديث الاستلام', 'Internal Server Error');
    }
  },

  /**
   * الحصول على استلام محدد
   */
  getReceipt: async (id: string) => {
    const receipt = await prisma.receipt.findUnique({
      where: { id },
      include: {
        declaration: true,
      },
    });

    if (!receipt) {
      throw new HttpException(404, 'الاستلام غير موجود', 'Not Found');
    }

    return receipt;
  },

  /**
   * حذف استلام
   */
  deleteReceipt: async (id: string, _userId: string) => {
    // التحقق من وجود الاستلام
    const receipt = await prisma.receipt.findUnique({
      where: { id },
    });

    if (!receipt) {
      throw new HttpException(404, 'الاستلام غير موجود', 'Not Found');
    }

    // تم إزالة التحقق من صلاحية المستخدم لأن نموذج الاستلام لا يحتوي على حقل userId

    // حذف الاستلام
    await prisma.receipt.delete({
      where: { id },
    });

    return { success: true };
  },

  /**
   * الحصول على قائمة الاستلامات
   */
  listReceipts: async (params: ListReceiptsParams = {}) => {
    const {
      page = 1,
      limit = 10,
      sort = 'receiptDate',
      order = 'desc',
      search,
      declarationId,
      fromDate,
      toDate,
    } = params;

    // بناء شروط البحث
    const where: Prisma.ReceiptWhereInput = {};

    if (search) {
      where.OR = [
        { receivedBy: { contains: search } },
      ];
    }

    if (declarationId) {
      where.declarationId = declarationId;
    }

    if (fromDate && toDate) {
      where.receiptDate = {
        gte: fromDate,
        lte: toDate,
      };
    } else if (fromDate) {
      where.receiptDate = {
        gte: fromDate,
      };
    } else if (toDate) {
      where.receiptDate = {
        lte: toDate,
      };
    }

    // حساب إجمالي عدد الاستلامات
    const total = await prisma.receipt.count({ where });

    // الحصول على الاستلامات
    const receipts = await prisma.receipt.findMany({
      where,
      include: {
        declaration: true,
      },
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: receipts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};
