// أنواع البيان
export enum DeclarationType {
  IMPORT = 'IMPORT',
  EXPORT = 'EXPORT',
}

// واجهة السائق
export interface Driver {
  id: string;
  name: string;
  truckNumber?: string;
  trailerNumber?: string;
  phoneNumber?: string;
  declarationId: string;
  createdAt: string;
  updatedAt: string;
}

// واجهة العميل
export interface Client {
  id: string;
  clientNumber?: number;
  taxNumber: string;
  name: string;
  companyName?: string;
  phone?: string;
  createdAt: string;
  updatedAt: string;
}

// واجهة المستخدم
export interface User {
  id: string;
  username: string;
  name: string;
}

// واجهة البيان
export interface Declaration {
  id: string;
  declarationNumber: number;
  taxNumber: string;
  clientName?: string;
  companyName?: string;
  policyNumber?: number;
  invoiceNumber?: number;
  gatewayEntryNumber: number;
  declarationType: DeclarationType;
  declarationDate?: string;
  count?: number;
  weight?: string;
  goodsType?: string;
  itemsCount?: number;
  entryDate?: string;
  exitDate?: string;
  pdfFile?: string;
  clientId?: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  drivers: Driver[];
  client?: Client;
  createdBy?: User;
}

// واجهة نموذج البيان
export interface DeclarationFormValues {
  taxNumber: string;
  clientName: string;
  companyName: string;
  policyNumber: number | '';
  invoiceNumber: number | '';
  gatewayEntryNumber: number | '';
  declarationType: DeclarationType;
  declarationDate: Date | null;
  count: number | '';
  weight: string;
  goodsType: string;
  itemsCount: number | '';
  entryDate: Date | null;
  exitDate: Date | null;
  clientId: string | null;
  drivers: {
    id?: string;
    name: string;
    truckNumber: string;
    trailerNumber: string;
    phoneNumber: string;
  }[];
  file: File | null;
}
