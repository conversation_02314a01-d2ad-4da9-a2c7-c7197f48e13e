module.exports = {
  extends: [
    "./index.js",
    "plugin:node/recommended"
  ],
  plugins: ["node"],
  rules: {
    // Node specific rules
    "node/no-missing-import": "error",
    "node/no-missing-require": "error",
    "node/no-unpublished-import": "error",
    "node/no-unpublished-require": "error",
    "node/no-unsupported-features/es-syntax": ["error", {
      "ignores": ["modules"]
    }],
    "node/no-extraneous-import": "error",
    "node/no-extraneous-require": "error",
    "node/no-process-exit": "error",
    "node/global-require": "error",
    "node/no-sync": "error",
    "node/callback-return": "error",
    "node/handle-callback-err": "error",
    "node/no-new-require": "error",
    "node/no-path-concat": "error",
    "node/prefer-global/buffer": ["error", "always"],
    "node/prefer-global/console": ["error", "always"],
    "node/prefer-global/process": ["error", "always"],
    "node/prefer-global/url-search-params": ["error", "always"],
    "node/prefer-global/url": ["error", "always"],
    "node/prefer-promises/dns": "error",
    "node/prefer-promises/fs": "error"
  }
};
