import axios from 'axios';
import { Document, DocumentType } from '../types/document.types';

// واجهة طلب إنشاء المستند
export interface CreateDocumentRequest {
  documentNumber?: number;
  documentType?: DocumentType;
  documentDate?: string;
  documentValue?: number;
  title: string;
  description?: string;
  expiryDate?: string;
  issuedBy?: string;
}

// واجهة طلب تحديث المستند
export interface UpdateDocumentRequest {
  documentNumber?: number;
  documentType?: DocumentType;
  documentDate?: string;
  documentValue?: number;
  title?: string;
  description?: string;
  expiryDate?: string;
  issuedBy?: string;
}

// واجهة معلمات البحث عن المستندات
export interface DocumentSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  documentType?: DocumentType;
  fromDate?: Date;
  toDate?: Date;
  isExpired?: boolean;
}

// واجهة استجابة المستند
export interface DocumentResponse {
  data: Document;
  message: string;
}

// واجهة استجابة قائمة المستندات
export interface DocumentsResponse {
  data: Document[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// الحصول على قائمة المستندات
export const getDocuments = async (params: DocumentSearchParams = {}): Promise<DocumentsResponse> => {
  try {
    const { data } = await axios.get<DocumentsResponse>('/api/documents', { params });
    return data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء الحصول على قائمة المستندات');
    }
    throw new Error('حدث خطأ أثناء الحصول على قائمة المستندات');
  }
};

// الحصول على مستند محدد
export const getDocument = async (id: string): Promise<Document> => {
  try {
    const { data } = await axios.get<DocumentResponse>(`/api/documents/${id}`);
    return data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء الحصول على المستند');
    }
    throw new Error('حدث خطأ أثناء الحصول على المستند');
  }
};

// إنشاء مستند جديد
export const createDocument = async (
  documentData: CreateDocumentRequest,
  file?: File
): Promise<Document> => {
  try {
    const formData = new FormData();
    formData.append('data', JSON.stringify(documentData));

    if (file) {
      formData.append('file', file);
    }

    const { data } = await axios.post<DocumentResponse>('/api/documents', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء إنشاء المستند');
    }
    throw new Error('حدث خطأ أثناء إنشاء المستند');
  }
};

// تحديث مستند
export const updateDocument = async (
  id: string,
  documentData: UpdateDocumentRequest,
  file?: File
): Promise<Document> => {
  try {
    const formData = new FormData();
    formData.append('data', JSON.stringify(documentData));

    if (file) {
      formData.append('file', file);
    }

    const { data } = await axios.put<DocumentResponse>(`/api/documents/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء تحديث المستند');
    }
    throw new Error('حدث خطأ أثناء تحديث المستند');
  }
};

// حذف مستند
export const deleteDocument = async (id: string): Promise<void> => {
  try {
    await axios.delete(`/api/documents/${id}`);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء حذف المستند');
    }
    throw new Error('حدث خطأ أثناء حذف المستند');
  }
};

// تحميل ملف PDF للمستند
export const downloadDocumentPdf = async (id: string): Promise<void> => {
  try {
    const response = await axios.get(`/api/documents/${id}/pdf`, {
      responseType: 'blob',
    });
    
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `document-${id}.pdf`);
    document.body.appendChild(link);
    link.click();
    link.remove();
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء تحميل ملف PDF');
    }
    throw new Error('حدث خطأ أثناء تحميل ملف PDF');
  }
};
