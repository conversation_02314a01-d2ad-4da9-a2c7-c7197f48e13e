import React from 'react';
import { Box, Paper } from '@mui/material';

interface CodeEditorProps {
  value: string;
  onChange?: (value: string) => void;
  language?: string;
  height?: string | number;
  readOnly?: boolean;
}

/**
 * مكون محرر الكود
 * يعرض محرر كود بسيط
 * ملاحظة: هذا مجرد مكون وهمي، يجب استبداله بمكتبة محرر كود حقيقية مثل Monaco Editor أو CodeMirror
 */
export const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChange,
  language = 'javascript',
  height = 300,
  readOnly = false,
}) => {
  return (
    <Paper
      sx={{
        height,
        overflow: 'auto',
        fontFamily: 'monospace',
        fontSize: '0.9rem',
        p: 2,
        backgroundColor: '#f5f5f5',
      }}
    >
      <Box
        component="pre"
        sx={{
          margin: 0,
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-all',
        }}
      >
        {value}
      </Box>
    </Paper>
  );
};
