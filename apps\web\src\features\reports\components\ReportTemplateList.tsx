import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FileCopy as CopyIcon,
  Search as SearchIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
} from '@mui/icons-material';
import { useGetReportTemplatesQuery, useDeleteReportTemplateMutation, useUpdateReportTemplateMutation } from '../api/report-template.api';
import { ListReportTemplatesParams, ReportTemplate } from '../types/report-template.types';
import { formatDate } from '@/core/utils/date';
import { useConfirm } from '@/core/hooks/useConfirm';

interface ReportTemplateListProps {
  reportType?: string;
  onAddTemplate: () => void;
  onEditTemplate: (template: ReportTemplate) => void;
  onCopyTemplate: (template: ReportTemplate) => void;
}

/**
 * مكون قائمة قوالب التقارير
 */
const ReportTemplateList = ({
  reportType,
  onAddTemplate,
  onEditTemplate,
  onCopyTemplate,
}: ReportTemplateListProps) => {
  const { t } = useTranslation();
  const { confirm } = useConfirm();

  // حالة البحث والصفحة
  const [searchParams, setSearchParams] = useState<ListReportTemplatesParams>({
    page: 1,
    limit: 10,
    sort: 'createdAt',
    order: 'desc',
    search: '',
    reportType,
  });

  // استخدام خطافات API
  const { data, isLoading, isFetching } = useGetReportTemplatesQuery(searchParams);
  const deleteTemplateMutation = useDeleteReportTemplateMutation();
  const updateTemplateMutation = useUpdateReportTemplateMutation();

  // التعامل مع تغيير الصفحة
  const handleChangePage = (event: unknown, newPage: number) => {
    setSearchParams((prev) => ({ ...prev, page: newPage + 1 }));
  };

  // التعامل مع تغيير عدد العناصر في الصفحة
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchParams((prev) => ({
      ...prev,
      limit: parseInt(event.target.value, 10),
      page: 1,
    }));
  };

  // التعامل مع تغيير البحث
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const search = event.target.value;
    setSearchParams((prev) => ({ ...prev, search, page: 1 }));
  };

  // التعامل مع حذف قالب
  const handleDeleteTemplate = async (template: ReportTemplate) => {
    const confirmed = await confirm({
      title: t('reports.templates.deleteConfirmTitle'),
      message: t('reports.templates.deleteConfirmMessage', { name: template.name }),
      confirmText: t('common.delete'),
      cancelText: t('common.cancel'),
      confirmColor: 'error',
    });

    if (confirmed) {
      try {
        await deleteTemplateMutation.mutateAsync(template.id);
      } catch (error) {
        console.error('Error deleting template:', error);
      }
    }
  };

  // التعامل مع تعيين القالب الافتراضي
  const handleSetDefault = async (template: ReportTemplate) => {
    if (template.isDefault) return;

    try {
      await updateTemplateMutation.mutateAsync({
        id: template.id,
        data: { isDefault: true },
      });
    } catch (error) {
      console.error('Error setting default template:', error);
    }
  };

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h6">{t('reports.templates.list')}</Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={onAddTemplate}
          >
            {t('reports.templates.add')}
          </Button>
        </Box>

        <Box mb={3}>
          <TextField
            fullWidth
            placeholder={t('common.search')}
            value={searchParams.search || ''}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />,
            }}
            variant="outlined"
            size="small"
          />
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('reports.templates.name')}</TableCell>
                <TableCell>{t('reports.templates.reportType')}</TableCell>
                <TableCell>{t('reports.templates.createdAt')}</TableCell>
                <TableCell>{t('reports.templates.createdBy')}</TableCell>
                <TableCell>{t('reports.templates.isDefault')}</TableCell>
                <TableCell align="center">{t('common.actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <CircularProgress size={24} />
                  </TableCell>
                </TableRow>
              ) : data?.data.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    {t('reports.templates.noTemplates')}
                  </TableCell>
                </TableRow>
              ) : (
                data?.data.map((template: any) => (
                  <TableRow key={template.id}>
                    <TableCell>{template.name}</TableCell>
                    <TableCell>{t(`reports.types.${template.reportType}`)}</TableCell>
                    <TableCell>{formatDate(template.createdAt)}</TableCell>
                    <TableCell>{template.createdBy.name}</TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        color={template.isDefault ? 'primary' : 'default'}
                        onClick={() => handleSetDefault(template)}
                        disabled={template.isDefault || updateTemplateMutation.isPending}
                      >
                        {template.isDefault ? <StarIcon /> : <StarBorderIcon />}
                      </IconButton>
                    </TableCell>
                    <TableCell align="center">
                      <Box display="flex" justifyContent="center">
                        <Tooltip title={t('common.edit')}>
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => onEditTemplate(template)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.copy')}>
                          <IconButton
                            size="small"
                            color="info"
                            onClick={() => onCopyTemplate(template)}
                          >
                            <CopyIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.delete')}>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteTemplate(template)}
                            disabled={deleteTemplateMutation.isPending}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {data && (
          <TablePagination
            component="div"
            count={data.meta.total}
            page={(data.meta.page - 1) || 0}
            rowsPerPage={data.meta.limit || 10}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            labelRowsPerPage={t('common.rowsPerPage')}
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} ${t('common.of')} ${count}`
            }
            rowsPerPageOptions={[5, 10, 25, 50]}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default ReportTemplateList;
