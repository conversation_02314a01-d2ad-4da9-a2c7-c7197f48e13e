import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { Button, TextField, Typography, Paper, Box, Container, Alert } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { AuthLayout } from '../layouts/AuthLayout';
import { useAuth } from '../hooks/useAuth';

// تعريف مخطط التحقق من صحة البيانات
const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, 'كلمة المرور يجب أن تكون على الأقل 8 أحرف')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
        'كلمة المرور يجب أن تحتوي على حرف كبير، حرف صغير، رقم، ورمز خاص'
      ),
    confirmPassword: z.string().min(1, 'تأكيد كلمة المرور مطلوب'),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'كلمات المرور غير متطابقة',
    path: ['confirmPassword'],
  });

// نوع بيانات النموذج
type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

/**
 * صفحة إعادة تعيين كلمة المرور
 */
const ResetPasswordPage: React.FC = () => {
  const { t } = useTranslation();
  const { resetPassword } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // إعداد نموذج إعادة تعيين كلمة المرور
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  // التحقق من وجود التوكن
  if (!token) {
    return (
      <AuthLayout>
        <Container maxWidth="sm">
          <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
            <Typography variant="h5" component="h1" align="center" gutterBottom>
              {t('auth.resetPassword.invalidToken')}
            </Typography>
            <Alert severity="error" sx={{ mt: 2 }}>
              {t('auth.resetPassword.tokenMissing')}
            </Alert>
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Link to="/forgot-password">
                <Button variant="text" color="primary">
                  {t('auth.resetPassword.requestNewToken')}
                </Button>
              </Link>
            </Box>
          </Paper>
        </Container>
      </AuthLayout>
    );
  }

  // معالجة تقديم النموذج
  const onSubmit = async (data: ResetPasswordFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // استدعاء خدمة إعادة تعيين كلمة المرور
      await resetPassword(token, data.password);

      // عرض رسالة النجاح
      setSuccess(true);

      // الانتقال إلى صفحة تسجيل الدخول بعد 3 ثوانٍ
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err) {
      // عرض رسالة الخطأ
      setError(err instanceof Error ? err.message : 'حدث خطأ أثناء إعادة تعيين كلمة المرور');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AuthLayout>
      <Container maxWidth="sm">
        <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
          <Typography variant="h5" component="h1" align="center" gutterBottom>
            {t('auth.resetPassword.title')}
          </Typography>

          {success ? (
            <Box sx={{ mt: 2 }}>
              <Alert severity="success">
                {t('auth.resetPassword.successMessage')}
              </Alert>
              <Typography variant="body2" sx={{ mt: 2, textAlign: 'center' }}>
                {t('auth.resetPassword.redirecting')}
              </Typography>
            </Box>
          ) : (
            <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              <Typography variant="body2" sx={{ mb: 3 }}>
                {t('auth.resetPassword.instructions')}
              </Typography>

              <TextField
                margin="normal"
                required
                fullWidth
                id="password"
                label={t('auth.password')}
                type="password"
                {...register('password')}
                error={!!errors.password}
                helperText={errors.password?.message}
                autoComplete="new-password"
                disabled={isSubmitting}
              />

              <TextField
                margin="normal"
                required
                fullWidth
                id="confirmPassword"
                label={t('auth.confirmPassword')}
                type="password"
                {...register('confirmPassword')}
                error={!!errors.confirmPassword}
                helperText={errors.confirmPassword?.message}
                autoComplete="new-password"
                disabled={isSubmitting}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                color="primary"
                sx={{ mt: 3, mb: 2 }}
                disabled={isSubmitting}
              >
                {isSubmitting
                  ? t('auth.resetPassword.submitting')
                  : t('auth.resetPassword.submit')}
              </Button>

              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Link to="/login">
                  <Button variant="text" color="primary">
                    {t('auth.resetPassword.backToLogin')}
                  </Button>
                </Link>
              </Box>
            </Box>
          )}
        </Paper>
      </Container>
    </AuthLayout>
  );
};

export default ResetPasswordPage;
