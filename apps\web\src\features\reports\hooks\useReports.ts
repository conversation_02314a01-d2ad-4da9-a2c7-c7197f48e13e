import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { ReportTemplate } from '../types/report.types';

/**
 * خطاف للحصول على قائمة قوالب التقارير
 */
export const useReportTemplates = (params?: any) => {
  return useQuery({
    queryKey: ['reportTemplates', params],
    queryFn: async () => {
      const response = await api.get('/reports/templates', { params });
      return response.data;
    },
  });
};

/**
 * خطاف للحصول على قالب تقرير محدد
 */
export const useReportTemplate = (id: string, options?: any) => {
  return useQuery({
    queryKey: ['reportTemplate', id],
    queryFn: async () => {
      const response = await api.get(`/reports/templates/${id}`);
      return response.data;
    },
    ...options,
  });
};

/**
 * خطاف لإنشاء قالب تقرير جديد
 */
export const useCreateReportTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Partial<ReportTemplate>) => {
      const response = await api.post('/reports/templates', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reportTemplates'] });
    },
  });
};

/**
 * خطاف لتحديث قالب تقرير
 */
export const useUpdateReportTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<ReportTemplate> }) => {
      const response = await api.put(`/reports/templates/${id}`, data);
      return response.data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['reportTemplates'] });
      queryClient.invalidateQueries({ queryKey: ['reportTemplate', variables.id] });
    },
  });
};

/**
 * خطاف لحذف قالب تقرير
 */
export const useDeleteReportTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await api.delete(`/reports/templates/${id}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reportTemplates'] });
    },
  });
};

/**
 * خطاف لإنشاء تقرير
 */
export const useGenerateReport = () => {
  return useMutation({
    mutationFn: async (params: any) => {
      const response = await api.post('/reports/generate', params, {
        responseType: 'blob',
      });
      return response.data;
    },
  });
};
