import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { createMockHandlers, server, rest, HttpResponse } from '../../../test/integration-setup';
import { configureStore } from '@reduxjs/toolkit';
import { authReducer } from '../../auth/store/authSlice';
import ItemMovementList from '../components/ItemMovementList';

// إنشاء متجر وهمي
const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer as any,
    },
    preloadedState: {
      auth: {
        isAuthenticated: true,
        user: {
          id: 'user-1',
          username: 'test_user',
          name: 'مستخدم اختبار',
          role: 'ADMIN',
        },
        token: 'mock-token',
        refreshToken: 'mock-refresh-token',
        loading: false,
        error: null,
      },
    },
  });
};

// إنشاء عميل استعلام وهمي
const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  });
};

// مكون الاختبار مع المزودين
const TestComponent = () => {
  const store = createTestStore();
  const queryClient = createTestQueryClient();

  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <ItemMovementList
            movements={[]}
            loading={false}
          />
        </BrowserRouter>
      </QueryClientProvider>
    </Provider>
  );
};

describe('ItemMovementList Integration Tests', () => {
  // إعداد المعالجات الوهمية
  const mockHandlers = createMockHandlers();

  beforeEach(() => {
    // إعادة تعيين المعالجات الوهمية قبل كل اختبار
    server.resetHandlers();
  });

  it('يجب أن يعرض قائمة حركات الأصناف بنجاح', async () => {
    // تعريف معالج وهمي لجلب حركات الأصناف
    server.use(
      rest.get('http://localhost:3001/api/item-movements', () => {
        return HttpResponse.json({
            success: true,
            data: [
              {
                id: 'item-movement-1',
                movementNumber: 1001,
                itemName: 'هاتف ذكي',
                quantity: 10,
                unitPrice: 1000,
                totalPrice: 10000,
                movementType: 'IN',
                movementDate: '2023-01-15T00:00:00.000Z',
                createdAt: '2023-01-15T00:00:00.000Z',
                updatedAt: '2023-01-15T00:00:00.000Z',
              },
              {
                id: 'item-movement-2',
                movementNumber: 1002,
                itemName: 'حاسوب محمول',
                quantity: 5,
                unitPrice: 2000,
                totalPrice: 10000,
                movementType: 'OUT',
                movementDate: '2023-02-15T00:00:00.000Z',
                createdAt: '2023-02-15T00:00:00.000Z',
                updatedAt: '2023-02-15T00:00:00.000Z',
              },
            ],
            pagination: {
              page: 1,
              limit: 10,
              totalItems: 2,
              totalPages: 1,
            },
          });
      })
    );

    render(<TestComponent />);

    // التحقق من عرض حركات الأصناف
    await waitFor(() => {
      expect(screen.getByText('1001')).toBeInTheDocument();
      expect(screen.getByText('1002')).toBeInTheDocument();
      expect(screen.getByText('هاتف ذكي')).toBeInTheDocument();
      expect(screen.getByText('حاسوب محمول')).toBeInTheDocument();
    });
  });

  it('يجب أن يعرض رسالة عند عدم وجود حركات أصناف', async () => {
    // تعريف معالج وهمي لجلب حركات الأصناف فارغة
    server.use(
      rest.get('http://localhost:3001/api/item-movements', () => {
        return HttpResponse.json({
            success: true,
            data: [],
            pagination: {
              page: 1,
              limit: 10,
              totalItems: 0,
              totalPages: 0,
            },
          });
      })
    );

    render(<TestComponent />);

    // التحقق من عرض رسالة عدم وجود حركات أصناف
    await waitFor(() => {
      expect(screen.getByText(/لا توجد حركات أصناف/i)).toBeInTheDocument();
    });
  });

  it('يجب أن يعرض رسالة خطأ عند فشل جلب حركات الأصناف', async () => {
    // تعريف معالج وهمي لفشل جلب حركات الأصناف
    server.use(
      rest.get('http://localhost:3001/api/item-movements', () => {
        return new HttpResponse(JSON.stringify({
            success: false,
            error: 'حدث خطأ أثناء جلب حركات الأصناف',
          }), { status: 500 });
      })
    );

    render(<TestComponent />);

    // التحقق من عرض رسالة الخطأ
    await waitFor(() => {
      expect(screen.getByText(/حدث خطأ/i)).toBeInTheDocument();
    });
  });

  it('يجب أن يقوم بتصفية حركات الأصناف حسب نوع الحركة', async () => {
    // تعريف معالج وهمي لجلب حركات الأصناف
    server.use(
      rest.get('http://localhost:3001/api/item-movements', ({ request }) => {
        const url = new URL(request.url);
        const movementType = url.searchParams.get('movementType');

        if (movementType === 'IN') {
          return HttpResponse.json({
              success: true,
              data: [
                {
                  id: 'item-movement-1',
                  movementNumber: 1001,
                  itemName: 'هاتف ذكي',
                  quantity: 10,
                  unitPrice: 1000,
                  totalPrice: 10000,
                  movementType: 'IN',
                  movementDate: '2023-01-15T00:00:00.000Z',
                  createdAt: '2023-01-15T00:00:00.000Z',
                  updatedAt: '2023-01-15T00:00:00.000Z',
                },
              ],
              pagination: {
                page: 1,
                limit: 10,
                totalItems: 1,
                totalPages: 1,
              },
            });
        } else {
          return HttpResponse.json({
              success: true,
              data: [
                {
                  id: 'item-movement-1',
                  movementNumber: 1001,
                  itemName: 'هاتف ذكي',
                  quantity: 10,
                  unitPrice: 1000,
                  totalPrice: 10000,
                  movementType: 'IN',
                  movementDate: '2023-01-15T00:00:00.000Z',
                  createdAt: '2023-01-15T00:00:00.000Z',
                  updatedAt: '2023-01-15T00:00:00.000Z',
                },
                {
                  id: 'item-movement-2',
                  movementNumber: 1002,
                  itemName: 'حاسوب محمول',
                  quantity: 5,
                  unitPrice: 2000,
                  totalPrice: 10000,
                  movementType: 'OUT',
                  movementDate: '2023-02-15T00:00:00.000Z',
                  createdAt: '2023-02-15T00:00:00.000Z',
                  updatedAt: '2023-02-15T00:00:00.000Z',
                },
              ],
              pagination: {
                page: 1,
                limit: 10,
                totalItems: 2,
                totalPages: 1,
              },
            });
        }
      })
    );

    render(<TestComponent />);

    // التحقق من عرض جميع حركات الأصناف في البداية
    await waitFor(() => {
      expect(screen.getByText('1001')).toBeInTheDocument();
      expect(screen.getByText('1002')).toBeInTheDocument();
    });

    // اختيار تصفية حسب نوع الحركة (دخول)
    const filterSelect = screen.getByLabelText(/نوع الحركة/i);
    await userEvent.selectOptions(filterSelect, 'IN');

    // التحقق من عرض حركات الأصناف المصفاة
    await waitFor(() => {
      expect(screen.getByText('1001')).toBeInTheDocument();
      expect(screen.queryByText('1002')).not.toBeInTheDocument();
    });
  });
});
