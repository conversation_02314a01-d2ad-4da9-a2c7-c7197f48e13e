/**
 * أنواع API المشتركة
 */

/**
 * واجهة استجابة مُصفحة
 * تستخدم للاستجابات التي تحتوي على بيانات مُصفحة
 */
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

/**
 * واجهة استجابة بسيطة
 * تستخدم للاستجابات التي تحتوي على بيانات فردية
 */
export interface SingleResponse<T> {
  data: T;
}

/**
 * واجهة استجابة النجاح
 * تستخدم للاستجابات التي تشير إلى نجاح العملية
 */
export interface SuccessResponse {
  success: boolean;
  message?: string;
}

/**
 * واجهة استجابة الخطأ
 * تستخدم للاستجابات التي تشير إلى فشل العملية
 */
export interface ErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}

/**
 * واجهة خيارات الاستعلام
 * تستخدم لتمرير خيارات الاستعلام إلى طلبات API
 */
export interface QueryOptions {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  filter?: Record<string, any>;
}

/**
 * واجهة خيارات التصفية
 * تستخدم لتمرير خيارات التصفية إلى طلبات API
 */
export interface FilterOptions {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'like';
  value: any;
}

/**
 * واجهة خيارات الترتيب
 * تستخدم لتمرير خيارات الترتيب إلى طلبات API
 */
export interface SortOptions {
  field: string;
  order: 'asc' | 'desc';
}
