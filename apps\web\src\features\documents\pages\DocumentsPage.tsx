import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  MenuItem,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Dayjs } from 'dayjs';
import { useDocuments } from '../hooks/useDocuments';
import { useDeleteDocument } from '../hooks/useDeleteDocument';
import { useDownloadDocument } from '../hooks/useDownloadDocument';
import { PageHeader } from '@components/PageHeader';
import { DataTable } from '@components/DataTable';
import { ConfirmDialog } from '@components/ConfirmDialog';
import { formatDate } from '@lib/utils/date-utils';
import { DocumentType } from '../types/document.types';

/**
 * نموذج البحث
 */
interface SearchFormData {
  search: string;
  documentType: string;
  fromDate: Dayjs | null;
  toDate: Dayjs | null;
}

/**
 * صفحة المستندات
 */
const DocumentsPage: React.FC = () => {
  const { t } = useTranslation();
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);

  // استخدام خطافات البيانات
  const {
    data: documentsData,
    isLoading,
    refetch,
  } = useDocuments({
    page: page + 1,
    limit: pageSize,
    ...filters,
  });

  const deleteDocumentMutation = useDeleteDocument();
  const downloadDocumentMutation = useDownloadDocument();

  // إعداد نموذج البحث
  const { control, handleSubmit, reset } = useForm<SearchFormData>({
    defaultValues: {
      search: '',
      documentType: '',
      fromDate: null,
      toDate: null,
    },
  });

  // معالجة تقديم نموذج البحث
  const onSubmit = (data: SearchFormData) => {
    const newFilters: Record<string, any> = {};

    if (data.search) {
      newFilters.search = data.search;
    }

    if (data.documentType) {
      newFilters.documentType = data.documentType;
    }

    if (data.fromDate) {
      newFilters.fromDate = data.fromDate.format('YYYY-MM-DD');
    }

    if (data.toDate) {
      newFilters.toDate = data.toDate.format('YYYY-MM-DD');
    }

    setFilters(newFilters);
    setPage(0);
  };

  // إعادة تعيين البحث
  const handleReset = () => {
    reset();
    setFilters({});
    setPage(0);
  };

  // التعامل مع حذف مستند
  const handleDelete = async () => {
    if (!documentToDelete) return;

    try {
      await deleteDocumentMutation.mutateAsync(documentToDelete);
      setDeleteDialogOpen(false);
      refetch();
    } catch (error) {
      console.error('Error deleting document:', error);
    }
  };

  // التعامل مع تحميل مستند
  const handleDownload = async (id: string) => {
    try {
      await downloadDocumentMutation.mutateAsync(id);
    } catch (error) {
      console.error('Error downloading document:', error);
    }
  };

  // تعريف أعمدة الجدول
  const columns = [
    {
      id: 'documentNumber',
      label: t('documents.documentNumber'),
      field: 'documentNumber',
      flex: 1,
    },
    {
      id: 'title',
      label: t('documents.title'),
      field: 'title',
      flex: 1.5,
    },
    {
      id: 'documentType',
      label: t('documents.documentType'),
      field: 'documentType',
      flex: 1,
      renderCell: (params: any) => t(`documents.types.${params.row.documentType}`),
    },
    {
      id: 'documentDate',
      label: t('documents.documentDate'),
      field: 'documentDate',
      flex: 1,
      renderCell: (params: any) => formatDate(params.row.documentDate),
    },
    {
      id: 'actions',
      label: t('common.actions'),
      field: 'actions',
      flex: 1,
      sortable: false,
      renderCell: (params: any) => (
        <Box sx={{ display: 'flex' }}>
          <Tooltip title={t('common.view')}>
            <IconButton
              component={Link}
              to={`/documents/${params.row.id}`}
              size="small"
            >
              <VisibilityIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('common.edit')}>
            <IconButton
              component={Link}
              to={`/documents/edit/${params.row.id}`}
              size="small"
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('common.download')}>
            <IconButton
              size="small"
              onClick={() => handleDownload(params.row.id)}
              disabled={downloadDocumentMutation.isPending}
            >
              <DownloadIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('common.delete')}>
            <IconButton
              size="small"
              onClick={() => {
                setDocumentToDelete(params.row.id);
                setDeleteDialogOpen(true);
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  return (
    <Box>
      <PageHeader
        title={t('documents.title')}
        subtitle={t('documents.subtitle')}
        actions={
          <Button
            component={Link}
            to="/documents/new"
            startIcon={<AddIcon />}
            variant="contained"
          >
            {t('documents.addNew')}
          </Button>
        }
      />

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <Controller
                  name="search"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('documents.search.placeholder')}
                      fullWidth
                      variant="outlined"
                      size="small"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Controller
                  name="documentType"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      select
                      label={t('documents.documentType')}
                      fullWidth
                      variant="outlined"
                      size="small"
                    >
                      <MenuItem value="">
                        {t('common.all')}
                      </MenuItem>
                      <MenuItem value={DocumentType.INVOICE}>
                        {t('documents.types.INVOICE')}
                      </MenuItem>
                      <MenuItem value={DocumentType.CONTRACT}>
                        {t('documents.types.CONTRACT')}
                      </MenuItem>
                      <MenuItem value={DocumentType.CERTIFICATE}>
                        {t('documents.types.CERTIFICATE')}
                      </MenuItem>
                      <MenuItem value={DocumentType.REPORT}>
                        {t('documents.types.REPORT')}
                      </MenuItem>
                      <MenuItem value={DocumentType.OTHER}>
                        {t('documents.types.OTHER')}
                      </MenuItem>
                    </TextField>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={2}>
                <Controller
                  name="fromDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label={t('common.fromDate')}
                      value={field.value}
                      onChange={field.onChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          variant: 'outlined',
                          size: 'small',
                        },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={2}>
                <Controller
                  name="toDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label={t('common.toDate')}
                      value={field.value}
                      onChange={field.onChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          variant: 'outlined',
                          size: 'small',
                        },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={2} sx={{ display: 'flex', gap: 1 }}>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<SearchIcon />}
                  fullWidth
                >
                  {t('common.search')}
                </Button>
                <Button
                  type="button"
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={handleReset}
                >
                  {t('common.reset')}
                </Button>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>

      <DataTable
        rows={documentsData?.data || []}
        columns={columns}
        rowCount={documentsData?.pagination?.total || 0}
        page={page}
        pageSize={pageSize}
        loading={isLoading}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        emptyContent={
          <Typography align="center" color="text.secondary" sx={{ py: 3 }}>
            {t('documents.noDocuments')}
          </Typography>
        }
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        title={t('documents.delete.confirmTitle')}
        message={t('documents.delete.confirmMessage')}
        onConfirm={handleDelete}
        onClose={() => setDeleteDialogOpen(false)}
        confirmLoading={deleteDocumentMutation.isPending}
      />
    </Box>
  );
};

export default DocumentsPage;
