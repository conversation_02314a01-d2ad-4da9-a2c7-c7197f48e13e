# اختبارات الأداء لمشروع النور للأرشفة

هذا المجلد يحتوي على أدوات لاختبار أداء مشروع النور للأرشفة.

## المتطلبات

- Node.js (الإصدار 16 أو أحدث)
- npm أو pnpm

## التثبيت

قم بتثبيت التبعيات باستخدام الأمر التالي:

```bash
# باستخدام npm
npm install

# أو باستخدام pnpm
pnpm install
```

## اختبارات أداء واجهة برمجة التطبيقات (API)

### الاستخدام الأساسي

يمكنك تشغيل اختبارات الأداء لنقطة نهاية محددة في واجهة برمجة التطبيقات باستخدام الأمر التالي:

```bash
node api-performance.js [endpoint] [--auth]
```

حيث:
- `[endpoint]`: مسار نقطة النهاية في واجهة برمجة التطبيقات (مثل `/api/declarations`)
- `[--auth]`: خيار اختياري لإضافة رمز المصادقة إلى الطلبات

### أمثلة

```bash
# اختبار نقطة نهاية البيانات بدون مصادقة
node api-performance.js /api/declarations

# اختبار نقطة نهاية التصاريح مع المصادقة
node api-performance.js /api/permits --auth
```

### باستخدام النصوص المعرفة مسبقًا

يمكنك أيضًا استخدام النصوص المعرفة مسبقًا في ملف `package.json`:

```bash
# اختبار نقطة نهاية البيانات
npm run test:declarations

# اختبار نقطة نهاية التصاريح
npm run test:permits

# اختبار نقطة نهاية الضمانات
npm run test:guarantees

# اختبار نقطة نهاية الاستلامات
npm run test:receipts

# اختبار جميع نقاط النهاية في واجهة برمجة التطبيقات
npm run test:all:api
```

## اختبارات أداء واجهة المستخدم (UI)

### الاستخدام الأساسي

يمكنك تشغيل اختبارات الأداء لصفحة محددة في واجهة المستخدم باستخدام الأمر التالي:

```bash
node ui-performance.js [page] [--auth]
```

حيث:
- `[page]`: مسار الصفحة في واجهة المستخدم (مثل `/declarations`)
- `[--auth]`: خيار اختياري لتسجيل الدخول قبل زيارة الصفحة

### أمثلة

```bash
# اختبار صفحة البيانات بدون تسجيل الدخول
node ui-performance.js /declarations

# اختبار صفحة التصاريح مع تسجيل الدخول
node ui-performance.js /permits --auth
```

### باستخدام النصوص المعرفة مسبقًا

يمكنك أيضًا استخدام النصوص المعرفة مسبقًا في ملف `package.json`:

```bash
# اختبار صفحة البيانات
npm run test:ui:declarations

# اختبار صفحة التصاريح
npm run test:ui:permits

# اختبار صفحة الضمانات
npm run test:ui:guarantees

# اختبار صفحة الاستلامات
npm run test:ui:receipts

# اختبار جميع الصفحات في واجهة المستخدم
npm run test:all:ui
```

## التقارير

### تقارير واجهة برمجة التطبيقات

يتم حفظ تقارير أداء واجهة برمجة التطبيقات في مجلد `reports` بتنسيق JSON. يحتوي كل تقرير على معلومات مفصلة عن الاختبار، بما في ذلك:

- عدد الطلبات في الثانية
- متوسط زمن الاستجابة
- معدل نقل البيانات
- الأخطاء

### تقارير واجهة المستخدم

يتم حفظ تقارير أداء واجهة المستخدم في مجلد `reports` بتنسيق JSON. يحتوي كل تقرير على معلومات مفصلة عن الاختبار، بما في ذلك:

- وقت التحميل الكامل للصفحة
- وقت DOM Content Loaded
- حجم الصفحة
- استخدام ذاكرة JavaScript
- لقطة شاشة للصفحة

## اختبارات تجربة المستخدم (UX)

### الاستخدام الأساسي

يمكنك تشغيل اختبارات تجربة المستخدم باستخدام الأمر التالي:

```bash
node ux-tests.js [test] [--headless]
```

حيث:
- `[test]`: اسم الاختبار (مثل `login` أو `declaration-flow`)
- `[--headless]`: خيار اختياري لتشغيل المتصفح بدون واجهة رسومية

### أمثلة

```bash
# اختبار تسجيل الدخول مع عرض المتصفح
node ux-tests.js login

# اختبار سير عمل البيانات بدون عرض المتصفح
node ux-tests.js declaration-flow --headless
```

### باستخدام النصوص المعرفة مسبقًا

يمكنك أيضًا استخدام النصوص المعرفة مسبقًا في ملف `package.json`:

```bash
# اختبار تسجيل الدخول مع عرض المتصفح
npm run test:ux:login

# اختبار سير عمل البيانات مع عرض المتصفح
npm run test:ux:declaration

# اختبار تسجيل الدخول بدون عرض المتصفح
npm run test:ux:login:headless

# اختبار سير عمل البيانات بدون عرض المتصفح
npm run test:ux:declaration:headless

# تشغيل جميع اختبارات تجربة المستخدم بدون عرض المتصفح
npm run test:all:ux
```

## تشغيل جميع الاختبارات

يمكنك تشغيل جميع اختبارات الأداء وتجربة المستخدم باستخدام الأمر التالي:

```bash
npm run test:all
```

## تخصيص الإعدادات

### إعدادات واجهة برمجة التطبيقات

يمكنك تخصيص إعدادات اختبار واجهة برمجة التطبيقات بتعديل المتغيرات في ملف `api-performance.js`:

- `connections`: عدد الاتصالات المتزامنة
- `duration`: مدة الاختبار بالثواني
- `url`: عنوان URL الأساسي لواجهة برمجة التطبيقات

### إعدادات واجهة المستخدم

يمكنك تخصيص إعدادات اختبار واجهة المستخدم بتعديل المتغيرات في ملف `ui-performance.js`:

- `baseUrl`: عنوان URL الأساسي لواجهة المستخدم
- `headless`: تشغيل المتصفح بدون واجهة رسومية
- `viewport`: حجم نافذة العرض

## ملاحظات

- تأكد من تشغيل خادم واجهة برمجة التطبيقات وخادم واجهة المستخدم قبل تشغيل الاختبارات.
- يمكنك تعديل بيانات اعتماد المستخدم في ملف `.env` أو تعيين متغيرات البيئة `TEST_USERNAME` و `TEST_PASSWORD`.
