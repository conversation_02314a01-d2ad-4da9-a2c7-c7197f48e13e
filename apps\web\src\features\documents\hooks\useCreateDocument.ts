import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';
import { Document } from './useDocument';

export interface CreateDocumentRequest {
  documentNumber: string;
  documentType: string;
  title: string;
  description?: string;
  documentDate: string;
  expiryDate?: string;
  issuingAuthority?: string;
  referenceNumber?: string;
}

export const useCreateDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ data, file }: { data: CreateDocumentRequest; file?: File | null }): Promise<Document> => {
      const formData = new FormData();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value);
        }
      });

      if (file) {
        formData.append('file', file);
      }

      const response = await api.post<Document>('/api/documents', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
    },
  });
};
