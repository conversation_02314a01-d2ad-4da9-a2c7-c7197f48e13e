import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  Card,
  CardContent,
  Checkbox,
  Chip,
  CircularProgress,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import {
  Save as SaveIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ReportType } from '../../../types/shared-types';
import {
  ReportTemplate,
  ReportTemplateStructure,
  ReportColumn,
  REPORT_FIELDS,
} from '../types/report-template.types';

// واجهة خصائص تبويب
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// مكون تبويب
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`template-editor-tabpanel-${index}`}
      aria-labelledby={`template-editor-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

// مخطط التحقق من صحة نموذج قالب التقرير
const reportTemplateSchema = z.object({
  name: z.string().min(3, 'اسم القالب يجب أن يكون على الأقل 3 أحرف'),
  description: z.string().optional(),
  reportType: z.string(),
  isDefault: z.boolean().optional(),
  template: z.object({
    title: z.string().min(1, 'عنوان التقرير مطلوب'),
    subtitle: z.string().optional(),
    showLogo: z.boolean(),
    showDate: z.boolean(),
    showPageNumbers: z.boolean(),
    orientation: z.enum(['portrait', 'landscape']),
    footer: z.string().optional(),
    header: z.string().optional(),
  }),
});

// واجهة خصائص محرر قالب التقرير
interface ReportTemplateEditorProps {
  template?: ReportTemplate;
  onSave: (data: any) => Promise<void>;
  onCancel: () => void;
  onPreview: (template: ReportTemplateStructure) => void;
}

/**
 * مكون محرر قوالب التقارير
 */
const ReportTemplateEditor = ({
  template,
  onSave,
  onCancel,
  onPreview,
}: ReportTemplateEditorProps) => {
  const { t } = useTranslation();
  const [tabValue, setTabValue] = useState(0);
  const [columns, setColumns] = useState<ReportColumn[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<any>({
    resolver: zodResolver(reportTemplateSchema),
    defaultValues: {
      name: template?.name || '',
      description: template?.description || '',
      reportType: template?.reportType || Object.values(ReportType)[0],
      isDefault: template?.isDefault || false,
      template: {
        title: template?.template.title || '',
        subtitle: template?.template.subtitle || '',
        showLogo: template?.template.showLogo ?? true,
        showDate: template?.template.showDate ?? true,
        showPageNumbers: template?.template.showPageNumbers ?? true,
        orientation: template?.template.orientation || 'portrait',
        footer: template?.template.footer || '',
        header: template?.template.header || '',
      },
    },
  });

  // مراقبة نوع التقرير
  const reportType = watch('reportType');

  // تحديث الأعمدة عند تغيير نوع التقرير أو تحميل القالب
  useEffect(() => {
    if (template && template.template.columns) {
      setColumns(template.template.columns);
    } else if (reportType) {
      // إنشاء أعمدة افتراضية بناءً على نوع التقرير
      const fields = REPORT_FIELDS[reportType] || {};
      const defaultColumns = Object.entries(fields)
        .slice(0, 5) // أخذ أول 5 حقول فقط
        .map(([field, { label }]) => ({
          field,
          header: label,
          width: 100,
          visible: true,
        }));
      setColumns(defaultColumns);
    }
  }, [reportType, template]);

  // التعامل مع تغيير التبويب
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // التعامل مع إضافة عمود
  const handleAddColumn = () => {
    const fields = REPORT_FIELDS[reportType] || {};
    const availableFields = Object.entries(fields).filter(
      ([field]) => !columns.some((col) => col.field === field)
    );

    if (availableFields.length > 0) {
      const [field, { label }] = availableFields[0];
      setColumns([
        ...columns,
        {
          field,
          header: label,
          width: 100,
          visible: true,
        },
      ]);
    }
  };

  // التعامل مع حذف عمود
  const handleDeleteColumn = (index: number) => {
    const newColumns = [...columns];
    newColumns.splice(index, 1);
    setColumns(newColumns);
  };

  // التعامل مع تحديث عمود
  const handleUpdateColumn = (index: number, field: string, value: any) => {
    const newColumns = [...columns];
    newColumns[index] = { ...newColumns[index], [field]: value };
    setColumns(newColumns);
  };

  // إعداد أجهزة الاستشعار للسحب والإفلات
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // التعامل مع تغيير ترتيب الأعمدة
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = columns.findIndex((_, index) => `column-${index}` === active.id);
      const newIndex = columns.findIndex((_, index) => `column-${index}` === over?.id);

      setColumns(arrayMove(columns, oldIndex, newIndex));
    }
  };

  // التعامل مع تقديم النموذج
  const onSubmit = async (data: any) => {
    try {
      setIsSaving(true);

      // إضافة الأعمدة إلى القالب
      const formData = {
        ...data,
        template: {
          ...data.template,
          columns,
        },
      };

      await onSave(formData);
    } catch (error) {
      console.error('Error saving template:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // التعامل مع معاينة القالب
  const handlePreview = () => {
    const templateData: ReportTemplateStructure = {
      ...watch('template'),
      columns,
    };
    onPreview(templateData);
  };

  // مكون العمود القابل للسحب
  const SortableColumnItem = ({ column, index }: { column: any; index: number }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
    } = useSortable({ id: `column-${index}` });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    };

    return (
      <Box
        ref={setNodeRef}
        style={style}
        sx={{
          p: 2,
          mb: 2,
          border: '1px solid #e0e0e0',
          borderRadius: 1,
          bgcolor: 'background.paper',
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={1}>
            <Box {...attributes} {...listeners} sx={{ cursor: 'grab', display: 'flex', justifyContent: 'center' }}>
              <ArrowUpwardIcon />
            </Box>
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth size="small">
              <InputLabel>{t('reports.templates.field')}</InputLabel>
              <Select
                value={column.field}
                onChange={(e) => handleUpdateColumn(index, 'field', e.target.value)}
                label={t('reports.templates.field')}
              >
                {reportType &&
                  REPORT_FIELDS[reportType] &&
                  Object.entries(REPORT_FIELDS[reportType]).map(
                    ([field, { label, group }]) => (
                      <MenuItem key={field} value={field}>
                        {label} {group && `(${group})`}
                      </MenuItem>
                    )
                  )}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <TextField
              fullWidth
              size="small"
              label={t('reports.templates.header')}
              value={column.header}
              onChange={(e) => handleUpdateColumn(index, 'header', e.target.value)}
            />
          </Grid>

          <Grid item xs={6} md={2}>
            <TextField
              fullWidth
              size="small"
              type="number"
              label={t('reports.templates.width')}
              value={column.width || 100}
              onChange={(e) =>
                handleUpdateColumn(
                  index,
                  'width',
                  parseInt(e.target.value) || 100
                )
              }
              InputProps={{ inputProps: { min: 50, max: 500 } }}
            />
          </Grid>

          <Grid item xs={6} md={2}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={column.visible !== false}
                  onChange={(e) =>
                    handleUpdateColumn(index, 'visible', e.target.checked)
                  }
                />
              }
              label={t('reports.templates.visible')}
            />
          </Grid>

          <Grid item xs={12} md={1}>
            <Box display="flex" justifyContent="flex-end">
              <IconButton
                color="error"
                onClick={() => handleDeleteColumn(index)}
              >
                <DeleteIcon />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </Box>
    );
  };

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h6">
            {template ? t('reports.templates.edit') : t('reports.templates.create')}
          </Typography>
          <Box>
            <Button
              variant="outlined"
              color="primary"
              sx={{ mr: 1 }}
              startIcon={<VisibilityIcon />}
              onClick={handlePreview}
            >
              {t('reports.templates.preview')}
            </Button>
            <Button variant="outlined" color="secondary" sx={{ mr: 1 }} onClick={onCancel}>
              {t('common.cancel')}
            </Button>
            <Button
              variant="contained"
              color="primary"
              startIcon={isSaving ? <CircularProgress size={24} /> : <SaveIcon />}
              onClick={handleSubmit(onSubmit)}
              disabled={isSaving}
            >
              {t('common.save')}
            </Button>
          </Box>
        </Box>

        <form>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="template editor tabs">
              <Tab label={t('reports.templates.basicInfo')} />
              <Tab label={t('reports.templates.columns')} />
              <Tab label={t('reports.templates.appearance')} />
            </Tabs>
          </Box>

          {/* تبويب المعلومات الأساسية */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('reports.templates.name')}
                      fullWidth
                      error={!!errors.name}
                      helperText={errors.name?.message as string}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="reportType"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.reportType}>
                      <InputLabel>{t('reports.templates.reportType')}</InputLabel>
                      <Select {...field} label={t('reports.templates.reportType')}>
                        {Object.values(ReportType).map((type) => (
                          <MenuItem key={type} value={type}>
                            {t(`reports.types.${type}`)}
                          </MenuItem>
                        ))}
                      </Select>
                      {errors.reportType && (
                        <FormHelperText>{errors.reportType.message as string}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('reports.templates.description')}
                      fullWidth
                      multiline
                      rows={3}
                      error={!!errors.description}
                      helperText={errors.description?.message as string}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="isDefault"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      }
                      label={t('reports.templates.isDefault')}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* تبويب الأعمدة */}
          <TabPanel value={tabValue} index={1}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="subtitle1">{t('reports.templates.columns')}</Typography>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<AddIcon />}
                onClick={handleAddColumn}
                disabled={
                  !reportType ||
                  !REPORT_FIELDS[reportType] ||
                  Object.keys(REPORT_FIELDS[reportType]).length <= columns.length
                }
              >
                {t('reports.templates.addColumn')}
              </Button>
            </Box>

            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={columns.map((_, index) => `column-${index}`)}
                strategy={verticalListSortingStrategy}
              >
                <Paper sx={{ p: 2, mb: 2 }}>
                  {columns.length === 0 ? (
                    <Typography align="center" color="textSecondary">
                      {t('reports.templates.noColumns')}
                    </Typography>
                  ) : (
                    columns.map((column, index) => (
                      <SortableColumnItem
                        key={index}
                        column={column}
                        index={index}
                      />
                    ))
                  )}
                </Paper>
              </SortableContext>
            </DndContext>

          </TabPanel>

          {/* تبويب المظهر */}
          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Controller
                  name="template.title"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('reports.templates.title')}
                      fullWidth
                      error={!!(errors.template as any)?.title}
                      helperText={(errors.template as any)?.title?.message as string}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="template.subtitle"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('reports.templates.subtitle')}
                      fullWidth
                      error={!!(errors.template as any)?.subtitle}
                      helperText={(errors.template as any)?.subtitle?.message as string}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="template.orientation"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!(errors.template as any)?.orientation}>
                      <InputLabel>{t('reports.templates.orientation')}</InputLabel>
                      <Select {...field} label={t('reports.templates.orientation')}>
                        <MenuItem value="portrait">{t('reports.templates.portrait')}</MenuItem>
                        <MenuItem value="landscape">{t('reports.templates.landscape')}</MenuItem>
                      </Select>
                      {(errors.template as any)?.orientation && (
                        <FormHelperText>
                          {(errors.template as any)?.orientation?.message as string}
                        </FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('reports.templates.options')}
                  </Typography>
                  <Box display="flex" flexDirection="column">
                    <Controller
                      name="template.showLogo"
                      control={control}
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={field.value}
                              onChange={(e) => field.onChange(e.target.checked)}
                            />
                          }
                          label={t('reports.templates.showLogo')}
                        />
                      )}
                    />
                    <Controller
                      name="template.showDate"
                      control={control}
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={field.value}
                              onChange={(e) => field.onChange(e.target.checked)}
                            />
                          }
                          label={t('reports.templates.showDate')}
                        />
                      )}
                    />
                    <Controller
                      name="template.showPageNumbers"
                      control={control}
                      render={({ field }) => (
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={field.value}
                              onChange={(e) => field.onChange(e.target.checked)}
                            />
                          }
                          label={t('reports.templates.showPageNumbers')}
                        />
                      )}
                    />
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="template.header"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('reports.templates.header')}
                      fullWidth
                      multiline
                      rows={3}
                      error={!!(errors.template as any)?.header}
                      helperText={(errors.template as any)?.header?.message as string}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="template.footer"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('reports.templates.footer')}
                      fullWidth
                      multiline
                      rows={3}
                      error={!!(errors.template as any)?.footer}
                      helperText={(errors.template as any)?.footer?.message as string}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </TabPanel>
        </form>
      </CardContent>
    </Card>
  );
};

export default ReportTemplateEditor;
