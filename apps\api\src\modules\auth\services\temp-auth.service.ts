import bcrypt from 'bcryptjs';
import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { logger } from '../../../core/utils/logger.js';

export const authService = {
  /**
   * Login service
   */
  login: async (username: string, password: string) => {
    // Find user by username
    const user = await prisma.user.findUnique({
      where: { username },
    });

    // Check if user exists
    if (!user) {
      throw new HttpException(401, 'Invalid username or password', 'Unauthorized');
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      throw new HttpException(401, 'Invalid username or password', 'Unauthorized');
    }

    // Return user without tokens for now
    return {
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
      },
      token: 'dummy-token',
      refreshToken: 'dummy-refresh-token',
    };
  },

  /**
   * Refresh token service
   */
  refreshToken: async (refreshToken: string) => {
    try {
      // For now, just return a dummy token
      return {
        user: {
          id: 'dummy-id',
          username: 'dummy-username',
          name: 'Dummy User',
          email: '<EMAIL>',
          role: 'USER',
        },
        token: 'dummy-token',
        refreshToken: 'dummy-refresh-token',
      };
    } catch (error) {
      logger.error('Refresh token error:', error);
      throw new HttpException(500, 'Internal server error', 'Internal Server Error');
    }
  },

  /**
   * Logout service
   */
  logout: async (token: string) => {
    try {
      // For now, we'll just return success
      return { success: true };
    } catch (error) {
      logger.error('Logout error:', error);
      throw new HttpException(500, 'Internal server error', 'Internal Server Error');
    }
  },
};
