import apiService from './api.service';
import { User } from './user.service';

/**
 * واجهة استجابة تسجيل الدخول
 */
export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

/**
 * خدمة المصادقة
 * توفر واجهة موحدة للتعامل مع المصادقة
 */
class AuthService {
  /**
   * تسجيل الدخول
   * @param username اسم المستخدم
   * @param password كلمة المرور
   * @returns وعد باستجابة تسجيل الدخول
   */
  public async login(username: string, password: string): Promise<LoginResponse> {
    try {
      // تسجيل الدخول
      const response = await apiService.post('/api/auth/login', {
        username,
        password,
      });

      // حفظ التوكن في التخزين المحلي
      localStorage.setItem('token', response.token);
      localStorage.setItem('refreshToken', response.refreshToken);

      return response;
    } catch (error) {
      console.error('Error logging in:', error);
      throw error;
    }
  }

  /**
   * تسجيل الخروج
   * @returns وعد بنتيجة تسجيل الخروج
   */
  public async logout(): Promise<any> {
    try {
      // تسجيل الخروج
      const response = await apiService.post('/api/auth/logout');

      // حذف التوكن من التخزين المحلي
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');

      return response;
    } catch (error) {
      console.error('Error logging out:', error);

      // حذف التوكن من التخزين المحلي حتى في حالة الخطأ
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');

      throw error;
    }
  }

  /**
   * تجديد التوكن
   * @returns وعد باستجابة تجديد التوكن
   */
  public async refreshToken(): Promise<LoginResponse> {
    try {
      // الحصول على توكن التجديد من التخزين المحلي
      const refreshToken = localStorage.getItem('refreshToken');

      if (!refreshToken) {
        throw new Error('No refresh token found');
      }

      // تجديد التوكن
      const response = await apiService.post<LoginResponse>('/api/auth/refresh-token', {
        refreshToken,
      });

      // حفظ التوكن الجديد في التخزين المحلي
      localStorage.setItem('token', response.token);
      localStorage.setItem('refreshToken', response.refreshToken);

      return response;
    } catch (error) {
      console.error('Error refreshing token:', error);

      // حذف التوكن من التخزين المحلي في حالة الخطأ
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');

      throw error;
    }
  }

  /**
   * التحقق مما إذا كان المستخدم مسجل الدخول
   * @returns ما إذا كان المستخدم مسجل الدخول
   */
  public isLoggedIn(): boolean {
    return !!localStorage.getItem('token');
  }

  /**
   * الحصول على التوكن
   * @returns التوكن
   */
  public getToken(): string | null {
    return localStorage.getItem('token');
  }

  /**
   * الحصول على توكن التجديد
   * @returns توكن التجديد
   */
  public getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }

  /**
   * نسيت كلمة المرور
   * @param email البريد الإلكتروني
   * @returns وعد بنتيجة طلب إعادة تعيين كلمة المرور
   */
  public async forgotPassword(email: string): Promise<any> {
    try {
      // إرسال طلب إعادة تعيين كلمة المرور
      const response = await apiService.post('/api/auth/forgot-password', {
        email,
      });

      return response;
    } catch (error) {
      console.error('Error in forgot password:', error);
      throw error;
    }
  }

  /**
   * إعادة تعيين كلمة المرور
   * @param token توكن إعادة تعيين كلمة المرور
   * @param newPassword كلمة المرور الجديدة
   * @returns وعد بنتيجة إعادة تعيين كلمة المرور
   */
  public async resetPassword(token: string, newPassword: string): Promise<any> {
    try {
      // إعادة تعيين كلمة المرور
      const response = await apiService.post('/api/auth/reset-password', {
        token,
        password: newPassword,
      });

      return response;
    } catch (error) {
      console.error('Error in reset password:', error);
      throw error;
    }
  }

  /**
   * تحديث الملف الشخصي
   * @param data بيانات الملف الشخصي
   * @returns وعد بنتيجة تحديث الملف الشخصي
   */
  public async updateProfile(data: Partial<User>): Promise<any> {
    try {
      // تحديث الملف الشخصي
      const response = await apiService.put('/api/users/profile', data);

      return response;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw error;
    }
  }

  /**
   * تحديث كلمة المرور
   * @param currentPassword كلمة المرور الحالية
   * @param newPassword كلمة المرور الجديدة
   * @returns وعد بنتيجة تحديث كلمة المرور
   */
  public async updatePassword(currentPassword: string, newPassword: string): Promise<any> {
    try {
      // تحديث كلمة المرور
      const response = await apiService.put('/api/users/password', {
        currentPassword,
        newPassword,
      });

      return response;
    } catch (error) {
      console.error('Error updating password:', error);
      throw error;
    }
  }
}

// إنشاء نسخة واحدة من الخدمة
const authService = new AuthService();

export default authService;
