import { createLazyComponent } from '../../components/LazyLoad/LazyLoad';

// تعريف المكونات الكسولة للصفحات
// استخدام preload=true للصفحات الأكثر استخدامًا

// صفحات المصادقة
export const LazyLoginPage = createLazyComponent(
  () => import('../../features/auth/pages/LoginPage'),
  { preload: true }
);

export const LazyForgotPasswordPage = createLazyComponent(
  () => import('../../features/auth/pages/ForgotPasswordPage')
);

export const LazyResetPasswordPage = createLazyComponent(
  () => import('../../features/auth/pages/ResetPasswordPage')
);

// صفحة لوحة التحكم
export const LazyDashboardPage = createLazyComponent(
  () => import('../../features/dashboard/pages/DashboardPage'),
  { preload: true }
);

// صفحات البيانات
export const LazyDeclarationsPage = createLazyComponent(
  () => import('../../features/declarations/pages/DeclarationsPage'),
  { preload: true }
);

export const LazyDeclarationDetailsPage = createLazyComponent(
  () => import('../../features/declarations/pages/DeclarationDetailsPage')
);

export const LazyDeclarationFormPage = createLazyComponent(
  () => import('../../features/declarations/pages/DeclarationFormPage')
);

// صفحات حركة الأصناف
export const LazyItemMovementsPage = createLazyComponent(
  () => import('../../features/item-movements/pages/ItemMovementsPage')
);

export const LazyItemMovementDetailsPage = createLazyComponent(
  () => import('../../features/item-movements/pages/ItemMovementDetailsPage')
);

export const LazyItemMovementFormPage = createLazyComponent(
  () => import('../../features/item-movements/pages/ItemMovementFormPage')
);

// صفحات التفويضات
export const LazyAuthorizationsPage = createLazyComponent(
  () => import('../../features/authorizations/pages/AuthorizationsPage')
);

export const LazyAuthorizationDetailsPage = createLazyComponent(
  () => import('../../features/authorizations/pages/AuthorizationDetailsPage')
);

export const LazyAuthorizationFormPage = createLazyComponent(
  () => import('../../features/authorizations/pages/AuthorizationFormPage')
);

// صفحات الإفراجات
export const LazyReleasesPage = createLazyComponent(
  () => import('../../features/releases/pages/ReleasesPage')
);

export const LazyReleaseDetailsPage = createLazyComponent(
  () => import('../../features/releases/pages/ReleaseDetailsPage')
);

export const LazyReleaseFormPage = createLazyComponent(
  () => import('../../features/releases/pages/ReleaseFormPage')
);

// صفحات التصاريح
export const LazyPermitsPage = createLazyComponent(
  () => import('../../features/permits/pages/PermitsPage')
);

export const LazyPermitDetailsPage = createLazyComponent(
  () => import('../../features/permits/pages/PermitDetailsPage')
);

export const LazyPermitFormPage = createLazyComponent(
  () => import('../../features/permits/pages/PermitFormPage')
);

// صفحات الضمانات
export const LazyGuaranteesPage = createLazyComponent(
  () => import('../../features/guarantees/pages/GuaranteesPage')
);

export const LazyGuaranteeDetailsPage = createLazyComponent(
  () => import('../../features/guarantees/pages/GuaranteeDetailsPage')
);

export const LazyGuaranteeFormPage = createLazyComponent(
  () => import('../../features/guarantees/pages/GuaranteeFormPage')
);

// صفحات الاستلامات
export const LazyReceiptsPage = createLazyComponent(
  () => import('../../features/receipts/pages/ReceiptsPage')
);

export const LazyReceiptDetailsPage = createLazyComponent(
  () => import('../../features/receipts/pages/ReceiptDetailsPage')
);

export const LazyReceiptFormPage = createLazyComponent(
  () => import('../../features/receipts/pages/ReceiptFormPage')
);

// صفحات العملاء
export const LazyClientsPage = createLazyComponent(
  () => import('../../features/clients/pages/ClientsPage')
);

export const LazyClientDetailsPage = createLazyComponent(
  () => import('../../features/clients/pages/ClientDetailsPage')
);

export const LazyClientFormPage = createLazyComponent(
  () => import('../../features/clients/pages/ClientFormPage')
);

// صفحات المستندات
export const LazyDocumentsPage = createLazyComponent(
  () => import('../../features/documents/pages/DocumentsPage')
);

export const LazyDocumentDetailsPage = createLazyComponent(
  () => import('../../features/documents/pages/DocumentDetailsPage')
);

export const LazyDocumentFormPage = createLazyComponent(
  () => import('../../features/documents/pages/DocumentFormPage')
);

// صفحة البحث المتقدم
export const LazyAdvancedSearchPage = createLazyComponent(
  () => import('../../features/advanced-search/pages/AdvancedSearchPage')
);

// صفحات النماذج المخصصة
export const LazyCustomFormsPage = createLazyComponent(
  () => import('../../features/custom-forms/pages/CustomFormsPage')
);

export const LazyCustomFormBuilderPage = createLazyComponent(
  () => import('../../features/custom-forms/pages/CustomFormBuilderPage')
);

// صفحات التقارير
export const LazyReportsPage = createLazyComponent(
  () => import('../../features/reports/pages/ReportsPage')
);

export const LazyReportBuilderPage = createLazyComponent(
  () => import('../../features/reports/pages/ReportBuilderPage')
);

// صفحة قاعدة البيانات
export const LazyDatabasePage = createLazyComponent(
  () => import('../../features/database/pages/DatabasePage')
);

// صفحة اختبار API
export const LazyApiTestPage = createLazyComponent(
  () => import('../../features/api-test/pages/ApiTestPage')
);

// صفحة الإعدادات
export const LazySettingsPage = createLazyComponent(
  () => import('../../features/settings/pages/SettingsPage')
);

// صفحة الملف الشخصي
export const LazyProfilePage = createLazyComponent(
  () => import('../../features/profile/pages/ProfilePage')
);

// صفحة الخطأ
export const LazyErrorPage = createLazyComponent(
  () => import('../../features/common/pages/NotFoundPage')
);

// صفحة غير موجودة
export const LazyNotFoundPage = createLazyComponent(
  () => import('../../features/common/pages/NotFoundPage')
);
