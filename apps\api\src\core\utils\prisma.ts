import { PrismaClient } from '@prisma/client';
import { logger } from './logger.js';

// إنشاء Prisma client مع دعم بيئة الاختبار
function createPrismaClient() {
  if (process.env.NODE_ENV === 'test') {
    // في بيئة الاختبار، استخدم SQLite مع DATABASE_URL المحدد في متغيرات البيئة
    console.log('🔧 إنشاء Prisma Client للاختبارات مع SQLite');
    console.log('🔗 DATABASE_URL:', process.env.DATABASE_URL);

    return new PrismaClient({
      log: ['error'],
      // استخدام DATABASE_URL من متغيرات البيئة (SQLite للاختبارات)
      datasources: {
        db: {
          url: process.env.DATABASE_URL || 'file:./prisma/test.db'
        }
      }
    });
  } else {
    // في بيئة الإنتاج والتطوير، استخدم PostgreSQL
    return new PrismaClient({
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
    });
  }
}

export const prisma = createPrismaClient();

// Event listeners are disabled for SQLite compatibility

// Handle Prisma connection
export const connectPrisma = async () => {
  try {
    await prisma.$connect();
    logger.info('Connected to database');
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    process.exit(1);
  }
};

// Handle Prisma disconnection
export const disconnectPrisma = async () => {
  try {
    await prisma.$disconnect();
    logger.info('Disconnected from database');
  } catch (error) {
    logger.error('Failed to disconnect from database:', error);
    process.exit(1);
  }
};
