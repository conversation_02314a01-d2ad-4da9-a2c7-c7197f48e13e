// أنواع التفويض
export enum AuthorizationType {
  FOLLOW_UP = 'FOLLOW_UP',
  CLEARANCE = 'CLEARANCE',
  RECEIPT = 'RECEIPT',
  FULL = 'FULL',
}

// حالات التفويض
export enum AuthorizationStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
}

// واجهة المستخدم
export interface User {
  id: string;
  username: string;
  name: string;
}

// واجهة العميل
export interface Client {
  id: string;
  clientNumber?: number;
  taxNumber: string;
  name: string;
  companyName?: string;
  phone?: string;
}

// واجهة التفويض
export interface Authorization {
  id: string;
  authorizationNumber?: number;
  clientName?: string;
  taxNumber: string;
  authorizationType: AuthorizationType;
  startDate: Date;
  endDate: Date;
  pdfFile?: string;
  clientId?: string;
  client?: Client;
  createdBy: User;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

// واجهة قيم نموذج التفويض
export interface AuthorizationFormValues {
  clientName?: string;
  taxNumber: string;
  authorizationType: AuthorizationType;
  startDate: Date | null;
  endDate: Date | null;
  clientId?: string;
}

// واجهة استجابة قائمة التفويضات
export interface AuthorizationsResponse {
  data: Authorization[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
