import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

/**
 * واجهة معلمات البحث للنماذج المخصصة
 */
interface ListCustomFormsParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  formType?: string;
  isActive?: boolean;
}

/**
 * واجهة بيانات النموذج المخصص
 */
interface CustomFormData {
  name: string;
  description?: string;
  formType: string;
  fields: any[];
  isActive?: boolean;
}

export const customFormService = {
  /**
   * إنشاء نموذج مخصص جديد
   */
  createCustomForm: async (data: CustomFormData, userId: string) => {
    try {
      // التحقق من عدم وجود نموذج بنفس الاسم
      const existingForm = await prisma.customForm.findUnique({
        where: { name: data.name },
      });

      if (existingForm) {
        throw new HttpException(400, 'يوجد نموذج مخصص بنفس الاسم', 'Duplicate form name');
      }

      // إنشاء النموذج المخصص
      const customForm = await prisma.customForm.create({
        data: {
          name: data.name,
          description: data.description,
          formType: data.formType,
          fields: data.fields || {}, // استخدام fields كما هو محدد في Schema
          isActive: data.isActive ?? true,
          userId: userId, // استخدام userId كما هو محدد في Schema
        },
      });

      return customForm;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء إنشاء النموذج المخصص', 'Internal Server Error');
    }
  },

  /**
   * الحصول على نموذج مخصص محدد
   */
  getCustomForm: async (id: string) => {
    try {
      const customForm = await prisma.customForm.findUnique({
        where: { id },
      });

      if (!customForm) {
        throw new HttpException(404, 'النموذج المخصص غير موجود', 'Custom form not found');
      }

      return customForm;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء الحصول على النموذج المخصص', 'Internal Server Error');
    }
  },

  /**
   * الحصول على قائمة النماذج المخصصة
   */
  listCustomForms: async (params: ListCustomFormsParams = {}) => {
    try {
      const {
        page = 1,
        limit = 10,
        sort = 'createdAt',
        order = 'desc',
        formType,
        isActive,
      } = params;

      // بناء شروط البحث
      const where: any = {};

      if (formType) {
        where.formType = formType;
      }

      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      // الحصول على إجمالي عدد النماذج المخصصة
      const total = await prisma.customForm.count({ where });

      // الحصول على النماذج المخصصة
      const customForms = await prisma.customForm.findMany({
        where,
        orderBy: {
          [sort]: order,
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      return {
        data: customForms,
        pagination: {
          page,
          limit,
          total,
        },
      };
    } catch (error) {
      throw new HttpException(500, 'حدث خطأ أثناء الحصول على قائمة النماذج المخصصة', 'Internal Server Error');
    }
  },

  /**
   * تحديث نموذج مخصص
   */
  updateCustomForm: async (id: string, data: Partial<CustomFormData>) => {
    try {
      // التحقق من وجود النموذج المخصص
      const existingForm = await prisma.customForm.findUnique({
        where: { id },
      });

      if (!existingForm) {
        throw new HttpException(404, 'النموذج المخصص غير موجود', 'Custom form not found');
      }

      // التحقق من عدم وجود نموذج آخر بنفس الاسم
      if (data.name && data.name !== existingForm.name) {
        const duplicateName = await prisma.customForm.findUnique({
          where: { name: data.name },
        });

        if (duplicateName) {
          throw new HttpException(400, 'يوجد نموذج مخصص آخر بنفس الاسم', 'Duplicate form name');
        }
      }

      // تحديث النموذج المخصص
      const updatedForm = await prisma.customForm.update({
        where: { id },
        data: {
          name: data.name,
          description: data.description,
          formType: data.formType,
          fields: data.fields || {}, // استخدام fields كما هو محدد في Schema
          isActive: data.isActive,
        },
      });

      return updatedForm;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء تحديث النموذج المخصص', 'Internal Server Error');
    }
  },

  /**
   * حذف نموذج مخصص
   */
  deleteCustomForm: async (id: string) => {
    try {
      // التحقق من وجود النموذج المخصص
      const existingForm = await prisma.customForm.findUnique({
        where: { id },
      });

      if (!existingForm) {
        throw new HttpException(404, 'النموذج المخصص غير موجود', 'Custom form not found');
      }

      // حذف النموذج المخصص
      await prisma.customForm.delete({
        where: { id },
      });

      return true;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء حذف النموذج المخصص', 'Internal Server Error');
    }
  },
};
