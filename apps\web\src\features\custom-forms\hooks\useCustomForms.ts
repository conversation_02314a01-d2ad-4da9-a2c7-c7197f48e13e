import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getCustomForms,
  getCustomForm,
  createCustomForm,
  updateCustomForm,
  deleteCustomForm,
  CustomFormsParams,
} from '../api/custom-forms.api';
import { CustomFormFormValues } from '../types/custom-form.types';
import { useToast } from '@lib/hooks/useToast';
import { useTranslation } from 'react-i18next';

/**
 * خطاف للحصول على قائمة النماذج المخصصة
 */
export const useCustomForms = (params: CustomFormsParams = {}) => {
  return useQuery({
    queryKey: ['customForms', params],
    queryFn: () => getCustomForms(params),
  });
};

/**
 * خطاف للحصول على نموذج مخصص محدد
 */
export const useCustomForm = (id: string) => {
  return useQuery({
    queryKey: ['customForm', id],
    queryFn: () => getCustomForm(id),
    enabled: !!id,
  });
};

/**
 * خطاف لإنشاء نموذج مخصص جديد
 */
export const useCreateCustomForm = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (data: CustomFormFormValues) => createCustomForm(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customForms'] });
      toast.showSuccess(t('customForms.createSuccess'));
    },
    onError: (error: any) => {
      toast.showError(error.response?.data?.message || t('customForms.createError'));
    },
  });
};

/**
 * خطاف لتحديث نموذج مخصص
 */
export const useUpdateCustomForm = (id: string) => {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (data: Partial<CustomFormFormValues>) => updateCustomForm(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customForms'] });
      queryClient.invalidateQueries({ queryKey: ['customForm', id] });
      toast.showSuccess(t('customForms.updateSuccess'));
    },
    onError: (error: any) => {
      toast.showError(error.response?.data?.message || t('customForms.updateError'));
    },
  });
};

/**
 * خطاف لحذف نموذج مخصص
 */
export const useDeleteCustomForm = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => deleteCustomForm(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customForms'] });
      toast.showSuccess(t('customForms.deleteSuccess'));
    },
    onError: (error: any) => {
      toast.showError(error.response?.data?.message || t('customForms.deleteError'));
    },
  });
};
