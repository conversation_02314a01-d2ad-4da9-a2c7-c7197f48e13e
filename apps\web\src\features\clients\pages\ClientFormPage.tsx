import { useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import { useClient, useCreateClient, useUpdateClient } from '../hooks/useClients';
import { ClientFormValues } from '../types/client.types';

// مخطط التحقق من صحة نموذج العميل
const clientFormSchema = z.object({
  taxNumber: z.string().min(1, { message: 'الرقم الضريبي مطلوب' }),
  name: z.string().min(1, { message: 'اسم العميل مطلوب' }),
  companyName: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email({ message: 'البريد الإلكتروني غير صالح' }).optional().or(z.literal('')),
  address: z.string().optional(),
  contactPerson: z.string().optional(),
  contactPhone: z.string().optional(),
  notes: z.string().optional(),
});

const ClientFormPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  
  // استخدام خطافات العميل
  const { data: client, isLoading: isLoadingClient } = useClient(id || '');
  const createMutation = useCreateClient();
  const updateMutation = useUpdateClient();
  
  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<ClientFormValues>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: {
      taxNumber: '',
      name: '',
      companyName: '',
      phone: '',
      email: '',
      address: '',
      contactPerson: '',
      contactPhone: '',
      notes: '',
    },
  });
  
  // تحديث النموذج عند تحميل بيانات العميل
  useEffect(() => {
    if (isEditMode && client) {
      reset({
        taxNumber: client.taxNumber,
        name: client.name,
        companyName: client.companyName,
        phone: client.phone,
        email: client.email,
        address: client.address,
        contactPerson: client.contactPerson,
        contactPhone: client.contactPhone,
        notes: client.notes,
      });
    }
  }, [isEditMode, client, reset]);
  
  // التعامل مع تقديم النموذج
  const onSubmit = async (data: ClientFormValues) => {
    try {
      if (isEditMode && id) {
        // تحديث العميل
        await updateMutation.mutateAsync({
          id,
          data: {
            taxNumber: data.taxNumber,
            name: data.name,
            companyName: data.companyName,
            phone: data.phone,
            email: data.email,
            address: data.address,
            contactPerson: data.contactPerson,
            contactPhone: data.contactPhone,
            notes: data.notes,
          },
        });
      } else {
        // إنشاء عميل جديد
        await createMutation.mutateAsync({
          taxNumber: data.taxNumber,
          name: data.name,
          companyName: data.companyName,
          phone: data.phone,
          email: data.email,
          address: data.address,
          contactPerson: data.contactPerson,
          contactPhone: data.contactPhone,
          notes: data.notes,
        });
      }
      
      // العودة إلى صفحة قائمة العملاء
      navigate('/clients');
    } catch (error) {
      console.error('Error submitting client form:', error);
    }
  };
  
  // التعامل مع إلغاء النموذج
  const handleCancel = () => {
    navigate('/clients');
  };
  
  if (isEditMode && isLoadingClient) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {isEditMode ? t('clients.edit') : t('clients.create')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {isEditMode ? t('clients.editDescription') : t('clients.createDescription')}
        </Typography>
      </Box>
      
      <Paper>
        <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="taxNumber"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('clients.taxNumber')}
                      fullWidth
                      required
                      error={!!errors.taxNumber}
                      helperText={errors.taxNumber?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('clients.name')}
                      fullWidth
                      required
                      error={!!errors.name}
                      helperText={errors.name?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="companyName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('clients.companyName')}
                      fullWidth
                      error={!!errors.companyName}
                      helperText={errors.companyName?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="phone"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('clients.phone')}
                      fullWidth
                      error={!!errors.phone}
                      helperText={errors.phone?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="email"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('clients.email')}
                      fullWidth
                      error={!!errors.email}
                      helperText={errors.email?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="contactPerson"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('clients.contactPerson')}
                      fullWidth
                      error={!!errors.contactPerson}
                      helperText={errors.contactPerson?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="contactPhone"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('clients.contactPhone')}
                      fullWidth
                      error={!!errors.contactPhone}
                      helperText={errors.contactPhone?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="address"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('clients.address')}
                      fullWidth
                      error={!!errors.address}
                      helperText={errors.address?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12}>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('clients.notes')}
                      fullWidth
                      multiline
                      rows={4}
                      error={!!errors.notes}
                      helperText={errors.notes?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </CardContent>
          
          <Divider />
          
          <Box p={2} display="flex" justifyContent="flex-end">
            <Button
              variant="outlined"
              onClick={handleCancel}
              startIcon={<ArrowBackIcon />}
              sx={{ mr: 1 }}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="submit"
              variant="contained"
              startIcon={<SaveIcon />}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <CircularProgress size={24} />
              ) : isEditMode ? (
                t('common.update')
              ) : (
                t('common.save')
              )}
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default ClientFormPage;
