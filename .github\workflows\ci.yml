name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  lint:
    name: <PERSON>t
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Check line endings
        run: |
          if grep -r $'\r' --include="*.{js,ts,tsx,json,md,yml}" .; then
            echo "CRLF line endings found. Please use LF line endings."
            exit 1
          fi
      
      - name: Lint
        run: npm run lint

  test-api:
    name: Test API
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: alnoor_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Setup test database
        run: |
          cd apps/api
          npx prisma migrate deploy
          npx prisma db seed
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/alnoor_test?schema=public
      
      - name: Run API tests
        run: npm run test:api
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/alnoor_test?schema=public
          JWT_SECRET: test_secret
          NODE_ENV: test

  test-web:
    name: Test Web
    runs-on: ubuntu-latest
    needs: lint
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run Web tests
        run: npm run test:web

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [test-api, test-web]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build API
        run: npm run build:api
      
      - name: Build Web
        run: npm run build:web
      
      - name: Upload API build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: api-build
          path: apps/api/dist
      
      - name: Upload Web build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: web-build
          path: apps/web/dist
