import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { DataTable, Column } from '../DataTable';

// بيانات اختبار
interface TestData {
  id: string;
  name: string;
  age: number;
}

const testColumns: Column<TestData>[] = [
  { id: 'name', label: 'الاسم' },
  { id: 'age', label: 'العمر' },
];

const testRows: TestData[] = [
  { id: '1', name: 'أحمد', age: 30 },
  { id: '2', name: 'محمد', age: 25 },
  { id: '3', name: 'علي', age: 40 },
];

describe('DataTable Component', () => {
  const defaultProps = {
    columns: testColumns,
    rows: testRows,
    rowCount: testRows.length,
    page: 0,
    pageSize: 10,
    onPageChange: jest.fn(),
    onPageSizeChange: jest.fn(),
  };

  test('renders column headers correctly', () => {
    render(<DataTable {...defaultProps} />);
    expect(screen.getByText('الاسم')).toBeInTheDocument();
    expect(screen.getByText('العمر')).toBeInTheDocument();
  });

  test('renders row data correctly', () => {
    render(<DataTable {...defaultProps} />);
    expect(screen.getByText('أحمد')).toBeInTheDocument();
    expect(screen.getByText('30')).toBeInTheDocument();
    expect(screen.getByText('محمد')).toBeInTheDocument();
    expect(screen.getByText('25')).toBeInTheDocument();
  });

  test('shows loading state when loading is true', () => {
    render(<DataTable {...defaultProps} loading={true} />);
    expect(screen.getByText('جاري تحميل البيانات...')).toBeInTheDocument();
  });

  test('shows empty content when no rows', () => {
    render(<DataTable {...defaultProps} rows={[]} />);
    expect(screen.getByText('لا توجد بيانات')).toBeInTheDocument();
  });

  test('shows custom empty content when provided', () => {
    render(
      <DataTable
        {...defaultProps}
        rows={[]}
        emptyContent={<div>لا توجد نتائج للبحث</div>}
      />
    );
    expect(screen.getByText('لا توجد نتائج للبحث')).toBeInTheDocument();
  });

  test('calls onRowClick when row is clicked and rowsClickable is true', () => {
    const handleRowClick = jest.fn();
    render(
      <DataTable
        {...defaultProps}
        onRowClick={handleRowClick}
        rowsClickable={true}
      />
    );
    
    // النقر على الصف الأول
    fireEvent.click(screen.getByText('أحمد'));
    
    expect(handleRowClick).toHaveBeenCalledWith(testRows[0]);
  });

  test('does not call onRowClick when rowsClickable is false', () => {
    const handleRowClick = jest.fn();
    render(
      <DataTable
        {...defaultProps}
        onRowClick={handleRowClick}
        rowsClickable={false}
      />
    );
    
    // النقر على الصف الأول
    fireEvent.click(screen.getByText('أحمد'));
    
    expect(handleRowClick).not.toHaveBeenCalled();
  });

  test('calls onPageChange when page is changed', () => {
    render(<DataTable {...defaultProps} />);
    
    // النقر على زر الصفحة التالية
    const nextPageButton = screen.getByRole('button', { name: /التالي/i });
    fireEvent.click(nextPageButton);
    
    expect(defaultProps.onPageChange).toHaveBeenCalledWith(1);
  });

  test('formats cell values using format function when provided', () => {
    const columnsWithFormat: Column<TestData>[] = [
      { id: 'name', label: 'الاسم' },
      { 
        id: 'age', 
        label: 'العمر',
        format: (value) => `${value} سنة` 
      },
    ];
    
    render(<DataTable {...defaultProps} columns={columnsWithFormat} />);
    
    expect(screen.getByText('30 سنة')).toBeInTheDocument();
    expect(screen.getByText('25 سنة')).toBeInTheDocument();
  });
});
