import { useTranslation } from 'react-i18next';
import { Box, CircularProgress, Typography } from '@mui/material';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
import { useDeclarationsByType, ChartDataItem } from '../hooks/useDeclarations';

const DeclarationsByTypeChart = () => {
  const { t } = useTranslation();
  const { data: chartData = [], isLoading, isError } = useDeclarationsByType() as {
    data: ChartDataItem[];
    isLoading: boolean;
    isError: boolean;
  };

  if (isLoading) {
    return (
      <Box sx={{ width: '100%', height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <CircularProgress size={40} />
        <Typography variant="body2" sx={{ ml: 2 }}>
          جاري تحميل البيانات...
        </Typography>
      </Box>
    );
  }

  if (isError || chartData.length === 0) {
    return (
      <Box sx={{ width: '100%', height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          لا توجد بيانات لعرضها
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', height: 300 }}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip
            formatter={(value) => [`${value}%`, t('dashboard.percentage')]}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </Box>
  );
};

export default DeclarationsByTypeChart;
