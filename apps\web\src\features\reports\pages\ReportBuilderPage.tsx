import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Divider,
  Grid,
  Paper,
  TextField,
  Typography,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Card,
  CardContent,
  FormControlLabel,
  Switch,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Code as CodeIcon,
  Table<PERSON>hart as TableChartIcon,
  Bar<PERSON>hart as BarChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  <PERSON><PERSON><PERSON> as LineChartIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useReportTemplate, useCreateReportTemplate, useUpdateReportTemplate } from '../hooks/useReports';
import { ReportType, ChartType, DataSource } from '../types/report.types';
import { PageHeader } from '@features/common/components/PageHeader';
import { LoadingIndicator } from '@features/common/components/LoadingIndicator';
import { ConfirmDialog } from '@features/common/components/ConfirmDialog';
import { ReportPreview } from '../components/ReportPreview';
import { CodeEditor } from '@features/common/components/CodeEditor';

// تعريف مخطط التحقق من صحة البيانات
const columnSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'اسم العمود مطلوب'),
  label: z.string().min(1, 'عنوان العمود مطلوب'),
  type: z.string().min(1, 'نوع العمود مطلوب'),
  format: z.string().optional(),
  width: z.number().optional(),
  sortable: z.boolean().default(true),
  filterable: z.boolean().default(true),
});

const filterSchema = z.object({
  id: z.string(),
  field: z.string().min(1, 'حقل التصفية مطلوب'),
  operator: z.string().min(1, 'عامل التصفية مطلوب'),
  value: z.string().optional(),
});

const chartSchema = z.object({
  type: z.nativeEnum(ChartType, {
    errorMap: () => ({ message: 'نوع الرسم البياني مطلوب' }),
  }),
  title: z.string().min(1, 'عنوان الرسم البياني مطلوب'),
  xAxis: z.string().min(1, 'محور X مطلوب'),
  yAxis: z.string().min(1, 'محور Y مطلوب'),
  options: z.record(z.any()).optional(),
});

const reportTemplateSchema = z.object({
  title: z.string().min(1, 'عنوان التقرير مطلوب'),
  description: z.string().optional(),
  type: z.nativeEnum(ReportType, {
    errorMap: () => ({ message: 'نوع التقرير مطلوب' }),
  }),
  dataSource: z.nativeEnum(DataSource, {
    errorMap: () => ({ message: 'مصدر البيانات مطلوب' }),
  }),
  query: z.string().optional(),
  columns: z.array(columnSchema).min(1, 'يجب إضافة عمود واحد على الأقل'),
  filters: z.array(filterSchema).optional(),
  chart: chartSchema.optional(),
  isPublished: z.boolean().default(false),
});

// نوع بيانات النموذج
type ReportTemplateValues = z.infer<typeof reportTemplateSchema>;

/**
 * صفحة منشئ التقارير
 * تستخدم لإنشاء وتعديل قوالب التقارير
 */
const ReportBuilderPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;

  // حالة التقرير
  const [activeTab, setActiveTab] = useState(0);
  const [columns, setColumns] = useState<any[]>([]);
  const [filters, setFilters] = useState<any[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [showCode, setShowCode] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  // استخدام خطافات التقارير
  const { data: reportTemplate, isLoading: isLoadingTemplate } = useReportTemplate(id || '', {
    enabled: isEditMode,
  });
  const createReportTemplateMutation = useCreateReportTemplate();
  const updateReportTemplateMutation = useUpdateReportTemplate();

  // إعداد نموذج البيانات
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<ReportTemplateValues>({
    resolver: zodResolver(reportTemplateSchema),
    defaultValues: {
      title: '',
      description: '',
      type: ReportType.TABLE,
      dataSource: DataSource.DECLARATIONS,
      query: '',
      columns: [],
      filters: [],
      isPublished: false,
    },
  });

  // مراقبة قيم النموذج
  const formValues = watch();

  // تحديث النموذج عند تحميل البيانات
  useEffect(() => {
    if (reportTemplate && (reportTemplate as any).title) {
      reset({
        title: (reportTemplate as any).title,
        description: (reportTemplate as any).description || '',
        type: (reportTemplate as any).type,
        dataSource: (reportTemplate as any).dataSource,
        query: (reportTemplate as any).query || '',
        columns: (reportTemplate as any).columns,
        filters: (reportTemplate as any).filters || [],
        chart: (reportTemplate as any).chart,
        isPublished: (reportTemplate as any).isPublished,
      });
      setColumns((reportTemplate as any).columns);
      setFilters((reportTemplate as any).filters || []);
    }
  }, [reportTemplate, reset]);

  // تغيير التبويب النشط
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // إضافة عمود جديد
  const handleAddColumn = () => {
    const newColumn = {
      id: `column_${Date.now()}`,
      name: `column_${columns.length + 1}`,
      label: `العمود ${columns.length + 1}`,
      type: 'string',
      sortable: true,
      filterable: true,
    };

    const updatedColumns = [...columns, newColumn];
    setColumns(updatedColumns);
    setValue('columns', updatedColumns);
  };

  // حذف عمود
  const handleDeleteColumn = (index: number) => {
    const updatedColumns = [...columns];
    updatedColumns.splice(index, 1);
    setColumns(updatedColumns);
    setValue('columns', updatedColumns);
  };

  // تحديث عمود
  const handleColumnChange = (index: number, field: string, value: any) => {
    const updatedColumns = [...columns];
    updatedColumns[index] = { ...updatedColumns[index], [field]: value };
    setColumns(updatedColumns);
    setValue('columns', updatedColumns);
  };

  // إضافة تصفية جديدة
  const handleAddFilter = () => {
    const newFilter = {
      id: `filter_${Date.now()}`,
      field: '',
      operator: 'equals',
      value: '',
    };

    const updatedFilters = [...filters, newFilter];
    setFilters(updatedFilters);
    setValue('filters', updatedFilters);
  };

  // حذف تصفية
  const handleDeleteFilter = (index: number) => {
    const updatedFilters = [...filters];
    updatedFilters.splice(index, 1);
    setFilters(updatedFilters);
    setValue('filters', updatedFilters);
  };

  // تحديث تصفية
  const handleFilterChange = (index: number, field: string, value: any) => {
    const updatedFilters = [...filters];
    updatedFilters[index] = { ...updatedFilters[index], [field]: value };
    setFilters(updatedFilters);
    setValue('filters', updatedFilters);
  };

  // معالجة تقديم النموذج
  const onSubmit = async (data: ReportTemplateValues) => {
    try {
      if (isEditMode) {
        await updateReportTemplateMutation.mutateAsync({
          id: id!,
          data,
        });
      } else {
        await createReportTemplateMutation.mutateAsync(data);
      }

      navigate('/reports');
    } catch (error) {
      console.error('Error saving report template:', error);
    }
  };

  // معالجة فتح حوار التأكيد
  const handleOpenConfirmDialog = () => {
    setConfirmDialogOpen(true);
  };

  // معالجة إغلاق حوار التأكيد
  const handleCloseConfirmDialog = () => {
    setConfirmDialogOpen(false);
  };

  if (isEditMode && isLoadingTemplate) {
    return <LoadingIndicator />;
  }

  return (
    <Container maxWidth="xl">
      <PageHeader
        title={isEditMode ? t('reports.edit.title') : t('reports.create.title')}
        subtitle={isEditMode ? (reportTemplate as any)?.title : t('reports.create.subtitle')}
        backButton={{
          to: '/reports',
          label: t('common.backToList'),
        }}
      />

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={3}>
          <Grid item xs={12} md={showPreview || showCode ? 6 : 12}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  {t('reports.properties')}
                </Typography>
                <Box>
                  <Tooltip title={t('reports.preview')}>
                    <IconButton
                      color={showPreview ? 'primary' : 'default'}
                      onClick={() => {
                        setShowPreview(!showPreview);
                        setShowCode(false);
                      }}
                    >
                      <VisibilityIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title={t('reports.code')}>
                    <IconButton
                      color={showCode ? 'primary' : 'default'}
                      onClick={() => {
                        setShowCode(!showCode);
                        setShowPreview(false);
                      }}
                    >
                      <CodeIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('reports.fields.title')}
                        fullWidth
                        required
                        error={!!errors.title}
                        helperText={errors.title?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <Controller
                    name="type"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.type}>
                        <InputLabel>{t('reports.fields.type')}</InputLabel>
                        <Select
                          {...field}
                          label={t('reports.fields.type')}
                        >
                          {Object.values(ReportType).map((type) => (
                            <MenuItem key={type} value={type}>
                              {t(`reports.types.${type.toLowerCase()}`)}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <Controller
                    name="isPublished"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Switch
                            checked={field.value}
                            onChange={(e) => field.onChange(e.target.checked)}
                          />
                        }
                        label={t('reports.fields.isPublished')}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('reports.fields.description')}
                        fullWidth
                        multiline
                        rows={2}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="dataSource"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.dataSource}>
                        <InputLabel>{t('reports.fields.dataSource')}</InputLabel>
                        <Select
                          {...field}
                          label={t('reports.fields.dataSource')}
                        >
                          {Object.values(DataSource).map((source) => (
                            <MenuItem key={source} value={source}>
                              {t(`reports.dataSources.${source.toLowerCase()}`)}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <Controller
                    name="query"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('reports.fields.query')}
                        fullWidth
                        placeholder={t('reports.queryPlaceholder')}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </Paper>

            <Paper sx={{ p: 3, mb: 3 }}>
              <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
                <Tab
                  icon={<TableChartIcon />}
                  label={t('reports.tabs.columns')}
                  id="tab-0"
                  aria-controls="tabpanel-0"
                />
                <Tab
                  icon={<BarChartIcon />}
                  label={t('reports.tabs.chart')}
                  id="tab-1"
                  aria-controls="tabpanel-1"
                />
                <Tab
                  icon={<FilterListIcon />}
                  label={t('reports.tabs.filters')}
                  id="tab-2"
                  aria-controls="tabpanel-2"
                />
              </Tabs>

              <Box role="tabpanel" hidden={activeTab !== 0} id="tabpanel-0" aria-labelledby="tab-0">
                {activeTab === 0 && (
                  <>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="subtitle1">
                        {t('reports.columns')}
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<AddIcon />}
                        onClick={handleAddColumn}
                      >
                        {t('reports.addColumn')}
                      </Button>
                    </Box>

                    {errors.columns && !Array.isArray(errors.columns) && (
                      <Typography color="error" sx={{ mb: 2 }}>
                        {errors.columns.message}
                      </Typography>
                    )}

                    {columns.length === 0 ? (
                      <Card sx={{ mb: 2, bgcolor: 'background.default' }}>
                        <CardContent sx={{ textAlign: 'center' }}>
                          <Typography color="text.secondary">
                            {t('reports.noColumns')}
                          </Typography>
                          <Button
                            variant="text"
                            startIcon={<AddIcon />}
                            onClick={handleAddColumn}
                            sx={{ mt: 1 }}
                          >
                            {t('reports.addColumn')}
                          </Button>
                        </CardContent>
                      </Card>
                    ) : (
                      columns.map((column, index) => (
                        <Paper key={column.id} sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                          <Grid container spacing={2} alignItems="center">
                            <Grid item xs={12} md={3}>
                              <TextField
                                label={t('reports.fields.columnLabel')}
                                value={column.label}
                                onChange={(e) => handleColumnChange(index, 'label', e.target.value)}
                                fullWidth
                                required
                                error={!!errors.columns?.[index]?.label}
                                helperText={errors.columns?.[index]?.label?.message}
                              />
                            </Grid>

                            <Grid item xs={12} md={3}>
                              <TextField
                                label={t('reports.fields.columnName')}
                                value={column.name}
                                onChange={(e) => handleColumnChange(index, 'name', e.target.value)}
                                fullWidth
                                required
                                error={!!errors.columns?.[index]?.name}
                                helperText={errors.columns?.[index]?.name?.message}
                              />
                            </Grid>

                            <Grid item xs={12} md={2}>
                              <FormControl fullWidth>
                                <InputLabel>{t('reports.fields.columnType')}</InputLabel>
                                <Select
                                  value={column.type}
                                  onChange={(e) => handleColumnChange(index, 'type', e.target.value)}
                                  label={t('reports.fields.columnType')}
                                >
                                  <MenuItem value="string">{t('reports.columnTypes.string')}</MenuItem>
                                  <MenuItem value="number">{t('reports.columnTypes.number')}</MenuItem>
                                  <MenuItem value="date">{t('reports.columnTypes.date')}</MenuItem>
                                  <MenuItem value="boolean">{t('reports.columnTypes.boolean')}</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={6} md={1}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={column.sortable}
                                    onChange={(e) => handleColumnChange(index, 'sortable', e.target.checked)}
                                  />
                                }
                                label={t('reports.fields.sortable')}
                              />
                            </Grid>

                            <Grid item xs={6} md={2}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={column.filterable}
                                    onChange={(e) => handleColumnChange(index, 'filterable', e.target.checked)}
                                  />
                                }
                                label={t('reports.fields.filterable')}
                              />
                            </Grid>

                            <Grid item xs={12} md={1}>
                              <IconButton
                                color="error"
                                onClick={() => handleDeleteColumn(index)}
                                disabled={columns.length <= 1}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Grid>
                          </Grid>
                        </Paper>
                      ))
                    )}
                  </>
                )}
              </Box>

              <Box role="tabpanel" hidden={activeTab !== 1} id="tabpanel-1" aria-labelledby="tab-1">
                {activeTab === 1 && (
                  <>
                    <Typography variant="subtitle1" gutterBottom>
                      {t('reports.chart')}
                    </Typography>

                    <Grid container spacing={2}>
                      <Grid item xs={12} md={4}>
                        <Controller
                          name="chart.type"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth error={!!errors.chart?.type}>
                              <InputLabel>{t('reports.fields.chartType')}</InputLabel>
                              <Select
                                {...field}
                                label={t('reports.fields.chartType')}
                              >
                                {Object.values(ChartType).map((type) => (
                                  <MenuItem key={type} value={type}>
                                    {t(`reports.chartTypes.${type.toLowerCase()}`)}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} md={8}>
                        <Controller
                          name="chart.title"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label={t('reports.fields.chartTitle')}
                              fullWidth
                              error={!!errors.chart?.title}
                              helperText={errors.chart?.title?.message}
                            />
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Controller
                          name="chart.xAxis"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth error={!!errors.chart?.xAxis}>
                              <InputLabel>{t('reports.fields.xAxis')}</InputLabel>
                              <Select
                                {...field}
                                label={t('reports.fields.xAxis')}
                              >
                                {columns.map((column) => (
                                  <MenuItem key={column.id} value={column.name}>
                                    {column.label}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Controller
                          name="chart.yAxis"
                          control={control}
                          render={({ field }) => (
                            <FormControl fullWidth error={!!errors.chart?.yAxis}>
                              <InputLabel>{t('reports.fields.yAxis')}</InputLabel>
                              <Select
                                {...field}
                                label={t('reports.fields.yAxis')}
                              >
                                {columns.filter(c => c.type === 'number').map((column) => (
                                  <MenuItem key={column.id} value={column.name}>
                                    {column.label}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        />
                      </Grid>
                    </Grid>
                  </>
                )}
              </Box>

              <Box role="tabpanel" hidden={activeTab !== 2} id="tabpanel-2" aria-labelledby="tab-2">
                {activeTab === 2 && (
                  <>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="subtitle1">
                        {t('reports.filters')}
                      </Typography>
                      <Button
                        variant="outlined"
                        startIcon={<AddIcon />}
                        onClick={handleAddFilter}
                      >
                        {t('reports.addFilter')}
                      </Button>
                    </Box>

                    {filters.length === 0 ? (
                      <Card sx={{ mb: 2, bgcolor: 'background.default' }}>
                        <CardContent sx={{ textAlign: 'center' }}>
                          <Typography color="text.secondary">
                            {t('reports.noFilters')}
                          </Typography>
                          <Button
                            variant="text"
                            startIcon={<AddIcon />}
                            onClick={handleAddFilter}
                            sx={{ mt: 1 }}
                          >
                            {t('reports.addFilter')}
                          </Button>
                        </CardContent>
                      </Card>
                    ) : (
                      filters.map((filter, index) => (
                        <Paper key={filter.id} sx={{ p: 2, mb: 2, bgcolor: 'background.default' }}>
                          <Grid container spacing={2} alignItems="center">
                            <Grid item xs={12} md={4}>
                              <FormControl fullWidth>
                                <InputLabel>{t('reports.fields.filterField')}</InputLabel>
                                <Select
                                  value={filter.field}
                                  onChange={(e) => handleFilterChange(index, 'field', e.target.value)}
                                  label={t('reports.fields.filterField')}
                                >
                                  {columns.filter(c => c.filterable).map((column) => (
                                    <MenuItem key={column.id} value={column.name}>
                                      {column.label}
                                    </MenuItem>
                                  ))}
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={3}>
                              <FormControl fullWidth>
                                <InputLabel>{t('reports.fields.filterOperator')}</InputLabel>
                                <Select
                                  value={filter.operator}
                                  onChange={(e) => handleFilterChange(index, 'operator', e.target.value)}
                                  label={t('reports.fields.filterOperator')}
                                >
                                  <MenuItem value="equals">{t('reports.operators.equals')}</MenuItem>
                                  <MenuItem value="notEquals">{t('reports.operators.notEquals')}</MenuItem>
                                  <MenuItem value="contains">{t('reports.operators.contains')}</MenuItem>
                                  <MenuItem value="greaterThan">{t('reports.operators.greaterThan')}</MenuItem>
                                  <MenuItem value="lessThan">{t('reports.operators.lessThan')}</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <TextField
                                label={t('reports.fields.filterValue')}
                                value={filter.value}
                                onChange={(e) => handleFilterChange(index, 'value', e.target.value)}
                                fullWidth
                              />
                            </Grid>

                            <Grid item xs={12} md={1}>
                              <IconButton
                                color="error"
                                onClick={() => handleDeleteFilter(index)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Grid>
                          </Grid>
                        </Paper>
                      ))
                    )}
                  </>
                )}
              </Box>
            </Paper>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mb: 3 }}>
              <Button
                variant="outlined"
                startIcon={<CancelIcon />}
                onClick={handleOpenConfirmDialog}
              >
                {t('common.cancel')}
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                disabled={isSubmitting}
              >
                {isSubmitting
                  ? t('common.saving')
                  : isEditMode
                  ? t('common.update')
                  : t('common.save')}
              </Button>
            </Box>
          </Grid>

          {showPreview && (
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, mb: 3, position: 'sticky', top: 20 }}>
                <Typography variant="h6" gutterBottom>
                  {t('reports.preview')}
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <ReportPreview
                  title={formValues.title}
                  description={formValues.description}
                  type={formValues.type}
                  columns={formValues.columns}
                  chart={formValues.chart}
                />
              </Paper>
            </Grid>
          )}

          {showCode && (
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 3, mb: 3, position: 'sticky', top: 20 }}>
                <Typography variant="h6" gutterBottom>
                  {t('reports.code')}
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <CodeEditor
                  value={JSON.stringify(formValues, null, 2)}
                  language="json"
                  readOnly
                />
              </Paper>
            </Grid>
          )}
        </Grid>
      </Box>

      <ConfirmDialog
        open={confirmDialogOpen}
        title={t('reports.cancelConfirmTitle')}
        message={t('reports.cancelConfirmMessage')}
        onConfirm={() => navigate('/reports')}
        onCancel={handleCloseConfirmDialog}
      />
    </Container>
  );
};

export default ReportBuilderPage;
