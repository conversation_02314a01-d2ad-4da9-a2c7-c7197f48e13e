import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useGuarantee, useCreateGuarantee, useUpdateGuarantee } from '../hooks/useGuarantees';
import { GuaranteeFormValues, GuaranteeType, GuaranteeNature, GuaranteeCurrency } from '../types/guarantee.types';
import { useDeclarations } from '../../declarations/hooks/useDeclarations';

// مخطط التحقق من صحة نموذج الضمان
const guaranteeFormSchema = z.object({
  declarationId: z.string({
    required_error: 'معرف البيان مطلوب',
  }),
  guaranteeType: z.nativeEnum(GuaranteeType, {
    required_error: 'نوع الضمان مطلوب',
  }),
  guaranteeNature: z.nativeEnum(GuaranteeNature, {
    required_error: 'طبيعة الضمان مطلوبة',
  }),
  guaranteeNumber: z.string({
    required_error: 'رقم الضمان مطلوب',
  }),
  issueDate: z.date({
    required_error: 'تاريخ الإصدار مطلوب',
  }),
  expiryDate: z.date().nullable(),
  amount: z.number({
    required_error: 'مبلغ الضمان مطلوب',
  }),
  currency: z.nativeEnum(GuaranteeCurrency, {
    required_error: 'عملة الضمان مطلوبة',
  }),
  notes: z.string().optional(),
  file: z.any().nullable(),
  isReturned: z.boolean().optional(),
  returnDate: z.date().nullable(),
});

const GuaranteeFormPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  // حالة الملف المحدد
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);

  // استخدام خطافات الضمانات
  const { data: guarantee, isLoading: isLoadingGuarantee } = useGuarantee(id || '');
  const createMutation = useCreateGuarantee();
  const updateMutation = useUpdateGuarantee();

  // استخدام خطاف البيانات للحصول على قائمة البيانات
  const { data: declarationsData } = useDeclarations({
    limit: 100,
  });

  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<GuaranteeFormValues>({
    resolver: zodResolver(guaranteeFormSchema),
    defaultValues: {
      declarationId: '',
      guaranteeType: GuaranteeType.RETURNABLE,
      guaranteeNature: GuaranteeNature.DOCUMENTS,
      guaranteeNumber: '',
      issueDate: null,
      expiryDate: null,
      amount: 0,
      currency: GuaranteeCurrency.SAR,
      notes: '',
      file: null,
      isReturned: false,
      returnDate: null,
    },
  });

  // مراقبة قيمة isReturned
  const isReturned = watch('isReturned');

  // تحديث النموذج عند تحميل بيانات الضمان
  useEffect(() => {
    if (isEditMode && guarantee) {
      reset({
        declarationId: guarantee.data.declarationId,
        guaranteeType: guarantee.data.guaranteeType,
        guaranteeNature: guarantee.data.guaranteeNature,
        guaranteeNumber: guarantee.data.guaranteeNumber,
        issueDate: new Date(guarantee.data.issueDate),
        expiryDate: guarantee.data.expiryDate ? new Date(guarantee.data.expiryDate) : null,
        amount: guarantee.data.amount,
        currency: guarantee.data.currency,
        notes: guarantee.data.notes || '',
        file: null,
        isReturned: guarantee.data.isReturned,
        returnDate: guarantee.data.returnDate ? new Date(guarantee.data.returnDate) : null,
      });

      if (guarantee.data.pdfFile) {
        setPdfUrl(`/api/guarantees/pdf/${guarantee.data.id}`);
      }
    }
  }, [isEditMode, guarantee, reset]);

  // التعامل مع تغيير الملف
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedFile(file);
      setValue('file', file);
    }
  };

  // التعامل مع تقديم النموذج
  const onSubmit = async (data: GuaranteeFormValues) => {
    try {
      if (isEditMode && id) {
        // تحديث الضمان
        await updateMutation.mutateAsync({
          id,
          data: {
            declarationId: data.declarationId,
            guaranteeType: data.guaranteeType,
            guaranteeNature: data.guaranteeNature,
            guaranteeNumber: data.guaranteeNumber,
            issueDate: data.issueDate?.toISOString() || '',
            expiryDate: data.expiryDate?.toISOString(),
            amount: data.amount,
            currency: data.currency,
            notes: data.notes,
            isReturned: data.isReturned,
            returnDate: data.returnDate?.toISOString(),
          },
          file: selectedFile || undefined,
        });
      } else {
        // إنشاء ضمان جديد
        await createMutation.mutateAsync({
          data: {
            declarationId: data.declarationId,
            guaranteeType: data.guaranteeType,
            guaranteeNature: data.guaranteeNature,
            guaranteeNumber: data.guaranteeNumber,
            issueDate: data.issueDate?.toISOString() || '',
            expiryDate: data.expiryDate?.toISOString(),
            amount: data.amount,
            currency: data.currency,
            notes: data.notes,
          },
          file: selectedFile || undefined,
        });
      }

      // العودة إلى صفحة قائمة الضمانات
      navigate('/guarantees');
    } catch (error) {
      console.error('Error submitting guarantee form:', error);
    }
  };

  // التعامل مع العودة إلى قائمة الضمانات
  const handleBack = () => {
    navigate('/guarantees');
  };

  // عرض رسالة التحميل
  if (isEditMode && isLoadingGuarantee) {
    return (
      <Container maxWidth="lg">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {isEditMode ? t('guarantees.edit') : t('guarantees.create')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {isEditMode ? t('guarantees.editDescription') : t('guarantees.createDescription')}
        </Typography>
      </Box>

      <Paper sx={{ p: 3 }}>
        <Box component="form" noValidate onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            {/* البيان */}
            <Grid item xs={12} md={6}>
              <Controller
                name="declarationId"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.declarationId}>
                    <InputLabel>{t('guarantees.declaration')}</InputLabel>
                    <Select
                      {...field}
                      label={t('guarantees.declaration')}
                      disabled={isEditMode}
                    >
                      {declarationsData?.data.map((declaration) => (
                        <MenuItem key={declaration.id} value={declaration.id}>
                          {t('declarations.number')}: {declaration.declarationNumber} - {declaration.clientName}
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.declarationId && (
                      <FormHelperText>{errors.declarationId.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            {/* نوع الضمان */}
            <Grid item xs={12} md={6}>
              <Controller
                name="guaranteeType"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.guaranteeType}>
                    <InputLabel>{t('guarantees.guaranteeType')}</InputLabel>
                    <Select
                      {...field}
                      label={t('guarantees.guaranteeType')}
                    >
                      <MenuItem value={GuaranteeType.RETURNABLE}>
                        {t('guarantees.returnable')}
                      </MenuItem>
                      <MenuItem value={GuaranteeType.NON_RETURNABLE}>
                        {t('guarantees.nonReturnable')}
                      </MenuItem>
                    </Select>
                    {errors.guaranteeType && (
                      <FormHelperText>{errors.guaranteeType.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            {/* طبيعة الضمان */}
            <Grid item xs={12} md={6}>
              <Controller
                name="guaranteeNature"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.guaranteeNature}>
                    <InputLabel>{t('guarantees.guaranteeNature')}</InputLabel>
                    <Select
                      {...field}
                      label={t('guarantees.guaranteeNature')}
                    >
                      <MenuItem value={GuaranteeNature.DOCUMENTS}>
                        {t('guarantees.documents')}
                      </MenuItem>
                      <MenuItem value={GuaranteeNature.FINANCIAL}>
                        {t('guarantees.financial')}
                      </MenuItem>
                    </Select>
                    {errors.guaranteeNature && (
                      <FormHelperText>{errors.guaranteeNature.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            {/* رقم الضمان */}
            <Grid item xs={12} md={6}>
              <Controller
                name="guaranteeNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label={t('guarantees.guaranteeNumber')}
                    error={!!errors.guaranteeNumber}
                    helperText={errors.guaranteeNumber?.message}
                  />
                )}
              />
            </Grid>

            {/* تاريخ الإصدار */}
            <Grid item xs={12} md={6}>
              <Controller
                name="issueDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label={t('guarantees.issueDate')}
                    value={field.value}
                    onChange={(date) => field.onChange(date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.issueDate,
                        helperText: errors.issueDate?.message,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* تاريخ الانتهاء */}
            <Grid item xs={12} md={6}>
              <Controller
                name="expiryDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label={t('guarantees.expiryDate')}
                    value={field.value}
                    onChange={(date) => field.onChange(date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.expiryDate,
                        helperText: errors.expiryDate?.message,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* مبلغ الضمان */}
            <Grid item xs={12} md={6}>
              <Controller
                name="amount"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label={t('guarantees.amount')}
                    error={!!errors.amount}
                    helperText={errors.amount?.message}
                    onChange={(e) => field.onChange(parseFloat(e.target.value))}
                  />
                )}
              />
            </Grid>

            {/* العملة */}
            <Grid item xs={12} md={6}>
              <Controller
                name="currency"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.currency}>
                    <InputLabel>{t('guarantees.currency')}</InputLabel>
                    <Select
                      {...field}
                      label={t('guarantees.currency')}
                    >
                      <MenuItem value={GuaranteeCurrency.SAR}>
                        {t('guarantees.sar')}
                      </MenuItem>
                      <MenuItem value={GuaranteeCurrency.USD}>
                        {t('guarantees.usd')}
                      </MenuItem>
                      <MenuItem value={GuaranteeCurrency.EUR}>
                        {t('guarantees.eur')}
                      </MenuItem>
                      <MenuItem value={GuaranteeCurrency.GBP}>
                        {t('guarantees.gbp')}
                      </MenuItem>
                    </Select>
                    {errors.currency && (
                      <FormHelperText>{errors.currency.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            {/* ملاحظات */}
            <Grid item xs={12}>
              <Controller
                name="notes"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    multiline
                    rows={4}
                    label={t('guarantees.notes')}
                    error={!!errors.notes}
                    helperText={errors.notes?.message}
                  />
                )}
              />
            </Grid>

            {/* ملف PDF */}
            <Grid item xs={12}>
              <Button
                variant="outlined"
                component="label"
                startIcon={<UploadIcon />}
              >
                {t('common.uploadPdf')}
                <input
                  type="file"
                  accept="application/pdf"
                  hidden
                  onChange={handleFileChange}
                />
              </Button>
              {selectedFile && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {t('common.selectedFile')}: {selectedFile.name}
                </Typography>
              )}
              {pdfUrl && !selectedFile && (
                <Box display="flex" alignItems="center" mt={1}>
                  <Typography variant="body2" sx={{ mr: 1 }}>
                    {t('common.currentFile')}:
                  </Typography>
                  <Button
                    variant="text"
                    size="small"
                    href={pdfUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {t('common.viewPdf')}
                  </Button>
                </Box>
              )}
            </Grid>

            {/* حالة الاسترجاع (في وضع التعديل فقط) */}
            {isEditMode && (
              <>
                <Grid item xs={12}>
                  <Controller
                    name="isReturned"
                    control={control}
                    render={({ field }) => (
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={field.value}
                            onChange={(e) => field.onChange(e.target.checked)}
                          />
                        }
                        label={t('guarantees.isReturned')}
                      />
                    )}
                  />
                </Grid>

                {/* تاريخ الاسترجاع (إذا كان مسترجعًا) */}
                {isReturned && (
                  <Grid item xs={12} md={6}>
                    <Controller
                      name="returnDate"
                      control={control}
                      render={({ field }) => (
                        <DatePicker
                          label={t('guarantees.returnDate')}
                          value={field.value}
                          onChange={(date) => field.onChange(date)}
                          slotProps={{
                            textField: {
                              fullWidth: true,
                              error: !!errors.returnDate,
                              helperText: errors.returnDate?.message,
                            },
                          }}
                        />
                      )}
                    />
                  </Grid>
                )}
              </>
            )}

            {/* أزرار الإجراءات */}
            <Grid item xs={12}>
              <Box display="flex" justifyContent="flex-end" mt={2}>
                <Button
                  variant="outlined"
                  onClick={handleBack}
                  startIcon={<ArrowBackIcon />}
                  sx={{ mr: 1 }}
                >
                  {t('common.cancel')}
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <CircularProgress size={24} />
                  ) : (
                    t('common.save')
                  )}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Container>
  );
};

export default GuaranteeFormPage;
