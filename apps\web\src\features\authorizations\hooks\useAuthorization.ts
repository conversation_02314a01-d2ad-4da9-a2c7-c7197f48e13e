import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api/api';

export interface Authorization {
  id: string;
  declarationId: string;
  authorizationNumber: string;
  status: string;
  issuedDate: string;
  expiryDate?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  declaration?: {
    declarationNumber: string;
  };
  authorizationDate?: string;
  startDate?: string;
  endDate?: string;
  authorizedPerson?: string;
  authorizedPersonId?: string;
  pdfFile?: string;
}

export const useAuthorization = (id: string) => {
  return useQuery({
    queryKey: ['authorization', id],
    queryFn: async (): Promise<Authorization> => {
      const response = await api.get<Authorization>(`/api/authorizations/${id}`);
      return response;
    },
    enabled: !!id,
  });
};
