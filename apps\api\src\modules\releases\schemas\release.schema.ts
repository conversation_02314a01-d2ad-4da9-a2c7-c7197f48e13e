import { z } from 'zod';

// مخطط إنشاء إفراج جديد
export const createReleaseSchema = z.object({
  body: z.object({
    declarationId: z.string({
      required_error: 'معرف البيان مطلوب',
    }).uuid(),
    releaseDate: z.string({
      required_error: 'تاريخ الإفراج مطلوب',
    }).transform(val => new Date(val)),
    startDate: z.string({
      required_error: 'تاريخ بداية سند الإفراج مطلوب',
    }).transform(val => new Date(val)),
    endDate: z.string({
      required_error: 'تاريخ نهاية سند الإفراج مطلوب',
    }).transform(val => new Date(val)),
    notes: z.string().optional(),
  }),
});

// مخطط تحديث إفراج
export const updateReleaseSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف الإفراج مطلوب',
    }),
  }),
  body: z.object({
    declarationId: z.string().uuid().optional(),
    releaseDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    notes: z.string().optional(),
  }),
});

// مخطط الحصول على إفراج محدد
export const getReleaseSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف الإفراج مطلوب',
    }),
  }),
});

// مخطط حذف إفراج
export const deleteReleaseSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف الإفراج مطلوب',
    }),
  }),
});

// مخطط قائمة الإفراجات
export const listReleasesSchema = z.object({
  query: z.object({
    page: z.string().optional().transform((val) => (val ? parseInt(val) : 1)),
    limit: z.string().optional().transform((val) => (val ? parseInt(val) : 10)),
    sort: z.string().optional().default('releaseNumber'),
    order: z.enum(['asc', 'desc']).optional().default('desc'),
    search: z.string().optional(),
    declarationId: z.string().uuid().optional(),
    fromDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    toDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    isActive: z.string().optional().transform((val) => {
      if (val === 'true') return true;
      if (val === 'false') return false;
      return undefined;
    }),
  }),
});
