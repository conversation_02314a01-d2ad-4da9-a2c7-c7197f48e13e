/** @type {import('ts-jest').JestConfigWithTsJest} */
export default {
  preset: 'ts-jest/presets/default-esm',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],

  // إعدادات ES Modules
  extensionsToTreatAsEsm: ['.ts'],

  // إضافة Jest globals
  injectGlobals: true,

  // إعدادات التحويل المحسنة (إزالة globals المهجور)
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      useESM: true,
      tsconfig: {
        module: 'esnext',
        target: 'es2020',
        isolatedModules: true,
      },
    }],
  },

  // إعدادات الوحدات
  moduleNameMapper: {
    '^@prisma/client$': '<rootDir>/src/core/utils/__mocks__/@prisma/client.ts',
    '^../services/auth\\.service\\.js$': '<rootDir>/src/core/utils/__mocks__/auth.service.ts',
    '^../../services/auth\\.service\\.js$': '<rootDir>/src/core/utils/__mocks__/auth.service.ts',
    '^(\\.{1,2}/.*)\\.js$': '$1',
  },

  // إعدادات الاختبارات
  testRegex: '(/tests/.*|(\\.|/)(test|spec))\\.ts$',
  moduleFileExtensions: ['ts', 'js', 'json', 'node'],

  // تحسينات الأداء المحسنة
  maxWorkers: process.env.CI ? 1 : 2, // زيادة عدد العمال قليلاً لتحسين الأداء
  cache: true, // تفعيل التخزين المؤقت
  cacheDirectory: '<rootDir>/.jest-cache',
  workerIdleMemoryLimit: '2GB', // تقليل حد الذاكرة لتحسين الاستقرار
  detectOpenHandles: false, // إيقاف كشف المقابض لتحسين الأداء

  // إعدادات timeout عامة
  testTimeout: 120000, // 2 دقيقة للاختبارات
  forceExit: true, // إجبار الخروج لتجنب التعليق

  // تحسينات إضافية للسرعة
  watchman: false, // إيقاف watchman لتحسين الأداء
  haste: {
    computeSha1: false, // إيقاف حساب SHA1 لتحسين الأداء
  },

  // تحسينات إضافية للاستقرار
  verbose: false, // تقليل المخرجات لتحسين الأداء
  silent: false, // السماح بالمخرجات المهمة
  bail: false, // عدم التوقف عند أول خطأ

  // تحسينات إضافية للاستقرار
  clearMocks: true, // تنظيف المحاكيات بين الاختبارات
  resetMocks: true, // إعادة تعيين المحاكيات
  restoreMocks: true, // استعادة المحاكيات الأصلية

  // إعدادات البيئة
  setupFilesAfterEnv: ['<rootDir>/src/core/utils/test/setup.ts'],

  // إعدادات التغطية
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/server.ts',
    '!src/core/utils/test/**',
    '!src/**/__mocks__/**',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },

  // تكوين مشاريع منفصلة للاختبارات
  projects: [
    {
      displayName: 'unit',
      preset: 'ts-jest/presets/default-esm',
      testEnvironment: 'node',
      testMatch: ['<rootDir>/src/**/tests/**/*.test.ts'],
      setupFilesAfterEnv: ['<rootDir>/src/core/utils/test/setup.ts'],
      extensionsToTreatAsEsm: ['.ts'],
      // تحسينات الأداء للاختبارات الوحدة
      maxWorkers: '75%', // استخدام معالجات أكثر للاختبارات الوحدة

      transform: {
        '^.+\\.ts$': ['ts-jest', {
          useESM: true,
          tsconfig: {
            module: 'esnext',
            target: 'es2020',
            isolatedModules: true,
          },
        }],
      },
      moduleNameMapper: {
        '^@prisma/client$': '<rootDir>/src/core/utils/__mocks__/@prisma/client.ts',
        '^(\\.{1,2}/.*)\\.js$': '$1',
      },
    },
    {
      displayName: 'integration',
      preset: 'ts-jest/presets/default-esm',
      testEnvironment: 'node',
      testMatch: ['<rootDir>/src/**/tests/**/*.integration.test.ts'],
      setupFilesAfterEnv: ['<rootDir>/src/core/utils/test/integration-test-setup.ts'],
      extensionsToTreatAsEsm: ['.ts'],
      // تحسينات الأداء لاختبارات التكامل
      maxWorkers: 1, // تشغيل متسلسل لاختبارات التكامل لتجنب تضارب قاعدة البيانات
      testTimeout: 180000, // 3 دقائق لاختبارات التكامل
      // تحسينات إضافية للاستقرار
      clearMocks: true,
      resetMocks: true,
      restoreMocks: true,
      detectOpenHandles: true,
      forceExit: true,
      transform: {
        '^.+\\.ts$': ['ts-jest', {
          useESM: true,
          tsconfig: {
            module: 'esnext',
            target: 'es2020',
            isolatedModules: true,
          },
        }],
      },
      moduleNameMapper: {
        '^(\\.{1,2}/.*)\\.js$': '$1',
      },
    },
  ],
};
