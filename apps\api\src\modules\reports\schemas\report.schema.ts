import { z } from 'zod';

/**
 * مخطط إنشاء تقرير
 */
export const generateReportSchema = z.object({
  query: z.object({
    reportType: z.enum([
      'DECLARATIONS',
      'ITEM_MOVEMENTS',
      'AUTHORIZATIONS',
      'RELEASES',
      'PERMITS',
      'GUARANTEES',
      'RECEIPTS',
      'CLIENTS',
      'DOCUMENTS',
    ], {
      required_error: 'نوع التقرير مطلوب',
    }),
    format: z.enum(['PDF', 'EXCEL', 'CSV'], {
      required_error: 'صيغة التقرير مطلوبة',
    }),
    fromDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    toDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    clientId: z.string().uuid().optional(),
    declarationType: z.enum(['IMPORT', 'EXPORT']).optional(),
    goodsType: z.enum([
      'HUMAN_MEDICINE',
      'LABORATORY_SOLUTIONS',
      'MEDICAL_SUPPLIES',
      'SUGAR_STRIPS',
      'MEDICAL_DEVICES',
      'MISCELLANEOUS',
    ]).optional(),
    includeDetails: z.string().optional().transform((val) => val === 'true'),
  }),
});
