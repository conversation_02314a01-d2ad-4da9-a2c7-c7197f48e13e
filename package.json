{"name": "alnoor-archive", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "start": "turbo run start", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "lint:strict": "eslint --max-warnings=0 .", "test": "turbo run test", "test:unit": "turbo run test:unit", "test:integration": "turbo run test:integration", "test:coverage": "turbo run test:coverage", "format": "prettier --write \"**/*.{ts,tsx,md,json,js,jsx,yml,yaml,css,html}\"", "format:check": "prettier --check \"**/*.{ts,tsx,md,json,js,jsx,yml,yaml,css,html}\"", "prepare": "husky install"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unicorn": "^59.0.1", "husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "turbo": "^2.5.3", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}, "packageManager": "pnpm@8.15.4", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --max-warnings=0", "prettier --write"], "*.{json,md,yml,yaml,css,html}": ["prettier --write"]}}