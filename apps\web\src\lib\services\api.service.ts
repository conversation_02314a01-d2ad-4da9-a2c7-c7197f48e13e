import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * خدمة API المشتركة
 * توفر واجهة موحدة للتعامل مع طلبات API
 */
class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

    // إنشاء نسخة من Axios مع الإعدادات الافتراضية
    this.api = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // إضافة معترض الطلبات
    this.api.interceptors.request.use(
      (config) => {
        // الحصول على التوكن من التخزين المحلي
        const token = localStorage.getItem('token');

        // إضافة التوكن إلى رأس الطلب إذا كان موجودًا
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // إضافة معترض الاستجابات
    this.api.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        // معالجة أخطاء الاستجابة
        if (error.response) {
          // خطأ 401 (غير مصرح)
          if (error.response.status === 401) {
            // محاولة تجديد التوكن
            const refreshToken = localStorage.getItem('refreshToken');

            if (refreshToken) {
              try {
                // محاولة تجديد التوكن
                const response = await axios.post(`${this.baseURL}/api/auth/refresh-token`, {
                  refreshToken,
                });

                // تحديث التوكن في التخزين المحلي
                const { token } = response.data;
                localStorage.setItem('token', token);

                // إعادة محاولة الطلب الأصلي
                if (error.config) {
                  error.config.headers.Authorization = `Bearer ${token}`;
                  return this.api(error.config);
                }
              } catch (refreshError) {
                // فشل تجديد التوكن، تسجيل الخروج
                localStorage.removeItem('token');
                localStorage.removeItem('refreshToken');
                window.location.href = '/login';
              }
            } else {
              // لا يوجد توكن تجديد، تسجيل الخروج
              localStorage.removeItem('token');
              window.location.href = '/login';
            }
          }
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * إرسال طلب GET
   * @param url مسار الطلب
   * @param config إعدادات الطلب
   * @returns وعد بالاستجابة
   */
  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.get(url, config);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError);
      throw error;
    }
  }

  /**
   * إرسال طلب POST
   * @param url مسار الطلب
   * @param data البيانات المرسلة
   * @param config إعدادات الطلب
   * @returns وعد بالاستجابة
   */
  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.post(url, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError);
      throw error;
    }
  }

  /**
   * إرسال طلب PUT
   * @param url مسار الطلب
   * @param data البيانات المرسلة
   * @param config إعدادات الطلب
   * @returns وعد بالاستجابة
   */
  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.put(url, data, config);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError);
      throw error;
    }
  }

  /**
   * إرسال طلب DELETE
   * @param url مسار الطلب
   * @param config إعدادات الطلب
   * @returns وعد بالاستجابة
   */
  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.delete(url, config);
      return response.data;
    } catch (error) {
      this.handleError(error as AxiosError);
      throw error;
    }
  }

  /**
   * معالجة أخطاء الطلبات
   * @param error خطأ الطلب
   */
  private handleError(error: AxiosError): void {
    if (error.response) {
      // خطأ من الخادم
      console.error('Server Error:', error.response.data);
    } else if (error.request) {
      // لم يتم استلام استجابة
      console.error('No Response:', error.request);
    } else {
      // خطأ في إعداد الطلب
      console.error('Request Error:', error.message);
    }
  }

  /**
   * الحصول على عنوان URL الأساسي
   * @returns عنوان URL الأساسي
   */
  public getBaseURL(): string {
    return this.baseURL;
  }
}

// إنشاء نسخة واحدة من الخدمة
const apiService = new ApiService();

export default apiService;
