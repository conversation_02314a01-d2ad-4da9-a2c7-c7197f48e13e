import { format as formatDateFn, parseISO, isValid, compareAsc, addDays as addDaysDateFns } from 'date-fns';
import { arSA } from 'date-fns/locale';

/**
 * تنسيق التاريخ بالصيغة المطلوبة
 * @param date التاريخ المراد تنسيقه
 * @param formatStr صيغة التنسيق
 * @returns التاريخ المنسق
 */
export const formatDate = (date: Date | string | null | undefined, formatStr: string = 'yyyy-MM-dd'): string => {
  if (!date) return '';

  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    return formatDateFn(dateObj, formatStr, { locale: arSA });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * تحويل التاريخ إلى صيغة ISO
 * @param date التاريخ المراد تحويله
 * @returns التاريخ بصيغة ISO
 */
export const toISODate = (date: Date | string | null | undefined): string | undefined => {
  if (!date) return undefined;

  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return undefined;
    return dateObj.toISOString();
  } catch (error) {
    console.error('Error converting date to ISO:', error);
    return undefined;
  }
};

/**
 * تحويل التاريخ إلى كائن Date
 * @param date التاريخ المراد تحويله
 * @returns كائن Date
 */
export const parseDate = (date: string | null | undefined): Date | null => {
  if (!date) return null;

  try {
    const dateObj = parseISO(date);
    return isValid(dateObj) ? dateObj : null;
  } catch (error) {
    console.error('Error parsing date:', error);
    return null;
  }
};

/**
 * الحصول على التاريخ الحالي
 * @returns التاريخ الحالي
 */
export const getCurrentDate = (): Date => {
  const now = new Date();
  now.setHours(0, 0, 0, 0);
  return now;
};

/**
 * إضافة أيام إلى التاريخ
 * @param date التاريخ الأساسي
 * @param days عدد الأيام المراد إضافتها
 * @returns التاريخ بعد إضافة الأيام
 */
export const addDays = (date: Date | string, days: number): Date => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  return addDaysDateFns(dateObj, days);
};

/**
 * مقارنة تاريخين
 * @param date1 التاريخ الأول
 * @param date2 التاريخ الثاني
 * @returns -1 إذا كان التاريخ الأول أقدم، 0 إذا كانا متساويين، 1 إذا كان التاريخ الأول أحدث
 */
export const compareDates = (date1: Date | string, date2: Date | string): number => {
  const d1 = typeof date1 === 'string' ? parseISO(date1) : date1;
  const d2 = typeof date2 === 'string' ? parseISO(date2) : date2;

  return compareAsc(d1, d2);
};

/**
 * تنسيق التاريخ بتنسيق مناسب للعرض
 * @param date التاريخ المراد تنسيقه
 * @returns التاريخ المنسق
 */
export const formatDisplayDate = (date: Date | string | null | undefined): string => {
  return formatDate(date, 'PPP');
};

/**
 * تنسيق التاريخ والوقت بتنسيق مناسب للعرض
 * @param date التاريخ المراد تنسيقه
 * @returns التاريخ والوقت المنسق
 */
export const formatDateTime = (date: Date | string | null | undefined): string => {
  return formatDate(date, 'PPP HH:mm');
};

export default {
  formatDate,
  formatDisplayDate,
  formatDateTime,
  toISODate,
  parseDate,
  getCurrentDate,
  addDays,
  compareDates,
};
