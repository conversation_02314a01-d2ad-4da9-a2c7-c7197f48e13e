import { Box, Paper, Typography } from '@mui/material';
import { ReactNode } from 'react';

interface StatCardProps {
  title: string;
  value: number;
  icon: ReactNode;
  color: string;
}

const StatCard = ({ title, value, icon, color }: StatCardProps) => {
  return (
    <Paper
      sx={{
        p: 2,
        display: 'flex',
        flexDirection: 'column',
        height: 140,
        position: 'relative',
        overflow: 'hidden',
        borderTop: `4px solid ${color}`,
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: -15,
          right: -15,
          opacity: 0.2,
          transform: 'rotate(15deg)',
          color: color,
        }}
      >
        {icon}
      </Box>
      
      <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
        {value.toLocaleString()}
      </Typography>
      
      <Typography
        variant="subtitle1"
        color="text.secondary"
        sx={{ mt: 'auto' }}
      >
        {title}
      </Typography>
    </Paper>
  );
};

export default StatCard;
