# سجل التغييرات

جميع التغييرات الهامة في هذا المشروع سيتم توثيقها في هذا الملف.

يتبع هذا المشروع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [غير منشور]

### إضافات
- إضافة اختبارات الأداء وتجربة المستخدم
- إضافة سير عمل CI/CD باستخدام GitHub Actions
- إضافة توثيق المستخدم النهائي

## [1.0.0] - 2025-07-10

### إضافات
- إطلاق النسخة الأولى من نظام النور للأرشفة
- تنفيذ جميع الميزات الأساسية:
  - إدارة البيانات
  - إدارة حركة الأصناف
  - إدارة التفويضات
  - إدارة الإفراجات
  - إدارة التصاريح
  - إدارة الضمانات
  - إدارة الاستلامات
  - إدارة العملاء
  - إدارة أوراق المكتب
  - البحث المتقدم
  - التقارير
  - الإعدادات
  - الملف الشخصي
- دعم اللغة العربية والإنجليزية
- دعم اللغة العربية في ملفات PDF
- واجهة مستخدم متجاوبة تعمل على مختلف أحجام الشاشات

## [0.9.0] - 2025-07-01

### إضافات
- إضافة صفحة البحث المتقدم
- إضافة صفحة التقارير
- إضافة صفحة الإعدادات
- إضافة صفحة الملف الشخصي
- تحسين دعم اللغة العربية في واجهة المستخدم

### تحسينات
- تحسين أداء النظام
- تحسين واجهة المستخدم
- تحسين تجربة المستخدم

## [0.8.0] - 2025-06-25

### إضافات
- إضافة صفحة الإفراجات
- إضافة صفحة التصاريح
- إضافة صفحة العملاء
- تحسين دعم اللغة العربية في ملفات PDF

### تحسينات
- تحسين أداء النظام
- تحسين واجهة المستخدم

## [0.7.0] - 2025-06-20

### إضافات
- إضافة صفحة التفويضات
- إضافة صفحة الضمانات
- إضافة صفحة الاستلامات
- إضافة دعم رفع ملفات PDF بحجم كبير (100 ميجابايت)

### تحسينات
- تحسين عرض الجدول لدعم اللغة العربية بشكل كامل
- إضافة ترجمات للميزات الجديدة

## [0.6.0] - 2025-06-15

### إضافات
- تنفيذ وحدة البيانات بالكامل
- تنفيذ وحدة حركة الأصناف بالكامل
- تنفيذ وحدة التفويضات بالكامل
- تنفيذ وحدة الإفراجات بالكامل
- تنفيذ وحدة التصاريح بالكامل
- تنفيذ وحدة الضمانات بالكامل
- تنفيذ وحدة الاستلامات بالكامل
- تنفيذ وحدة العملاء بالكامل
- تنفيذ وحدة أوراق المكتب بالكامل
- تنفيذ وحدة البحث المتقدم بالكامل
- تنفيذ وحدة التقارير بالكامل

### تحسينات
- تحديث ملف app.ts لتسجيل جميع المسارات الجديدة

## [0.5.0] - 2025-06-10

### إضافات
- إضافة صفحة البيانات
- إضافة صفحة حركة الأصناف
- إضافة اختبارات الوحدات للواجهة الخلفية
- إضافة اختبارات التكامل

### تحسينات
- تحسين أداء النظام
- تحسين واجهة المستخدم

## [0.4.0] - 2025-06-05

### إضافات
- إضافة وحدة المصادقة والتفويض
- إضافة وحدة البيانات
- إضافة وحدة حركة الأصناف
- إضافة دعم اللغة العربية في ملفات PDF

### تحسينات
- تحسين أداء النظام
- تحسين واجهة المستخدم

## [0.3.0] - 2025-06-01

### إضافات
- إعداد قاعدة البيانات PostgreSQL
- تنفيذ ترحيلات قاعدة البيانات
- إضافة البيانات الأولية
- توثيق إعداد قاعدة البيانات

## [0.2.0] - 2025-05-25

### إضافات
- إعداد مشروع React مع Vite و TypeScript
- إضافة التبعيات اللازمة
- إعداد Tailwind CSS للتنسيق
- إعداد ملفات الترجمة للغة العربية والإنجليزية
- إعداد مزود الترجمة (i18n) مع دعم اللغة العربية
- إعداد مزود Material UI مع دعم RTL
- إعداد مزود React Query لإدارة حالة البيانات
- إعداد مزود Redux لإدارة حالة التطبيق
- إعداد مزود المصادقة
- إنشاء التخطيطات الرئيسية
- إنشاء المكونات المشتركة
- إنشاء صفحة تسجيل الدخول
- إنشاء صفحة لوحة التحكم

### تحسينات
- تحسين أداء النظام
- تحسين واجهة المستخدم

## [0.1.0] - 2025-05-20

### إضافات
- إنشاء هيكل المشروع باستخدام Monorepo مع Turborepo
- إعداد مجلدات المشروع الرئيسية
- إعداد ملفات التكوين الأساسية
- توثيق هيكل المشروع
- توثيق خطة التنفيذ
