import { Router } from 'express';
import { clientController } from '../controllers/client.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import {
  createClientSchema,
  updateClientSchema,
  getClientSchema,
  deleteClientSchema,
  listClientsSchema,
} from '../schemas/client.schema.js';

export const clientRoutes = Router();

// مسارات العملاء
clientRoutes.get(
  '/',
  authMiddleware,
  validateRequest(listClientsSchema),
  clientController.listClients
);

clientRoutes.post(
  '/',
  authMiddleware,
  validateRequest(createClientSchema),
  clientController.createClient
);

clientRoutes.get(
  '/:id',
  authMiddleware,
  validateRequest(getClientSchema),
  clientController.getClient
);

clientRoutes.put(
  '/:id',
  authMiddleware,
  validateRequest(updateClientSchema),
  clientController.updateClient
);

clientRoutes.delete(
  '/:id',
  authMiddleware,
  validateRequest(deleteClientSchema),
  clientController.deleteClient
);
