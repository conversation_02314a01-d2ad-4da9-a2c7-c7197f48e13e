import React from 'react';
import { render, screen } from '@testing-library/react';
import { PageHeader } from '../PageHeader';
import { Button } from '@mui/material';

describe('PageHeader Component', () => {
  test('renders title correctly', () => {
    render(<PageHeader title="عنوان الصفحة" />);
    expect(screen.getByText('عنوان الصفحة')).toBeInTheDocument();
  });

  test('renders subtitle when provided', () => {
    render(<PageHeader title="عنوان الصفحة" subtitle="عنوان فرعي" />);
    expect(screen.getByText('عنوان فرعي')).toBeInTheDocument();
  });

  test('renders back button when provided', () => {
    const backButton = <Button>رجوع</Button>;
    render(<PageHeader title="عنوان الصفحة" backButton={backButton} />);
    expect(screen.getByText('رجوع')).toBeInTheDocument();
  });

  test('renders actions when provided', () => {
    const actions = <Button>إجراء</Button>;
    render(<PageHeader title="عنوان الصفحة" actions={actions} />);
    expect(screen.getByText('إجراء')).toBeInTheDocument();
  });

  test('renders children when provided', () => {
    render(
      <PageHeader title="عنوان الصفحة">
        <div>محتوى إضافي</div>
      </PageHeader>
    );
    expect(screen.getByText('محتوى إضافي')).toBeInTheDocument();
  });
});
