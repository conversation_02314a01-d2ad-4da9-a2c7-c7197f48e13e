import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useReceipt, useCreateReceipt, useUpdateReceipt } from '../hooks/useReceipts';
import { ReceiptFormValues } from '../types/receipt.types';
import { useDeclarations } from '../../declarations/hooks/useDeclarations';

// مخطط التحقق من صحة نموذج الاستلام
const receiptFormSchema = z.object({
  declarationId: z.string({
    required_error: 'معرف البيان مطلوب',
  }),
  receiptDate: z.date({
    required_error: 'تاريخ الاستلام مطلوب',
  }),
  receiptNumber: z.string({
    required_error: 'رقم الاستلام مطلوب',
  }),
  receiverName: z.string({
    required_error: 'اسم المستلم مطلوب',
  }),
  receiverPhone: z.string().optional(),
  receiverIdNumber: z.string().optional(),
  itemsDescription: z.string().optional(),
  notes: z.string().optional(),
  file: z.any().nullable(),
});

const ReceiptFormPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  // حالة الملف المحدد
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);

  // استخدام خطافات الاستلامات
  const { data: receiptResponse, isLoading: isLoadingReceipt } = useReceipt(id || '');
  const createMutation = useCreateReceipt();
  const updateMutation = useUpdateReceipt();

  // استخراج بيانات الاستلام من الاستجابة
  const receipt = receiptResponse?.data;

  // استخدام خطاف البيانات للحصول على قائمة البيانات
  const { data: declarationsData } = useDeclarations({
    limit: 100,
  });

  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<ReceiptFormValues>({
    resolver: zodResolver(receiptFormSchema),
    defaultValues: {
      declarationId: '',
      receiptDate: null,
      receiptNumber: '',
      receiverName: '',
      receiverPhone: '',
      receiverIdNumber: '',
      itemsDescription: '',
      notes: '',
      file: null,
    },
  });

  // تحديث النموذج عند تحميل بيانات الاستلام
  useEffect(() => {
    if (isEditMode && receipt) {
      reset({
        declarationId: receipt.declarationId,
        receiptDate: new Date(receipt.receiptDate),
        receiptNumber: receipt.receiptNumber,
        receiverName: receipt.receiverName,
        receiverPhone: receipt.receiverPhone || '',
        receiverIdNumber: receipt.receiverIdNumber || '',
        itemsDescription: receipt.itemsDescription || '',
        notes: receipt.notes || '',
        file: null,
      });

      if (receipt.pdfFile) {
        setPdfUrl(`/api/receipts/pdf/${receipt.id}`);
      }
    }
  }, [isEditMode, receipt, reset]);

  // التعامل مع تغيير الملف
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedFile(file);
    }
  };

  // التعامل مع تقديم النموذج
  const onSubmit = async (data: ReceiptFormValues) => {
    try {
      if (isEditMode && id) {
        // تحديث الاستلام
        await updateMutation.mutateAsync({
          id,
          data: {
            declarationId: data.declarationId,
            receiptDate: data.receiptDate?.toISOString() || '',
            receiptNumber: data.receiptNumber,
            receiverName: data.receiverName,
            receiverPhone: data.receiverPhone,
            receiverIdNumber: data.receiverIdNumber,
            itemsDescription: data.itemsDescription,
            notes: data.notes,
          },
          file: selectedFile || undefined,
        });
      } else {
        // إنشاء استلام جديد
        await createMutation.mutateAsync({
          data: {
            declarationId: data.declarationId,
            receiptDate: data.receiptDate?.toISOString() || '',
            receiptNumber: data.receiptNumber,
            receiverName: data.receiverName,
            receiverPhone: data.receiverPhone,
            receiverIdNumber: data.receiverIdNumber,
            itemsDescription: data.itemsDescription,
            notes: data.notes,
          },
          file: selectedFile || undefined,
        });
      }

      // العودة إلى صفحة قائمة الاستلامات
      navigate('/receipts');
    } catch (error) {
      console.error('Error submitting receipt form:', error);
    }
  };

  // التعامل مع العودة إلى قائمة الاستلامات
  const handleBack = () => {
    navigate('/receipts');
  };

  // عرض رسالة التحميل
  if (isEditMode && isLoadingReceipt) {
    return (
      <Container maxWidth="lg">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {isEditMode ? t('receipts.edit') : t('receipts.create')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {isEditMode ? t('receipts.editDescription') : t('receipts.createDescription')}
        </Typography>
      </Box>

      <Paper sx={{ p: 3 }}>
        <Box component="form" noValidate onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            {/* البيان */}
            <Grid item xs={12} md={6}>
              <Controller
                name="declarationId"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.declarationId}>
                    <InputLabel>{t('receipts.declaration')}</InputLabel>
                    <Select
                      {...field}
                      label={t('receipts.declaration')}
                      disabled={isEditMode}
                    >
                      {declarationsData?.data.map((declaration) => (
                        <MenuItem key={declaration.id} value={declaration.id}>
                          {t('declarations.number')}: {declaration.declarationNumber} - {declaration.clientName}
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.declarationId && (
                      <FormHelperText>{errors.declarationId.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            {/* رقم الاستلام */}
            <Grid item xs={12} md={6}>
              <Controller
                name="receiptNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label={t('receipts.receiptNumber')}
                    error={!!errors.receiptNumber}
                    helperText={errors.receiptNumber?.message}
                  />
                )}
              />
            </Grid>

            {/* تاريخ الاستلام */}
            <Grid item xs={12} md={6}>
              <Controller
                name="receiptDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label={t('receipts.receiptDate')}
                    value={field.value}
                    onChange={(date) => field.onChange(date)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.receiptDate,
                        helperText: errors.receiptDate?.message,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* اسم المستلم */}
            <Grid item xs={12} md={6}>
              <Controller
                name="receiverName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label={t('receipts.receiverName')}
                    error={!!errors.receiverName}
                    helperText={errors.receiverName?.message}
                  />
                )}
              />
            </Grid>

            {/* رقم هاتف المستلم */}
            <Grid item xs={12} md={6}>
              <Controller
                name="receiverPhone"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label={t('receipts.receiverPhone')}
                    error={!!errors.receiverPhone}
                    helperText={errors.receiverPhone?.message}
                  />
                )}
              />
            </Grid>

            {/* رقم هوية المستلم */}
            <Grid item xs={12} md={6}>
              <Controller
                name="receiverIdNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label={t('receipts.receiverIdNumber')}
                    error={!!errors.receiverIdNumber}
                    helperText={errors.receiverIdNumber?.message}
                  />
                )}
              />
            </Grid>

            {/* وصف الأصناف */}
            <Grid item xs={12}>
              <Controller
                name="itemsDescription"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    multiline
                    rows={3}
                    label={t('receipts.itemsDescription')}
                    error={!!errors.itemsDescription}
                    helperText={errors.itemsDescription?.message}
                  />
                )}
              />
            </Grid>

            {/* ملاحظات */}
            <Grid item xs={12}>
              <Controller
                name="notes"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    multiline
                    rows={3}
                    label={t('receipts.notes')}
                    error={!!errors.notes}
                    helperText={errors.notes?.message}
                  />
                )}
              />
            </Grid>

            {/* ملف PDF */}
            <Grid item xs={12}>
              <Button
                variant="outlined"
                component="label"
                startIcon={<UploadIcon />}
              >
                {t('common.uploadPdf')}
                <input
                  type="file"
                  accept="application/pdf"
                  hidden
                  onChange={handleFileChange}
                />
              </Button>
              {selectedFile && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {t('common.selectedFile')}: {selectedFile.name}
                </Typography>
              )}
              {pdfUrl && !selectedFile && (
                <Box display="flex" alignItems="center" mt={1}>
                  <Typography variant="body2" sx={{ mr: 1 }}>
                    {t('common.currentFile')}:
                  </Typography>
                  <Button
                    variant="text"
                    size="small"
                    href={pdfUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {t('common.viewPdf')}
                  </Button>
                </Box>
              )}
            </Grid>

            {/* أزرار الإجراءات */}
            <Grid item xs={12}>
              <Box display="flex" justifyContent="flex-end" mt={2}>
                <Button
                  variant="outlined"
                  onClick={handleBack}
                  startIcon={<ArrowBackIcon />}
                  sx={{ mr: 1 }}
                >
                  {t('common.cancel')}
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <CircularProgress size={24} />
                  ) : (
                    t('common.save')
                  )}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Container>
  );
};

export default ReceiptFormPage;
