import { z } from 'zod';

// مخطط إنشاء مستند جديد
export const createDocumentSchema = z.object({
  body: z.object({
    documentNumber: z.number().optional(),
    documentType: z.enum(['TYPE_A', 'TYPE_B', 'TYPE_C']).optional(),
    documentDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    documentValue: z.number().optional(),
    title: z.string({
      required_error: 'عنوان المستند مطلوب',
    }),
    description: z.string().optional(),
    expiryDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    issuedBy: z.string().optional(),
  }),
});

// مخطط تحديث مستند
export const updateDocumentSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف المستند مطلوب',
    }),
  }),
  body: z.object({
    documentNumber: z.number().optional(),
    documentType: z.enum(['TYPE_A', 'TYPE_B', 'TYPE_C']).optional(),
    documentDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    documentValue: z.number().optional(),
    title: z.string().optional(),
    description: z.string().optional(),
    expiryDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    issuedBy: z.string().optional(),
  }),
});

// مخطط الحصول على مستند محدد
export const getDocumentSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف المستند مطلوب',
    }),
  }),
});

// مخطط حذف مستند
export const deleteDocumentSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف المستند مطلوب',
    }),
  }),
});

// مخطط قائمة المستندات
export const listDocumentsSchema = z.object({
  query: z.object({
    page: z.string().optional().transform((val) => (val ? parseInt(val) : 1)),
    limit: z.string().optional().transform((val) => (val ? parseInt(val) : 10)),
    sort: z.string().optional().default('documentDate'),
    order: z.enum(['asc', 'desc']).optional().default('desc'),
    search: z.string().optional(),
    documentType: z.enum(['TYPE_A', 'TYPE_B', 'TYPE_C']).optional(),
    fromDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    toDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    isExpired: z.string().optional().transform((val) => {
      if (val === 'true') return true;
      if (val === 'false') return false;
      return undefined;
    }),
  }),
});
