// أنواع البيانات لحركات الأصناف

export interface CreateItemMovementInput {
  declarationId: string;
  itemName: string;
  quantity: number;
  unit: string;
  movementDate: Date;
  movementType: string;
  notes?: string;
}

export interface UpdateItemMovementInput {
  itemName?: string;
  quantity?: number;
  unit?: string;
  movementDate?: Date;
  movementType?: string;
  notes?: string;
}

export interface ItemMovementFilters {
  search?: string;
  declarationId?: string;
  fromDate?: Date;
  toDate?: Date;
}

export interface ItemMovementListOptions {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}
