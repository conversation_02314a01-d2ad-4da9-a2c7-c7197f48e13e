import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  Grid,
  IconButton,
  MenuItem,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useDeclaration, useCreateDeclaration, useUpdateDeclaration } from '../hooks/useDeclarations';
import { DeclarationType, DeclarationFormValues } from '../types/declaration.types';

// مخطط التحقق من صحة نموذج البيان
const declarationFormSchema = z.object({
  taxNumber: z.string().min(1, { message: 'الرقم الضريبي مطلوب' }),
  clientName: z.string().optional(),
  companyName: z.string().optional(),
  policyNumber: z.union([z.number(), z.literal('')]).optional(),
  invoiceNumber: z.union([z.number(), z.literal('')]).optional(),
  gatewayEntryNumber: z.union([z.number(), z.literal('')]).refine((val) => val !== '', {
    message: 'رقم دخول البوابة مطلوب',
  }),
  declarationType: z.nativeEnum(DeclarationType, {
    errorMap: () => ({ message: 'نوع البيان مطلوب' }),
  }),
  declarationDate: z.date().nullable(),
  count: z.union([z.number(), z.literal('')]).optional(),
  weight: z.string().optional(),
  goodsType: z.string().optional(),
  itemsCount: z.union([z.number(), z.literal('')]).optional(),
  entryDate: z.date().nullable(),
  exitDate: z.date().nullable(),
  clientId: z.string().nullable(),
  drivers: z.array(
    z.object({
      id: z.string().optional(),
      name: z.string().min(1, { message: 'اسم السائق مطلوب' }),
      truckNumber: z.string().optional(),
      trailerNumber: z.string().optional(),
      phoneNumber: z.string().optional(),
    })
  ),
  file: z.any().nullable(),
});

const DeclarationFormPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  // استخدام خطافات البيانات
  const { data: declaration, isLoading: isLoadingDeclaration } = useDeclaration(id || '');
  const createMutation = useCreateDeclaration();
  const updateMutation = useUpdateDeclaration();

  // حالة الملف
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm<DeclarationFormValues>({
    resolver: zodResolver(declarationFormSchema),
    defaultValues: {
      taxNumber: '',
      clientName: '',
      companyName: '',
      policyNumber: '',
      invoiceNumber: '',
      gatewayEntryNumber: '',
      declarationType: DeclarationType.IMPORT,
      declarationDate: null,
      count: '',
      weight: '',
      goodsType: '',
      itemsCount: '',
      entryDate: null,
      exitDate: null,
      clientId: null,
      drivers: [{ name: '', truckNumber: '', trailerNumber: '', phoneNumber: '' }],
      file: null,
    },
  });

  // إعداد مصفوفة الحقول للسائقين
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'drivers',
  });

  // تحميل بيانات البيان عند التعديل
  useEffect(() => {
    if (isEditMode && declaration) {
      reset({
        taxNumber: declaration.taxNumber,
        clientName: declaration.clientName || '',
        companyName: declaration.companyName || '',
        policyNumber: declaration.policyNumber || '',
        invoiceNumber: declaration.invoiceNumber || '',
        gatewayEntryNumber: declaration.gatewayEntryNumber,
        declarationType: declaration.declarationType,
        declarationDate: declaration.declarationDate ? new Date(declaration.declarationDate) : null,
        count: declaration.count || '',
        weight: declaration.weight || '',
        goodsType: declaration.goodsType || '',
        itemsCount: declaration.itemsCount || '',
        entryDate: declaration.entryDate ? new Date(declaration.entryDate) : null,
        exitDate: declaration.exitDate ? new Date(declaration.exitDate) : null,
        clientId: declaration.clientId || null,
        drivers: declaration.drivers.length > 0
          ? declaration.drivers.map((driver) => ({
              id: driver.id,
              name: driver.name,
              truckNumber: driver.truckNumber || '',
              trailerNumber: driver.trailerNumber || '',
              phoneNumber: driver.phoneNumber || '',
            }))
          : [{ name: '', truckNumber: '', trailerNumber: '', phoneNumber: '' }],
        file: null,
      });
    }
  }, [isEditMode, declaration, reset]);

  // التعامل مع تغيير الملف
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedFile(file);
      setValue('file', file);
    }
  };

  // التعامل مع إرسال النموذج
  const onSubmit = async (data: DeclarationFormValues) => {
    try {
      if (isEditMode && id) {
        // تحديث البيان
        await updateMutation.mutateAsync({
          id,
          data: {
            taxNumber: data.taxNumber,
            clientName: data.clientName || undefined,
            companyName: data.companyName || undefined,
            policyNumber: typeof data.policyNumber === 'number' ? data.policyNumber : undefined,
            invoiceNumber: typeof data.invoiceNumber === 'number' ? data.invoiceNumber : undefined,
            gatewayEntryNumber: typeof data.gatewayEntryNumber === 'number' ? data.gatewayEntryNumber : undefined,
            declarationType: data.declarationType,
            declarationDate: data.declarationDate ? data.declarationDate.toISOString() : undefined,
            count: typeof data.count === 'number' ? data.count : undefined,
            weight: data.weight || undefined,
            goodsType: data.goodsType || undefined,
            itemsCount: typeof data.itemsCount === 'number' ? data.itemsCount : undefined,
            entryDate: data.entryDate ? data.entryDate.toISOString() : undefined,
            exitDate: data.exitDate ? data.exitDate.toISOString() : undefined,
            clientId: data.clientId || undefined,
            drivers: data.drivers.map((driver) => ({
              id: driver.id,
              name: driver.name,
              truckNumber: driver.truckNumber || undefined,
              trailerNumber: driver.trailerNumber || undefined,
              phoneNumber: driver.phoneNumber || undefined,
            })),
          },
          file: selectedFile || undefined,
        });
      } else {
        // إنشاء بيان جديد
        await createMutation.mutateAsync({
          data: {
            taxNumber: data.taxNumber,
            clientName: data.clientName || undefined,
            companyName: data.companyName || undefined,
            policyNumber: typeof data.policyNumber === 'number' ? data.policyNumber : undefined,
            invoiceNumber: typeof data.invoiceNumber === 'number' ? data.invoiceNumber : undefined,
            gatewayEntryNumber: typeof data.gatewayEntryNumber === 'number' ? data.gatewayEntryNumber : (data.gatewayEntryNumber as any),
            declarationType: data.declarationType,
            declarationDate: data.declarationDate ? data.declarationDate.toISOString() : undefined,
            count: typeof data.count === 'number' ? data.count : undefined,
            weight: data.weight || undefined,
            goodsType: data.goodsType || undefined,
            itemsCount: typeof data.itemsCount === 'number' ? data.itemsCount : undefined,
            entryDate: data.entryDate ? data.entryDate.toISOString() : undefined,
            exitDate: data.exitDate ? data.exitDate.toISOString() : undefined,
            clientId: data.clientId || undefined,
            drivers: data.drivers.map((driver) => ({
              name: driver.name,
              truckNumber: driver.truckNumber || undefined,
              trailerNumber: driver.trailerNumber || undefined,
              phoneNumber: driver.phoneNumber || undefined,
            })),
          },
          file: selectedFile || undefined,
        });
      }

      // العودة إلى صفحة قائمة البيانات
      navigate('/declarations');
    } catch (error) {
      console.error('Error submitting declaration:', error);
    }
  };

  // عرض حالة التحميل
  if (isEditMode && isLoadingDeclaration) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg">
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box display="flex" alignItems="center" mb={3}>
          <IconButton onClick={() => navigate('/declarations')} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h5" component="h1">
            {isEditMode ? t('declarations.edit') : t('declarations.create')}
          </Typography>
        </Box>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            {/* معلومات البيان الأساسية */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                {t('declarations.basicInfo')}
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="taxNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('declarations.taxNumber')}
                    fullWidth
                    required
                    error={!!errors.taxNumber}
                    helperText={errors.taxNumber?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="clientName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('declarations.clientName')}
                    fullWidth
                    error={!!errors.clientName}
                    helperText={errors.clientName?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="companyName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('declarations.companyName')}
                    fullWidth
                    error={!!errors.companyName}
                    helperText={errors.companyName?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="declarationType"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    select
                    label={t('declarations.declarationType')}
                    fullWidth
                    required
                    error={!!errors.declarationType}
                    helperText={errors.declarationType?.message}
                  >
                    <MenuItem value={DeclarationType.IMPORT}>{t('declarations.import')}</MenuItem>
                    <MenuItem value={DeclarationType.EXPORT}>{t('declarations.export')}</MenuItem>
                  </TextField>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="gatewayEntryNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('declarations.gatewayEntryNumber')}
                    fullWidth
                    required
                    type="number"
                    error={!!errors.gatewayEntryNumber}
                    helperText={errors.gatewayEntryNumber?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="declarationDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label={t('declarations.declarationDate')}
                    value={field.value}
                    onChange={field.onChange}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.declarationDate,
                        helperText: errors.declarationDate?.message,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* معلومات إضافية */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                {t('declarations.additionalInfo')}
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="policyNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('declarations.policyNumber')}
                    fullWidth
                    type="number"
                    error={!!errors.policyNumber}
                    helperText={errors.policyNumber?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="invoiceNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('declarations.invoiceNumber')}
                    fullWidth
                    type="number"
                    error={!!errors.invoiceNumber}
                    helperText={errors.invoiceNumber?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="count"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('declarations.count')}
                    fullWidth
                    type="number"
                    error={!!errors.count}
                    helperText={errors.count?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="weight"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('declarations.weight')}
                    fullWidth
                    error={!!errors.weight}
                    helperText={errors.weight?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="goodsType"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('declarations.goodsType')}
                    fullWidth
                    error={!!errors.goodsType}
                    helperText={errors.goodsType?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="itemsCount"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('declarations.itemsCount')}
                    fullWidth
                    type="number"
                    error={!!errors.itemsCount}
                    helperText={errors.itemsCount?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="entryDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label={t('declarations.entryDate')}
                    value={field.value}
                    onChange={field.onChange}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.entryDate,
                        helperText: errors.entryDate?.message,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="exitDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label={t('declarations.exitDate')}
                    value={field.value}
                    onChange={field.onChange}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.exitDate,
                        helperText: errors.exitDate?.message,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* ملف PDF */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                {t('declarations.pdfFile')}
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12}>
              <Button variant="outlined" component="label">
                {t('common.uploadFile')}
                <input
                  type="file"
                  hidden
                  accept="application/pdf"
                  onChange={handleFileChange}
                />
              </Button>
              {selectedFile && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {t('common.selectedFile')}: {selectedFile.name}
                </Typography>
              )}
              {isEditMode && declaration?.pdfFile && !selectedFile && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {t('common.currentFile')}: {declaration.pdfFile.split('/').pop()}
                </Typography>
              )}
            </Grid>

            {/* السائقين */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6" gutterBottom>
                  {t('declarations.drivers')}
                </Typography>
                <Button
                  startIcon={<AddIcon />}
                  onClick={() => append({ name: '', truckNumber: '', trailerNumber: '', phoneNumber: '' })}
                >
                  {t('declarations.addDriver')}
                </Button>
              </Box>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            {fields.map((field, index) => (
              <Grid item xs={12} key={field.id}>
                <Card variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Typography variant="subtitle1">
                        {t('declarations.driver')} #{index + 1}
                      </Typography>
                      {fields.length > 1 && (
                        <IconButton onClick={() => remove(index)} color="error" size="small">
                          <DeleteIcon />
                        </IconButton>
                      )}
                    </Box>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Controller
                          name={`drivers.${index}.name`}
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label={t('declarations.driverName')}
                              fullWidth
                              required
                              error={!!errors.drivers?.[index]?.name}
                              helperText={errors.drivers?.[index]?.name?.message}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Controller
                          name={`drivers.${index}.phoneNumber`}
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label={t('declarations.phoneNumber')}
                              fullWidth
                              error={!!errors.drivers?.[index]?.phoneNumber}
                              helperText={errors.drivers?.[index]?.phoneNumber?.message}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Controller
                          name={`drivers.${index}.truckNumber`}
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label={t('declarations.truckNumber')}
                              fullWidth
                              error={!!errors.drivers?.[index]?.truckNumber}
                              helperText={errors.drivers?.[index]?.truckNumber?.message}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Controller
                          name={`drivers.${index}.trailerNumber`}
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label={t('declarations.trailerNumber')}
                              fullWidth
                              error={!!errors.drivers?.[index]?.trailerNumber}
                              helperText={errors.drivers?.[index]?.trailerNumber?.message}
                            />
                          )}
                        />
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            ))}

            {/* أزرار الإرسال */}
            <Grid item xs={12} sx={{ mt: 3 }}>
              <Box display="flex" justifyContent="flex-end" gap={2}>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/declarations')}
                  disabled={isSubmitting}
                >
                  {t('common.cancel')}
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<SaveIcon />}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <CircularProgress size={24} />
                  ) : isEditMode ? (
                    t('common.update')
                  ) : (
                    t('common.save')
                  )}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </Container>
  );
};

export default DeclarationFormPage;
