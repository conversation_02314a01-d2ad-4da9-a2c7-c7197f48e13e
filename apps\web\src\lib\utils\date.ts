import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

/**
 * تنسيق التاريخ بالتنسيق المناسب للغة العربية
 * @param date التاريخ المراد تنسيقه
 * @param formatStr تنسيق التاريخ (اختياري)
 * @returns التاريخ المنسق
 */
export const formatDate = (date: Date, formatStr: string = 'PPP'): string => {
  if (!date) return '-';
  
  try {
    return format(date, formatStr, { locale: arSA });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '-';
  }
};
