import { useQuery } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';
import { ItemMovement } from './useItemMovement';

export interface ItemMovementsResponse {
  data: ItemMovement[];
  total: number;
  page: number;
  limit: number;
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface ItemMovementsParams {
  page?: number;
  limit?: number;
  search?: string;
  movementType?: string;
  fromDate?: string;
  toDate?: string;
}

export const useItemMovements = (params: ItemMovementsParams = {}) => {
  return useQuery({
    queryKey: ['itemMovements', params],
    queryFn: async (): Promise<ItemMovementsResponse> => {
      const response = await api.get<ItemMovementsResponse>('/api/item-movements', { params });
      return response;
    },
  });
};
