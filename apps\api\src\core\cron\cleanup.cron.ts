import { CronJob } from 'cron';
import { logger } from '../utils/logger.js';
import { tokenService } from '../utils/token.service.js';
import { sessionService } from '../utils/session.service.js';
import { loginAttemptService } from '../utils/login-attempt.service.js';

/**
 * مهمة مجدولة لتنظيف الجلسات منتهية الصلاحية
 * تعمل كل يوم في الساعة 3 صباحًا
 */
export const cleanupSessionsCron = new CronJob(
  '0 3 * * *', // كل يوم في الساعة 3 صباحًا
  async () => {
    try {
      logger.info('بدء تنظيف الجلسات منتهية الصلاحية...');
      const result = await sessionService.cleanupExpiredSessions();
      logger.info(`تم تنظيف ${result.count} جلسة منتهية الصلاحية`);
    } catch (error) {
      logger.error('خطأ في تنظيف الجلسات منتهية الصلاحية:', error);
    }
  },
  null, // onComplete
  false, // start
  'UTC' // timezone
);

/**
 * مهمة مجدولة لتنظيف التوكنات المبطلة
 * تعمل كل يوم في الساعة 4 صباحًا
 */
export const cleanupTokensCron = new CronJob(
  '0 4 * * *', // كل يوم في الساعة 4 صباحًا
  async () => {
    try {
      logger.info('بدء تنظيف التوكنات المبطلة...');
      const result = await tokenService.cleanupExpiredTokens();
      logger.info(`تم تنظيف ${result.count} توكن مبطل`);
    } catch (error) {
      logger.error('خطأ في تنظيف التوكنات المبطلة:', error);
    }
  },
  null, // onComplete
  false, // start
  'UTC' // timezone
);

/**
 * مهمة مجدولة لتنظيف محاولات تسجيل الدخول القديمة
 * تعمل كل أسبوع في يوم الأحد الساعة 5 صباحًا
 */
export const cleanupLoginAttemptsCron = new CronJob(
  '0 5 * * 0', // كل يوم أحد في الساعة 5 صباحًا
  async () => {
    try {
      logger.info('بدء تنظيف محاولات تسجيل الدخول القديمة...');
      // الاحتفاظ بمحاولات تسجيل الدخول لمدة 90 يومًا
      const result = await loginAttemptService.cleanupOldLoginAttempts(90);
      logger.info(`تم تنظيف ${result.count} محاولة تسجيل دخول قديمة`);
    } catch (error) {
      logger.error('خطأ في تنظيف محاولات تسجيل الدخول القديمة:', error);
    }
  },
  null, // onComplete
  false, // start
  'UTC' // timezone
);

/**
 * بدء تشغيل جميع المهام المجدولة
 */
export const startCleanupCronJobs = () => {
  cleanupSessionsCron.start();
  cleanupTokensCron.start();
  cleanupLoginAttemptsCron.start();
  logger.info('تم بدء تشغيل المهام المجدولة للتنظيف');
};

/**
 * إيقاف تشغيل جميع المهام المجدولة
 */
export const stopCleanupCronJobs = () => {
  cleanupSessionsCron.stop();
  cleanupTokensCron.stop();
  cleanupLoginAttemptsCron.stop();
  logger.info('تم إيقاف تشغيل المهام المجدولة للتنظيف');
};
