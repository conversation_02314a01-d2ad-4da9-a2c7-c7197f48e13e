import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  Divider,
  Grid,
  IconButton,
  MenuItem,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useItemMovement, useCreateItemMovement, useUpdateItemMovement } from '../hooks/useItemMovements';
import { PackageType, GoodsType, Currency, ItemMovementFormValues } from '../types/item-movement.types';
import { useDeclarations } from '../../declarations/hooks/useDeclarations';

// مخطط التحقق من صحة نموذج حركة الصنف
const itemMovementFormSchema = z.object({
  declarationId: z.string().min(1, { message: 'معرف البيان مطلوب' }),
  movementDate: z.date().nullable(),
  declarationNumber: z.union([z.number(), z.literal('')]).refine((val) => val !== '', {
    message: 'رقم البيان مطلوب',
  }),
  itemNumber: z.union([z.number(), z.literal('')]).optional(),
  invoiceNumber: z.union([z.number(), z.literal('')]).refine((val) => val !== '', {
    message: 'رقم الفاتورة مطلوب',
  }),
  packingListNumber: z.union([z.number(), z.literal('')]).optional(),
  identificationClause: z.union([z.number(), z.literal('')]).optional(),
  itemName: z.string().optional(),
  count: z.union([z.number(), z.literal('')]).optional(),
  packageType: z.union([z.nativeEnum(PackageType), z.literal('')]).optional(),
  goodsType: z.union([z.nativeEnum(GoodsType), z.literal('')]).optional(),
  countryOfOrigin: z.string().optional(),
  itemValue: z.union([z.number(), z.literal('')]).optional(),
  currency: z.union([z.nativeEnum(Currency), z.literal('')]).optional(),
  totalValue: z.union([z.number(), z.literal('')]).optional(),
  file: z.any().nullable(),
});

const ItemMovementFormPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  // استخدام خطافات البيانات
  const { data: itemMovement, isLoading: isLoadingItemMovement } = useItemMovement(id || '');
  const createMutation = useCreateItemMovement();
  const updateMutation = useUpdateItemMovement();
  const { data: declarationsData } = useDeclarations({ limit: 100 });

  // حالة الملف
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<ItemMovementFormValues>({
    resolver: zodResolver(itemMovementFormSchema),
    defaultValues: {
      declarationId: '',
      movementDate: null,
      declarationNumber: '',
      itemNumber: '',
      invoiceNumber: '',
      packingListNumber: '',
      identificationClause: '',
      itemName: '',
      count: '',
      packageType: '',
      goodsType: '',
      countryOfOrigin: '',
      itemValue: '',
      currency: '',
      totalValue: '',
      file: null,
    },
  });

  // مراقبة قيمة الصنف والعدد لحساب القيمة الإجمالية
  const itemValue = watch('itemValue');
  const count = watch('count');

  // حساب القيمة الإجمالية عند تغيير قيمة الصنف أو العدد
  useEffect(() => {
    if (typeof itemValue === 'number' && typeof count === 'number') {
      setValue('totalValue', itemValue * count);
    }
  }, [itemValue, count, setValue]);

  // تحميل بيانات حركة الصنف عند التعديل
  useEffect(() => {
    if (isEditMode && itemMovement) {
      reset({
        declarationId: itemMovement.declarationId,
        movementDate: itemMovement.movementDate ? new Date(itemMovement.movementDate) : null,
        declarationNumber: itemMovement.declarationNumber,
        itemNumber: itemMovement.itemNumber || '',
        invoiceNumber: itemMovement.invoiceNumber,
        packingListNumber: itemMovement.packingListNumber || '',
        identificationClause: itemMovement.identificationClause || '',
        itemName: itemMovement.itemName || '',
        count: itemMovement.count || '',
        packageType: itemMovement.packageType || '',
        goodsType: itemMovement.goodsType || '',
        countryOfOrigin: itemMovement.countryOfOrigin || '',
        itemValue: itemMovement.itemValue ? parseFloat(itemMovement.itemValue) : '',
        currency: itemMovement.currency || '',
        totalValue: itemMovement.totalValue ? parseFloat(itemMovement.totalValue) : '',
        file: null,
      });
    }
  }, [isEditMode, itemMovement, reset]);

  // التعامل مع تغيير الملف
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setSelectedFile(file);
      setValue('file', file);
    }
  };

  // التعامل مع تغيير البيان
  const handleDeclarationChange = (declarationId: string) => {
    if (declarationsData) {
      const declaration = declarationsData.data.find((d) => d.id === declarationId);
      if (declaration) {
        setValue('declarationNumber', declaration.declarationNumber);
      }
    }
  };

  // التعامل مع إرسال النموذج
  const onSubmit = async (data: ItemMovementFormValues) => {
    try {
      if (isEditMode && id) {
        // تحديث حركة الصنف
        await updateMutation.mutateAsync({
          id,
          data: {
            movementDate: data.movementDate ? data.movementDate.toISOString() : undefined,
            declarationNumber: typeof data.declarationNumber === 'number' ? data.declarationNumber : undefined,
            itemNumber: typeof data.itemNumber === 'number' ? data.itemNumber : undefined,
            invoiceNumber: typeof data.invoiceNumber === 'number' ? data.invoiceNumber : undefined,
            packingListNumber: typeof data.packingListNumber === 'number' ? data.packingListNumber : undefined,
            identificationClause: typeof data.identificationClause === 'number' ? data.identificationClause : undefined,
            itemName: data.itemName || undefined,
            count: typeof data.count === 'number' ? data.count : undefined,
            packageType: data.packageType || undefined,
            goodsType: data.goodsType || undefined,
            countryOfOrigin: data.countryOfOrigin || undefined,
            itemValue: typeof data.itemValue === 'number' ? data.itemValue : undefined,
            currency: data.currency || undefined,
            totalValue: typeof data.totalValue === 'number' ? data.totalValue : undefined,
          },
          file: selectedFile || undefined,
        });
      } else {
        // إنشاء حركة صنف جديدة
        await createMutation.mutateAsync({
          data: {
            declarationId: data.declarationId,
            movementDate: data.movementDate ? data.movementDate.toISOString() : undefined,
            declarationNumber: typeof data.declarationNumber === 'number' ? data.declarationNumber : 0,
            itemNumber: typeof data.itemNumber === 'number' ? data.itemNumber : undefined,
            invoiceNumber: typeof data.invoiceNumber === 'number' ? data.invoiceNumber : 0,
            packingListNumber: typeof data.packingListNumber === 'number' ? data.packingListNumber : undefined,
            identificationClause: typeof data.identificationClause === 'number' ? data.identificationClause : undefined,
            itemName: data.itemName || undefined,
            count: typeof data.count === 'number' ? data.count : undefined,
            packageType: data.packageType || undefined,
            goodsType: data.goodsType || undefined,
            countryOfOrigin: data.countryOfOrigin || undefined,
            itemValue: typeof data.itemValue === 'number' ? data.itemValue : undefined,
            currency: data.currency || undefined,
            totalValue: typeof data.totalValue === 'number' ? data.totalValue : undefined,
          },
          file: selectedFile || undefined,
        });
      }

      // العودة إلى صفحة قائمة حركات الأصناف
      navigate('/items-movement');
    } catch (error) {
      console.error('Error submitting item movement:', error);
    }
  };

  // عرض حالة التحميل
  if (isEditMode && isLoadingItemMovement) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg">
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box display="flex" alignItems="center" mb={3}>
          <IconButton onClick={() => navigate('/items-movement')} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h5" component="h1">
            {isEditMode ? t('itemMovements.edit') : t('itemMovements.create')}
          </Typography>
        </Box>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid container spacing={3}>
            {/* معلومات حركة الصنف الأساسية */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                {t('itemMovements.basicInfo')}
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="declarationId"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    select
                    label={t('itemMovements.declaration')}
                    fullWidth
                    required
                    error={!!errors.declarationId}
                    helperText={errors.declarationId?.message}
                    onChange={(e) => {
                      field.onChange(e);
                      handleDeclarationChange(e.target.value);
                    }}
                    disabled={isEditMode}
                  >
                    <MenuItem value="">{t('common.select')}</MenuItem>
                    {declarationsData?.data.map((declaration) => (
                      <MenuItem key={declaration.id} value={declaration.id}>
                        {declaration.declarationNumber} - {declaration.clientName || declaration.companyName}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="declarationNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('itemMovements.declarationNumber')}
                    fullWidth
                    required
                    type="number"
                    error={!!errors.declarationNumber}
                    helperText={errors.declarationNumber?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                    disabled={isEditMode}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="movementDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label={t('itemMovements.movementDate')}
                    value={field.value}
                    onChange={field.onChange}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.movementDate,
                        helperText: errors.movementDate?.message,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="invoiceNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('itemMovements.invoiceNumber')}
                    fullWidth
                    required
                    type="number"
                    error={!!errors.invoiceNumber}
                    helperText={errors.invoiceNumber?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="packingListNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('itemMovements.packingListNumber')}
                    fullWidth
                    type="number"
                    error={!!errors.packingListNumber}
                    helperText={errors.packingListNumber?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="identificationClause"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('itemMovements.identificationClause')}
                    fullWidth
                    type="number"
                    error={!!errors.identificationClause}
                    helperText={errors.identificationClause?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            {/* معلومات الصنف */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                {t('itemMovements.itemInfo')}
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="itemName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('itemMovements.itemName')}
                    fullWidth
                    error={!!errors.itemName}
                    helperText={errors.itemName?.message}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="itemNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('itemMovements.itemNumber')}
                    fullWidth
                    type="number"
                    error={!!errors.itemNumber}
                    helperText={errors.itemNumber?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="count"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('itemMovements.count')}
                    fullWidth
                    type="number"
                    error={!!errors.count}
                    helperText={errors.count?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="packageType"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    select
                    label={t('itemMovements.packageType')}
                    fullWidth
                    error={!!errors.packageType}
                    helperText={errors.packageType?.message}
                  >
                    <MenuItem value="">{t('common.select')}</MenuItem>
                    <MenuItem value={PackageType.PALLET}>{t('itemMovements.pallet')}</MenuItem>
                    <MenuItem value={PackageType.CARTON}>{t('itemMovements.carton')}</MenuItem>
                    <MenuItem value={PackageType.BARREL}>{t('itemMovements.barrel')}</MenuItem>
                  </TextField>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="goodsType"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    select
                    label={t('itemMovements.goodsType')}
                    fullWidth
                    error={!!errors.goodsType}
                    helperText={errors.goodsType?.message}
                  >
                    <MenuItem value="">{t('common.select')}</MenuItem>
                    <MenuItem value={GoodsType.HUMAN_MEDICINE}>{t('itemMovements.humanMedicine')}</MenuItem>
                    <MenuItem value={GoodsType.LABORATORY_SOLUTIONS}>{t('itemMovements.laboratorySolutions')}</MenuItem>
                    <MenuItem value={GoodsType.MEDICAL_SUPPLIES}>{t('itemMovements.medicalSupplies')}</MenuItem>
                    <MenuItem value={GoodsType.SUGAR_STRIPS}>{t('itemMovements.sugarStrips')}</MenuItem>
                    <MenuItem value={GoodsType.MEDICAL_DEVICES}>{t('itemMovements.medicalDevices')}</MenuItem>
                    <MenuItem value={GoodsType.MISCELLANEOUS}>{t('itemMovements.miscellaneous')}</MenuItem>
                  </TextField>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="countryOfOrigin"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('itemMovements.countryOfOrigin')}
                    fullWidth
                    error={!!errors.countryOfOrigin}
                    helperText={errors.countryOfOrigin?.message}
                  />
                )}
              />
            </Grid>

            {/* معلومات القيمة */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                {t('itemMovements.valueInfo')}
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="itemValue"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('itemMovements.itemValue')}
                    fullWidth
                    type="number"
                    error={!!errors.itemValue}
                    helperText={errors.itemValue?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="currency"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    select
                    label={t('itemMovements.currency')}
                    fullWidth
                    error={!!errors.currency}
                    helperText={errors.currency?.message}
                  >
                    <MenuItem value="">{t('common.select')}</MenuItem>
                    <MenuItem value={Currency.USD}>{t('itemMovements.usd')}</MenuItem>
                    <MenuItem value={Currency.EUR}>{t('itemMovements.eur')}</MenuItem>
                    <MenuItem value={Currency.GBP}>{t('itemMovements.gbp')}</MenuItem>
                  </TextField>
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={4}>
              <Controller
                name="totalValue"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={t('itemMovements.totalValue')}
                    fullWidth
                    type="number"
                    error={!!errors.totalValue}
                    helperText={errors.totalValue?.message}
                    onChange={(e) => field.onChange(e.target.value === '' ? '' : Number(e.target.value))}
                    InputProps={{
                      readOnly: typeof itemValue === 'number' && typeof count === 'number',
                    }}
                  />
                )}
              />
            </Grid>

            {/* ملف PDF */}
            <Grid item xs={12} sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                {t('itemMovements.pdfFile')}
              </Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>

            <Grid item xs={12}>
              <Button variant="outlined" component="label">
                {t('common.uploadFile')}
                <input
                  type="file"
                  hidden
                  accept="application/pdf"
                  onChange={handleFileChange}
                />
              </Button>
              {selectedFile && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {t('common.selectedFile')}: {selectedFile.name}
                </Typography>
              )}
              {isEditMode && itemMovement?.pdfFile && !selectedFile && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {t('common.currentFile')}: {itemMovement.pdfFile.split('/').pop()}
                </Typography>
              )}
            </Grid>

            {/* أزرار الإرسال */}
            <Grid item xs={12} sx={{ mt: 3 }}>
              <Box display="flex" justifyContent="flex-end" gap={2}>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/items-movement')}
                  disabled={isSubmitting}
                >
                  {t('common.cancel')}
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<SaveIcon />}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <CircularProgress size={24} />
                  ) : isEditMode ? (
                    t('common.update')
                  ) : (
                    t('common.save')
                  )}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </Container>
  );
};

export default ItemMovementFormPage;
