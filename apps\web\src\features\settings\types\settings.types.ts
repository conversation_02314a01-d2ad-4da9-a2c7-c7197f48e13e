// واجهة المستخدم
export interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'USER';
}

// واجهة إعدادات النظام
export interface SystemSettings {
  id: string;
  companyName: string;
  companyLogo?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
  companyWebsite?: string;
  primaryColor: string;
  secondaryColor: string;
  defaultFont: string;
  defaultLanguage: string;
  maxFileSize: number;
  enablePrinting: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// واجهة قيم نموذج الإعدادات
export interface SettingsFormValues {
  companyName: string;
  companyLogo?: File | null;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
  companyWebsite?: string;
  primaryColor: string;
  secondaryColor: string;
  defaultFont: string;
  defaultLanguage: string;
  maxFileSize: number;
  enablePrinting: boolean;
}

// واجهة استجابة الإعدادات
export interface SettingsResponse {
  data: SystemSettings;
}
