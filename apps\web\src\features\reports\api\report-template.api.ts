import { api } from '../../../lib/api/api';
import {
  ReportTemplate,
  ReportTemplateListResponse,
  CreateReportTemplateRequest,
  UpdateReportTemplateRequest,
  ListReportTemplatesParams,
} from '../types/report-template.types';

/**
 * واجهة API لقوالب التقارير
 */
export const reportTemplateApi = {
  // الحصول على قائمة قوالب التقارير
  getReportTemplates: async (params: ListReportTemplatesParams = {}): Promise<ReportTemplateListResponse> => {
    return api.get<ReportTemplateListResponse>('/report-templates', { params });
  },

  // الحصول على قالب تقرير محدد
  getReportTemplate: async (id: string): Promise<ReportTemplate> => {
    return api.get<ReportTemplate>(`/report-templates/${id}`);
  },

  // إنشاء قالب تقرير جديد
  createReportTemplate: async (data: CreateReportTemplateRequest): Promise<ReportTemplate> => {
    return api.post<ReportTemplate>('/report-templates', data);
  },

  // تحديث قالب تقرير
  updateReportTemplate: async (id: string, data: UpdateReportTemplateRequest): Promise<ReportTemplate> => {
    return api.put<ReportTemplate>(`/report-templates/${id}`, data);
  },

  // حذف قالب تقرير
  deleteReportTemplate: async (id: string): Promise<void> => {
    return api.delete<void>(`/report-templates/${id}`);
  },

  // الحصول على القالب الافتراضي لنوع تقرير معين
  getDefaultTemplate: async (reportType: string): Promise<ReportTemplate> => {
    return api.get<ReportTemplate>(`/report-templates/default/${reportType}`);
  },
};

// React Query hooks (بديل لـ RTK Query)
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export const useGetReportTemplatesQuery = (params: ListReportTemplatesParams = {}) => {
  return useQuery({
    queryKey: ['reportTemplates', params],
    queryFn: () => reportTemplateApi.getReportTemplates(params),
  });
};

export const useGetReportTemplateQuery = (id: string) => {
  return useQuery({
    queryKey: ['reportTemplate', id],
    queryFn: () => reportTemplateApi.getReportTemplate(id),
    enabled: !!id,
  });
};

export const useCreateReportTemplateMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: reportTemplateApi.createReportTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reportTemplates'] });
    },
  });
};

export const useUpdateReportTemplateMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateReportTemplateRequest }) =>
      reportTemplateApi.updateReportTemplate(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['reportTemplates'] });
      queryClient.invalidateQueries({ queryKey: ['reportTemplate', id] });
    },
  });
};

export const useDeleteReportTemplateMutation = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: reportTemplateApi.deleteReportTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reportTemplates'] });
    },
  });
};

export const useGetDefaultTemplateQuery = (reportType: string) => {
  return useQuery({
    queryKey: ['defaultTemplate', reportType],
    queryFn: () => reportTemplateApi.getDefaultTemplate(reportType),
    enabled: !!reportType,
  });
};
