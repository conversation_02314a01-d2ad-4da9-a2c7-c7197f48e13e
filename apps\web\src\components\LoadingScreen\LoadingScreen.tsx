import React from 'react';
import { Box, CircularProgress, Typography, Paper } from '@mui/material';

interface LoadingScreenProps {
  /**
   * رسالة التحميل
   */
  message?: string;
  
  /**
   * هل يتم عرض الرسالة
   * @default true
   */
  showMessage?: boolean;
  
  /**
   * حجم مؤشر التحميل
   * @default 40
   */
  size?: number;
  
  /**
   * هل يتم عرض الخلفية
   * @default true
   */
  withBackground?: boolean;
}

/**
 * مكون شاشة التحميل
 * يستخدم لعرض حالة التحميل للمستخدم
 */
export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = 'جاري التحميل...',
  showMessage = true,
  size = 40,
  withBackground = true,
}) => {
  const content = (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 4,
        minHeight: '200px',
        width: '100%',
      }}
    >
      <CircularProgress size={size} />
      {showMessage && (
        <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
          {message}
        </Typography>
      )}
    </Box>
  );

  if (withBackground) {
    return (
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          backgroundColor: 'background.paper',
          borderRadius: 2,
        }}
      >
        {content}
      </Paper>
    );
  }

  return content;
};
