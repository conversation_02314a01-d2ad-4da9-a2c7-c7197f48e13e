import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Grid,
} from '@mui/material';

interface Permit {
  id: string;
  permitNumber: string;
  permitType: string;
  status: string;
  issuedDate: string;
  expiryDate?: string;
  notes?: string;
}

interface PermitsListProps {
  permits: Permit[];
  loading?: boolean;
  declarationId?: string;
}

export const PermitsList: React.FC<PermitsListProps> = ({
  permits,
  loading = false,
  declarationId,
}) => {
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography>جاري التحميل...</Typography>
      </Box>
    );
  }

  if (!permits.length) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography color="text.secondary">
          لا توجد تصاريح
        </Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={2}>
      {permits.map((permit) => (
        <Grid item xs={12} md={6} key={permit.id}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                <Typography variant="h6" component="h3">
                  {permit.permitNumber}
                </Typography>
                <Chip
                  label={permit.status}
                  size="small"
                  color="primary"
                />
              </Box>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                نوع التصريح: {permit.permitType}
              </Typography>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                تاريخ الإصدار: {new Date(permit.issuedDate).toLocaleDateString('ar-SA')}
              </Typography>

              {permit.expiryDate && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  تاريخ الانتهاء: {new Date(permit.expiryDate).toLocaleDateString('ar-SA')}
                </Typography>
              )}

              {permit.notes && (
                <Typography variant="body2" color="text.secondary">
                  ملاحظات: {permit.notes}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};
