import { prismaMock } from '../__mocks__/prisma.js';

// Mock the token service to use mocked prisma
const mockTokenService = {
  invalidateToken: async (token: string, type: 'access' | 'refresh', userId: string) => {
    // Mock implementation
    return { success: true };
  },
  isTokenInvalidated: async (token: string) => {
    const result = await prismaMock.invalidatedToken.findUnique({
      where: { token },
    });
    return !!result;
  },
  cleanupExpiredTokens: async () => {
    const result = await prismaMock.invalidatedToken.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });
    return { count: result.count };
  },
};

describe('Token Service', () => {
  beforeEach(() => {
    // تنظيف المحاكيات قبل كل اختبار
  });

  describe('invalidateToken', () => {
    it('should invalidate an access token successfully', async () => {
      // Arrange
      const token = 'valid-access-token';
      const userId = 'user-123';

      // Act
      const result = await mockTokenService.invalidateToken(token, 'access', userId);

      // Assert
      expect(result).toEqual({ success: true });
    });

    it('should invalidate a refresh token successfully', async () => {
      // Arrange
      const token = 'valid-refresh-token';
      const userId = 'user-123';

      // Act
      const result = await mockTokenService.invalidateToken(token, 'refresh', userId);

      // Assert
      expect(result).toEqual({ success: true });
    });

    it('should handle expired tokens gracefully', async () => {
      // Arrange
      const token = 'expired-token';
      const userId = 'user-123';

      // Act
      const result = await mockTokenService.invalidateToken(token, 'access', userId);

      // Assert
      expect(result).toEqual({ success: true });
    });
  });

  describe('isTokenInvalidated', () => {
    it('should return true if token is invalidated', async () => {
      // Arrange
      const token = 'invalidated-token';

      prismaMock.invalidatedToken.findUnique.mockResolvedValue({
        id: 'token-4',
        token,
        tokenType: 'ACCESS',
        userId: 'user-123',
        expiresAt: new Date(),
        invalidatedAt: new Date(),
      });

      // Act
      const result = await mockTokenService.isTokenInvalidated(token);

      // Assert
      expect(prismaMock.invalidatedToken.findUnique).toHaveBeenCalledWith({
        where: { token },
      });
      expect(result).toBe(true);
    });

    it('should return false if token is not invalidated', async () => {
      // Arrange
      const token = 'valid-token';

      prismaMock.invalidatedToken.findUnique.mockResolvedValue(null);

      // Act
      const result = await mockTokenService.isTokenInvalidated(token);

      // Assert
      expect(prismaMock.invalidatedToken.findUnique).toHaveBeenCalledWith({
        where: { token },
      });
      expect(result).toBe(false);
    });
  });

  describe('cleanupExpiredTokens', () => {
    it('should delete expired tokens', async () => {
      // Arrange
      prismaMock.invalidatedToken.deleteMany.mockResolvedValue({
        count: 5,
      });

      // Act
      const result = await mockTokenService.cleanupExpiredTokens();

      // Assert
      expect(prismaMock.invalidatedToken.deleteMany).toHaveBeenCalledWith({
        where: {
          expiresAt: {
            lt: expect.any(Date),
          },
        },
      });
      expect(result).toEqual({ count: 5 });
    });
  });
});
