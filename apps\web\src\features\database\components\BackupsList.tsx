import React from 'react';
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  Restore as RestoreIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { useBackups, useDeleteBackup, useRestoreBackup } from '../hooks/useDatabase';
import { downloadBackup } from '../api/database.api';
import { useTranslation } from 'react-i18next';
import { useConfirm } from '@lib/hooks/useConfirm';
import { formatDate } from '@lib/utils/date';
import { formatFileSize } from '@lib/utils/file';

/**
 * مكون قائمة النسخ الاحتياطية
 */
const BackupsList: React.FC = () => {
  const { t } = useTranslation();
  const { data: backups, isLoading, refetch } = useBackups();
  const deleteBackupMutation = useDeleteBackup();
  const restoreBackupMutation = useRestoreBackup();
  const { showConfirm } = useConfirm();

  // التعامل مع حذف نسخة احتياطية
  const handleDeleteBackup = async (fileName: string) => {
    const confirmed = await showConfirm({
      title: t('database.confirmDeleteBackup'),
      message: t('database.confirmDeleteBackupMessage'),
      confirmButtonText: t('common.delete'),
      cancelButtonText: t('common.cancel'),
      confirmButtonColor: 'error',
    });

    if (confirmed) {
      deleteBackupMutation.mutate(fileName);
    }
  };

  // التعامل مع استعادة نسخة احتياطية
  const handleRestoreBackup = async (fileName: string) => {
    const confirmed = await showConfirm({
      title: t('database.confirmRestoreBackup'),
      message: t('database.confirmRestoreBackupMessage'),
      confirmButtonText: t('database.restore'),
      cancelButtonText: t('common.cancel'),
      confirmButtonColor: 'warning',
    });

    if (confirmed) {
      restoreBackupMutation.mutate(fileName);
    }
  };

  // التعامل مع تنزيل نسخة احتياطية
  const handleDownloadBackup = (fileName: string) => {
    downloadBackup(fileName);
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper sx={{ p: 2, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h6">{t('database.backupsList')}</Typography>
        <Button
          startIcon={<RefreshIcon />}
          onClick={() => refetch()}
          disabled={isLoading}
        >
          {t('common.refresh')}
        </Button>
      </Box>

      {backups && backups.length > 0 ? (
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('database.fileName')}</TableCell>
                <TableCell>{t('database.size')}</TableCell>
                <TableCell>{t('database.createdAt')}</TableCell>
                <TableCell align="center">{t('common.actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {backups.map((backup) => (
                <TableRow key={backup.fileName}>
                  <TableCell>{backup.fileName}</TableCell>
                  <TableCell>{formatFileSize(backup.size)}</TableCell>
                  <TableCell>{formatDate(new Date(backup.createdAt))}</TableCell>
                  <TableCell align="center">
                    <Tooltip title={t('database.restore')}>
                      <IconButton
                        color="warning"
                        onClick={() => handleRestoreBackup(backup.fileName)}
                        disabled={restoreBackupMutation.isPending}
                      >
                        <RestoreIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={t('database.download')}>
                      <IconButton
                        color="primary"
                        onClick={() => handleDownloadBackup(backup.fileName)}
                      >
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={t('common.delete')}>
                      <IconButton
                        color="error"
                        onClick={() => handleDeleteBackup(backup.fileName)}
                        disabled={deleteBackupMutation.isPending}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      ) : (
        <Typography align="center" color="textSecondary" py={4}>
          {t('database.noBackups')}
        </Typography>
      )}
    </Paper>
  );
};

export default BackupsList;
