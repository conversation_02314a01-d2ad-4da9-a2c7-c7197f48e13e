import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, <PERSON> } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  Tab,
  Tabs,
  Typography,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useDeclaration } from '../hooks/useDeclaration';
import { useDeleteDeclaration } from '../hooks/useDeleteDeclaration';
import { useDownloadDeclarationPdf } from '../hooks/useDownloadDeclarationPdf';
import { formatDate } from '@lib/utils/date-utils';
import { PageHeader } from '@components/PageHeader';
import { LoadingScreen } from '@components/LoadingScreen';
import { ErrorScreen } from '@components/ErrorScreen';
import ItemMovementList from '@features/items-movement/components/ItemMovementList';
import { AuthorizationsList } from '@features/authorizations/components/AuthorizationsList';
import { ReleasesList } from '@features/releases/components/ReleasesList';
import { PermitsList } from '@features/permits/components/PermitsList';
import { GuaranteesList } from '@features/guarantees/components/GuaranteesList';

/**
 * واجهة خصائص TabPanel
 */
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

/**
 * مكون TabPanel
 */
const TabPanel: React.FC<TabPanelProps> = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`declaration-tabpanel-${index}`}
      aria-labelledby={`declaration-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

/**
 * صفحة تفاصيل البيان
 */
const DeclarationDetailsPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // استخدام خطافات البيانات
  const { data: declaration, isLoading, error } = useDeclaration(id || '');
  const deleteDeclarationMutation = useDeleteDeclaration();
  const downloadPdfMutation = useDownloadDeclarationPdf();

  // التعامل مع تغيير التبويب
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // التعامل مع حذف البيان
  const handleDelete = async () => {
    if (!id) return;

    try {
      await deleteDeclarationMutation.mutateAsync(id);
      navigate('/declarations');
    } catch (error) {
      console.error('Error deleting declaration:', error);
    }
  };

  // التعامل مع تحميل ملف PDF
  const handleDownloadPdf = async () => {
    if (!id) return;

    try {
      await downloadPdfMutation.mutateAsync(id);
    } catch (error) {
      console.error('Error downloading PDF:', error);
    }
  };

  // عرض شاشة التحميل
  if (isLoading) {
    return <LoadingScreen />;
  }

  // عرض شاشة الخطأ
  if (error || !declaration) {
    return (
      <ErrorScreen
        message={t('declarations.details.errorLoading')}
        onRetry={() => window.location.reload()}
      />
    );
  }

  return (
    <Box>
      <PageHeader
        title={t('declarations.details.title')}
        subtitle={`${t('declarations.declarationNumber')}: ${declaration.declarationNumber || declaration.id}`}
        backButton={
          <Button
            component={Link}
            to="/declarations"
            startIcon={<ArrowBackIcon />}
            variant="outlined"
          >
            {t('common.back')}
          </Button>
        }
        actions={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              startIcon={<EditIcon />}
              variant="outlined"
              component={Link}
              to={`/declarations/edit/${id}`}
            >
              {t('common.edit')}
            </Button>
            <Button
              startIcon={<PrintIcon />}
              variant="outlined"
              onClick={() => window.print()}
            >
              {t('common.print')}
            </Button>
            <Button
              startIcon={<DownloadIcon />}
              variant="outlined"
              onClick={handleDownloadPdf}
              disabled={downloadPdfMutation.isPending}
            >
              {downloadPdfMutation.isPending ? (
                <CircularProgress size={24} />
              ) : (
                t('common.downloadPdf')
              )}
            </Button>
            <Button
              startIcon={<DeleteIcon />}
              variant="outlined"
              color="error"
              onClick={() => setDeleteDialogOpen(true)}
            >
              {t('common.delete')}
            </Button>
          </Box>
        }
      />

      <Paper sx={{ mb: 3 }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('declarations.details.basicInfo')}
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('declarations.declarationNumber')}
                </Typography>
                <Typography variant="body1">{declaration.declarationNumber || declaration.id}</Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('declarations.declarationType')}
                </Typography>
                <Typography variant="body1">
                  {t(`declarations.types.${declaration.declarationType}`)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('declarations.declarationDate')}
                </Typography>
                <Typography variant="body1">
                  {formatDate(declaration.declarationDate)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('declarations.client')}
                </Typography>
                <Typography variant="body1">
                  {declaration.client?.name || declaration.clientName || t('common.notSpecified')}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('declarations.status')}
                </Typography>
                <Typography variant="body1">
                  {declaration.status ? t(`declarations.statuses.${declaration.status}`) : t('common.notSpecified')}
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('declarations.notes')}
                </Typography>
                <Typography variant="body1">
                  {(declaration as any).notes || t('common.notSpecified')}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Paper>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label={t('declarations.tabs.itemMovements')} />
          <Tab label={t('declarations.tabs.authorizations')} />
          <Tab label={t('declarations.tabs.releases')} />
          <Tab label={t('declarations.tabs.permits')} />
          <Tab label={t('declarations.tabs.guarantees')} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <ItemMovementList movements={[]} declarationId={id || ''} />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <AuthorizationsList authorizations={[]} declarationId={id || ''} />
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <ReleasesList releases={[]} declarationId={id || ''} />
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <PermitsList permits={[]} declarationId={id || ''} />
        </TabPanel>

        <TabPanel value={tabValue} index={4}>
          <GuaranteesList guarantees={[]} declarationId={id || ''} />
        </TabPanel>
      </Paper>

      {/* حوار تأكيد الحذف */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>{t('declarations.delete.confirmTitle')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('declarations.delete.confirmMessage')}
          </DialogContentText>
          <Alert severity="warning" sx={{ mt: 2 }}>
            {t('declarations.delete.warning')}
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleDelete}
            color="error"
            disabled={deleteDeclarationMutation.isPending}
          >
            {deleteDeclarationMutation.isPending ? (
              <CircularProgress size={24} />
            ) : (
              t('common.delete')
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DeclarationDetailsPage;
