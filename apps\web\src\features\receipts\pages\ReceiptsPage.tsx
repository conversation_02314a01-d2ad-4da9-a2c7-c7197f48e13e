import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import { useReceipts, useDeleteReceipt, useDownloadReceiptPdf } from '../hooks/useReceipts';
import { ReceiptSearchParams } from '../types/receipt.types';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const ReceiptsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  // حالة البحث
  const [searchParams, setSearchParams] = useState<ReceiptSearchParams>({
    page: 1,
    limit: 10,
    sort: 'createdAt',
    order: 'desc',
  });
  
  // استخدام خطافات الاستلامات
  const { data, isLoading, isError, refetch } = useReceipts(searchParams);
  const deleteMutation = useDeleteReceipt();
  const downloadPdfMutation = useDownloadReceiptPdf();
  
  // التعامل مع تغيير معلمات البحث
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchParams((prev) => ({ ...prev, search: e.target.value }));
  };
  
  const handleSearch = () => {
    refetch();
  };
  
  // التعامل مع إنشاء استلام جديد
  const handleCreateReceipt = () => {
    navigate('/receipts/new');
  };
  
  // التعامل مع عرض تفاصيل الاستلام
  const handleViewReceipt = (id: string) => {
    navigate(`/receipts/${id}`);
  };
  
  // التعامل مع تعديل الاستلام
  const handleEditReceipt = (id: string) => {
    navigate(`/receipts/${id}/edit`);
  };
  
  // التعامل مع حذف الاستلام
  const handleDeleteReceipt = async (id: string) => {
    if (window.confirm(t('receipts.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id);
      } catch (error) {
        console.error('Error deleting receipt:', error);
      }
    }
  };
  
  // التعامل مع تحميل ملف PDF
  const handleDownloadPdf = async (id: string) => {
    try {
      const blob = await downloadPdfMutation.mutateAsync(id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `receipt-${id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF:', error);
    }
  };
  
  // عرض رسالة التحميل
  if (isLoading) {
    return (
      <Container maxWidth="lg">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }
  
  // عرض رسالة الخطأ
  if (isError) {
    return (
      <Container maxWidth="lg">
        <Box textAlign="center" py={4}>
          <Typography variant="h6" color="error">
            {t('common.errorOccurred')}
          </Typography>
          <Button variant="contained" onClick={() => refetch()} sx={{ mt: 2 }}>
            {t('common.retry')}
          </Button>
        </Box>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('receipts.title')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('receipts.description')}
        </Typography>
      </Box>
      
      {/* نموذج البحث */}
      <Box mb={4} component="form" noValidate autoComplete="off">
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              label={t('common.search')}
              variant="outlined"
              value={searchParams.search || ''}
              onChange={handleSearchChange}
              InputProps={{
                endAdornment: (
                  <IconButton size="small" onClick={handleSearch}>
                    <SearchIcon />
                  </IconButton>
                ),
              }}
            />
          </Grid>
        </Grid>
      </Box>
      
      {/* زر إنشاء استلام جديد */}
      <Box mb={3} display="flex" justifyContent="flex-end">
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreateReceipt}
        >
          {t('receipts.create')}
        </Button>
      </Box>
      
      {/* قائمة الاستلامات */}
      <Box>
        {data?.data.length === 0 ? (
          <Box textAlign="center" py={4}>
            <Typography variant="h6">{t('receipts.noReceipts')}</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateReceipt}
              sx={{ mt: 2 }}
            >
              {t('receipts.create')}
            </Button>
          </Box>
        ) : (
          <Grid container spacing={2}>
            {data?.data.map((receipt) => (
              <Grid item xs={12} sm={6} md={4} key={receipt.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="h6">
                        {t('receipts.number')}: {receipt.receiptNumber}
                      </Typography>
                    </Box>
                    
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      {t('receipts.receiverName')}: {receipt.receiverName}
                    </Typography>
                    
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      {t('receipts.receiptDate')}: {format(new Date(receipt.receiptDate), 'PPP', { locale: arSA })}
                    </Typography>
                    
                    {receipt.receiverPhone && (
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {t('receipts.receiverPhone')}: {receipt.receiverPhone}
                      </Typography>
                    )}
                    
                    {receipt.declarationNumber && (
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {t('declarations.number')}: {receipt.declarationNumber}
                      </Typography>
                    )}
                    
                    <Box display="flex" justifyContent="flex-end" mt={2}>
                      {receipt.pdfFile && (
                        <Tooltip title={t('common.downloadPdf')}>
                          <IconButton
                            size="small"
                            onClick={() => handleDownloadPdf(receipt.id)}
                            sx={{ mr: 1 }}
                          >
                            <PdfIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      <Tooltip title={t('common.view')}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewReceipt(receipt.id)}
                          sx={{ mr: 1 }}
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('common.edit')}>
                        <IconButton
                          size="small"
                          onClick={() => handleEditReceipt(receipt.id)}
                          sx={{ mr: 1 }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('common.delete')}>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteReceipt(receipt.id)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>
    </Container>
  );
};

export default ReceiptsPage;
