import { Request, Response, NextFunction } from 'express';
import { authorizationService } from '../services/authorization.service.js';
import { successResponse, paginatedResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const authorizationController = {
  /**
   * إنشاء تفويض جديد
   */
  createAuthorization: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على بيانات التفويض من الطلب
      const authorizationData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // إنشاء التفويض
      const authorization = await authorizationService.createAuthorization(
        authorizationData,
        file
      );

      return res.status(201).json(successResponse(authorization, 'تم إنشاء التفويض بنجاح', 201));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث تفويض
   */
  updateAuthorization: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف التفويض من المعلمات
      const { id } = req.params;

      // الحصول على بيانات التفويض من الطلب
      const authorizationData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // تحديث التفويض
      const authorization = await authorizationService.updateAuthorization(
        id,
        authorizationData,
        file
      );

      return res.status(200).json(successResponse(authorization, 'تم تحديث التفويض بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على تفويض محدد
   */
  getAuthorization: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معرف التفويض من المعلمات
      const { id } = req.params;

      // الحصول على التفويض
      const authorization = await authorizationService.getAuthorization(id);

      return res.status(200).json(successResponse(authorization));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف تفويض
   */
  deleteAuthorization: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف التفويض من المعلمات
      const { id } = req.params;

      // حذف التفويض
      await authorizationService.deleteAuthorization(id);

      return res.status(200).json(successResponse(null, 'تم حذف التفويض بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة التفويضات
   */
  listAuthorizations: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معلمات البحث
      const { page, limit, sort, order, search, authorizationType, fromDate, toDate, declarationId, isActive } =
        req.query as any;

      // الحصول على قائمة التفويضات
      const result = await authorizationService.listAuthorizations({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        search,
        authorizationType,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined,
        declarationId,
        isActive,
      });

      return res.status(200).json(paginatedResponse(
        result.data,
        result.pagination.page,
        result.pagination.limit,
        result.pagination.total
      ));
    } catch (error) {
      next(error);
    }
  },
};
