import { prisma } from '../../../core/utils/prisma';
import { HttpException } from '../../../core/middleware/error.middleware';
import { saveUploadedPdf } from '../../../core/utils/pdf/pdfService';
import { Prisma } from '@prisma/client';

// تعريف نوع البيان
type DeclarationType = 'IMPORT' | 'EXPORT';

// تعريف نوع البضاعة
type GoodsType = 'HUMAN_MEDICINE' | 'LABORATORY_SOLUTIONS' | 'MEDICAL_SUPPLIES' | 'SUGAR_STRIPS' | 'MEDICAL_DEVICES' | 'MISCELLANEOUS';

// واجهة إنشاء البيان
interface CreateDeclarationInput {
  taxNumber: string;
  clientName: string;
  companyName?: string;
  policyNumber?: string;
  invoiceNumber?: string;
  gatewayEntryNumber?: string;
  declarationType: DeclarationType;
  declarationDate: Date;
  count?: number;
  weight?: number;
  goodsType?: GoodsType;
  itemsCount?: number;
  entryDate?: Date;
  exitDate?: Date;
  clientId?: string | null;
  drivers?: {
    driverName: string;
    truckNumber: string;
    trailerNumber?: string;
    driverPhone?: string;
  }[];
}

// واجهة تحديث البيان
interface UpdateDeclarationInput {
  taxNumber?: string;
  clientName?: string;
  companyName?: string;
  policyNumber?: string;
  invoiceNumber?: string;
  gatewayEntryNumber?: string;
  declarationType?: DeclarationType;
  declarationDate?: Date;
  count?: number;
  weight?: number;
  goodsType?: GoodsType;
  itemsCount?: number;
  entryDate?: Date;
  exitDate?: Date;
  clientId?: string | null;
  drivers?: {
    id?: string;
    driverName: string;
    truckNumber: string;
    trailerNumber?: string;
    driverPhone?: string;
  }[];
}

// واجهة معلمات قائمة البيانات
interface ListDeclarationsParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationType?: DeclarationType;
  fromDate?: Date;
  toDate?: Date;
  clientId?: string;
}

// خدمة البيانات
export const declarationService = {
  // إنشاء بيان جديد
  createDeclaration: async (
    data: CreateDeclarationInput,
    _userId: string, // استخدام _ للإشارة إلى أن المتغير غير مستخدم
    file?: Express.Multer.File
  ) => {
    try {
      // الحصول على آخر رقم بيان
      const lastDeclaration = await prisma.declaration.findFirst({
        orderBy: { declarationNumber: 'desc' },
      });

      // إنشاء رقم بيان جديد
      const declarationNumber = lastDeclaration
        ? `${parseInt(lastDeclaration.declarationNumber) + 1}`
        : '1001';

      // إنشاء البيان
      const declaration = await prisma.declaration.create({
        data: {
          declarationNumber,
          declarationType: data.declarationType,
          declarationDate: data.declarationDate,
          clientId: data.clientId || null,
          // إضافة الحقول المخصصة باستخدام Prisma.raw
          ...Prisma.raw({
            taxNumber: data.taxNumber,
            clientName: data.clientName,
            companyName: data.companyName,
            policyNumber: data.policyNumber,
            invoiceNumber: data.invoiceNumber,
            gatewayEntryNumber: data.gatewayEntryNumber,
            count: data.count,
            weight: data.weight,
            goodsType: data.goodsType,
            itemsCount: data.itemsCount,
            entryDate: data.entryDate,
            exitDate: data.exitDate,
          } as any),
        },
      });

      // إنشاء السائقين إذا تم تقديمهم
      if (data.drivers && data.drivers.length > 0) {
        for (const driver of data.drivers) {
          await prisma.$executeRaw`
            INSERT INTO drivers ("id", "driverName", "truckNumber", "trailerNumber", "driverPhone", "declarationId", "createdAt", "updatedAt")
            VALUES (
              ${Prisma.raw('uuid_generate_v4()')}, 
              ${driver.driverName}, 
              ${driver.truckNumber}, 
              ${driver.trailerNumber || null}, 
              ${driver.driverPhone || null}, 
              ${declaration.id}, 
              ${new Date()}, 
              ${new Date()}
            )
          `;
        }
      }

      // حفظ ملف PDF إذا تم تقديمه
      if (file) {
        const pdfPath = saveUploadedPdf(file, 'declarations', declaration.id);
        await prisma.$executeRaw`
          UPDATE declarations 
          SET "pdfFile" = ${pdfPath} 
          WHERE id = ${declaration.id}
        `;
      }

      // استرجاع البيان مع العلاقات
      const result = await prisma.$queryRaw`
        SELECT d.*, 
               json_agg(dr.*) as drivers,
               json_build_object(
                 'id', c.id,
                 'name', c.name,
                 'taxNumber', c."taxNumber",
                 'phone', c.phone,
                 'email', c.email,
                 'address', c.address,
                 'createdAt', c."createdAt",
                 'updatedAt', c."updatedAt"
               ) as client
        FROM declarations d
        LEFT JOIN drivers dr ON d.id = dr."declarationId"
        LEFT JOIN clients c ON d."clientId" = c.id
        WHERE d.id = ${declaration.id}
        GROUP BY d.id, c.id
      `;
      
      return result[0] as any;
    } catch (error: any) {
      console.error('خطأ في إنشاء البيان:', error);
      throw new HttpException(500, 'حدث خطأ أثناء إنشاء البيان', 'Declaration Creation Error');
    }
  },

  // تحديث بيان
  updateDeclaration: async (
    id: string,
    data: UpdateDeclarationInput,
    _userId: string, // استخدام _ للإشارة إلى أن المتغير غير مستخدم
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود البيان
      const existingDeclaration = await prisma.declaration.findUnique({
        where: { id },
      });

      if (!existingDeclaration) {
        throw new HttpException(404, 'البيان غير موجود', 'Declaration Not Found');
      }

      // استخدام استعلام مباشر للحصول على مسار الملف الحالي
      const currentPdfResult = await prisma.$queryRaw`
        SELECT "pdfFile" FROM declarations WHERE id = ${id}
      `;
      
      let pdfFilePath = (currentPdfResult as any)[0]?.pdfFile;
      if (file) {
        pdfFilePath = saveUploadedPdf(file, 'declarations', id);
      }

      // تحديث البيان باستخدام استعلام مباشر
      const updateFields = [];
      
      if (data.taxNumber !== undefined) updateFields.push(`"taxNumber" = '${data.taxNumber}'`);
      if (data.clientName !== undefined) updateFields.push(`"clientName" = '${data.clientName}'`);
      if (data.companyName !== undefined) updateFields.push(`"companyName" = ${data.companyName === null ? 'NULL' : `'${data.companyName}'`}`);
      if (data.policyNumber !== undefined) updateFields.push(`"policyNumber" = ${data.policyNumber === null ? 'NULL' : `'${data.policyNumber}'`}`);
      if (data.invoiceNumber !== undefined) updateFields.push(`"invoiceNumber" = ${data.invoiceNumber === null ? 'NULL' : `'${data.invoiceNumber}'`}`);
      if (data.gatewayEntryNumber !== undefined) updateFields.push(`"gatewayEntryNumber" = ${data.gatewayEntryNumber === null ? 'NULL' : `'${data.gatewayEntryNumber}'`}`);
      if (data.declarationType !== undefined) updateFields.push(`"declarationType" = '${data.declarationType}'`);
      if (data.declarationDate !== undefined) updateFields.push(`"declarationDate" = '${data.declarationDate.toISOString()}'`);
      if (data.count !== undefined) updateFields.push(`"count" = ${data.count === null ? 'NULL' : data.count}`);
      if (data.weight !== undefined) updateFields.push(`"weight" = ${data.weight === null ? 'NULL' : data.weight}`);
      if (data.goodsType !== undefined) updateFields.push(`"goodsType" = ${data.goodsType === null ? 'NULL' : `'${data.goodsType}'`}`);
      if (data.itemsCount !== undefined) updateFields.push(`"itemsCount" = ${data.itemsCount === null ? 'NULL' : data.itemsCount}`);
      if (data.entryDate !== undefined) updateFields.push(`"entryDate" = ${data.entryDate === null ? 'NULL' : `'${data.entryDate.toISOString()}'`}`);
      if (data.exitDate !== undefined) updateFields.push(`"exitDate" = ${data.exitDate === null ? 'NULL' : `'${data.exitDate.toISOString()}'`}`);
      if (data.clientId !== undefined) updateFields.push(`"clientId" = ${data.clientId === null ? 'NULL' : `'${data.clientId}'`}`);
      if (pdfFilePath !== undefined) updateFields.push(`"pdfFile" = ${pdfFilePath === null ? 'NULL' : `'${pdfFilePath}'`}`);
      
      if (updateFields.length > 0) {
        await prisma.$executeRaw`
          UPDATE declarations 
          SET ${Prisma.raw(updateFields.join(', '))}
          WHERE id = ${id}
        `;
      }

      // تحديث السائقين إذا تم تقديمهم
      if (data.drivers && data.drivers.length > 0) {
        // حذف السائقين الحاليين باستخدام استعلام مباشر
        await prisma.$executeRaw`
          DELETE FROM drivers WHERE "declarationId" = ${id}
        `;

        // إنشاء سائقين جدد باستخدام استعلام مباشر
        for (const driver of data.drivers) {
          await prisma.$executeRaw`
            INSERT INTO drivers ("id", "driverName", "truckNumber", "trailerNumber", "driverPhone", "declarationId", "createdAt", "updatedAt")
            VALUES (
              ${Prisma.raw('uuid_generate_v4()')}, 
              ${driver.driverName}, 
              ${driver.truckNumber}, 
              ${driver.trailerNumber || null}, 
              ${driver.driverPhone || null}, 
              ${id}, 
              ${new Date()}, 
              ${new Date()}
            )
          `;
        }
      }

      // استرجاع البيان المحدث مع العلاقات
      const result = await prisma.$queryRaw`
        SELECT d.*, 
               json_agg(dr.*) as drivers,
               json_build_object(
                 'id', c.id,
                 'name', c.name,
                 'taxNumber', c."taxNumber",
                 'phone', c.phone,
                 'email', c.email,
                 'address', c.address,
                 'createdAt', c."createdAt",
                 'updatedAt', c."updatedAt"
               ) as client
        FROM declarations d
        LEFT JOIN drivers dr ON d.id = dr."declarationId"
        LEFT JOIN clients c ON d."clientId" = c.id
        WHERE d.id = ${id}
        GROUP BY d.id, c.id
      `;
      
      return result[0] as any;
    } catch (error: any) {
      console.error('خطأ في تحديث البيان:', error);
      throw new HttpException(500, 'حدث خطأ أثناء تحديث البيان', 'Declaration Update Error');
    }
  },
