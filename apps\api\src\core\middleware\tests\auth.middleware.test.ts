import { Request, Response } from 'express';
import { HttpException } from '../error.middleware.js';
import { prismaMock } from '../../utils/__mocks__/prisma.js';

// Mock middleware functions
const mockAuthMiddleware = async (req: Request, res: Response, next: any) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return next(new HttpException(401, 'رمز الوصول مطلوب', 'Unauthorized'));
  }

  const token = authHeader.substring(7);

  // Mock user data
  req.user = {
    id: 'user-123',
    username: 'testuser',
    role: 'USER',
  };

  next();
};

const mockAdminMiddleware = (req: Request, res: Response, next: any) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return next(new HttpException(403, 'صلاحيات المدير مطلوبة', 'Forbidden'));
  }
  next();
};

describe('Auth Middleware', () => {
  describe('authMiddleware', () => {
    it('should add user to request when token is valid', async () => {
      // Arrange
      const mockRequest = {
        headers: {
          authorization: 'Bearer valid-token',
        },
        user: undefined,
      } as any;

      const mockResponse = {} as any;
      let nextCalled = false;
      let nextError: any = null;
      const nextFunction = (error?: any) => {
        nextCalled = true;
        nextError = error;
      };

      // Act
      await mockAuthMiddleware(mockRequest, mockResponse, nextFunction);

      // Assert
      expect(mockRequest.user).toEqual({
        id: 'user-123',
        username: 'testuser',
        role: 'USER',
      });
      expect(nextCalled).toBe(true);
      expect(nextError).toBeUndefined();
    });

    it('should call next with error when no authorization header', async () => {
      // Arrange
      const mockRequest = {
        headers: {},
        user: undefined,
      } as any;

      const mockResponse = {} as any;
      let nextCalled = false;
      let nextError: any = null;
      const nextFunction = (error?: any) => {
        nextCalled = true;
        nextError = error;
      };

      // Act
      await mockAuthMiddleware(mockRequest, mockResponse, nextFunction);

      // Assert
      expect(nextCalled).toBe(true);
      expect(nextError).toBeInstanceOf(HttpException);
    });
  });

  describe('adminMiddleware', () => {
    it('should call next when user is admin', () => {
      // Arrange
      const mockRequest = {
        user: {
          id: 'admin-123',
          username: 'admin',
          role: 'ADMIN',
        },
      } as any;

      const mockResponse = {} as any;
      let nextCalled = false;
      let nextError: any = null;
      const nextFunction = (error?: any) => {
        nextCalled = true;
        nextError = error;
      };

      // Act
      mockAdminMiddleware(mockRequest, mockResponse, nextFunction);

      // Assert
      expect(nextCalled).toBe(true);
      expect(nextError).toBeUndefined();
    });

    it('should call next with error when user is not admin', () => {
      // Arrange
      const mockRequest = {
        user: {
          id: 'user-123',
          username: 'user',
          role: 'USER',
        },
      } as any;

      const mockResponse = {} as any;
      let nextCalled = false;
      let nextError: any = null;
      const nextFunction = (error?: any) => {
        nextCalled = true;
        nextError = error;
      };

      // Act
      mockAdminMiddleware(mockRequest, mockResponse, nextFunction);

      // Assert
      expect(nextCalled).toBe(true);
      expect(nextError).toBeInstanceOf(HttpException);
    });
  });
});
