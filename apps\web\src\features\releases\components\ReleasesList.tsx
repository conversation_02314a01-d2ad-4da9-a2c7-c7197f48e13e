import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Grid,
} from '@mui/material';

interface Release {
  id: string;
  releaseNumber: string;
  status: string;
  releaseDate: string;
  notes?: string;
}

interface ReleasesListProps {
  releases: Release[];
  loading?: boolean;
  declarationId?: string;
}

export const ReleasesList: React.FC<ReleasesListProps> = ({
  releases,
  loading = false,
  declarationId,
}) => {
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography>جاري التحميل...</Typography>
      </Box>
    );
  }

  if (!releases.length) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography color="text.secondary">
          لا توجد إفراجات
        </Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={2}>
      {releases.map((release) => (
        <Grid item xs={12} md={6} key={release.id}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                <Typography variant="h6" component="h3">
                  {release.releaseNumber}
                </Typography>
                <Chip
                  label={release.status}
                  size="small"
                  color="primary"
                />
              </Box>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                تاريخ الإفراج: {new Date(release.releaseDate).toLocaleDateString('ar-SA')}
              </Typography>

              {release.notes && (
                <Typography variant="body2" color="text.secondary">
                  ملاحظات: {release.notes}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};
