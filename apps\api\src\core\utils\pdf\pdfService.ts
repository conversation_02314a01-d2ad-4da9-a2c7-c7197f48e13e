import { PDFDocument, rgb } from 'pdf-lib';
import fontkit from '@pdf-lib/fontkit';
import fs from 'fs';
import path from 'path';
import { logger } from '../logger.js';
import { config } from '../../config/app.config.js';

// Path to Arabic fonts
const FONTS_DIR = path.join(process.cwd(), '../../apps/web/public/assets/fonts');

// Font cache
const fontCache: Record<string, Uint8Array> = {};

/**
 * Load font from file
 */
const loadFont = async (fontName: string): Promise<Uint8Array> => {
  if (fontCache[fontName]) {
    return fontCache[fontName];
  }

  try {
    const fontPath = path.join(FONTS_DIR, `${fontName}.ttf`);
    const fontBytes = fs.readFileSync(fontPath);
    fontCache[fontName] = fontBytes;
    return fontBytes;
  } catch (error) {
    logger.error(`Failed to load font ${fontName}:`, error);
    throw new Error(`Failed to load font ${fontName}`);
  }
};

/**
 * Create a new PDF document with Arabic text
 */
export const createPdfWithArabicText = async (
  text: string,
  outputPath: string,
  fontName = 'Amiri-Regular'
): Promise<string> => {
  try {
    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();

    // Register fontkit
    pdfDoc.registerFontkit(fontkit);

    // Load and embed font
    const fontBytes = await loadFont(fontName);
    const font = await pdfDoc.embedFont(fontBytes);

    // Add a page
    const page = pdfDoc.addPage();

    // Get page dimensions
    const { width, height } = page.getSize();

    // Add text to the page
    page.drawText(text, {
      x: width - 50,
      y: height - 50,
      font,
      size: 20,
      color: rgb(0, 0, 0),
    });

    // Save the PDF
    const pdfBytes = await pdfDoc.save();

    // Ensure directory exists
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write to file
    fs.writeFileSync(outputPath, pdfBytes);

    return outputPath;
  } catch (error) {
    logger.error('Failed to create PDF with Arabic text:', error);
    throw new Error('Failed to create PDF with Arabic text');
  }
};

/**
 * Add Arabic text to an existing PDF
 */
export const addArabicTextToPdf = async (
  inputPath: string,
  outputPath: string,
  text: string,
  x: number,
  y: number,
  fontName = 'Amiri-Regular',
  fontSize = 12
): Promise<string> => {
  try {
    // Load existing PDF
    const pdfBytes = fs.readFileSync(inputPath);
    const pdfDoc = await PDFDocument.load(pdfBytes);

    // Register fontkit
    pdfDoc.registerFontkit(fontkit);

    // Load and embed font
    const fontBytes = await loadFont(fontName);
    const font = await pdfDoc.embedFont(fontBytes);

    // Get the first page
    const page = pdfDoc.getPages()[0];

    // Add text to the page
    page.drawText(text, {
      x,
      y,
      font,
      size: fontSize,
      color: rgb(0, 0, 0),
    });

    // Save the PDF
    const modifiedPdfBytes = await pdfDoc.save();

    // Ensure directory exists
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write to file
    fs.writeFileSync(outputPath, modifiedPdfBytes);

    return outputPath;
  } catch (error) {
    logger.error('Failed to add Arabic text to PDF:', error);
    throw new Error('Failed to add Arabic text to PDF');
  }
};

/**
 * Save uploaded PDF file
 */
export const saveUploadedPdf = (
  file: Express.Multer.File,
  entityType: string,
  entityId: string
): string => {
  try {
    // Create upload directory if it doesn't exist
    const uploadDir = path.join(config.upload.dir, entityType);
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Generate file path
    const fileName = `${entityId}_${Date.now()}.pdf`;
    const filePath = path.join(uploadDir, fileName);

    // Write file
    fs.writeFileSync(filePath, file.buffer);

    // Return relative path
    return `${entityType}/${fileName}`;
  } catch (error) {
    logger.error('Failed to save uploaded PDF:', error);
    throw new Error('Failed to save uploaded PDF');
  }
};
