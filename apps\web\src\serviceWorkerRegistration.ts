/**
 * تسجيل Service Worker
 * يستخدم لتحسين أداء التطبيق وتمكين العمل دون اتصال
 */

// التحقق مما إذا كان Service Worker مدعومًا
const isServiceWorkerSupported = 'serviceWorker' in navigator;

/**
 * تسجيل Service Worker
 */
export const registerServiceWorker = () => {
  if (isServiceWorkerSupported) {
    window.addEventListener('load', () => {
      const swUrl = `${window.location.origin}/service-worker.js`;
      
      navigator.serviceWorker
        .register(swUrl)
        .then((registration) => {
          console.log('تم تسجيل Service Worker بنجاح:', registration);
          
          // التحقق من وجود تحديثات
          registration.onupdatefound = () => {
            const installingWorker = registration.installing;
            if (installingWorker == null) {
              return;
            }
            
            installingWorker.onstatechange = () => {
              if (installingWorker.state === 'installed') {
                if (navigator.serviceWorker.controller) {
                  // في هذه المرحلة، تم تنزيل المحتوى المحدث
                  console.log('تم تنزيل محتوى جديد وسيتم استخدامه عند إعادة تحميل الصفحة.');
                  
                  // إظهار إشعار للمستخدم
                  if (window.confirm('يتوفر تحديث جديد للتطبيق. هل ترغب في تحديث الصفحة لتطبيق التغييرات؟')) {
                    window.location.reload();
                  }
                } else {
                  // في هذه المرحلة، تم تخزين كل شيء مؤقتًا
                  console.log('تم تخزين المحتوى للاستخدام دون اتصال.');
                }
              }
            };
          };
        })
        .catch((error) => {
          console.error('فشل في تسجيل Service Worker:', error);
        });
    });
  }
};

/**
 * إلغاء تسجيل Service Worker
 */
export const unregisterServiceWorker = () => {
  if (isServiceWorkerSupported) {
    navigator.serviceWorker.ready
      .then((registration) => {
        registration.unregister();
      })
      .catch((error) => {
        console.error('فشل في إلغاء تسجيل Service Worker:', error);
      });
  }
};
