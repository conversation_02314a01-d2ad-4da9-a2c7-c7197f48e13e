import { useQuery } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';

export interface ItemMovement {
  id: string;
  itemName: string;
  movementType: string;
  movementNumber: string;
  quantity: number;
  unit: string;
  date: string;
  movementDate: string;
  notes?: string;
  declarationId?: string;
  declaration?: {
    declarationNumber: string;
  };
  status: string;
  pdfFile?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export const useItemMovement = (id: string) => {
  return useQuery({
    queryKey: ['itemMovement', id],
    queryFn: async (): Promise<ItemMovement> => {
      const response = await api.get<ItemMovement>(`/api/item-movements/${id}`);
      return response;
    },
    enabled: !!id,
  });
};
