import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  AppBar,
  Avatar,
  Box,
  Button,
  Divider,
  IconButton,
  ListItemIcon,
  Menu,
  MenuItem,
  Toolbar,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Translate as TranslateIcon,
  Logout as LogoutIcon,
  Person as PersonIcon,
  Settings as SettingsIcon,
  Help as HelpIcon,
} from '@mui/icons-material';
import { useAppSelector } from '@app/store/store';
import { useLogout } from '@features/auth/hooks/useLogout';
import NotificationCenter from '@components/NotificationCenter/NotificationCenter';
import { useNotifications, createSampleNotifications } from '../../../hooks/useNotifications';

interface HeaderProps {
  open: boolean;
  drawerWidth: number;
  handleDrawerOpen: () => void;
  handleDrawerToggle: () => void;
}

const Header = ({ open, drawerWidth, handleDrawerOpen, handleDrawerToggle }: HeaderProps) => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);
  const { mutate: logoutMutation, isPending: isLoggingOut } = useLogout();

  // إدارة الإشعارات
  const {
    notifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
    addSuccessNotification,
    addErrorNotification,
    addWarningNotification,
    addInfoNotification,
  } = useNotifications();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [langAnchorEl, setLangAnchorEl] = useState<null | HTMLElement>(null);

  // إضافة إشعارات تجريبية عند تحميل المكون لأول مرة
  React.useEffect(() => {
    if (notifications.length === 0) {
      const sampleNotifications = createSampleNotifications();
      sampleNotifications.forEach((notification: any) => {
        switch (notification.type) {
          case 'success':
            addSuccessNotification(notification.title, notification.message);
            break;
          case 'error':
            addErrorNotification(notification.title, notification.message);
            break;
          case 'warning':
            addWarningNotification(notification.title, notification.message);
            break;
          case 'info':
            addInfoNotification(notification.title, notification.message);
            break;
        }
      });
    }
  }, [notifications.length, addSuccessNotification, addErrorNotification, addWarningNotification, addInfoNotification]);

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleLangMenu = (event: React.MouseEvent<HTMLElement>) => {
    setLangAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLangClose = () => {
    setLangAnchorEl(null);
  };

  const handleLogout = () => {
    logoutMutation();
    handleClose();
  };

  const handleProfile = () => {
    navigate('/profile');
    handleClose();
  };

  const handleSettings = () => {
    navigate('/settings');
    handleClose();
  };

  const handleHelp = () => {
    navigate('/help');
    handleClose();
  };

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
    handleLangClose();
  };

  return (
    <AppBar
      position="fixed"
      sx={{
        width: { sm: `calc(100% - ${open ? drawerWidth : 0}px)` },
        mr: { sm: `${open ? drawerWidth : 0}px` },
        transition: (theme) =>
          theme.transitions.create(['margin', 'width'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
      }}
    >
      <Toolbar>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          edge="start"
          onClick={handleDrawerToggle}
          sx={{ mr: 2, display: { sm: 'none' } }}
        >
          <MenuIcon />
        </IconButton>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          onClick={handleDrawerOpen}
          edge="start"
          sx={{ mr: 2, ...(open && { display: 'none' }) }}
        >
          <MenuIcon />
        </IconButton>
        <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
          {t('app.name')}
        </Typography>

        {/* Language Selector */}
        <Tooltip title={t('common.language')}>
          <IconButton
            size="large"
            aria-label="change language"
            aria-controls="menu-language"
            aria-haspopup="true"
            onClick={handleLangMenu}
            color="inherit"
          >
            <TranslateIcon />
          </IconButton>
        </Tooltip>
        <Menu
          id="menu-language"
          anchorEl={langAnchorEl}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          keepMounted
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          open={Boolean(langAnchorEl)}
          onClose={handleLangClose}
        >
          <MenuItem onClick={() => changeLanguage('ar')} selected={i18n.language === 'ar'}>
            العربية
          </MenuItem>
          <MenuItem onClick={() => changeLanguage('en')} selected={i18n.language === 'en'}>
            English
          </MenuItem>
        </Menu>

        {/* Notification Center */}
        <NotificationCenter
          notifications={notifications}
          onMarkAsRead={markAsRead}
          onMarkAllAsRead={markAllAsRead}
          onDelete={deleteNotification}
          onClearAll={clearAllNotifications}
        />

        {/* User Menu */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Button
            onClick={handleMenu}
            color="inherit"
            sx={{ textTransform: 'none' }}
            startIcon={
              <Avatar
                sx={{ width: 32, height: 32, bgcolor: 'secondary.main' }}
                alt={(user as any)?.name ?? user?.username ?? ''}
              >
                {(user as any)?.name?.charAt(0) ?? user?.username?.charAt(0) ?? ''}
              </Avatar>
            }
          >
            <Typography variant="body2" sx={{ display: { xs: 'none', sm: 'block' } }}>
              {(user as any)?.name ?? user?.username ?? ''}
            </Typography>
          </Button>
          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <MenuItem onClick={handleProfile}>
              <ListItemIcon>
                <PersonIcon fontSize="small" />
              </ListItemIcon>
              {t('navigation.profile')}
            </MenuItem>
            <MenuItem onClick={handleSettings}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              {t('navigation.settings')}
            </MenuItem>
            <MenuItem onClick={handleHelp}>
              <ListItemIcon>
                <HelpIcon fontSize="small" />
              </ListItemIcon>
              {t('navigation.help')}
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout} disabled={isLoggingOut}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              {isLoggingOut ? t('common.loading') : t('auth.logout')}
            </MenuItem>
          </Menu>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
