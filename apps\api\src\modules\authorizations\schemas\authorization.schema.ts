import { z } from 'zod';

// مخطط إنشاء تفويض جديد
export const createAuthorizationSchema = z.object({
  body: z.object({
    declarationId: z.string({
      required_error: 'معرف البيان مطلوب',
    }).uuid(),
    authorizationType: z.enum(['FOLLOW_UP', 'CLEARANCE', 'RECEIPT', 'FULL'], {
      required_error: 'نوع التفويض مطلوب',
    }),
    authorizedPerson: z.string({
      required_error: 'اسم الشخص المفوض مطلوب',
    }),
    idNumber: z.string({
      required_error: 'رقم الهوية مطلوب',
    }),
    startDate: z.string({
      required_error: 'تاريخ بداية التفويض مطلوب',
    }).transform(val => new Date(val)),
    endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    notes: z.string().optional(),
  }),
});

// مخطط تحديث تفويض
export const updateAuthorizationSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف التفويض مطلوب',
    }),
  }),
  body: z.object({
    authorizationType: z.enum(['FOLLOW_UP', 'CLEARANCE', 'RECEIPT', 'FULL']).optional(),
    authorizedPerson: z.string().optional(),
    idNumber: z.string().optional(),
    startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    notes: z.string().optional(),
  }),
});

// مخطط الحصول على تفويض محدد
export const getAuthorizationSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف التفويض مطلوب',
    }),
  }),
});

// مخطط حذف تفويض
export const deleteAuthorizationSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف التفويض مطلوب',
    }),
  }),
});

// مخطط قائمة التفويضات
export const listAuthorizationsSchema = z.object({
  query: z.object({
    page: z.string().optional().transform((val) => (val ? parseInt(val) : 1)),
    limit: z.string().optional().transform((val) => (val ? parseInt(val) : 10)),
    sort: z.string().optional().default('createdAt'),
    order: z.enum(['asc', 'desc']).optional().default('desc'),
    search: z.string().optional(),
    authorizationType: z.enum(['FOLLOW_UP', 'CLEARANCE', 'RECEIPT', 'FULL']).optional(),
    fromDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    toDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    declarationId: z.string().uuid().optional(),
    isActive: z.string().optional().transform((val) => {
      if (val === 'true') return true;
      if (val === 'false') return false;
      return undefined;
    }),
  }),
});
