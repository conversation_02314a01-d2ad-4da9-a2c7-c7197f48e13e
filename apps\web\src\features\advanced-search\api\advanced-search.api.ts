import axios from 'axios';

/**
 * أنواع البحث المتقدم
 */
export enum SearchType {
  ALL = 'ALL',
  DECLARATIONS = 'DECLARATIONS',
  ITEM_MOVEMENTS = 'ITEM_MOVEMENTS',
  AUTHORIZATIONS = 'AUTHORIZATIONS',
  RELEASES = 'RELEASES',
  PERMITS = 'PERMITS',
  GUARANTEES = 'GUARANTEES',
  RECEIPTS = 'RECEIPTS',
  CLIENTS = 'CLIENTS',
  DOCUMENTS = 'DOCUMENTS',
}

/**
 * أنواع البيان
 */
export enum DeclarationType {
  IMPORT = 'IMPORT',
  EXPORT = 'EXPORT',
}

/**
 * أنواع البضائع
 */
export enum GoodsType {
  HUMAN_MEDICINE = 'HUMAN_MEDICINE',
  LABORATORY_SOLUTIONS = 'LABORATORY_SOLUTIONS',
  MEDICAL_SUPPLIES = 'MEDICAL_SUPPLIES',
  SUGAR_STRIPS = 'SUGAR_STRIPS',
  MEDICAL_DEVICES = 'MEDICAL_DEVICES',
  MISCELLANEOUS = 'MISCELLANEOUS',
}

/**
 * واجهة معلمات البحث المتقدم
 */
export interface AdvancedSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  searchType?: SearchType;
  keyword?: string;
  taxNumber?: string;
  clientId?: string;
  declarationNumber?: number;
  invoiceNumber?: number;
  declarationType?: DeclarationType;
  goodsType?: GoodsType;
  fromDate?: string;
  toDate?: string;
}

/**
 * واجهة نتائج البحث المتقدم
 */
export interface AdvancedSearchResults {
  declarations?: any[];
  itemMovements?: any[];
  authorizations?: any[];
  releases?: any[];
  permits?: any[];
  guarantees?: any[];
  receipts?: any[];
  clients?: any[];
  documents?: any[];
  total: number;
}

/**
 * واجهة استجابة البحث المتقدم
 */
export interface AdvancedSearchResponse {
  success: boolean;
  data: AdvancedSearchResults;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

/**
 * دالة البحث المتقدم
 * تقوم بإرسال طلب البحث المتقدم إلى الخادم وإرجاع النتائج
 */
export const advancedSearch = async (params: AdvancedSearchParams): Promise<AdvancedSearchResponse> => {
  try {
    // تحويل التاريخ إلى صيغة ISO إذا كان موجودًا
    const fromDate = params.fromDate ? new Date(params.fromDate).toISOString() : undefined;
    const toDate = params.toDate ? new Date(params.toDate).toISOString() : undefined;

    // إرسال طلب البحث
    const response = await axios.get<AdvancedSearchResponse>('/api/advanced-search', {
      params: {
        ...params,
        fromDate,
        toDate,
      },
    });

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء البحث المتقدم');
    }
    throw new Error('حدث خطأ أثناء البحث المتقدم');
  }
};
