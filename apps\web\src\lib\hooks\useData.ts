import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import dataService from '../services/data.service';
import { useToast } from './useToast';

/**
 * خطاف لاستخدام خدمة البيانات
 * يوفر واجهة سهلة للتعامل مع البيانات باستخدام React Query
 */
export const useData = <T>(endpoint: string, options?: {
  successMessage?: string;
  errorMessage?: string;
}) => {
  const queryClient = useQueryClient();
  const toast = useToast();

  // استعلام للحصول على قائمة العناصر
  const useList = (params?: Record<string, any>) => {
    return useQuery({
      queryKey: [endpoint, params],
      queryFn: () => dataService.getList<T>(endpoint, params),
    });
  };

  // استعلام للحصول على عنصر واحد
  const useOne = (id: string) => {
    return useQuery({
      queryKey: [endpoint, id],
      queryFn: () => dataService.getOne<T>(endpoint, id),
      enabled: !!id,
    });
  };

  // طلب لإنشاء عنصر جديد
  const useCreate = () => {
    return useMutation({
      mutationFn: (data: any) => dataService.create<T>(endpoint, data),
      onSuccess: () => {
        // تحديث ذاكرة التخزين المؤقت
        queryClient.invalidateQueries({ queryKey: [endpoint] });
        
        // عرض رسالة نجاح
        if (options?.successMessage) {
          toast.showSuccess(options.successMessage);
        }
      },
      onError: (error) => {
        // عرض رسالة خطأ
        if (options?.errorMessage) {
          toast.showError(options.errorMessage);
        } else if (error instanceof Error) {
          toast.showError(error.message);
        }
      },
    });
  };

  // طلب لتحديث عنصر
  const useUpdate = () => {
    return useMutation({
      mutationFn: ({ id, data }: { id: string; data: any }) => dataService.update<T>(endpoint, id, data),
      onSuccess: (_, variables) => {
        // تحديث ذاكرة التخزين المؤقت
        queryClient.invalidateQueries({ queryKey: [endpoint] });
        queryClient.invalidateQueries({ queryKey: [endpoint, variables.id] });
        
        // عرض رسالة نجاح
        if (options?.successMessage) {
          toast.showSuccess(options.successMessage);
        }
      },
      onError: (error) => {
        // عرض رسالة خطأ
        if (options?.errorMessage) {
          toast.showError(options.errorMessage);
        } else if (error instanceof Error) {
          toast.showError(error.message);
        }
      },
    });
  };

  // طلب لحذف عنصر
  const useDelete = () => {
    return useMutation({
      mutationFn: (id: string) => dataService.delete<T>(endpoint, id),
      onSuccess: (_, id) => {
        // تحديث ذاكرة التخزين المؤقت
        queryClient.invalidateQueries({ queryKey: [endpoint] });
        queryClient.invalidateQueries({ queryKey: [endpoint, id] });
        
        // عرض رسالة نجاح
        if (options?.successMessage) {
          toast.showSuccess(options.successMessage);
        }
      },
      onError: (error) => {
        // عرض رسالة خطأ
        if (options?.errorMessage) {
          toast.showError(options.errorMessage);
        } else if (error instanceof Error) {
          toast.showError(error.message);
        }
      },
    });
  };

  // طلب لتنفيذ إجراء على عنصر
  const useAction = (action: string) => {
    return useMutation({
      mutationFn: ({ id, data }: { id: string; data?: any }) => dataService.executeAction<T>(endpoint, id, action, data),
      onSuccess: (_, variables) => {
        // تحديث ذاكرة التخزين المؤقت
        queryClient.invalidateQueries({ queryKey: [endpoint] });
        queryClient.invalidateQueries({ queryKey: [endpoint, variables.id] });
        
        // عرض رسالة نجاح
        if (options?.successMessage) {
          toast.showSuccess(options.successMessage);
        }
      },
      onError: (error) => {
        // عرض رسالة خطأ
        if (options?.errorMessage) {
          toast.showError(options.errorMessage);
        } else if (error instanceof Error) {
          toast.showError(error.message);
        }
      },
    });
  };

  return {
    useList,
    useOne,
    useCreate,
    useUpdate,
    useDelete,
    useAction,
  };
};
