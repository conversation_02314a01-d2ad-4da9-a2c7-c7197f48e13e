import { Router } from 'express';
import { authController } from '../controllers/auth.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import {
  loginSchema,
  refreshTokenSchema,
  registerSchema,
  changePasswordSchema
} from '../schemas/auth.schema.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';

export const authRoutes = Router();

// مسار تسجيل الدخول
authRoutes.post(
  '/login',
  validateRequest(loginSchema),
  authController.login
);

// مسار تجديد التوكن
authRoutes.post(
  '/refresh-token',
  validateRequest(refreshTokenSchema),
  authController.refreshToken
);

// مسار تسجيل الخروج
authRoutes.post(
  '/logout',
  authController.logout
);

// مسار الحصول على جلسات المستخدم النشطة
authRoutes.get(
  '/sessions',
  authMiddleware,
  authController.getUserSessions
);

// مسار إنهاء جلسة محددة
authRoutes.delete(
  '/sessions/:sessionId',
  authMiddleware,
  authController.endSession
);

// مسار تسجيل مستخدم جديد
authRoutes.post(
  '/register',
  authMiddleware, // يتطلب مصادقة (فقط المسؤولين يمكنهم تسجيل مستخدمين جدد)
  validateRequest(registerSchema),
  authController.register
);

// مسار تغيير كلمة المرور
authRoutes.post(
  '/change-password',
  authMiddleware, // يتطلب مصادقة
  validateRequest(changePasswordSchema),
  authController.changePassword
);
