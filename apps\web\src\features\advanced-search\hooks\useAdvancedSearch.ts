import { useQuery } from '@tanstack/react-query';
import { advancedSearch, AdvancedSearchParams } from '../api/advanced-search.api';

/**
 * خطاف للبحث المتقدم
 * يستخدم React Query للقيام بعملية البحث المتقدم وإدارة حالة البيانات
 */
export const useAdvancedSearch = (params: AdvancedSearchParams = {}) => {
  return useQuery({
    queryKey: ['advancedSearch', params],
    queryFn: () => advancedSearch(params),
    enabled: Object.keys(params).length > 0, // تفعيل البحث فقط عند وجود معلمات
  });
};
