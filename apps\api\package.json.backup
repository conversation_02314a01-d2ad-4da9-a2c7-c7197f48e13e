{"name": "api", "version": "1.0.0", "description": "AlnoorArch API", "type": "module", "main": "dist/server.js", "scripts": {"dev": "nodemon", "build": "tsc", "start": "node --experimental-specifier-resolution=node dist/server.js", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "test": "node scripts/run-tests.js", "test:unit": "node scripts/run-tests.js --selectProjects=unit", "test:integration": "node scripts/run-tests.js --selectProjects=integration", "test:watch": "node scripts/run-tests.js --watch", "test:coverage": "node scripts/run-tests.js --coverage", "test:setup": "node scripts/setup-test-db.js", "test:db:setup": "prisma db push --schema=./prisma/schema.test.prisma --force-reset", "test:db:clean": "rm -f ./prisma/test.db", "test:db:reset": "npm run test:db:clean && npm run test:db:setup", "prisma:generate": "prisma generate --schema=../../database/schema.prisma", "prisma:generate:test": "prisma generate --schema=./prisma/schema.test.prisma", "prisma:migrate": "prisma migrate dev --schema=../../database/schema.prisma", "prisma:studio": "prisma studio --schema=../../database/schema.prisma", "prisma:studio:test": "prisma studio --schema=./prisma/schema.test.prisma", "prisma:seed": "node --loader ts-node/esm src/prisma/seeds/seed.ts"}, "dependencies": {"@pdf-lib/fontkit": "^1.1.1", "@prisma/client": "6.8.2", "bcryptjs": "^3.0.2", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^4.3.0", "csv-writer": "^1.6.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.21.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pdf-lib": "^1.17.1", "shared-types": "workspace:*", "ua-parser-js": "^2.0.3", "winston": "^3.11.0", "zod": "^3.25.28"}, "devDependencies": {"@types/compression": "^1.8.0", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^22.15.21", "@types/supertest": "^6.0.3", "@types/ua-parser-js": "^0.7.39", "jest": "^29.7.0", "jest-mock-extended": "4.0.0-beta1", "nodemon": "^3.0.3", "prisma": "6.8.2", "supertest": "^6.3.4", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}