import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { ReportTemplateStructure, REPORT_FIELDS } from '../types/report-template.types';
import { LazyImage } from '../../../components';

interface ReportTemplatePreviewProps {
  open: boolean;
  onClose: () => void;
  template: ReportTemplateStructure;
  reportType: string;
}

/**
 * مكون معاينة قالب التقرير
 */
const ReportTemplatePreview = ({
  open,
  onClose,
  template,
  reportType,
}: ReportTemplatePreviewProps) => {
  const { t } = useTranslation();

  // إنشاء بيانات وهمية للمعاينة
  const mockData = Array.from({ length: 5 }, (_, index) => {
    const item: Record<string, any> = { id: `item-${index + 1}` };

    // إضافة قيم وهمية لكل عمود
    template.columns.forEach((column) => {
      const field = REPORT_FIELDS[reportType]?.[column.field];
      if (field) {
        switch (field.type) {
          case 'string':
            item[column.field] = `قيمة ${index + 1}`;
            break;
          case 'number':
            item[column.field] = (index + 1) * 100;
            break;
          case 'date':
            const date = new Date();
            date.setDate(date.getDate() - index);
            item[column.field] = date.toLocaleDateString('ar-SA');
            break;
          case 'boolean':
            item[column.field] = index % 2 === 0;
            break;
          default:
            item[column.field] = `قيمة ${index + 1}`;
        }
      }
    });

    return item;
  });

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          height: '90vh',
          display: 'flex',
          flexDirection: 'column',
        },
      }}
    >
      <DialogTitle>
        {t('reports.templates.preview')} - {template.title}
      </DialogTitle>

      <DialogContent dividers sx={{ flex: 1, overflow: 'auto' }}>
        <Box
          sx={{
            width: '100%',
            bgcolor: 'background.paper',
            p: 4,
            boxShadow: 1,
            borderRadius: 1,
            mb: 2,
          }}
        >
          {/* رأس التقرير */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 3,
            }}
          >
            {template.showLogo && (
              <LazyImage
                src="/assets/images/logo.png"
                alt="Logo"
                height={60}
                width="auto"
                lazy={false}
              />
            )}

            <Box sx={{ textAlign: 'center', flex: 1 }}>
              <Typography variant="h4" gutterBottom>
                {template.title}
              </Typography>
              {template.subtitle && (
                <Typography variant="h6" color="textSecondary">
                  {template.subtitle}
                </Typography>
              )}
            </Box>

            {template.showDate && (
              <Box sx={{ textAlign: 'left' }}>
                <Typography variant="body2">
                  {t('reports.templates.date')}: {new Date().toLocaleDateString('ar-SA')}
                </Typography>
              </Box>
            )}
          </Box>

          {/* نص الرأس المخصص */}
          {template.header && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1">{template.header}</Typography>
            </Box>
          )}

          {/* جدول البيانات */}
          <TableContainer component={Paper} sx={{ mb: 3 }}>
            <Table size="small" sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow>
                  {template.columns
                    .filter((col) => col.visible !== false)
                    .map((column, index) => (
                      <TableCell
                        key={index}
                        align="center"
                        sx={{ fontWeight: 'bold', width: column.width }}
                      >
                        {column.header}
                      </TableCell>
                    ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {mockData.map((row, rowIndex) => (
                  <TableRow key={rowIndex}>
                    {template.columns
                      .filter((col) => col.visible !== false)
                      .map((column, colIndex) => (
                        <TableCell key={colIndex} align="center">
                          {row[column.field]}
                        </TableCell>
                      ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* نص التذييل المخصص */}
          {template.footer && (
            <Box>
              <Typography variant="body2">{template.footer}</Typography>
            </Box>
          )}

          {/* أرقام الصفحات */}
          {template.showPageNumbers && (
            <Box sx={{ textAlign: 'center', mt: 3 }}>
              <Typography variant="body2">
                {t('reports.templates.page')} 1 {t('reports.templates.of')} 1
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="primary">
          {t('common.close')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ReportTemplatePreview;
