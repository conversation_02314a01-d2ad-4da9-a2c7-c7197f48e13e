import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  TextField,
  MenuItem,
  Typography,
  Alert,
} from '@mui/material';
import { ArrowBack as ArrowBackIcon, Save as SaveIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useItemMovement } from '../hooks/useItemMovement';
import { useCreateItemMovement } from '../hooks/useCreateItemMovement';
import { useUpdateItemMovement } from '../hooks/useUpdateItemMovement';
import { useDeclarations } from '@features/declarations/hooks/useDeclarations';
import { PageHeader } from '@components/PageHeader';
import { LoadingScreen } from '@components/LoadingScreen';
import { ErrorScreen } from '@components/ErrorScreen';
import { FileUpload } from '@components/FileUpload';
import { ItemMovementStatus, ItemMovementType } from '../types/item-movement.types';

/**
 * مخطط التحقق من صحة نموذج حركة الصنف
 */
const itemMovementSchema = z.object({
  declarationId: z.string().min(1, 'البيان مطلوب'),
  itemName: z.string().min(1, 'اسم الصنف مطلوب'),
  quantity: z.number().positive('الكمية يجب أن تكون أكبر من صفر'),
  unit: z.string().min(1, 'وحدة القياس مطلوبة'),
  movementType: z.string().min(1, 'نوع الحركة مطلوب'),
  movementDate: z.date(),
  status: z.string().min(1, 'الحالة مطلوبة'),
  notes: z.string().optional(),
});

/**
 * نوع بيانات نموذج حركة الصنف
 */
type ItemMovementFormData = z.infer<typeof itemMovementSchema>;

/**
 * صفحة نموذج حركة الصنف (إضافة/تعديل)
 */
const ItemMovementFormPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;
  const [file, setFile] = useState<File | null>(null);

  // استخدام خطافات البيانات
  const { data: itemMovement, isLoading: isLoadingItemMovement, error: itemMovementError } = useItemMovement(
    id || ''
  );

  const { data: declarationsData } = useDeclarations({
    limit: 100,
  });

  const createItemMovementMutation = useCreateItemMovement();
  const updateItemMovementMutation = useUpdateItemMovement();

  // إعداد نموذج حركة الصنف
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<ItemMovementFormData>({
    resolver: zodResolver(itemMovementSchema),
    defaultValues: {
      declarationId: '',
      itemName: '',
      quantity: 0,
      unit: 'KG',
      movementType: ItemMovementType.IN,
      movementDate: new Date(),
      status: ItemMovementStatus.PENDING,
      notes: '',
    },
  });

  // تحديث النموذج عند تحميل البيانات في وضع التعديل
  useEffect(() => {
    if (isEditMode && itemMovement) {
      reset({
        declarationId: itemMovement.declarationId,
        itemName: itemMovement.itemName,
        quantity: itemMovement.quantity,
        unit: itemMovement.unit,
        movementType: itemMovement.movementType,
        movementDate: new Date(itemMovement.movementDate),
        status: itemMovement.status,
        notes: itemMovement.notes || '',
      });
    }
  }, [isEditMode, itemMovement, reset]);

  // معالجة تقديم النموذج
  const onSubmit = async (data: ItemMovementFormData) => {
    try {
      if (isEditMode) {
        // تحديث حركة صنف موجودة
        await updateItemMovementMutation.mutateAsync({
          id: id!,
          data: {
            ...data,
            movementDate: data.movementDate.toISOString(),
          },
          file,
        });
      } else {
        // إنشاء حركة صنف جديدة
        await createItemMovementMutation.mutateAsync({
          data: {
            ...data,
            movementDate: data.movementDate.toISOString(),
            date: data.movementDate.toISOString(),
          },
          file,
        });
      }

      // الانتقال إلى صفحة حركات الأصناف
      navigate('/item-movements');
    } catch (error) {
      console.error('Error saving item movement:', error);
    }
  };

  // التعامل مع تغيير الملف
  const handleFileChange = (files: File[]) => {
    setFile(files.length > 0 ? files[0] : null);
  };

  // عرض شاشة التحميل
  if (isEditMode && isLoadingItemMovement) {
    return <LoadingScreen />;
  }

  // عرض شاشة الخطأ
  if (isEditMode && (itemMovementError || !itemMovement)) {
    return (
      <ErrorScreen
        message={t('itemMovements.form.errorLoading')}
        onRetry={() => window.location.reload()}
      />
    );
  }

  return (
    <Box>
      <PageHeader
        title={
          isEditMode
            ? t('itemMovements.form.editTitle')
            : t('itemMovements.form.addTitle')
        }
        subtitle={
          isEditMode
            ? `${t('itemMovements.movementNumber')}: ${itemMovement?.movementNumber}`
            : t('itemMovements.form.subtitle')
        }
        backButton={
          <Button
            component={Link}
            to="/item-movements"
            startIcon={<ArrowBackIcon />}
            variant="outlined"
          >
            {t('common.back')}
          </Button>
        }
      />

      <Paper sx={{ mb: 3 }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('itemMovements.form.basicInfo')}
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="declarationId"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        select
                        label={t('itemMovements.declarationNumber')}
                        fullWidth
                        required
                        error={!!errors.declarationId}
                        helperText={errors.declarationId?.message}
                        disabled={isSubmitting}
                      >
                        {declarationsData?.data.map((declaration) => (
                          <MenuItem key={declaration.id} value={declaration.id}>
                            {declaration.declarationNumber}
                          </MenuItem>
                        ))}
                      </TextField>
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="movementDate"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        label={t('itemMovements.movementDate')}
                        value={field.value}
                        onChange={field.onChange}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            required: true,
                            error: !!errors.movementDate,
                            helperText: errors.movementDate?.message,
                          },
                        }}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="itemName"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('itemMovements.itemName')}
                        fullWidth
                        required
                        error={!!errors.itemName}
                        helperText={errors.itemName?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={3}>
                  <Controller
                    name="quantity"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('itemMovements.quantity')}
                        fullWidth
                        required
                        type="number"
                        inputProps={{ min: 0, step: 0.01 }}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        error={!!errors.quantity}
                        helperText={errors.quantity?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={3}>
                  <Controller
                    name="unit"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        select
                        label={t('itemMovements.unit')}
                        fullWidth
                        required
                        error={!!errors.unit}
                        helperText={errors.unit?.message}
                        disabled={isSubmitting}
                      >
                        <MenuItem value="KG">{t('itemMovements.units.KG')}</MenuItem>
                        <MenuItem value="TON">{t('itemMovements.units.TON')}</MenuItem>
                        <MenuItem value="PCS">{t('itemMovements.units.PCS')}</MenuItem>
                        <MenuItem value="BOX">{t('itemMovements.units.BOX')}</MenuItem>
                      </TextField>
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="movementType"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        select
                        label={t('itemMovements.movementType')}
                        fullWidth
                        required
                        error={!!errors.movementType}
                        helperText={errors.movementType?.message}
                        disabled={isSubmitting}
                      >
                        <MenuItem value={ItemMovementType.IN}>
                          {t('itemMovements.types.IN')}
                        </MenuItem>
                        <MenuItem value={ItemMovementType.OUT}>
                          {t('itemMovements.types.OUT')}
                        </MenuItem>
                        <MenuItem value={ItemMovementType.TRANSFER}>
                          {t('itemMovements.types.TRANSFER')}
                        </MenuItem>
                      </TextField>
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="status"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        select
                        label={t('itemMovements.status')}
                        fullWidth
                        required
                        error={!!errors.status}
                        helperText={errors.status?.message}
                        disabled={isSubmitting}
                      >
                        <MenuItem value={ItemMovementStatus.PENDING}>
                          {t('itemMovements.statuses.PENDING')}
                        </MenuItem>
                        <MenuItem value={ItemMovementStatus.IN_PROGRESS}>
                          {t('itemMovements.statuses.IN_PROGRESS')}
                        </MenuItem>
                        <MenuItem value={ItemMovementStatus.COMPLETED}>
                          {t('itemMovements.statuses.COMPLETED')}
                        </MenuItem>
                        <MenuItem value={ItemMovementStatus.CANCELLED}>
                          {t('itemMovements.statuses.CANCELLED')}
                        </MenuItem>
                      </TextField>
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Controller
                    name="notes"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('itemMovements.notes')}
                        fullWidth
                        multiline
                        rows={3}
                        error={!!errors.notes}
                        helperText={errors.notes?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('itemMovements.form.attachDocument')}
                  </Typography>
                  <FileUpload
                    accept={{ 'application/pdf': ['.pdf'] }}
                    maxSize={100 * 1024 * 1024} // 100 MB
                    onChange={handleFileChange}
                    disabled={isSubmitting}
                  />
                  {isEditMode && itemMovement?.pdfFile && !file && (
                    <Alert severity="info" sx={{ mt: 1 }}>
                      {t('itemMovements.form.existingDocument')}
                    </Alert>
                  )}
                </Grid>

                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button
                    component={Link}
                    to="/item-movements"
                    variant="outlined"
                    sx={{ mr: 1 }}
                    disabled={isSubmitting}
                  >
                    {t('common.cancel')}
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<SaveIcon />}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <CircularProgress size={24} />
                    ) : (
                      t('common.save')
                    )}
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </CardContent>
        </Card>
      </Paper>
    </Box>
  );
};

export default ItemMovementFormPage;
