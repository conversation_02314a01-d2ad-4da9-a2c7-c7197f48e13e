import { z } from 'zod';

// مخطط خيار القائمة المنسدلة
const selectOptionSchema = z.object({
  value: z.string(),
  label: z.string(),
});

// مخطط حقل النموذج
const formFieldSchema = z.object({
  id: z.string(),
  name: z.string(),
  label: z.string(),
  type: z.enum(['text', 'number', 'date', 'select', 'checkbox', 'textarea', 'file']),
  required: z.boolean(),
  order: z.number(),
  placeholder: z.string().optional(),
  defaultValue: z.union([z.string(), z.number(), z.boolean()]).optional(),
  options: z.array(selectOptionSchema).optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  minLength: z.number().optional(),
  maxLength: z.number().optional(),
  pattern: z.string().optional(),
  disabled: z.boolean().optional(),
  hidden: z.boolean().optional(),
  helpText: z.string().optional(),
});

// مخطط إنشاء نموذج مخصص
export const createCustomFormSchema = z.object({
  body: z.object({
    name: z.string().min(3, 'اسم النموذج يجب أن يكون على الأقل 3 أحرف'),
    description: z.string().optional(),
    formType: z.string(),
    fields: z.array(formFieldSchema),
    isActive: z.boolean().optional(),
  }),
});

// مخطط تحديث نموذج مخصص
export const updateCustomFormSchema = z.object({
  params: z.object({
    id: z.string().uuid('معرف النموذج غير صالح'),
  }),
  body: z.object({
    name: z.string().min(3, 'اسم النموذج يجب أن يكون على الأقل 3 أحرف').optional(),
    description: z.string().optional(),
    formType: z.string().optional(),
    fields: z.array(formFieldSchema).optional(),
    isActive: z.boolean().optional(),
  }),
});
