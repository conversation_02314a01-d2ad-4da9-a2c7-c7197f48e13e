/**
 * أنواع التقارير
 */
export enum ReportType {
  DECLARATIONS = 'DECLARATIONS',
  ITEM_MOVEMENTS = 'ITEM_MOVEMENTS',
  AUTHORIZATIONS = 'AUTHORIZATIONS',
  RELEASES = 'RELEASES',
  PERMITS = 'PERMITS',
  GUARANTEES = 'GUARANTEES',
  RECEIPTS = 'RECEIPTS',
  CLIENTS = 'CLIENTS',
  DOCUMENTS = 'DOCUMENTS',
  USERS = 'USERS',
  ACTIVITY_LOG = 'ACTIVITY_LOG',
  TABLE = 'TABLE',
}

/**
 * صيغ التقارير
 */
export enum ReportFormat {
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  CSV = 'CSV',
}

/**
 * واجهة معلمات التقرير
 */
export interface ReportParams {
  reportType: ReportType;
  format: ReportFormat;
  fromDate?: string;
  toDate?: string;
  filters?: Record<string, any>;
}

/**
 * واجهة قالب التقرير
 */
export interface ReportTemplate {
  id: string;
  name: string;
  description?: string;
  reportType: ReportType;
  fields: string[];
  filters: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  isDefault?: boolean;
  userId: string;
}

/**
 * أنواع الرسوم البيانية
 */
export enum ChartType {
  BAR = 'BAR',
  LINE = 'LINE',
  PIE = 'PIE',
  AREA = 'AREA',
  SCATTER = 'SCATTER',
  RADAR = 'RADAR',
  TABLE = 'TABLE',
}

/**
 * مصادر البيانات
 */
export enum DataSource {
  DECLARATIONS = 'DECLARATIONS',
  ITEM_MOVEMENTS = 'ITEM_MOVEMENTS',
  AUTHORIZATIONS = 'AUTHORIZATIONS',
  RELEASES = 'RELEASES',
  PERMITS = 'PERMITS',
  GUARANTEES = 'GUARANTEES',
  RECEIPTS = 'RECEIPTS',
  CLIENTS = 'CLIENTS',
  DOCUMENTS = 'DOCUMENTS',
  USERS = 'USERS',
  ACTIVITY_LOG = 'ACTIVITY_LOG',
  CUSTOM_SQL = 'CUSTOM_SQL',
}
