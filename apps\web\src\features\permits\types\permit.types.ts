// أنواع التصريح
export enum PermitType {
  ENTRY = 'ENTRY',
  EXIT = 'EXIT',
  SPECIAL = 'SPECIAL',
}

// واجهة المستخدم
export interface User {
  id: string;
  username: string;
  name: string;
}

// واجهة العميل
export interface Client {
  id: string;
  clientNumber?: number;
  taxNumber: string;
  name: string;
  companyName?: string;
  phone?: string;
}

// واجهة البيان
export interface Declaration {
  id: string;
  declarationNumber: string;
  taxNumber: string;
  clientName: string;
  companyName?: string;
}

// واجهة التصريح
export interface Permit {
  id: string;
  permitNumber: string;
  permitType: string;
  permitDate: Date;
  issueDate?: Date;
  expiryDate: Date;
  notes?: string;
  pdfFile?: string;
  declarationId: string;
  declaration?: Declaration;
  createdBy?: User;
  createdAt: Date;
  updatedAt: Date;
}

// واجهة قيم نموذج التصريح
export interface PermitFormValues {
  permitType: string;
  permitDate: Date | null;
  issueDate?: Date | null;
  expiryDate: Date | null;
  notes?: string;
  declarationId: string;
  file?: File | null;
}

// واجهة استجابة قائمة التصاريح
export interface PermitsResponse {
  data: Permit[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// واجهة استجابة التصريح
export interface PermitResponse {
  data: Permit;
}
