import React from 'react';
import { Box, Button, Paper, Typography } from '@mui/material';
import { FileDownload as FileDownloadIcon } from '@mui/icons-material';
import { exportDatabase } from '../api/database.api';
import { useTranslation } from 'react-i18next';

/**
 * مكون تصدير قاعدة البيانات
 */
const ExportDatabaseForm: React.FC = () => {
  const { t } = useTranslation();

  // التعامل مع تصدير قاعدة البيانات
  const handleExportDatabase = () => {
    exportDatabase();
  };

  return (
    <Paper sx={{ p: 2, mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        {t('database.exportDatabase')}
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        {t('database.exportDatabaseDescription')}
      </Typography>
      <Box display="flex" justifyContent="flex-end">
        <Button
          variant="contained"
          color="primary"
          startIcon={<FileDownloadIcon />}
          onClick={handleExportDatabase}
        >
          {t('database.exportDatabase')}
        </Button>
      </Box>
    </Paper>
  );
};

export default ExportDatabaseForm;
