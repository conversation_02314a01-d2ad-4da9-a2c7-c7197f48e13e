# سكريبت الصيانة الدورية لمشروع AlnoorArch
# Periodic Maintenance Cleanup Script

param(
    [switch]$DryRun = $false,
    [switch]$Verbose = $false
)

Write-Host "🔧 بدء الصيانة الدورية لمشروع AlnoorArch" -ForegroundColor Green
Write-Host "Starting periodic maintenance for AlnoorArch project" -ForegroundColor Green

$startTime = Get-Date
$projectRoot = "C:\AlnoorArch"

# التحقق من وجود المشروع
if (-not (Test-Path $projectRoot)) {
    Write-Error "❌ مجلد المشروع غير موجود: $projectRoot"
    exit 1
}

Set-Location $projectRoot

# 1. تنظيف ملفات السجلات القديمة
Write-Host "`n📋 تنظيف ملفات السجلات..." -ForegroundColor Yellow

$logFiles = @(
    "apps\api\logs\*.log",
    "apps\web\logs\*.log",
    ".turbo\daemon\*.log.*"
)

foreach ($pattern in $logFiles) {
    $files = Get-ChildItem $pattern -ErrorAction SilentlyContinue
    if ($files) {
        foreach ($file in $files) {
            $age = (Get-Date) - $file.LastWriteTime
            if ($age.Days -gt 7) {
                if ($DryRun) {
                    Write-Host "  [DRY RUN] سيتم حذف: $($file.FullName)" -ForegroundColor Gray
                } else {
                    Remove-Item $file.FullName -Force
                    Write-Host "  ✅ تم حذف: $($file.Name)" -ForegroundColor Green
                }
            }
        }
    }
}

# 2. تنظيف ملفات البناء المؤقتة
Write-Host "`n🏗️ تنظيف ملفات البناء..." -ForegroundColor Yellow

$buildDirs = @(
    "apps\api\dist",
    "apps\web\dist",
    "apps\web\build",
    "packages\*\dist"
)

foreach ($pattern in $buildDirs) {
    $dirs = Get-ChildItem $pattern -Directory -ErrorAction SilentlyContinue
    foreach ($dir in $dirs) {
        if ($DryRun) {
            Write-Host "  [DRY RUN] سيتم حذف: $($dir.FullName)" -ForegroundColor Gray
        } else {
            Remove-Item $dir.FullName -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "  ✅ تم حذف: $($dir.Name)" -ForegroundColor Green
        }
    }
}

# 3. تنظيف ملفات التخزين المؤقت
Write-Host "`n💾 تنظيف التخزين المؤقت..." -ForegroundColor Yellow

$cacheDirs = @(
    ".turbo\cache",
    "node_modules\.cache",
    "apps\*\node_modules\.cache"
)

foreach ($pattern in $cacheDirs) {
    $dirs = Get-ChildItem $pattern -Directory -ErrorAction SilentlyContinue
    foreach ($dir in $dirs) {
        $size = (Get-ChildItem $dir.FullName -Recurse -File | Measure-Object -Property Length -Sum).Sum / 1MB
        if ($size -gt 100) { # أكبر من 100 MB
            if ($DryRun) {
                Write-Host "  [DRY RUN] سيتم حذف: $($dir.FullName) (${size:F1} MB)" -ForegroundColor Gray
            } else {
                Remove-Item $dir.FullName -Recurse -Force -ErrorAction SilentlyContinue
                Write-Host "  ✅ تم حذف: $($dir.Name) (${size:F1} MB)" -ForegroundColor Green
            }
        }
    }
}

# 4. تنظيف ملفات الاختبار المؤقتة
Write-Host "`n🧪 تنظيف ملفات الاختبار..." -ForegroundColor Yellow

$testFiles = @(
    "apps\api\prisma\test.db*",
    "apps\api\coverage",
    "apps\web\coverage"
)

foreach ($pattern in $testFiles) {
    $items = Get-ChildItem $pattern -ErrorAction SilentlyContinue
    foreach ($item in $items) {
        if ($DryRun) {
            Write-Host "  [DRY RUN] سيتم حذف: $($item.FullName)" -ForegroundColor Gray
        } else {
            Remove-Item $item.FullName -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "  ✅ تم حذف: $($item.Name)" -ForegroundColor Green
        }
    }
}

# 5. فحص الثغرات الأمنية
Write-Host "`n🔒 فحص الثغرات الأمنية..." -ForegroundColor Yellow

if (-not $DryRun) {
    try {
        $auditResult = pnpm audit --json 2>$null | ConvertFrom-Json
        if ($auditResult.vulnerabilities) {
            $vulnCount = ($auditResult.vulnerabilities | Measure-Object).Count
            Write-Host "  ⚠️ تم العثور على $vulnCount ثغرة أمنية" -ForegroundColor Red
            Write-Host "  💡 قم بتشغيل 'pnpm audit' لمزيد من التفاصيل" -ForegroundColor Yellow
        } else {
            Write-Host "  ✅ لا توجد ثغرات أمنية" -ForegroundColor Green
        }
    } catch {
        Write-Host "  ⚠️ فشل فحص الثغرات الأمنية" -ForegroundColor Yellow
    }
}

# 6. فحص التبعيات القديمة
Write-Host "`n📦 فحص التبعيات القديمة..." -ForegroundColor Yellow

if (-not $DryRun) {
    try {
        $outdatedResult = pnpm outdated --format=json 2>$null | ConvertFrom-Json
        if ($outdatedResult) {
            $outdatedCount = ($outdatedResult | Measure-Object).Count
            Write-Host "  📈 $outdatedCount تبعية تحتاج تحديث" -ForegroundColor Yellow
            Write-Host "  💡 قم بتشغيل 'pnpm outdated' لمزيد من التفاصيل" -ForegroundColor Yellow
        } else {
            Write-Host "  ✅ جميع التبعيات محدثة" -ForegroundColor Green
        }
    } catch {
        Write-Host "  ⚠️ فشل فحص التبعيات" -ForegroundColor Yellow
    }
}

# 7. إحصائيات المساحة المحررة
Write-Host "`n📊 إحصائيات الصيانة..." -ForegroundColor Yellow

$endTime = Get-Date
$duration = $endTime - $startTime

Write-Host "  ⏱️ مدة الصيانة: $($duration.TotalMinutes.ToString('F1')) دقيقة" -ForegroundColor Cyan
Write-Host "  📅 تاريخ الصيانة: $($endTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan

if ($DryRun) {
    Write-Host "`n🔍 تم تشغيل الفحص التجريبي - لم يتم حذف أي ملفات" -ForegroundColor Blue
    Write-Host "💡 قم بتشغيل السكريبت بدون -DryRun لتنفيذ التنظيف الفعلي" -ForegroundColor Blue
} else {
    Write-Host "`n✅ تمت الصيانة الدورية بنجاح!" -ForegroundColor Green
}

Write-Host "`n📝 لمزيد من المعلومات، راجع ملفات التوثيق في مجلد docs/" -ForegroundColor Cyan
