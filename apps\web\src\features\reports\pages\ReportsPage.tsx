import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  But<PERSON>,
  Card,
  CardContent,
  Container,
  Divider,
  Grid,
  Paper,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import {
  Description as DescriptionIcon,
  Settings as SettingsIcon,
  PictureAsPdf as PdfIcon,
  TableChart as TableIcon,
  InsertDriveFile as CsvIcon,
} from '@mui/icons-material';
import { ReportType, ReportFormat } from '../types/report.types';

/**
 * صفحة التقارير
 */
const ReportsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [selectedReportType, setSelectedReportType] = useState<string>(ReportType.DECLARATIONS);
  const [selectedFormat, setSelectedFormat] = useState<string>(ReportFormat.PDF);

  // التعامل مع تغيير نوع التقرير
  const handleReportTypeChange = (event: React.SyntheticEvent, newValue: string) => {
    setSelectedReportType(newValue);
  };

  // التعامل مع تغيير صيغة التقرير
  const handleFormatChange = (format: string) => {
    setSelectedFormat(format);
  };

  // التعامل مع إنشاء تقرير
  const handleGenerateReport = () => {
    // توجيه المستخدم إلى صفحة إنشاء التقرير مع المعلمات المحددة
    window.open(
      `/api/reports/generate?reportType=${selectedReportType}&format=${selectedFormat}`,
      '_blank'
    );
  };

  // التعامل مع الانتقال إلى صفحة قوالب التقارير
  const handleGoToTemplates = () => {
    navigate('/reports/templates');
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h4" component="h1" gutterBottom>
            {t('reports.title')}
          </Typography>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<SettingsIcon />}
            onClick={handleGoToTemplates}
          >
            {t('reports.templates.manage')}
          </Button>
        </Box>
        <Divider sx={{ mb: 3 }} />

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs
            value={selectedReportType}
            onChange={handleReportTypeChange}
            variant="scrollable"
            scrollButtons="auto"
            aria-label="report types tabs"
          >
            {Object.values(ReportType).map((type) => (
              <Tab key={type} label={t(`reports.types.${type}`)} value={type} />
            ))}
          </Tabs>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {t('reports.formats.title')}
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Card
                      variant="outlined"
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        cursor: 'pointer',
                        bgcolor:
                          selectedFormat === ReportFormat.PDF
                            ? 'primary.light'
                            : 'background.paper',
                        color:
                          selectedFormat === ReportFormat.PDF ? 'primary.contrastText' : 'text.primary',
                        '&:hover': {
                          bgcolor: 'primary.light',
                          color: 'primary.contrastText',
                        },
                      }}
                      onClick={() => handleFormatChange(ReportFormat.PDF)}
                    >
                      <PdfIcon sx={{ fontSize: 48, mb: 1 }} />
                      <Typography variant="subtitle1">{t('reports.formats.pdf')}</Typography>
                    </Card>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Card
                      variant="outlined"
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        cursor: 'pointer',
                        bgcolor:
                          selectedFormat === ReportFormat.EXCEL
                            ? 'primary.light'
                            : 'background.paper',
                        color:
                          selectedFormat === ReportFormat.EXCEL
                            ? 'primary.contrastText'
                            : 'text.primary',
                        '&:hover': {
                          bgcolor: 'primary.light',
                          color: 'primary.contrastText',
                        },
                      }}
                      onClick={() => handleFormatChange(ReportFormat.EXCEL)}
                    >
                      <TableIcon sx={{ fontSize: 48, mb: 1 }} />
                      <Typography variant="subtitle1">{t('reports.formats.excel')}</Typography>
                    </Card>
                  </Grid>

                  <Grid item xs={12} sm={4}>
                    <Card
                      variant="outlined"
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        cursor: 'pointer',
                        bgcolor:
                          selectedFormat === ReportFormat.CSV
                            ? 'primary.light'
                            : 'background.paper',
                        color:
                          selectedFormat === ReportFormat.CSV ? 'primary.contrastText' : 'text.primary',
                        '&:hover': {
                          bgcolor: 'primary.light',
                          color: 'primary.contrastText',
                        },
                      }}
                      onClick={() => handleFormatChange(ReportFormat.CSV)}
                    >
                      <CsvIcon sx={{ fontSize: 48, mb: 1 }} />
                      <Typography variant="subtitle1">{t('reports.formats.csv')}</Typography>
                    </Card>
                  </Grid>
                </Grid>

                <Box display="flex" justifyContent="center" mt={4}>
                  <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    startIcon={<DescriptionIcon />}
                    onClick={handleGenerateReport}
                  >
                    {t('reports.generate')}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {t('reports.info.title')}
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Typography variant="subtitle1" gutterBottom>
                  {t(`reports.types.${selectedReportType}`)}
                </Typography>
                <Typography variant="body2" paragraph>
                  {t(`reports.info.${selectedReportType}`)}
                </Typography>

                <Typography variant="subtitle1" gutterBottom>
                  {t(`reports.formats.${selectedFormat.toLowerCase()}`)}
                </Typography>
                <Typography variant="body2" paragraph>
                  {t(`reports.info.format.${selectedFormat.toLowerCase()}`)}
                </Typography>

                <Typography variant="body2" color="text.secondary">
                  {t('reports.info.note')}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default ReportsPage;
