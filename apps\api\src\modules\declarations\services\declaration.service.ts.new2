  // الحصول على بيان بواسطة المعرف
  getDeclaration: async (id: string) => {
    // استخدام استعلام مباشر للحصول على البيان مع العلاقات
    const result = await prisma.$queryRaw`
      SELECT d.*, 
             json_agg(dr.*) as drivers,
             json_build_object(
               'id', c.id,
               'name', c.name,
               'taxNumber', c."taxNumber",
               'phone', c.phone,
               'email', c.email,
               'address', c.address,
               'createdAt', c."createdAt",
               'updatedAt', c."updatedAt"
             ) as client
      FROM declarations d
      LEFT JOIN drivers dr ON d.id = dr."declarationId"
      LEFT JOIN clients c ON d."clientId" = c.id
      WHERE d.id = ${id}
      GROUP BY d.id, c.id
    `;

    const declaration = result[0] as any;

    if (!declaration) {
      throw new HttpException(404, 'البيان غير موجود', 'Declaration Not Found');
    }

    return declaration;
  },

  // حذف بيان
  deleteDeclaration: async (id: string, _userId: string) => { // استخدام _ للإشارة إلى أن المتغير غير مستخدم
    // التحقق من وجود البيان
    const declaration = await prisma.declaration.findUnique({
      where: { id },
    });

    if (!declaration) {
      throw new HttpException(404, 'البيان غير موجود', 'Declaration Not Found');
    }

    // حذف السائقين المرتبطين بالبيان باستخدام استعلام مباشر
    await prisma.$executeRaw`
      DELETE FROM drivers WHERE "declarationId" = ${id}
    `;

    // حذف البيان
    await prisma.declaration.delete({
      where: { id },
    });

    return { success: true, message: 'تم حذف البيان بنجاح' };
  },

  // الحصول على قائمة البيانات
  listDeclarations: async (params: ListDeclarationsParams) => {
    const {
      page = 1,
      limit = 10,
      sort = 'declarationNumber',
      order = 'desc',
      // استخدام البحث إذا تم تقديمه (غير مستخدم حاليًا)
      search: _search = '',
      declarationType,
      fromDate,
      toDate,
      clientId,
    } = params;

    // بناء شرط WHERE للاستعلام
    let whereConditions = [];
    
    if (declarationType) {
      whereConditions.push(`d."declarationType" = '${declarationType}'`);
    }
    
    if (clientId) {
      whereConditions.push(`d."clientId" = '${clientId}'`);
    }
    
    if (fromDate && toDate) {
      whereConditions.push(`d."declarationDate" BETWEEN '${fromDate.toISOString()}' AND '${toDate.toISOString()}'`);
    } else if (fromDate) {
      whereConditions.push(`d."declarationDate" >= '${fromDate.toISOString()}'`);
    } else if (toDate) {
      whereConditions.push(`d."declarationDate" <= '${toDate.toISOString()}'`);
    }
    
    const whereClause = whereConditions.length > 0 
      ? Prisma.raw(`WHERE ${whereConditions.join(' AND ')}`) 
      : Prisma.raw('');

    // الحصول على البيانات باستخدام استعلام مباشر
    const declarations = await prisma.$queryRaw`
      SELECT d.*,
             (
               SELECT json_agg(dr.*)
               FROM drivers dr
               WHERE dr."declarationId" = d.id
             ) as drivers,
             (
               SELECT json_build_object(
                 'id', c.id,
                 'name', c.name,
                 'taxNumber', c."taxNumber",
                 'phone', c.phone,
                 'email', c.email,
                 'address', c.address,
                 'createdAt', c."createdAt",
                 'updatedAt', c."updatedAt"
               )
               FROM clients c
               WHERE c.id = d."clientId"
             ) as client
      FROM declarations d
      ${whereClause}
      ORDER BY d."${Prisma.raw(sort)}" ${Prisma.raw(order)}
      LIMIT ${limit} OFFSET ${(page - 1) * limit}
    `;

    // الحصول على إجمالي عدد البيانات باستخدام استعلام مباشر
    const totalResult = await prisma.$queryRaw`
      SELECT COUNT(*) as total FROM declarations d ${whereClause}
    `;
    
    const totalCount = Number((totalResult as any)[0]?.total || 0);

    return {
      data: declarations,
      meta: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  },
};
