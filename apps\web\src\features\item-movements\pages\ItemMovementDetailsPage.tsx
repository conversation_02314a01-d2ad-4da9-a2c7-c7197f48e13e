import React, { useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  Typography,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Chip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useItemMovement } from '../hooks/useItemMovement';
import { useDeleteItemMovement } from '../hooks/useDeleteItemMovement';
import { useDownloadItemMovementPdf } from '../hooks/useDownloadItemMovementPdf';
import { formatDate, formatDateTime } from '@lib/utils/date-utils';
import { PageHeader } from '@components/PageHeader';
import { LoadingScreen } from '@components/LoadingScreen';
import { ErrorScreen } from '@components/ErrorScreen';
import { ItemMovementStatus } from '../types/item-movement.types';

/**
 * صفحة تفاصيل حركة الصنف
 */
const ItemMovementDetailsPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // استخدام خطافات البيانات
  const { data: itemMovement, isLoading, error } = useItemMovement(id || '');
  const deleteItemMovementMutation = useDeleteItemMovement();
  const downloadPdfMutation = useDownloadItemMovementPdf();

  // التعامل مع حذف حركة الصنف
  const handleDelete = async () => {
    if (!id) return;

    try {
      await deleteItemMovementMutation.mutateAsync(id);
      navigate('/item-movements');
    } catch (error) {
      console.error('Error deleting item movement:', error);
    }
  };

  // التعامل مع تحميل ملف PDF
  const handleDownloadPdf = async () => {
    if (!id) return;

    try {
      await downloadPdfMutation.mutateAsync(id);
    } catch (error) {
      console.error('Error downloading PDF:', error);
    }
  };

  // عرض شاشة التحميل
  if (isLoading) {
    return <LoadingScreen />;
  }

  // عرض شاشة الخطأ
  if (error || !itemMovement) {
    return (
      <ErrorScreen
        message={t('itemMovements.details.errorLoading')}
        onRetry={() => window.location.reload()}
      />
    );
  }

  // تحديد لون حالة حركة الصنف
  const getStatusColor = (status: ItemMovementStatus) => {
    switch (status) {
      case ItemMovementStatus.PENDING:
        return 'warning';
      case ItemMovementStatus.IN_PROGRESS:
        return 'info';
      case ItemMovementStatus.COMPLETED:
        return 'success';
      case ItemMovementStatus.CANCELLED:
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <PageHeader
        title={t('itemMovements.details.title')}
        subtitle={`${t('itemMovements.movementNumber')}: ${itemMovement.movementNumber}`}
        backButton={
          <Button
            component={Link}
            to="/item-movements"
            startIcon={<ArrowBackIcon />}
            variant="outlined"
          >
            {t('common.back')}
          </Button>
        }
        actions={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              startIcon={<EditIcon />}
              variant="outlined"
              component={Link}
              to={`/item-movements/edit/${id}`}
            >
              {t('common.edit')}
            </Button>
            <Button
              startIcon={<PrintIcon />}
              variant="outlined"
              onClick={() => window.print()}
            >
              {t('common.print')}
            </Button>
            <Button
              startIcon={<DownloadIcon />}
              variant="outlined"
              onClick={handleDownloadPdf}
              disabled={downloadPdfMutation.isPending}
            >
              {downloadPdfMutation.isPending ? (
                <CircularProgress size={24} />
              ) : (
                t('common.downloadPdf')
              )}
            </Button>
            <Button
              startIcon={<DeleteIcon />}
              variant="outlined"
              color="error"
              onClick={() => setDeleteDialogOpen(true)}
            >
              {t('common.delete')}
            </Button>
          </Box>
        }
      />

      <Paper sx={{ mb: 3 }}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                {t('itemMovements.details.basicInfo')}
              </Typography>
              <Chip
                label={t(`itemMovements.statuses.${itemMovement.status}`)}
                color={getStatusColor(itemMovement.status as ItemMovementStatus)}
              />
            </Box>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('itemMovements.movementNumber')}
                </Typography>
                <Typography variant="body1">{itemMovement.movementNumber}</Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('itemMovements.declarationNumber')}
                </Typography>
                <Typography variant="body1">
                  <Link to={`/declarations/${itemMovement.declarationId}`}>
                    {itemMovement.declaration?.declarationNumber || t('common.notSpecified')}
                  </Link>
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('itemMovements.movementDate')}
                </Typography>
                <Typography variant="body1">
                  {formatDate(itemMovement.movementDate)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('itemMovements.itemName')}
                </Typography>
                <Typography variant="body1">
                  {itemMovement.itemName}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('itemMovements.quantity')}
                </Typography>
                <Typography variant="body1">
                  {itemMovement.quantity} {itemMovement.unit}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('itemMovements.movementType')}
                </Typography>
                <Typography variant="body1">
                  {t(`itemMovements.types.${itemMovement.movementType}`)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('itemMovements.createdAt')}
                </Typography>
                <Typography variant="body1">
                  {formatDateTime(itemMovement.createdAt)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('itemMovements.updatedAt')}
                </Typography>
                <Typography variant="body1">
                  {formatDateTime(itemMovement.updatedAt)}
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('itemMovements.notes')}
                </Typography>
                <Typography variant="body1">
                  {itemMovement.notes || t('common.notSpecified')}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Paper>

      {itemMovement.pdfFile && (
        <Paper sx={{ mb: 3 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('itemMovements.details.attachedDocument')}
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  startIcon={<DownloadIcon />}
                  onClick={handleDownloadPdf}
                  disabled={downloadPdfMutation.isPending}
                >
                  {downloadPdfMutation.isPending ? (
                    <CircularProgress size={24} />
                  ) : (
                    t('common.downloadPdf')
                  )}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Paper>
      )}

      {/* حوار تأكيد الحذف */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>{t('itemMovements.delete.confirmTitle')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('itemMovements.delete.confirmMessage')}
          </DialogContentText>
          <Alert severity="warning" sx={{ mt: 2 }}>
            {t('itemMovements.delete.warning')}
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleDelete}
            color="error"
            disabled={deleteItemMovementMutation.isPending}
          >
            {deleteItemMovementMutation.isPending ? (
              <CircularProgress size={24} />
            ) : (
              t('common.delete')
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ItemMovementDetailsPage;
