import { Request, Response, NextFunction } from 'express';
import { guaranteeService } from '../services/guarantee.service.js';
import { successResponse, paginatedResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const guaranteeController = {
  /**
   * إنشاء ضمان جديد
   */
  createGuarantee: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على بيانات الضمان من الطلب
      const guaranteeData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // إنشاء الضمان
      const guarantee = await guaranteeService.createGuarantee(
        guaranteeData,
        req.user.id, // تم الاحتفاظ بمعرف المستخدم للتوافق مع واجهة الخدمة
        file
      );

      return res.status(201).json(successResponse(guarantee, 'تم إنشاء الضمان بنجاح', 201));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث ضمان
   */
  updateGuarantee: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف الضمان من المعلمات
      const { id } = req.params;

      // الحصول على بيانات الضمان من الطلب
      const guaranteeData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // تحديث الضمان
      const guarantee = await guaranteeService.updateGuarantee(
        id,
        guaranteeData,
        req.user.id, // تم الاحتفاظ بمعرف المستخدم للتوافق مع واجهة الخدمة
        file
      );

      return res.status(200).json(successResponse(guarantee, 'تم تحديث الضمان بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على ضمان محدد
   */
  getGuarantee: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معرف الضمان من المعلمات
      const { id } = req.params;

      // الحصول على الضمان
      const guarantee = await guaranteeService.getGuarantee(id);

      return res.status(200).json(successResponse(guarantee));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف ضمان
   */
  deleteGuarantee: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف الضمان من المعلمات
      const { id } = req.params;

      // حذف الضمان
      await guaranteeService.deleteGuarantee(id, req.user.id); // تم الاحتفاظ بمعرف المستخدم للتوافق مع واجهة الخدمة

      return res.status(200).json(successResponse(null, 'تم حذف الضمان بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة الضمانات
   */
  listGuarantees: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معلمات البحث
      const {
        page,
        limit,
        sort,
        order,
        search,
        declarationId,
        fromDate,
        toDate,
        isActive
      } = req.query as any;

      // الحصول على قائمة الضمانات
      const result = await guaranteeService.listGuarantees({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        search,
        declarationId,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined,
        isActive,
      });

      return res.status(200).json(paginatedResponse(
        result.data,
        result.pagination.page,
        result.pagination.limit,
        result.pagination.total,
        'تم الحصول على قائمة الضمانات بنجاح'
      ));
    } catch (error) {
      next(error);
    }
  },
};
