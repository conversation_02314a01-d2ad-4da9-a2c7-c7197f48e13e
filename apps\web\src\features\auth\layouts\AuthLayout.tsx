import React, { ReactNode } from 'react';
import { Box, Container, Paper, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { LazyImage } from '../../../components';

interface AuthLayoutProps {
  children: ReactNode;
}

/**
 * تخطيط صفحات المصادقة
 * يستخدم لصفحات تسجيل الدخول ونسيت كلمة المرور وإعادة تعيين كلمة المرور
 */
export const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const { t } = useTranslation();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: 'primary.light',
        backgroundImage: 'linear-gradient(to bottom right, #1976d2, #64b5f6)',
      }}
    >
      <Container maxWidth="sm" sx={{ mt: 8, mb: 4 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            mb: 3,
          }}
        >
          <LazyImage
            src="/assets/images/logos/logo.png"
            alt={t('app.name')}
            width={120}
            height={120}
            lazy={false} // لا نريد تأخير تحميل الشعار
            sx={{ mb: 2 }}
          />
          <Typography component="h1" variant="h4" color="white" fontWeight="bold">
            {t('app.name')}
          </Typography>
          <Typography variant="body1" color="white" align="center" sx={{ mt: 1 }}>
            {t('app.description')}
          </Typography>
        </Box>
        {children}
      </Container>
      <Box
        component="footer"
        sx={{
          py: 3,
          px: 2,
          mt: 'auto',
          textAlign: 'center',
        }}
      >
        <Typography variant="body2" color="white">
          © {new Date().getFullYear()} {t('app.name')}
        </Typography>
      </Box>
    </Box>
  );
};
