import request from 'supertest';
import {
  app,
  setupTestFolders,
  cleanupDatabase,
  setupTestData,
  cleanupFiles,
} from '../../../core/utils/test/integration-setup.js';
import { prisma } from '../../../core/utils/prisma.js';
import fs from 'fs';
import path from 'path';
import { config } from '../../../core/config/app.config.js';

/**
 * اختبارات وحدة التقارير
 */
describe('Reports Module', () => {
  let authToken: string;
  let userId: string;
  let createdFiles: string[] = [];

  // قبل جميع الاختبارات
  beforeAll(async () => {
    setupTestFolders();
    await cleanupDatabase();

    // إنشاء بيانات الاختبار
    const { user, authToken: token } = await setupTestData();
    userId = user.id;
    authToken = token;

    // إنشاء مجلد التقارير إذا لم يكن موجودًا
    const reportsDir = path.join(config.upload.dir, 'reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
  });

  // بعد جميع الاختبارات
  afterAll(async () => {
    try {
      await cleanupDatabase();
      cleanupFiles();

      // حذف الملفات التي تم إنشاؤها أثناء الاختبار
      for (const filePath of createdFiles) {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      }
      console.log('تم تنظيف بيانات اختبار التقارير');
    } catch (error) {
      console.error('خطأ في تنظيف بيانات اختبار التقارير:', error);
    }
  });

  /**
   * اختبار إنشاء تقرير
   */
  describe('GET /api/reports/generate', () => {
    it('يجب أن يرفض الطلب إذا لم يكن المستخدم مصرحًا', async () => {
      const response = await request(app)
        .get('/api/reports/generate')
        .query({
          reportType: 'DECLARATIONS',
          format: 'PDF',
        })
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('يجب أن يرفض الطلب إذا كان نوع التقرير غير صالح', async () => {
      const response = await request(app)
        .get('/api/reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          reportType: 'INVALID_TYPE',
          format: 'PDF',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('يجب أن يرفض الطلب إذا كانت صيغة التقرير غير صالحة', async () => {
      const response = await request(app)
        .get('/api/reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          reportType: 'DECLARATIONS',
          format: 'INVALID_FORMAT',
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('يجب أن ينشئ تقرير PDF بنجاح', async () => {
      const response = await request(app)
        .get('/api/reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          reportType: 'DECLARATIONS',
          format: 'PDF',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.downloadUrl).toBeDefined();
      expect(response.body.data.fileName).toBeDefined();

      // إضافة الملف إلى قائمة الملفات التي سيتم حذفها بعد الاختبار
      const filePath = path.join(config.upload.dir, 'reports', response.body.data.fileName);
      createdFiles.push(filePath);
    });

    it('يجب أن ينشئ تقرير Excel بنجاح', async () => {
      const response = await request(app)
        .get('/api/reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          reportType: 'DECLARATIONS',
          format: 'EXCEL',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.downloadUrl).toBeDefined();
      expect(response.body.data.fileName).toBeDefined();

      // إضافة الملف إلى قائمة الملفات التي سيتم حذفها بعد الاختبار
      const filePath = path.join(config.upload.dir, 'reports', response.body.data.fileName);
      createdFiles.push(filePath);
    });

    it('يجب أن ينشئ تقرير CSV بنجاح', async () => {
      const response = await request(app)
        .get('/api/reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          reportType: 'DECLARATIONS',
          format: 'CSV',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.downloadUrl).toBeDefined();
      expect(response.body.data.fileName).toBeDefined();

      // إضافة الملف إلى قائمة الملفات التي سيتم حذفها بعد الاختبار
      const filePath = path.join(config.upload.dir, 'reports', response.body.data.fileName);
      createdFiles.push(filePath);
    });
  });

  /**
   * اختبار تحميل تقرير
   */
  describe('GET /api/reports/download/:fileName', () => {
    it('يجب أن يرفض الطلب إذا لم يكن المستخدم مصرحًا', async () => {
      const response = await request(app)
        .get('/api/reports/download/test.pdf')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('يجب أن يرفض الطلب إذا كان الملف غير موجود', async () => {
      const response = await request(app)
        .get('/api/reports/download/non_existent_file.pdf')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    it('يجب أن يسمح بتحميل الملف بنجاح', async () => {
      // أولاً، إنشاء تقرير
      const generateResponse = await request(app)
        .get('/api/reports/generate')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          reportType: 'DECLARATIONS',
          format: 'PDF',
        })
        .expect(200);

      const fileName = generateResponse.body.data.fileName;

      // إضافة الملف إلى قائمة الملفات التي سيتم حذفها بعد الاختبار
      const filePath = path.join(config.upload.dir, 'reports', fileName);
      createdFiles.push(filePath);

      // ثم محاولة تحميله
      const response = await request(app)
        .get(`/api/reports/download/${fileName}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.headers['content-disposition']).toContain(fileName);
    });
  });
});
