import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getSettings,
  updateSettings,
  UpdateSettingsRequest,
} from '../api/settings.api';
import { useToast } from '@lib/hooks/useToast';
import { useTranslation } from 'react-i18next';

// خطاف للحصول على إعدادات النظام
export const useSettings = () => {
  return useQuery({
    queryKey: ['settings'],
    queryFn: () => getSettings(),
  });
};

// خطاف لتحديث إعدادات النظام
export const useUpdateSettings = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ data, file }: { data: UpdateSettingsRequest; file?: File }) =>
      updateSettings(data, file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['settings'] });
      showSuccess(t('settings.updateSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};
