import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { saveUploadedPdf } from '../../../core/utils/pdf/pdfService.js';
import { Prisma } from '@prisma/client';

interface CreateReleaseInput {
  declarationId: string;
  releaseDate: Date;
  startDate: Date;
  endDate: Date;
  notes?: string;
  issuingAuthority?: string;
  invoiceNumber?: string;
  invoiceDate?: Date;
  invoiceValue?: number;
  approvalIssueDate?: Date;
  bondStartDate?: Date;
  bondEndDate?: Date;
  driverPassAvailable?: boolean;
}

interface UpdateReleaseInput {
  declarationId?: string;
  releaseDate?: Date;
  startDate?: Date;
  endDate?: Date;
  notes?: string;
  issuingAuthority?: string;
  invoiceNumber?: string;
  invoiceDate?: Date;
  invoiceValue?: number;
  approvalIssueDate?: Date;
  bondStartDate?: Date;
  bondEndDate?: Date;
  driverPassAvailable?: boolean;
}

interface ListReleasesParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationId?: string;
  fromDate?: Date;
  toDate?: Date;
  isActive?: boolean;
}

export const releaseService = {
  /**
   * إنشاء إفراج جديد
   */
  createRelease: async (
    data: CreateReleaseInput,
    userId: string,
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود البيان
      const declaration = await prisma.declaration.findUnique({
        where: { id: data.declarationId },
      });

      if (!declaration) {
        throw new HttpException(404, 'البيان غير موجود', 'Not Found');
      }

      // الحصول على آخر رقم إفراج
      const lastRelease = await prisma.release.findFirst({
        orderBy: {
          releaseNumber: 'desc',
        },
      });

      const releaseNumber = lastRelease ? String(Number(lastRelease.releaseNumber) + 1) : '1001';

      // حفظ ملف PDF إذا تم تقديمه
      let pdfFile: string | undefined;
      if (file) {
        pdfFile = saveUploadedPdf(file, 'releases', `release_${releaseNumber}`);
      }

      // إنشاء الإفراج
      const releaseData = {
        releaseNumber,
        issuingAuthority: data.issuingAuthority,
        invoiceNumber: data.invoiceNumber,
        invoiceDate: data.invoiceDate,
        invoiceValue: data.invoiceValue,
        approvalIssueDate: data.approvalIssueDate,
        bondStartDate: data.bondStartDate,
        bondEndDate: data.bondEndDate,
        driverPassAvailable: data.driverPassAvailable,
        pdfFile,
        declarationId: data.declarationId,
        userId,
      };

      const release = await prisma.release.create({
        data: releaseData as any,
      });

      return release;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(400, 'رقم الإفراج موجود بالفعل', 'Bad Request');
        }
      }
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء إنشاء الإفراج', 'Internal Server Error');
    }
  },

  /**
   * تحديث إفراج
   */
  updateRelease: async (
    id: string,
    data: UpdateReleaseInput,
    userId: string,
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود الإفراج
      const existingRelease = await prisma.release.findUnique({
        where: { id },
      });

      if (!existingRelease) {
        throw new HttpException(404, 'الإفراج غير موجود', 'Not Found');
      }

      // التحقق من صلاحية المستخدم
      const releaseWithUser = existingRelease as any;
      if (releaseWithUser.userId !== userId) {
        throw new HttpException(403, 'غير مصرح لك بتحديث هذا الإفراج', 'Forbidden');
      }

      // التحقق من وجود البيان إذا تم تغييره
      if (data.declarationId) {
        const declaration = await prisma.declaration.findUnique({
          where: { id: data.declarationId },
        });

        if (!declaration) {
          throw new HttpException(404, 'البيان غير موجود', 'Not Found');
        }
      }

      // حفظ ملف PDF إذا تم تقديمه
      const releaseWithPdf = existingRelease as any;
      let pdfFile = releaseWithPdf.pdfFile;
      if (file) {
        pdfFile = saveUploadedPdf(file, 'releases', id);
      }

      // تحديث الإفراج
      const updateData = {
        issuingAuthority: data.issuingAuthority,
        invoiceNumber: data.invoiceNumber,
        invoiceDate: data.invoiceDate,
        invoiceValue: data.invoiceValue,
        approvalIssueDate: data.approvalIssueDate,
        bondStartDate: data.bondStartDate,
        bondEndDate: data.bondEndDate,
        driverPassAvailable: data.driverPassAvailable,
        pdfFile,
        declarationId: data.declarationId,
      };

      const release = await prisma.release.update({
        where: { id },
        data: updateData as any,
      });

      return release;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء تحديث الإفراج', 'Internal Server Error');
    }
  },

  /**
   * الحصول على إفراج محدد
   */
  getRelease: async (id: string) => {
    const release = await prisma.release.findUnique({
      where: { id },
    }) as any;

    if (!release) {
      throw new HttpException(404, 'الإفراج غير موجود', 'Not Found');
    }

    return release;
  },

  /**
   * حذف إفراج
   */
  deleteRelease: async (id: string, userId: string) => {
    // التحقق من وجود الإفراج
    const release = await prisma.release.findUnique({
      where: { id },
    });

    if (!release) {
      throw new HttpException(404, 'الإفراج غير موجود', 'Not Found');
    }

    // التحقق من صلاحية المستخدم
    const releaseWithUser = release as any;
    if (releaseWithUser.userId !== userId) {
      throw new HttpException(403, 'غير مصرح لك بحذف هذا الإفراج', 'Forbidden');
    }

    // حذف الإفراج
    await prisma.release.delete({
      where: { id },
    });

    return { success: true };
  },

  /**
   * الحصول على قائمة الإفراجات
   */
  listReleases: async (params: ListReleasesParams = {}) => {
    const {
      page = 1,
      limit = 10,
      sort = 'releaseNumber',
      order = 'desc',
      search,
      declarationId,
      fromDate,
      toDate,
      isActive,
    } = params;

    // بناء شروط البحث
    const where: any = {};

    if (search) {
      where.OR = [
        { declaration: { declarationNumber: { equals: isNaN(parseInt(search)) ? undefined : parseInt(search) } } },
      ];
    }

    if (declarationId) {
      where.declarationId = declarationId;
    }

    if (fromDate && toDate) {
      where.approvalIssueDate = {
        gte: fromDate,
        lte: toDate,
      };
    } else if (fromDate) {
      where.approvalIssueDate = {
        gte: fromDate,
      };
    } else if (toDate) {
      where.approvalIssueDate = {
        lte: toDate,
      };
    }

    // التحقق من حالة الإفراج (نشط أو منتهي)
    if (isActive !== undefined) {
      const now = new Date();
      if (isActive) {
        where.bondEndDate = {
          gte: now,
        };
      } else {
        where.bondEndDate = {
          lt: now,
        };
      }
    }

    // حساب إجمالي عدد الإفراجات
    const total = await prisma.release.count({ where });

    // الحصول على الإفراجات
    const releases = await prisma.release.findMany({
      where,
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    }) as any;

    return {
      data: releases,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};
