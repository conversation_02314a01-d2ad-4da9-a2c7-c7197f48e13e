import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  InputAdornment,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useReleases, useDeleteRelease } from '../hooks/useReleases';
import { ReleaseType } from '../types/release.types';
import { ReleaseSearchParams } from '../api/releases.api';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const ReleasesPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // حالة البحث والتصفية
  const [search, setSearch] = useState('');
  const [releaseType, setReleaseType] = useState<ReleaseType | ''>('');
  const [fromDate, setFromDate] = useState<Date | null>(null);
  const [toDate, setToDate] = useState<Date | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);

  // إعداد معلمات البحث
  const searchParams: ReleaseSearchParams = {
    page: page + 1,
    limit,
    search: search || undefined,
    releaseType: releaseType || undefined,
    fromDate: fromDate || undefined,
    toDate: toDate || undefined,
  };

  // استخدام خطاف الحصول على قائمة الإفراجات
  const { data, isLoading, isError } = useReleases(searchParams);

  // استخدام خطاف حذف إفراج
  const deleteMutation = useDeleteRelease();

  // التعامل مع تغيير الصفحة
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  // التعامل مع تغيير عدد العناصر في الصفحة
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLimit(parseInt(event.target.value, 10));
    setPage(0);
  };

  // التعامل مع البحث
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(0);
  };

  // التعامل مع إعادة تعيين التصفية
  const handleResetFilters = () => {
    setSearch('');
    setReleaseType('');
    setFromDate(null);
    setToDate(null);
    setPage(0);
  };

  // التعامل مع حذف إفراج
  const handleDeleteRelease = async (id: string) => {
    if (window.confirm(t('releases.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id);
      } catch (error) {
        console.error('Error deleting release:', error);
      }
    }
  };

  // التعامل مع عرض إفراج
  const handleViewRelease = (id: string) => {
    navigate(`/releases/${id}`);
  };

  // التعامل مع تعديل إفراج
  const handleEditRelease = (id: string) => {
    navigate(`/releases/${id}/edit`);
  };

  // التعامل مع إنشاء إفراج جديد
  const handleCreateRelease = () => {
    navigate('/releases/new');
  };

  return (
    <Container maxWidth="xl">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('releases.title')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('releases.description')}
        </Typography>
      </Box>

      <Box mb={4} display="flex" justifyContent="space-between" alignItems="center">
        <TextField
          placeholder={t('common.search')}
          value={search}
          onChange={handleSearch}
          variant="outlined"
          size="small"
          sx={{ width: 300 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: search && (
              <InputAdornment position="end">
                <IconButton size="small" onClick={() => setSearch('')}>
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />

        <Box>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{ mr: 1 }}
          >
            {t('common.filters')}
          </Button>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateRelease}
          >
            {t('releases.create')}
          </Button>
        </Box>
      </Box>

      {showFilters && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                select
                fullWidth
                label={t('releases.type')}
                value={releaseType}
                onChange={(e) => setReleaseType(e.target.value as ReleaseType | '')}
              >
                <MenuItem value="">{t('common.all')}</MenuItem>
                <MenuItem value={ReleaseType.TEMPORARY}>{t('releases.types.TEMPORARY')}</MenuItem>
                <MenuItem value={ReleaseType.PERMANENT}>{t('releases.types.PERMANENT')}</MenuItem>
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label={t('common.fromDate')}
                value={fromDate}
                onChange={(date) => {
                  if (date && 'toDate' in date && typeof date.toDate === 'function') {
                    setFromDate(date.toDate());
                  } else {
                    setFromDate(date as Date | null);
                  }
                }}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label={t('common.toDate')}
                value={toDate}
                onChange={(date) => {
                  if (date && 'toDate' in date && typeof date.toDate === 'function') {
                    setToDate(date.toDate());
                  } else {
                    setToDate(date as Date | null);
                  }
                }}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3} display="flex" alignItems="center">
              <Button
                variant="outlined"
                onClick={handleResetFilters}
                fullWidth
              >
                {t('common.resetFilters')}
              </Button>
            </Grid>
          </Grid>
        </Paper>
      )}

      {isLoading ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      ) : isError ? (
        <Box textAlign="center" my={4}>
          <Typography color="error">{t('common.errorLoading')}</Typography>
          <Button
            variant="outlined"
            onClick={() => window.location.reload()}
            sx={{ mt: 2 }}
          >
            {t('common.retry')}
          </Button>
        </Box>
      ) : (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('releases.number')}</TableCell>
                  <TableCell>{t('releases.type')}</TableCell>
                  <TableCell>{t('releases.startDate')}</TableCell>
                  <TableCell>{t('releases.endDate')}</TableCell>
                  <TableCell>{t('releases.declaration')}</TableCell>
                  <TableCell>{t('releases.client')}</TableCell>
                  <TableCell align="center">{t('common.actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data?.data.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      {t('releases.noReleases')}
                    </TableCell>
                  </TableRow>
                ) : (
                  data?.data.map((release: any) => (
                    <TableRow key={release.id}>
                      <TableCell>{release.releaseNumber}</TableCell>
                      <TableCell>
                        <Chip
                          label={t(`releases.types.${release.releaseType}`)}
                          color={release.releaseType === ReleaseType.PERMANENT ? 'primary' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {format(new Date(release.startDate), 'yyyy-MM-dd', { locale: arSA })}
                      </TableCell>
                      <TableCell>
                        {format(new Date(release.endDate), 'yyyy-MM-dd', { locale: arSA })}
                      </TableCell>
                      <TableCell>
                        {release.declaration?.declarationNumber || '-'}
                      </TableCell>
                      <TableCell>
                        {release.client?.name || release.client?.companyName || '-'}
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title={t('common.view')}>
                          <IconButton
                            size="small"
                            onClick={() => handleViewRelease(release.id)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.edit')}>
                          <IconButton
                            size="small"
                            onClick={() => handleEditRelease(release.id)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.delete')}>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteRelease(release.id)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            component="div"
            count={data?.pagination.total || 0}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={limit}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 25, 50]}
            labelRowsPerPage={t('common.rowsPerPage')}
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} ${t('common.of')} ${count}`
            }
          />
        </Paper>
      )}
    </Container>
  );
};

export default ReleasesPage;
