import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { LazyLoad, createLazyComponent } from '../LazyLoad';

// مكون اختبار بسيط
const TestComponent = () => <div>مكون الاختبار</div>;

// إنشاء مكون كسول للاختبار
const LazyTestComponent = createLazyComponent(
  () => Promise.resolve({ default: TestComponent }),
  { preload: false }
);

// Mock React.Suspense
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual as any,
    Suspense: ({ children, fallback }: { children: React.ReactNode, fallback: React.ReactNode }) => {
      return (
        <div data-testid="suspense">
          {fallback}
          {children}
        </div>
      );
    },
  };
});

describe('LazyLoad', () => {
  it('should render loading state initially', () => {
    render(<LazyLoad component={LazyTestComponent} loadingMessage="جاري التحميل..." />);

    // التحقق من عرض حالة التحميل
    expect(screen.getByText('جاري التحميل...')).toBeInTheDocument();
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('should render component after loading', async () => {
    render(<LazyLoad component={LazyTestComponent} />);

    // التحقق من عرض المكون بعد التحميل
    await screen.findByText('مكون الاختبار');
    expect(screen.getByText('مكون الاختبار')).toBeInTheDocument();
  });

  it('should pass props to the lazy component', async () => {
    // مكون اختبار يعرض الخصائص
    const PropsTestComponent = ({ message }: { message: string }) => <div>{message}</div>;

    // إنشاء مكون كسول للاختبار
    const LazyPropsTestComponent = createLazyComponent(
      () => Promise.resolve({ default: PropsTestComponent }),
      { preload: false }
    );

    render(
      <LazyLoad
        component={LazyPropsTestComponent}
        props={{ message: 'رسالة الاختبار' }}
      />
    );

    // التحقق من تمرير الخصائص للمكون
    await screen.findByText('رسالة الاختبار');
    expect(screen.getByText('رسالة الاختبار')).toBeInTheDocument();
  });

  it('should render custom fallback if provided', () => {
    const customFallback = <div>تحميل مخصص...</div>;

    render(
      <LazyLoad
        component={LazyTestComponent}
        fallback={customFallback}
      />
    );

    // التحقق من عرض حالة التحميل المخصصة
    expect(screen.getByText('تحميل مخصص...')).toBeInTheDocument();
  });
});

describe('createLazyComponent', () => {
  it('should create a lazy component', async () => {
    const LazyComponent = createLazyComponent(
      () => Promise.resolve({ default: TestComponent })
    );

    render(<LazyLoad component={LazyComponent} />);

    // التحقق من عرض المكون بعد التحميل
    await screen.findByText('مكون الاختبار');
    expect(screen.getByText('مكون الاختبار')).toBeInTheDocument();
  });

  it('should preload component if preload option is true', () => {
    const mockFactory = vi.fn(() => Promise.resolve({ default: TestComponent }));

    // إنشاء مكون كسول مع تحميل مسبق
    createLazyComponent(mockFactory, { preload: true });

    // التحقق من استدعاء دالة المصنع للتحميل المسبق
    setTimeout(() => {
      expect(mockFactory).toHaveBeenCalled();
    }, 10);
  });

  it('should respect preloadDelay option', () => {
    vi.useFakeTimers();
    const mockFactory = vi.fn(() => Promise.resolve({ default: TestComponent }));

    // إنشاء مكون كسول مع تحميل مسبق وتأخير
    createLazyComponent(mockFactory, { preload: true, preloadDelay: 100 });

    // التحقق من عدم استدعاء دالة المصنع قبل انتهاء التأخير
    expect(mockFactory).not.toHaveBeenCalled();

    // تقدم الوقت بمقدار 50 مللي ثانية
    vi.advanceTimersByTime(50);
    expect(mockFactory).not.toHaveBeenCalled();

    // تقدم الوقت بمقدار 50 مللي ثانية إضافية (المجموع 100 مللي ثانية)
    vi.advanceTimersByTime(50);
    expect(mockFactory).toHaveBeenCalled();

    vi.useRealTimers();
  });
});
