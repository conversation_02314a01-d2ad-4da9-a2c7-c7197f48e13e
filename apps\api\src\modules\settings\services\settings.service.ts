import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const settingsService = {
  /**
   * الحصول على إعدادات النظام
   */
  getSettings: async () => {
    try {
      // البحث عن إعدادات النظام (يجب أن يكون هناك سجل واحد فقط)
      let settings = await prisma.systemSettings.findFirst();

      // إذا لم يتم العثور على إعدادات، إنشاء إعدادات افتراضية
      if (!settings) {
        settings = await prisma.systemSettings.create({
          data: {
            id: 'default',
            companyName: 'نظام النور للأرشفة',
            companyLogo: null,
            companyAddress: null,
            companyPhone: null,
            companyEmail: null,
            companyWebsite: null,
            primaryColor: '#1976d2',
            secondaryColor: '#dc004e',
            defaultFont: 'Tajawal',
            defaultLanguage: 'ar',
            maxFileSize: *********, // 100MB
            enablePrinting: true,
          },
        });
      }

      return settings;
    } catch (error) {
      console.error('Error getting settings:', error);
      throw new HttpException(500, 'خطأ في الحصول على إعدادات النظام', 'Internal Server Error');
    }
  },

  /**
   * تحديث إعدادات النظام
   */
  updateSettings: async (data: any, _userId?: string) => {
    try {
      // تحديث إعدادات النظام (سجل واحد فقط)
      const updatedSettings = await prisma.systemSettings.upsert({
        where: { id: 'default' },
        update: {
          companyName: data.companyName,
          companyLogo: data.companyLogo,
          companyAddress: data.companyAddress,
          companyPhone: data.companyPhone,
          companyEmail: data.companyEmail,
          companyWebsite: data.companyWebsite,
          primaryColor: data.primaryColor,
          secondaryColor: data.secondaryColor,
          defaultFont: data.defaultFont,
          defaultLanguage: data.defaultLanguage,
          maxFileSize: data.maxFileSize,
          enablePrinting: data.enablePrinting,
        },
        create: {
          id: 'default',
          companyName: data.companyName || 'نظام النور للأرشفة',
          companyLogo: data.companyLogo,
          companyAddress: data.companyAddress,
          companyPhone: data.companyPhone,
          companyEmail: data.companyEmail,
          companyWebsite: data.companyWebsite,
          primaryColor: data.primaryColor || '#1976d2',
          secondaryColor: data.secondaryColor || '#dc004e',
          defaultFont: data.defaultFont || 'Tajawal',
          defaultLanguage: data.defaultLanguage || 'ar',
          maxFileSize: data.maxFileSize || *********,
          enablePrinting: data.enablePrinting ?? true,
        },
      });

      return updatedSettings;
    } catch (error) {
      console.error('Error updating settings:', error);
      throw new HttpException(500, 'خطأ في تحديث إعدادات النظام', 'Internal Server Error');
    }
  },
};
