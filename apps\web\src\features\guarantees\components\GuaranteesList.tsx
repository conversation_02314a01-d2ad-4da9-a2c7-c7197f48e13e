import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Grid,
} from '@mui/material';

interface Guarantee {
  id: string;
  guaranteeNumber: string;
  guaranteeType: string;
  status: string;
  issuedDate: string;
  expiryDate?: string;
  amount: number;
  notes?: string;
}

interface GuaranteesListProps {
  guarantees: Guarantee[];
  loading?: boolean;
  declarationId?: string;
}

export const GuaranteesList: React.FC<GuaranteesListProps> = ({
  guarantees,
  loading = false,
  declarationId,
}) => {
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography>جاري التحميل...</Typography>
      </Box>
    );
  }

  if (!guarantees.length) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography color="text.secondary">
          لا توجد ضمانات
        </Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={2}>
      {guarantees.map((guarantee) => (
        <Grid item xs={12} md={6} key={guarantee.id}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                <Typography variant="h6" component="h3">
                  {guarantee.guaranteeNumber}
                </Typography>
                <Chip
                  label={guarantee.status}
                  size="small"
                  color="primary"
                />
              </Box>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                نوع الضمان: {guarantee.guaranteeType}
              </Typography>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                المبلغ: {guarantee.amount.toLocaleString('ar-SA')} ريال
              </Typography>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                تاريخ الإصدار: {new Date(guarantee.issuedDate).toLocaleDateString('ar-SA')}
              </Typography>

              {guarantee.expiryDate && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  تاريخ الانتهاء: {new Date(guarantee.expiryDate).toLocaleDateString('ar-SA')}
                </Typography>
              )}

              {guarantee.notes && (
                <Typography variant="body2" color="text.secondary">
                  ملاحظات: {guarantee.notes}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};
