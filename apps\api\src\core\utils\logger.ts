import winston from 'winston';
import { config } from '../config/app.config.js';

/**
 * وحدة التسجيل
 * تستخدم لتسجيل الأحداث والأخطاء في التطبيق
 */

/**
 * وحدة التسجيل
 * تستخدم لتسجيل الأحداث والأخطاء في التطبيق
 */

const { combine, timestamp, printf, colorize } = winston.format;

// تنسيق مخصص للسجلات
const myFormat = printf(({ level, message, timestamp }) => {
  return `${timestamp} [${level}]: ${message}`;
});

/**
 * إنشاء مسجل الأحداث
 * يقوم بتسجيل الأحداث في وحدة التحكم وملفات السجل
 */
export const logger = winston.createLogger({
  // مستوى التسجيل يعتمد على بيئة التشغيل
  level: config.nodeEnv === 'development' ? 'debug' : 'info',
  format: combine(
    timestamp(),
    myFormat
  ),
  transports: [
    // نقل السجلات إلى وحدة التحكم
    new winston.transports.Console({
      format: combine(
        colorize(),
        timestamp(),
        myFormat
      ),
    }),
    // نقل سجلات الأخطاء إلى ملف
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 5242880, // 5 ميجابايت
      maxFiles: 5,
    }),
    // نقل جميع السجلات إلى ملف
    new winston.transports.File({
      filename: 'logs/combined.log',
      maxsize: 5242880, // 5 ميجابايت
      maxFiles: 5,
    }),
  ],
});
