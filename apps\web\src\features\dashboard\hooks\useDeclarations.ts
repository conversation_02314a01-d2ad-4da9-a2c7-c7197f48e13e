import { useQuery } from '@tanstack/react-query';
import { api } from '@lib/api/api';

export interface DashboardDeclaration {
  id: string;
  declarationNumber: number;
  clientName: string;
  declarationType: 'IMPORT' | 'EXPORT';
  declarationDate: string;
  status: 'COMPLETED' | 'PENDING' | 'IN_PROGRESS';
}

export interface DashboardStats {
  totalDeclarations: number;
  totalItemMovements: number;
  totalAuthorizations: number;
  totalReleases: number;
  totalPermits: number;
  totalGuarantees: number;
  totalReceipts: number;
  totalClients: number;
}

/**
 * Hook لجلب البيانات الحديثة للوحة التحكم
 */
export const useRecentDeclarations = () => {
  return useQuery({
    queryKey: ['dashboard', 'recent-declarations'],
    queryFn: async (): Promise<DashboardDeclaration[]> => {
      try {
        const response = await api.get<DashboardDeclaration[]>('/api/dashboard/recent-declarations');
        return response;
      } catch (error) {
        // في حالة فشل الاتصال، نعيد بيانات وهمية
        return [
          {
            id: '1',
            declarationNumber: 1001,
            clientName: 'شركة الأمل',
            declarationType: 'IMPORT',
            declarationDate: '2024-01-15',
            status: 'COMPLETED',
          },
          {
            id: '2',
            declarationNumber: 1002,
            clientName: 'شركة النور',
            declarationType: 'EXPORT',
            declarationDate: '2024-02-15',
            status: 'PENDING',
          },
          {
            id: '3',
            declarationNumber: 1003,
            clientName: 'شركة المستقبل',
            declarationType: 'IMPORT',
            declarationDate: '2024-03-15',
            status: 'IN_PROGRESS',
          },
          {
            id: '4',
            declarationNumber: 1004,
            clientName: 'شركة الإبداع',
            declarationType: 'EXPORT',
            declarationDate: '2024-04-15',
            status: 'COMPLETED',
          },
          {
            id: '5',
            declarationNumber: 1005,
            clientName: 'شركة التقدم',
            declarationType: 'IMPORT',
            declarationDate: '2024-05-15',
            status: 'PENDING',
          },
        ];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 دقائق
    refetchInterval: 30 * 1000, // تحديث كل 30 ثانية
  });
};

/**
 * Hook لجلب إحصائيات لوحة التحكم
 */
export const useDashboardStats = () => {
  return useQuery({
    queryKey: ['dashboard', 'stats'],
    queryFn: async (): Promise<DashboardStats> => {
      try {
        const response = await api.get<DashboardStats>('/api/dashboard/stats');
        return response;
      } catch (error) {
        // في حالة فشل الاتصال، نعيد بيانات وهمية
        return {
          totalDeclarations: 256,
          totalItemMovements: 1024,
          totalAuthorizations: 128,
          totalReleases: 64,
          totalPermits: 32,
          totalGuarantees: 16,
          totalReceipts: 8,
          totalClients: 4,
        };
      }
    },
    staleTime: 10 * 60 * 1000, // 10 دقائق
    refetchInterval: 60 * 1000, // تحديث كل دقيقة
  });
};

export interface ChartDataItem {
  name: string;
  value: number;
  color: string;
}

/**
 * Hook لجلب بيانات الرسم البياني
 */
export const useDeclarationsByType = () => {
  return useQuery({
    queryKey: ['dashboard', 'declarations-by-type'],
    queryFn: async (): Promise<ChartDataItem[]> => {
      try {
        const response = await api.get<ChartDataItem[]>('/api/dashboard/declarations-by-type');
        return response;
      } catch (error) {
        // في حالة فشل الاتصال، نعيد بيانات وهمية
        return [
          { name: 'وارد', value: 65, color: '#1976d2' },
          { name: 'صادر', value: 35, color: '#dc004e' },
        ];
      }
    },
    staleTime: 15 * 60 * 1000, // 15 دقيقة
  });
};
