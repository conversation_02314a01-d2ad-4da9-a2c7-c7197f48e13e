{"name": "alnoor-performance-tests", "version": "1.0.0", "description": "اختبارات أداء لمشروع النور للأرشفة", "main": "api-performance.js", "scripts": {"test:api": "node api-performance.js", "test:api:auth": "node api-performance.js --auth", "test:declarations": "node api-performance.js /api/declarations --auth", "test:permits": "node api-performance.js /api/permits --auth", "test:guarantees": "node api-performance.js /api/guarantees --auth", "test:receipts": "node api-performance.js /api/receipts --auth", "test:all:api": "npm run test:declarations && npm run test:permits && npm run test:guarantees && npm run test:receipts", "test:ui": "node ui-performance.js", "test:ui:auth": "node ui-performance.js --auth", "test:ui:declarations": "node ui-performance.js /declarations --auth", "test:ui:permits": "node ui-performance.js /permits --auth", "test:ui:guarantees": "node ui-performance.js /guarantees --auth", "test:ui:receipts": "node ui-performance.js /receipts --auth", "test:all:ui": "npm run test:ui:declarations && npm run test:ui:permits && npm run test:ui:guarantees && npm run test:ui:receipts", "test:ux:login": "node ux-tests.js login", "test:ux:declaration": "node ux-tests.js declaration-flow", "test:ux:login:headless": "node ux-tests.js login --headless", "test:ux:declaration:headless": "node ux-tests.js declaration-flow --headless", "test:all:ux": "npm run test:ux:login:headless && npm run test:ux:declaration:headless", "test:responsive-table": "node responsive-table-performance.js", "test:all": "npm run test:all:api && npm run test:all:ui && npm run test:all:ux && npm run test:responsive-table"}, "dependencies": {"autocannon": "^7.12.0", "dotenv": "^16.3.1", "puppeteer": "^21.3.8", "lighthouse": "^11.0.0", "chrome-launcher": "^0.15.2"}}