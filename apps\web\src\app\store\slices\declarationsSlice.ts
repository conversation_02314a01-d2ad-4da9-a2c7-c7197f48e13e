import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Declaration } from '@features/declarations/types/declaration.types';

export interface DeclarationsState {
  selectedDeclaration: Declaration | null;
  filters: {
    declarationNumber?: string;
    clientName?: string;
    declarationType?: string;
    startDate?: string;
    endDate?: string;
  };
  pagination: {
    page: number;
    limit: number;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: DeclarationsState = {
  selectedDeclaration: null,
  filters: {},
  pagination: {
    page: 0,
    limit: 10,
  },
  isLoading: false,
  error: null,
};

export const declarationsSlice = createSlice({
  name: 'declarations',
  initialState,
  reducers: {
    setSelectedDeclaration: (state, action: PayloadAction<Declaration | null>) => {
      state.selectedDeclaration = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<DeclarationsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = {};
    },
    setPagination: (state, action: PayloadAction<Partial<DeclarationsState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setSelectedDeclaration,
  setFilters,
  resetFilters,
  setPagination,
  setLoading,
  setError,
} = declarationsSlice.actions;

export default declarationsSlice.reducer;
