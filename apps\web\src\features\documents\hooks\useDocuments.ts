import { useQuery } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';
import { Document } from './useDocument';

export interface DocumentsResponse {
  data: Document[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface DocumentsParams {
  page?: number;
  limit?: number;
  search?: string;
  documentType?: string;
  fromDate?: string;
  toDate?: string;
}

export const useDocuments = (params: DocumentsParams = {}) => {
  return useQuery({
    queryKey: ['documents', params],
    queryFn: async (): Promise<DocumentsResponse> => {
      const searchParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, value.toString());
        }
      });

      const response = await api.get<DocumentsResponse>(`/api/documents?${searchParams.toString()}`);
      return response;
    },
  });
};
