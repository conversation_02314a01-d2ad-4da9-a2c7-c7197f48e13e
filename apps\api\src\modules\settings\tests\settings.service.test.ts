import { prismaMock } from '../../../core/utils/__mocks__/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

// Mock the settings service
const mockSettingsService = {
  getSettings: async () => {
    // Mock finding existing settings (single record)
    const existingSettings = {
      id: 'default',
      companyName: 'نظام النور للأرشفة',
      companyLogo: null,
      companyAddress: null,
      companyPhone: null,
      companyEmail: null,
      companyWebsite: null,
      primaryColor: '#1976d2',
      secondaryColor: '#dc004e',
      defaultFont: 'Tajawal',
      defaultLanguage: 'ar',
      maxFileSize: *********,
      enablePrinting: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    prismaMock.systemSettings.findFirst.mockResolvedValue(existingSettings);
    return existingSettings;
  },

  getSettingsWithDefaults: async () => {
    // Mock case when no settings exist, create defaults
    prismaMock.systemSettings.findFirst.mockResolvedValue(null);

    const defaultSettings = {
      id: 'default',
      companyName: 'نظام النور للأرشفة',
      companyLogo: null,
      companyAddress: null,
      companyPhone: null,
      companyEmail: null,
      companyWebsite: null,
      primaryColor: '#1976d2',
      secondaryColor: '#dc004e',
      defaultFont: 'Tajawal',
      defaultLanguage: 'ar',
      maxFileSize: *********,
      enablePrinting: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    prismaMock.systemSettings.create.mockResolvedValue(defaultSettings);
    return defaultSettings;
  },

  updateSettings: async (data: any) => {
    const updatedSettings = {
      id: 'default',
      companyName: data.companyName || 'نظام النور للأرشفة',
      companyLogo: data.companyLogo || null,
      companyAddress: data.companyAddress || null,
      companyPhone: data.companyPhone || null,
      companyEmail: data.companyEmail || null,
      companyWebsite: data.companyWebsite || null,
      primaryColor: data.primaryColor || '#1976d2',
      secondaryColor: data.secondaryColor || '#dc004e',
      defaultFont: data.defaultFont || 'Tajawal',
      defaultLanguage: data.defaultLanguage || 'ar',
      maxFileSize: data.maxFileSize || *********,
      enablePrinting: data.enablePrinting ?? true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    prismaMock.systemSettings.upsert.mockResolvedValue(updatedSettings);
    return updatedSettings;
  },

  resetSettings: async () => {
    const defaultSettings = {
      id: 'default',
      companyName: 'نظام النور للأرشفة',
      companyLogo: null,
      companyAddress: null,
      companyPhone: null,
      companyEmail: null,
      companyWebsite: null,
      primaryColor: '#1976d2',
      secondaryColor: '#dc004e',
      defaultFont: 'Tajawal',
      defaultLanguage: 'ar',
      maxFileSize: *********,
      enablePrinting: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    prismaMock.systemSettings.update.mockResolvedValue(defaultSettings);
    return defaultSettings;
  },

  validateSettings: (data: any) => {
    const errors: string[] = [];

    if (data.maxFileSize && (data.maxFileSize < 1024 || data.maxFileSize > 1073741824)) {
      errors.push('حجم الملف يجب أن يكون بين 1KB و 1GB');
    }

    if (data.primaryColor && !/^#[0-9A-F]{6}$/i.test(data.primaryColor)) {
      errors.push('اللون الأساسي يجب أن يكون بصيغة hex صحيحة');
    }

    if (data.secondaryColor && !/^#[0-9A-F]{6}$/i.test(data.secondaryColor)) {
      errors.push('اللون الثانوي يجب أن يكون بصيغة hex صحيحة');
    }

    if (data.defaultLanguage && !['ar', 'en'].includes(data.defaultLanguage)) {
      errors.push('اللغة الافتراضية يجب أن تكون ar أو en');
    }

    if (errors.length > 0) {
      throw new HttpException(400, errors.join(', '), 'Bad Request');
    }

    return true;
  },
};

describe('Settings Service', () => {
  beforeEach(() => {
    // تنظيف المحاكيات قبل كل اختبار
  });

  describe('getSettings', () => {
    it('should return existing settings', async () => {
      // Act
      const result = await mockSettingsService.getSettings();

      // Assert
      expect(typeof result).toBe('object');
      expect(result).toHaveProperty('id', 'default');
      expect(result).toHaveProperty('companyName');
      expect(result).toHaveProperty('primaryColor');
    });

    it('should create default settings when none exist', async () => {
      // Act
      const result = await mockSettingsService.getSettingsWithDefaults();

      // Assert
      expect(typeof result).toBe('object');
      expect(result).toHaveProperty('id', 'default');
      expect(result).toHaveProperty('companyName', 'نظام النور للأرشفة');
    });
  });

  describe('updateSettings', () => {
    it('should update settings successfully', async () => {
      // Arrange
      const updateData = {
        companyName: 'شركة النور المحدثة',
      };

      // Act
      const result = await mockSettingsService.updateSettings(updateData);

      // Assert
      expect(typeof result).toBe('object');
      expect(result).toHaveProperty('companyName', 'شركة النور المحدثة');
    });
  });

  describe('resetSettings', () => {
    it('should reset settings to defaults', async () => {
      // Act
      const result = await mockSettingsService.resetSettings();

      // Assert
      expect(typeof result).toBe('object');
      expect(result).toHaveProperty('id', 'default');
      expect(result).toHaveProperty('companyName', 'نظام النور للأرشفة');
    });
  });

  describe('validateSettings', () => {
    it('should validate settings successfully', () => {
      // Arrange
      const validData = {
        companyName: 'شركة صحيحة',
        primaryColor: '#1976d2',
        secondaryColor: '#dc004e',
        defaultLanguage: 'ar',
        maxFileSize: 52428800,
        enablePrinting: true,
      };

      // Act & Assert
      expect(() => {
        mockSettingsService.validateSettings(validData);
      }).not.toThrow();
    });

    it('should throw error for invalid file size', () => {
      // Arrange
      const invalidData = {
        maxFileSize: 500, // Too small
      };

      // Act & Assert
      expect(() => {
        mockSettingsService.validateSettings(invalidData);
      }).toThrow(HttpException);
    });

    it('should throw error for invalid color format', () => {
      // Arrange
      const invalidData = {
        primaryColor: 'invalid-color',
      };

      // Act & Assert
      expect(() => {
        mockSettingsService.validateSettings(invalidData);
      }).toThrow(HttpException);
    });

    it('should throw error for invalid language', () => {
      // Arrange
      const invalidData = {
        defaultLanguage: 'fr', // Not supported
      };

      // Act & Assert
      expect(() => {
        mockSettingsService.validateSettings(invalidData);
      }).toThrow(HttpException);
    });

    it('should throw error with multiple validation issues', () => {
      // Arrange
      const invalidData = {
        maxFileSize: 500,
        primaryColor: 'invalid',
        defaultLanguage: 'fr',
      };

      // Act & Assert
      expect(() => {
        mockSettingsService.validateSettings(invalidData);
      }).toThrow(HttpException);
    });
  });
});
