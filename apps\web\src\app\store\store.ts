import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

// Import reducers
import authReducer from './slices/authSlice';
import declarationsReducer from './slices/declarationsSlice';
import itemMovementsReducer from './slices/itemMovementsSlice';
import clientsReducer from './slices/clientsSlice';
import documentsReducer from './slices/documentsSlice';
import settingsReducer from './slices/settingsSlice';
import { createStore } from './createStore';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    declarations: declarationsReducer,
    itemMovements: itemMovementsReducer,
    clients: clientsReducer,
    documents: documentsReducer,
    settings: settingsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
  devTools: import.meta.env.DEV,
});

// Enable listener behavior for the store
setupListeners(store.dispatch);

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Export createStore for testing
export { createStore };
