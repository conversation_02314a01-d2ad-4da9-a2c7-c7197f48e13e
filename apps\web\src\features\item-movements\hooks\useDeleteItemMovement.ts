import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';

export const useDeleteItemMovement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      await api.delete(`/api/item-movements/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['itemMovements'] });
    },
  });
};
