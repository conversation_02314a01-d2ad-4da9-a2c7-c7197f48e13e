import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import cookieParser from 'cookie-parser';
import { errorMiddleware } from './core/middleware/error.middleware.js';
import { config } from './core/config/app.config.js';
import { startCleanupCronJobs } from './core/cron/cleanup.cron.js';
import { authRoutes } from './modules/auth/routes/auth.routes.js';
import { declarationRoutes } from './modules/declarations/routes/declaration.routes.js';
import { itemMovementRoutes } from './modules/items-movement/routes/item-movement.routes.js';
import { authorizationRoutes } from './modules/authorizations/routes/authorization.routes.js';
import { releaseRoutes } from './modules/releases/routes/release.routes.js';
import { permitRoutes } from './modules/permits/routes/permit.routes.js';
import { guaranteeRoutes } from './modules/guarantees/routes/guarantee.routes.js';
import { receiptRoutes } from './modules/receipts/routes/receipt.routes.js';
import { clientRoutes } from './modules/clients/routes/client.routes.js';
import { documentRoutes } from './modules/documents/routes/document.routes.js';
import { advancedSearchRoutes } from './modules/advanced-search/routes/advanced-search.routes.js';
import { reportRoutes } from './modules/reports/routes/report.routes.js';
import { reportTemplateRoutes } from './modules/reports/routes/report-template.routes.js';
import customFormRoutes from './modules/custom-forms/routes/custom-form.routes.js';
import { databaseRoutes } from './modules/database/routes/database.routes.js';
import { settingsRoutes } from './modules/settings/routes/settings.routes.js';

const app = express();

// Middleware
app.use(cors({
  origin: config.nodeEnv === 'production'
    ? config.corsOrigin
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true,
}));
app.use(helmet({
  crossOriginResourcePolicy: { policy: 'cross-origin' }
}));
app.use(compression());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(morgan(config.nodeEnv === 'development' ? 'dev' : 'combined'));

// Static files
app.use('/uploads', express.static('uploads'));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/declarations', declarationRoutes);
app.use('/api/item-movements', itemMovementRoutes);
app.use('/api/authorizations', authorizationRoutes);
app.use('/api/releases', releaseRoutes);
app.use('/api/permits', permitRoutes);
app.use('/api/guarantees', guaranteeRoutes);
app.use('/api/receipts', receiptRoutes);
app.use('/api/clients', clientRoutes);
app.use('/api/documents', documentRoutes);
app.use('/api/advanced-search', advancedSearchRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/report-templates', reportTemplateRoutes);
app.use('/api/custom-forms', customFormRoutes);
app.use('/api/database', databaseRoutes);
app.use('/api/settings', settingsRoutes);

// Health check
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
  });
});

// Error handling
app.use(errorMiddleware);

// بدء تشغيل المهام المجدولة
if (config.nodeEnv !== 'test') {
  startCleanupCronJobs();
}

export default app;
export { app };
