// إعداد بيئة اختبار التكامل للواجهة الأمامية
import '@testing-library/jest-dom';
import { vi } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';

// إعداد خادم وهمي للاختبارات
export const server = setupServer();

// إعداد بيئة الاختبار
beforeAll(() => {
  // بدء تشغيل الخادم الوهمي
  server.listen({ onUnhandledRequest: 'error' });
});

// إعادة تعيين المعالجات بين الاختبارات
afterEach(() => {
  server.resetHandlers();
  vi.clearAllMocks();
});

// إيقاف الخادم بعد الانتهاء من جميع الاختبارات
afterAll(() => {
  server.close();
});

// إنشاء معالجات وهمية للطلبات
export const createMockHandlers = (baseUrl: string = 'http://localhost:3001') => {
  return {
    // معالج تسجيل الدخول
    login: server.use(
      http.post(`${baseUrl}/api/auth/login`, () => {
        return HttpResponse.json({
          success: true,
          data: {
            token: 'mock-token',
            user: {
              id: 'user-1',
              username: 'test_user',
              name: 'مستخدم اختبار',
              role: 'ADMIN',
            },
          },
        }, { status: 200 });
      })
    ),

    // معالج جلب البيانات
    getDeclarations: server.use(
      http.get(`${baseUrl}/api/declarations`, () => {
        return HttpResponse.json({
          success: true,
          data: [
            {
              id: 'declaration-1',
              declarationNumber: 1001,
              taxNumber: '*********',
              clientName: 'عميل اختبار',
              declarationType: 'IMPORT',
              declarationDate: '2023-01-15T00:00:00.000Z',
              createdAt: '2023-01-15T00:00:00.000Z',
              updatedAt: '2023-01-15T00:00:00.000Z',
            },
          ],
          pagination: {
            page: 1,
            limit: 10,
            totalItems: 1,
            totalPages: 1,
          },
        }, { status: 200 });
      })
    ),

    // معالج جلب حركات الأصناف
    getItemMovements: server.use(
      http.get(`${baseUrl}/api/item-movements`, () => {
        return HttpResponse.json({
          success: true,
          data: [
            {
              id: 'item-movement-1',
              movementNumber: 1001,
              itemName: 'صنف اختبار',
              quantity: 10,
              unitPrice: 100,
              totalPrice: 1000,
              movementType: 'IN',
              movementDate: '2023-01-15T00:00:00.000Z',
              createdAt: '2023-01-15T00:00:00.000Z',
              updatedAt: '2023-01-15T00:00:00.000Z',
            },
          ],
          pagination: {
            page: 1,
            limit: 10,
            totalItems: 1,
            totalPages: 1,
          },
        }, { status: 200 });
      })
    ),

    // يمكن إضافة المزيد من المعالجات حسب الحاجة
  };
};

// تصدير الدوال المساعدة
export { http, HttpResponse };

// إضافة rest للتوافق مع الكود القديم
export const rest = {
  get: http.get,
  post: http.post,
  put: http.put,
  delete: http.delete,
  patch: http.patch,
};
