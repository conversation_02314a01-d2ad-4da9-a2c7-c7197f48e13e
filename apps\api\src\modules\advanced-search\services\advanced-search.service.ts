import { prisma } from '../../../core/utils/prisma.js';
import { Prisma } from '@prisma/client';

/**
 * واجهة معلمات البحث المتقدم
 * تحدد جميع المعلمات الممكنة للبحث في مختلف وحدات النظام
 */
interface AdvancedSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  searchType?: 'ALL' | 'DECLARATIONS' | 'ITEM_MOVEMENTS' | 'AUTHORIZATIONS' | 'RELEASES' | 'PERMITS' | 'GUARANTEES' | 'RECEIPTS' | 'CLIENTS' | 'DOCUMENTS';
  keyword?: string;
  taxNumber?: string;
  clientId?: string;
  declarationNumber?: number;
  invoiceNumber?: number;
  declarationType?: 'IMPORT' | 'EXPORT';
  goodsType?: 'HUMAN_MEDICINE' | 'LABORATORY_SOLUTIONS' | 'MEDICAL_SUPPLIES' | 'SUGAR_STRIPS' | 'MEDICAL_DEVICES' | 'MISCELLANEOUS';
  fromDate?: Date;
  toDate?: Date;
}

export const advancedSearchService = {
  /**
   * البحث المتقدم
   * يقوم بالبحث في مختلف وحدات النظام حسب نوع البحث المحدد
   * ويدعم البحث بالكلمات المفتاحية والتصفية حسب معايير متعددة
   */
  search: async (params: AdvancedSearchParams = {}) => {
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'desc',
      searchType = 'ALL',
      keyword,
      taxNumber,
      clientId,
      declarationNumber,
      invoiceNumber,
      declarationType,
      goodsType,
      fromDate,
      toDate,
    } = params;

    // نتائج البحث
    const results: {
      declarations?: any[];
      itemMovements?: any[];
      authorizations?: any[];
      releases?: any[];
      permits?: any[];
      guarantees?: any[];
      receipts?: any[];
      clients?: any[];
      documents?: any[];
      total: number;
    } = {
      total: 0,
    };

    // البحث في البيانات
    if (searchType === 'ALL' || searchType === 'DECLARATIONS') {
      // بناء شروط البحث للبيانات
      const declarationsWhere: Prisma.DeclarationWhereInput = {};

      // البحث بالكلمة المفتاحية في عدة حقول
      if (keyword) {
        declarationsWhere.OR = [
          { clientName: { startsWith: keyword } },
          { companyName: { startsWith: keyword } },
          { taxNumber: { startsWith: keyword } },
          { declarationNumber: { startsWith: keyword } },
        ];
      }

      // تصفية حسب الرقم الضريبي
      if (taxNumber) {
        declarationsWhere.taxNumber = taxNumber;
      }

      // تصفية حسب العميل
      if (clientId) {
        declarationsWhere.clientId = clientId;
      }

      // تصفية حسب رقم البيان
      if (declarationNumber) {
        declarationsWhere.declarationNumber = String(declarationNumber);
      }

      // تصفية حسب رقم الفاتورة
      if (invoiceNumber) {
        declarationsWhere.invoiceNumber = String(invoiceNumber);
      }

      // تصفية حسب نوع البيان
      if (declarationType) {
        declarationsWhere.declarationType = declarationType;
      }

      // تصفية حسب التاريخ
      if (fromDate && toDate) {
        declarationsWhere.declarationDate = {
          gte: fromDate,
          lte: toDate,
        };
      } else if (fromDate) {
        declarationsWhere.declarationDate = {
          gte: fromDate,
        };
      } else if (toDate) {
        declarationsWhere.declarationDate = {
          lte: toDate,
        };
      }

      // استعلام البيانات مع العلاقات
      const declarations = await prisma.declaration.findMany({
        where: declarationsWhere,
        include: {
          client: {
            select: {
              id: true,
              name: true,
              taxNumber: true,
            },
          },
        },
        orderBy: {
          [sort]: order,
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      // حساب إجمالي عدد البيانات
      const declarationsCount = await prisma.declaration.count({
        where: declarationsWhere,
      });

      // إضافة النتائج إلى مصفوفة النتائج
      results.declarations = declarations;
      results.total += declarationsCount;
    }

    // البحث في حركة الأصناف
    if (searchType === 'ALL' || searchType === 'ITEM_MOVEMENTS') {
      const itemMovementsWhere: Prisma.ItemMovementWhereInput = {};

      if (keyword) {
        itemMovementsWhere.OR = [
          { itemName: { startsWith: keyword } },
          { movementType: { startsWith: keyword } },
          { notes: { startsWith: keyword } },
        ];
      }

      // تصفية حسب البيان
      let declarationFilter: Prisma.DeclarationWhereInput = {};

      if (declarationNumber) {
        declarationFilter.declarationNumber = String(declarationNumber);
      }

      if (invoiceNumber) {
        declarationFilter.invoiceNumber = String(invoiceNumber);
      }

      if (goodsType) {
        declarationFilter.goodsType = goodsType;
      }

      if (Object.keys(declarationFilter).length > 0) {
        itemMovementsWhere.declaration = {
          is: declarationFilter
        };
      }

      if (fromDate && toDate) {
        itemMovementsWhere.movementDate = {
          gte: fromDate,
          lte: toDate,
        };
      } else if (fromDate) {
        itemMovementsWhere.movementDate = {
          gte: fromDate,
        };
      } else if (toDate) {
        itemMovementsWhere.movementDate = {
          lte: toDate,
        };
      }

      if (clientId) {
        itemMovementsWhere.declaration = {
          clientId,
        };
      }

      const itemMovements = await prisma.itemMovement.findMany({
        where: itemMovementsWhere,
        include: {
          declaration: {
            select: {
              id: true,
              declarationNumber: true,
              declarationType: true,
              clientName: true,
            },
          },
        },
        orderBy: {
          [sort]: order,
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      const itemMovementsCount = await prisma.itemMovement.count({
        where: itemMovementsWhere,
      });

      results.itemMovements = itemMovements;
      results.total += itemMovementsCount;
    }

    // البحث في التفويضات
    if (searchType === 'ALL' || searchType === 'AUTHORIZATIONS') {
      const authorizationsWhere: Prisma.AuthorizationWhereInput = {};

      if (keyword) {
        authorizationsWhere.OR = [
          { authorizedPerson: { startsWith: keyword } },
          { idNumber: { startsWith: keyword } },
          { notes: { startsWith: keyword } },
        ];
      }

      // تصفية حسب البيان
      let declarationFilter: Prisma.DeclarationWhereInput = {};

      if (taxNumber) {
        declarationFilter.taxNumber = taxNumber;
      }

      if (clientId) {
        declarationFilter.clientId = clientId;
      }

      if (Object.keys(declarationFilter).length > 0) {
        authorizationsWhere.declaration = {
          is: declarationFilter
        };
      }

      if (fromDate && toDate) {
        authorizationsWhere.OR = [
          {
            startDate: {
              gte: fromDate,
              lte: toDate,
            },
          },
          {
            endDate: {
              gte: fromDate,
              lte: toDate,
            },
          },
        ];
      } else if (fromDate) {
        authorizationsWhere.startDate = {
          gte: fromDate,
        };
      } else if (toDate) {
        authorizationsWhere.endDate = {
          lte: toDate,
        };
      }

      const authorizations = await prisma.authorization.findMany({
        where: authorizationsWhere,
        include: {
          declaration: {
            select: {
              id: true,
              declarationNumber: true,
              clientName: true,
              taxNumber: true,
              client: {
                select: {
                  id: true,
                  name: true,
                  taxNumber: true,
                }
              }
            },
          },
        },
        orderBy: {
          [sort]: order,
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      const authorizationsCount = await prisma.authorization.count({
        where: authorizationsWhere,
      });

      results.authorizations = authorizations;
      results.total += authorizationsCount;
    }

    return {
      data: results,
      pagination: {
        page,
        limit,
        total: results.total,
        pages: Math.ceil(results.total / limit),
      },
    };
  },
};
