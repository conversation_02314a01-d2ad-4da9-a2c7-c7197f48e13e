import { useCallback } from 'react';
import { useSnackbar } from 'notistack';

/**
 * خطاف لعرض رسائل التنبيه (Toast)
 * يستخدم مكتبة notistack لعرض رسائل التنبيه بطريقة سهلة
 */
export const useToast = () => {
  const { enqueueSnackbar, closeSnackbar } = useSnackbar();

  /**
   * عرض رسالة نجاح
   * @param message نص الرسالة
   */
  const showSuccess = useCallback(
    (message: string) => {
      enqueueSnackbar(message, {
        variant: 'success',
        autoHideDuration: 3000,
      });
    },
    [enqueueSnackbar]
  );

  /**
   * عرض رسالة خطأ
   * @param message نص الرسالة
   */
  const showError = useCallback(
    (message: string) => {
      enqueueSnackbar(message, {
        variant: 'error',
        autoHideDuration: 5000,
      });
    },
    [enqueueSnackbar]
  );

  /**
   * عرض رسالة تحذير
   * @param message نص الرسالة
   */
  const showWarning = useCallback(
    (message: string) => {
      enqueueSnackbar(message, {
        variant: 'warning',
        autoHideDuration: 4000,
      });
    },
    [enqueueSnackbar]
  );

  /**
   * عرض رسالة معلومات
   * @param message نص الرسالة
   */
  const showInfo = useCallback(
    (message: string) => {
      enqueueSnackbar(message, {
        variant: 'info',
        autoHideDuration: 3000,
      });
    },
    [enqueueSnackbar]
  );

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    closeSnackbar,
  };
};

export default useToast;
