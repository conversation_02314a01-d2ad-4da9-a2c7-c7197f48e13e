import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getReleases,
  getRelease,
  createRelease,
  updateRelease,
  deleteRelease,
  ReleaseSearchParams,
  CreateReleaseRequest,
  UpdateReleaseRequest,
} from '../api/releases.api';
import { useToast } from '@lib/hooks/useToast';
import { useTranslation } from 'react-i18next';

// خطاف للحصول على قائمة الإفراجات
export const useReleases = (params: ReleaseSearchParams = {}) => {
  return useQuery({
    queryKey: ['releases', params],
    queryFn: () => getReleases(params),
  });
};

// خطاف للحصول على إفراج محدد
export const useRelease = (id: string) => {
  return useQuery({
    queryKey: ['release', id],
    queryFn: () => getRelease(id),
    enabled: !!id,
  });
};

// خطاف لإنشاء إفراج جديد
export const useCreateRelease = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ data, file }: { data: CreateReleaseRequest; file?: File }) =>
      createRelease(data, file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['releases'] });
      showSuccess(t('releases.createSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

// خطاف لتحديث إفراج
export const useUpdateRelease = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, data, file }: { id: string; data: UpdateReleaseRequest; file?: File }) =>
      updateRelease(id, data, file),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['releases'] });
      queryClient.invalidateQueries({ queryKey: ['release', variables.id] });
      showSuccess(t('releases.updateSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

// خطاف لحذف إفراج
export const useDeleteRelease = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => deleteRelease(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['releases'] });
      showSuccess(t('releases.deleteSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

// خطاف لتحميل ملف PDF للإفراج
export const useDownloadReleasePdf = () => {
  const { showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => {
      window.open(`/api/releases/pdf/${id}`, '_blank');
      return Promise.resolve();
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};
