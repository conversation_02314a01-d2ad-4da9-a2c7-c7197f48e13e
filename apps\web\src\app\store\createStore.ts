import { configureStore } from '@reduxjs/toolkit';
import { authReducer } from '../../features/auth/store/authSlice';
import declarationsReducer from '../store/slices/declarationsSlice';
import itemMovementsReducer from '../store/slices/itemMovementsSlice';
import clientsReducer from '../store/slices/clientsSlice';
import documentsReducer from '../store/slices/documentsSlice';
import settingsReducer from '../store/slices/settingsSlice';

/**
 * إنشاء متجر Redux للاختبارات
 * @returns متجر Redux
 */
export const createStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
      declarations: declarationsReducer,
      itemMovements: itemMovementsReducer,
      clients: clientsReducer,
      documents: documentsReducer,
      settings: settingsReducer,
    },
    // تعطيل middleware DevTools في بيئة الاختبار
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  });
};

export default createStore;
