import axios from 'axios';
import { getToken, clearTokens } from './auth';

/**
 * إنشاء نسخة من Axios مع الإعدادات الافتراضية
 */
export const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * إضافة معترض للطلبات لإضافة رمز المصادقة
 */
api.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * إضافة معترض للاستجابات للتعامل مع أخطاء المصادقة
 */
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // تسجيل الخروج عند انتهاء صلاحية الرمز
      clearTokens();
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
