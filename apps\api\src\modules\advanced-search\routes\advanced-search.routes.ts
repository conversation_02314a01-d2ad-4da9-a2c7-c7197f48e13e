import { Router } from 'express';
import { advancedSearchController } from '../controllers/advanced-search.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import { advancedSearchSchema } from '../schemas/advanced-search.schema.js';

export const advancedSearchRoutes = Router();

/**
 * مسار البحث المتقدم
 * يتيح البحث في مختلف وحدات النظام بمعايير متعددة
 */
advancedSearchRoutes.get(
  '/',
  authMiddleware,
  validateRequest(advancedSearchSchema),
  advancedSearchController.search
);
