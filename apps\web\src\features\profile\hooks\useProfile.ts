import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getProfile,
  updateProfile,
  changePassword,
  UpdateProfileRequest,
  ChangePasswordRequest,
} from '../api/profile.api';
import { useToast } from '@lib/hooks/useToast';
import { useTranslation } from 'react-i18next';

// خطاف للحصول على معلومات الملف الشخصي
export const useProfile = () => {
  return useQuery({
    queryKey: ['profile'],
    queryFn: () => getProfile(),
  });
};

// خطاف لتحديث معلومات الملف الشخصي
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (data: UpdateProfileRequest) => updateProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile'] });
      showSuccess(t('profile.updateSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

// خطاف لتغيير كلمة المرور
export const useChangePassword = () => {
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (data: ChangePasswordRequest) => changePassword(data),
    onSuccess: () => {
      showSuccess(t('profile.passwordChangeSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};
