import { Request, Response, NextFunction } from 'express';
import { HttpException } from './error.middleware.js';
import { prisma } from '../utils/prisma.js';
import { verifyToken } from '../utils/jwt.js';
import { tokenService } from '../utils/token.service.js';

// دالة للحصول على عميل قاعدة البيانات المناسب
const getDatabaseClient = async () => {
  if (process.env.NODE_ENV === 'test') {
    try {
      const testPrismaModule = await import('../utils/test/test-prisma-client.js');
      return testPrismaModule.testPrisma;
    } catch (error) {
      console.warn('تحذير: فشل في تحميل testPrisma، استخدام prisma العادي');
      return prisma;
    }
  }
  return prisma;
};

// توسيع واجهة الطلب في Express
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        username: string;
        role: string;
      };
    }
  }
}

/**
 * وسيط المصادقة
 * يتحقق من صحة توكن المصادقة ويضيف معلومات المستخدم إلى الطلب
 */
export const authMiddleware = async (
  req: Request,
  _res: Response,
  next: NextFunction
) => {
  try {
    // الحصول على التوكن من رأس الطلب
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new HttpException(401, 'لم يتم توفير التوكن', 'غير مصرح');
    }

    const token = authHeader.split(' ')[1];

    // التحقق مما إذا كان التوكن مبطلاً (تجاهل في بيئة الاختبار)
    if (process.env.NODE_ENV !== 'test') {
      const isInvalidated = await tokenService.isTokenInvalidated(token);
      if (isInvalidated) {
        throw new HttpException(401, 'التوكن غير صالح أو تم إبطاله', 'غير مصرح');
      }
    }

    // التحقق من صحة التوكن
    const decoded = verifyToken(token, 'access') as {
      id: string;
      username: string;
      role: string;
    };

    // التحقق من وجود المستخدم (استخدام testPrisma في بيئة الاختبار)
    const dbClient = await getDatabaseClient();
    const user = await dbClient.user.findUnique({
      where: { id: decoded.id },
      select: { id: true, username: true, role: true, isActive: true },
    });

    if (!user) {
      throw new HttpException(401, 'المستخدم غير موجود', 'غير مصرح');
    }

    // تجاهل فحص النشاط في بيئة الاختبار
    if (process.env.NODE_ENV !== 'test' && !user.isActive) {
      throw new HttpException(401, 'المستخدم غير نشط', 'غير مصرح');
    }

    // إضافة معلومات المستخدم إلى الطلب
    req.user = {
      id: user.id,
      username: user.username,
      role: user.role,
    };

    next();
  } catch (error: any) {
    if (error instanceof HttpException) {
      next(error);
    } else if (error.name === 'JsonWebTokenError') {
      next(new HttpException(401, 'التوكن غير صالح', 'غير مصرح'));
    } else if (error.name === 'TokenExpiredError') {
      next(new HttpException(401, 'انتهت صلاحية التوكن', 'غير مصرح'));
    } else {
      next(new HttpException(500, 'خطأ في المصادقة', 'خطأ داخلي'));
    }
  }
};

/**
 * وسيط المسؤول
 * يتحقق من أن المستخدم لديه صلاحيات المسؤول
 */
export const adminMiddleware = (
  req: Request,
  _res: Response,
  next: NextFunction
) => {
  if (req.user?.role !== 'ADMIN') {
    return next(
      new HttpException(403, 'مطلوب صلاحيات المسؤول', 'ممنوع')
    );
  }

  next();
};
