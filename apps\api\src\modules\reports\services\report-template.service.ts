import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { Prisma } from '@prisma/client';

/**
 * واجهة معلمات قائمة قوالب التقارير
 */
interface ListReportTemplatesParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  reportType?: string;
}

/**
 * خدمة قوالب التقارير
 */
export const reportTemplateService = {
  /**
   * إنشاء قالب تقرير جديد
   */
  createReportTemplate: async (data: any, userId: string) => {
    try {
      // التحقق مما إذا كان القالب الافتراضي
      if (data.isDefault) {
        // إذا كان القالب الافتراضي، قم بإلغاء تعيين القوالب الافتراضية الأخرى من نفس النوع
        await prisma.reportTemplate.updateMany({
          where: {
            reportType: data.reportType,
            isDefault: true,
          },
          data: {
            isDefault: false,
          },
        });
      }

      // إنشاء قالب تقرير جديد
      const reportTemplate = await prisma.reportTemplate.create({
        data: {
          name: data.name,
          description: data.description,
          reportType: data.reportType,
          template: data.template,
          isDefault: data.isDefault || false,
          userId: userId,
        },
      });

      return reportTemplate;
    } catch (error: any) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(400, 'اسم القالب موجود بالفعل', 'Bad Request');
        }
      }
      throw new HttpException(500, 'خطأ في إنشاء قالب التقرير', 'Internal Server Error');
    }
  },

  /**
   * تحديث قالب تقرير
   */
  updateReportTemplate: async (id: string, data: any, userId: string) => {
    try {
      // التحقق من وجود القالب
      const existingTemplate = await prisma.reportTemplate.findUnique({
        where: { id },
      });

      if (!existingTemplate) {
        throw new HttpException(404, 'قالب التقرير غير موجود', 'Not Found');
      }

      // التحقق مما إذا كان المستخدم هو منشئ القالب أو مدير
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (existingTemplate.userId !== userId && user?.role !== 'ADMIN') {
        throw new HttpException(403, 'غير مصرح لك بتعديل هذا القالب', 'Forbidden');
      }

      // التحقق مما إذا كان القالب الافتراضي
      if (data.isDefault) {
        // إذا كان القالب الافتراضي، قم بإلغاء تعيين القوالب الافتراضية الأخرى من نفس النوع
        await prisma.reportTemplate.updateMany({
          where: {
            reportType: data.reportType || existingTemplate.reportType,
            isDefault: true,
            id: { not: id },
          },
          data: {
            isDefault: false,
          },
        });
      }

      // تحديث قالب التقرير
      const reportTemplate = await prisma.reportTemplate.update({
        where: { id },
        data: {
          name: data.name,
          description: data.description,
          reportType: data.reportType,
          template: data.template,
          isDefault: data.isDefault,
        },
      });

      return reportTemplate;
    } catch (error: any) {
      if (error instanceof HttpException) {
        throw error;
      }
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(400, 'اسم القالب موجود بالفعل', 'Bad Request');
        }
      }
      throw new HttpException(500, 'خطأ في تحديث قالب التقرير', 'Internal Server Error');
    }
  },

  /**
   * حذف قالب تقرير
   */
  deleteReportTemplate: async (id: string, userId: string) => {
    try {
      // التحقق من وجود القالب
      const existingTemplate = await prisma.reportTemplate.findUnique({
        where: { id },
      });

      if (!existingTemplate) {
        throw new HttpException(404, 'قالب التقرير غير موجود', 'Not Found');
      }

      // التحقق مما إذا كان المستخدم هو منشئ القالب أو مدير
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (existingTemplate.userId !== userId && user?.role !== 'ADMIN') {
        throw new HttpException(403, 'غير مصرح لك بحذف هذا القالب', 'Forbidden');
      }

      // حذف قالب التقرير
      await prisma.reportTemplate.delete({
        where: { id },
      });

      return { success: true };
    } catch (error: any) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'خطأ في حذف قالب التقرير', 'Internal Server Error');
    }
  },

  /**
   * الحصول على قالب تقرير
   */
  getReportTemplate: async (id: string) => {
    try {
      const reportTemplate = await prisma.reportTemplate.findUnique({
        where: { id },
      });

      if (!reportTemplate) {
        throw new HttpException(404, 'قالب التقرير غير موجود', 'Not Found');
      }

      return reportTemplate;
    } catch (error: any) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'خطأ في الحصول على قالب التقرير', 'Internal Server Error');
    }
  },

  /**
   * الحصول على قائمة قوالب التقارير
   */
  listReportTemplates: async (params: ListReportTemplatesParams) => {
    try {
      const {
        page = 1,
        limit = 10,
        sort = 'createdAt',
        order = 'desc',
        search = '',
        reportType,
      } = params;

      // إنشاء شروط البحث
      const where: any = {};

      // إضافة شرط البحث عن نوع التقرير
      if (reportType) {
        where.reportType = reportType;
      }

      // إضافة شرط البحث عن النص
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      // حساب عدد النتائج
      const total = await prisma.reportTemplate.count({ where });

      // الحصول على قائمة قوالب التقارير
      const reportTemplates = await prisma.reportTemplate.findMany({
        where,
        orderBy: {
          [sort]: order,
        },
        skip: (page - 1) * limit,
        take: limit,
      });

      return {
        data: reportTemplates,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error: any) {
      throw new HttpException(500, 'خطأ في الحصول على قائمة قوالب التقارير', 'Internal Server Error');
    }
  },

  /**
   * الحصول على القالب الافتراضي لنوع تقرير معين
   */
  getDefaultTemplate: async (reportType: string) => {
    try {
      const defaultTemplate = await prisma.reportTemplate.findFirst({
        where: {
          reportType,
          isDefault: true,
        },
      });

      if (!defaultTemplate) {
        // إذا لم يكن هناك قالب افتراضي، قم بإنشاء قالب افتراضي
        return reportTemplateService.createDefaultTemplate(reportType);
      }

      return defaultTemplate;
    } catch (error: any) {
      throw new HttpException(500, 'خطأ في الحصول على القالب الافتراضي', 'Internal Server Error');
    }
  },

  /**
   * إنشاء قالب افتراضي لنوع تقرير معين
   */
  createDefaultTemplate: async (reportType: string) => {
    try {
      // الحصول على مستخدم مدير
      const admin = await prisma.user.findFirst({
        where: {
          role: 'ADMIN',
        },
      });

      if (!admin) {
        throw new HttpException(500, 'لا يوجد مستخدم مدير لإنشاء القالب الافتراضي', 'Internal Server Error');
      }

      // إنشاء قالب افتراضي
      const defaultTemplate = await prisma.reportTemplate.create({
        data: {
          name: `قالب افتراضي - ${reportType}`,
          description: 'قالب افتراضي تم إنشاؤه تلقائيًا',
          reportType,
          template: getDefaultTemplateStructure(reportType),
          isDefault: true,
          userId: admin.id,
        },
      });

      return defaultTemplate;
    } catch (error: any) {
      throw new HttpException(500, 'خطأ في إنشاء القالب الافتراضي', 'Internal Server Error');
    }
  },
};

/**
 * الحصول على هيكل القالب الافتراضي لنوع تقرير معين
 */
function getDefaultTemplateStructure(reportType: string): any {
  // هيكل القالب الافتراضي حسب نوع التقرير
  switch (reportType) {
    case 'DECLARATIONS':
      return {
        title: 'تقرير البيانات',
        subtitle: 'قائمة البيانات',
        showLogo: true,
        showDate: true,
        showPageNumbers: true,
        orientation: 'portrait',
        columns: [
          { field: 'declarationNumber', header: 'رقم البيان', width: 100 },
          { field: 'declarationType', header: 'نوع البيان', width: 100 },
          { field: 'clientName', header: 'اسم العميل', width: 150 },
          { field: 'declarationDate', header: 'تاريخ البيان', width: 100 },
          { field: 'entryDate', header: 'تاريخ الدخول', width: 100 },
          { field: 'exitDate', header: 'تاريخ الخروج', width: 100 },
        ],
      };
    // يمكن إضافة المزيد من أنواع التقارير هنا
    default:
      return {
        title: `تقرير ${reportType}`,
        subtitle: `قائمة ${reportType}`,
        showLogo: true,
        showDate: true,
        showPageNumbers: true,
        orientation: 'portrait',
        columns: [],
      };
  }
}
