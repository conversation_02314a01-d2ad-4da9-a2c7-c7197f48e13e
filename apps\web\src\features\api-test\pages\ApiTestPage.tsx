import React, { useState } from 'react';
import {
  Box,
  Button,
  Container,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import {
  Send as SendIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  ContentCopy as CopyIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { PageHeader } from '@features/common/components/PageHeader';
import { CodeEditor } from '@features/common/components/CodeEditor';
import { useToast } from '@lib/hooks/useToast';
import { api } from '@lib/api/api';

// تعريف أنواع الطلبات
enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

// تعريف أنواع المحتوى
enum ContentType {
  JSON = 'application/json',
  FORM = 'application/x-www-form-urlencoded',
  MULTIPART = 'multipart/form-data',
  TEXT = 'text/plain',
}

// تعريف مخطط التحقق من صحة البيانات
const headerSchema = z.object({
  key: z.string().min(1, 'اسم الرأس مطلوب'),
  value: z.string(),
});

const requestSchema = z.object({
  name: z.string().min(1, 'اسم الطلب مطلوب'),
  url: z.string().min(1, 'عنوان URL مطلوب'),
  method: z.nativeEnum(RequestMethod, {
    errorMap: () => ({ message: 'طريقة الطلب مطلوبة' }),
  }),
  contentType: z.nativeEnum(ContentType, {
    errorMap: () => ({ message: 'نوع المحتوى مطلوب' }),
  }).optional(),
  headers: z.array(headerSchema).optional(),
  body: z.string().optional(),
});

// نوع بيانات النموذج
type RequestFormValues = z.infer<typeof requestSchema>;

/**
 * صفحة اختبار API
 * تستخدم لاختبار واجهات API وحفظ الطلبات للاستخدام المستقبلي
 */
const ApiTestPage: React.FC = () => {
  const { t } = useTranslation();
  const toast = useToast();

  // حالة الصفحة
  const [activeTab, setActiveTab] = useState(0);
  const [response, setResponse] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [savedRequests, setSavedRequests] = useState<RequestFormValues[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<number | null>(null);

  // إعداد نموذج الطلب
  const {
    control,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<RequestFormValues>({
    resolver: zodResolver(requestSchema),
    defaultValues: {
      name: 'طلب جديد',
      url: '',
      method: RequestMethod.GET,
      contentType: ContentType.JSON,
      headers: [{ key: 'Content-Type', value: 'application/json' }],
      body: '',
    },
  });

  // إعداد مصفوفة الرؤوس
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'headers',
  });

  // مراقبة قيم النموذج
  const formValues = watch();

  // تغيير التبويب النشط
  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // إضافة رأس جديد
  const handleAddHeader = () => {
    append({ key: '', value: '' });
  };

  // حفظ الطلب
  const handleSaveRequest = () => {
    const newRequest = { ...formValues };
    const updatedRequests = [...savedRequests];

    if (selectedRequest !== null) {
      updatedRequests[selectedRequest] = newRequest;
    } else {
      updatedRequests.push(newRequest);
    }

    setSavedRequests(updatedRequests);
    setSelectedRequest(selectedRequest !== null ? selectedRequest : updatedRequests.length - 1);
    toast.showSuccess(t('apiTest.requestSaved'));
  };

  // حذف الطلب
  const handleDeleteRequest = (index: number) => {
    const updatedRequests = [...savedRequests];
    updatedRequests.splice(index, 1);
    setSavedRequests(updatedRequests);

    if (selectedRequest === index) {
      setSelectedRequest(null);
    } else if (selectedRequest !== null && selectedRequest > index) {
      setSelectedRequest(selectedRequest - 1);
    }

    toast.showSuccess(t('apiTest.requestDeleted'));
  };

  // تحميل طلب محفوظ
  const handleLoadRequest = (index: number) => {
    const request = savedRequests[index];
    reset(request);
    setSelectedRequest(index);
  };

  // نسخ الاستجابة
  const handleCopyResponse = () => {
    if (response) {
      navigator.clipboard.writeText(JSON.stringify(response, null, 2));
      toast.showSuccess(t('apiTest.responseCopied'));
    }
  };

  // إرسال الطلب
  const onSubmit = async (data: RequestFormValues) => {
    try {
      setIsLoading(true);
      setResponse(null);

      // تحويل الرؤوس إلى كائن
      const headers: Record<string, string> = {};
      data.headers?.forEach((header) => {
        if (header.key) {
          headers[header.key] = header.value;
        }
      });

      // إعداد خيارات الطلب
      const options: any = {
        url: data.url,
        method: data.method,
        headers,
      };

      // إضافة الجسم إذا كان مطلوبًا
      if (data.method !== RequestMethod.GET && data.body) {
        try {
          options.data = JSON.parse(data.body);
        } catch (e) {
          options.data = data.body;
        }
      }

      // إرسال الطلب
      const result = await api.request(options);
      setResponse(result);

      toast.showSuccess(t('apiTest.requestSuccess'));
    } catch (error: any) {
      console.error('API request error:', error);

      // عرض الاستجابة حتى في حالة الخطأ
      if (error.response) {
        setResponse({
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers,
        });
      } else {
        setResponse({
          error: error.message || 'Unknown error',
        });
      }

      toast.showError(t('apiTest.requestError'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container maxWidth="xl">
      <PageHeader
        title={t('apiTest.title')}
        subtitle={t('apiTest.subtitle')}
      />

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
              <Grid container spacing={2}>
                <Grid item xs={12} md={8}>
                  <Controller
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('apiTest.fields.name')}
                        fullWidth
                        required
                        error={!!errors.name}
                        helperText={errors.name?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <Controller
                    name="method"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.method}>
                        <InputLabel>{t('apiTest.fields.method')}</InputLabel>
                        <Select
                          {...field}
                          label={t('apiTest.fields.method')}
                        >
                          {Object.values(RequestMethod).map((method) => (
                            <MenuItem key={method} value={method}>
                              {method}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Controller
                    name="url"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('apiTest.fields.url')}
                        fullWidth
                        required
                        error={!!errors.url}
                        helperText={errors.url?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    {t('apiTest.headers')}
                  </Typography>

                  {fields.map((field, index) => (
                    <Box key={field.id} sx={{ display: 'flex', mb: 1 }}>
                      <TextField
                        label={t('apiTest.fields.headerKey')}
                        {...control.register(`headers.${index}.key` as const)}
                        error={!!errors.headers?.[index]?.key}
                        helperText={errors.headers?.[index]?.key?.message}
                        sx={{ mr: 1, flex: 1 }}
                      />
                      <TextField
                        label={t('apiTest.fields.headerValue')}
                        {...control.register(`headers.${index}.value` as const)}
                        error={!!errors.headers?.[index]?.value}
                        helperText={errors.headers?.[index]?.value?.message}
                        sx={{ mr: 1, flex: 1 }}
                      />
                      <Button
                        color="error"
                        onClick={() => remove(index)}
                        sx={{ minWidth: 40 }}
                      >
                        <DeleteIcon />
                      </Button>
                    </Box>
                  ))}

                  <Button
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={handleAddHeader}
                    size="small"
                    sx={{ mt: 1 }}
                  >
                    {t('apiTest.addHeader')}
                  </Button>
                </Grid>

                {formValues.method !== RequestMethod.GET && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" gutterBottom>
                      {t('apiTest.requestBody')}
                    </Typography>

                    <Controller
                      name="body"
                      control={control}
                      render={({ field }) => (
                        <CodeEditor
                          value={field.value || ''}
                          onChange={field.onChange}
                          language="json"
                          height="200px"
                        />
                      )}
                    />
                  </Grid>
                )}

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<SaveIcon />}
                      onClick={handleSaveRequest}
                    >
                      {t('apiTest.saveRequest')}
                    </Button>

                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      startIcon={<SendIcon />}
                      disabled={isLoading}
                    >
                      {isLoading ? t('apiTest.sending') : t('apiTest.sendRequest')}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Paper>

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {t('apiTest.savedRequests')}
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {savedRequests.length === 0 ? (
              <Typography color="text.secondary">
                {t('apiTest.noSavedRequests')}
              </Typography>
            ) : (
              savedRequests.map((request, index) => (
                <Box
                  key={index}
                  sx={{
                    p: 2,
                    mb: 1,
                    border: '1px solid',
                    borderColor: selectedRequest === index ? 'primary.main' : 'divider',
                    borderRadius: 1,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    bgcolor: selectedRequest === index ? 'action.selected' : 'background.paper',
                  }}
                >
                  <Box>
                    <Typography variant="subtitle1">
                      {request.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {request.method} {request.url}
                    </Typography>
                  </Box>
                  <Box>
                    <Button
                      size="small"
                      onClick={() => handleLoadRequest(index)}
                    >
                      {t('apiTest.load')}
                    </Button>
                    <Button
                      size="small"
                      color="error"
                      onClick={() => handleDeleteRequest(index)}
                    >
                      {t('apiTest.delete')}
                    </Button>
                  </Box>
                </Box>
              ))
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                {t('apiTest.response')}
              </Typography>
              {response && (
                <Button
                  startIcon={<CopyIcon />}
                  onClick={handleCopyResponse}
                >
                  {t('apiTest.copy')}
                </Button>
              )}
            </Box>

            <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
              <Tab label={t('apiTest.tabs.response')} id="tab-0" aria-controls="tabpanel-0" />
              <Tab label={t('apiTest.tabs.headers')} id="tab-1" aria-controls="tabpanel-1" />
            </Tabs>

            <Box role="tabpanel" hidden={activeTab !== 0} id="tabpanel-0" aria-labelledby="tab-0">
              {activeTab === 0 && (
                <>
                  {response ? (
                    <>
                      {response.status && (
                        <Box sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" color="text.secondary">
                            {t('apiTest.status')}
                          </Typography>
                          <Typography
                            variant="body1"
                            color={response.status >= 400 ? 'error.main' : 'success.main'}
                          >
                            {response.status} {response.statusText}
                          </Typography>
                        </Box>
                      )}

                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        {t('apiTest.responseBody')}
                      </Typography>

                      <CodeEditor
                        value={JSON.stringify(response.data || response, null, 2)}
                        language="json"
                        readOnly
                        height="400px"
                      />
                    </>
                  ) : (
                    <Typography color="text.secondary">
                      {t('apiTest.noResponse')}
                    </Typography>
                  )}
                </>
              )}
            </Box>

            <Box role="tabpanel" hidden={activeTab !== 1} id="tabpanel-1" aria-labelledby="tab-1">
              {activeTab === 1 && (
                <>
                  {response && response.headers ? (
                    <Box>
                      {Object.entries(response.headers).map(([key, value]) => (
                        <Box key={key} sx={{ mb: 1, pb: 1, borderBottom: '1px solid', borderColor: 'divider' }}>
                          <Typography variant="subtitle2">
                            {key}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {value as string}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  ) : (
                    <Typography color="text.secondary">
                      {t('apiTest.noHeaders')}
                    </Typography>
                  )}
                </>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ApiTestPage;
