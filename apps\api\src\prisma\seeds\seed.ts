import { PrismaClient, TokenType, UserRole, DeclarationType, GoodsType, AuthorizationType, Currency } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('بدء زراعة البيانات الأولية...');

  // إنشاء مستخدمين
  const adminPassword = await bcrypt.hash('admin123', 10);
  const userPassword = await bcrypt.hash('user123', 10);
  const managerPassword = await bcrypt.hash('manager123', 10);

  const admin = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      password: adminPassword,
      name: 'مدير النظام',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
    },
  });

  const user = await prisma.user.upsert({
    where: { username: 'user' },
    update: {},
    create: {
      username: 'user',
      password: userPassword,
      name: 'مستخدم النظام',
      email: '<EMAIL>',
      role: UserRole.USER,
    },
  });

  const manager = await prisma.user.upsert({
    where: { username: 'manager' },
    update: {},
    create: {
      username: 'manager',
      password: managerPassword,
      name: 'مدير القسم',
      email: '<EMAIL>',
      role: UserRole.MANAGER,
    },
  });

  console.log({ admin, user, manager });

  // إنشاء عملاء
  const client1 = await prisma.client.upsert({
    where: { taxNumber: '1234567890' },
    update: {},
    create: {
      name: 'شركة الأمل للأدوية',
      taxNumber: '1234567890',
      phone: '0555123456',
      email: '<EMAIL>',
      address: 'الرياض - حي العليا',
    },
  });

  const client2 = await prisma.client.upsert({
    where: { taxNumber: '9876543210' },
    update: {},
    create: {
      name: 'شركة الصحة الدولية',
      taxNumber: '9876543210',
      phone: '0555987654',
      email: '<EMAIL>',
      address: 'جدة - حي الروضة',
    },
  });

  const client3 = await prisma.client.upsert({
    where: { taxNumber: '5555555555' },
    update: {},
    create: {
      name: 'مؤسسة الشفاء الطبية',
      taxNumber: '5555555555',
      phone: '0555555555',
      email: '<EMAIL>',
      address: 'الدمام - حي الفيصلية',
    },
  });

  console.log({ client1, client2, client3 });

  // إنشاء بيانات
  const declaration1 = await prisma.declaration.upsert({
    where: { declarationNumber: 'DEC-2024-001' },
    update: {},
    create: {
      declarationNumber: 'DEC-2024-001',
      taxNumber: client1.taxNumber,
      clientName: client1.name,
      companyName: 'شركة الأمل للأدوية',
      policyNumber: 'POL-001',
      invoiceNumber: 'INV-001',
      gatewayEntryNumber: 'GW-001',
      declarationType: DeclarationType.IMPORT,
      declarationDate: new Date('2024-01-15'),
      count: 100,
      weight: 500.5,
      goodsType: GoodsType.HUMAN_MEDICINE,
      itemsCount: 5,
      entryDate: new Date('2024-01-20'),
      exitDate: new Date('2024-01-25'),
      clientId: client1.id,
    },
  });

  const declaration2 = await prisma.declaration.upsert({
    where: { declarationNumber: 'DEC-2024-002' },
    update: {},
    create: {
      declarationNumber: 'DEC-2024-002',
      taxNumber: client2.taxNumber,
      clientName: client2.name,
      companyName: 'شركة الصحة الدولية',
      policyNumber: 'POL-002',
      invoiceNumber: 'INV-002',
      gatewayEntryNumber: 'GW-002',
      declarationType: DeclarationType.EXPORT,
      declarationDate: new Date('2024-02-10'),
      count: 200,
      weight: 1000.75,
      goodsType: GoodsType.MEDICAL_SUPPLIES,
      itemsCount: 10,
      entryDate: new Date('2024-02-15'),
      exitDate: new Date('2024-02-20'),
      clientId: client2.id,
    },
  });

  console.log({ declaration1, declaration2 });

  // إنشاء سائقين
  const driver1 = await prisma.driver.upsert({
    where: {
      id: 'driver1',
    },
    update: {},
    create: {
      id: 'driver1',
      declarationId: declaration1.id,
      driverName: 'أحمد محمد',
      truckNumber: 'TR-123',
      trailerNumber: 'TRL-123',
      driverPhone: '**********',
    },
  });

  const driver2 = await prisma.driver.upsert({
    where: {
      id: 'driver2',
    },
    update: {},
    create: {
      id: 'driver2',
      declarationId: declaration2.id,
      driverName: 'خالد عبدالله',
      truckNumber: 'TR-456',
      trailerNumber: 'TRL-456',
      driverPhone: '**********',
    },
  });

  console.log({ driver1, driver2 });

  // إنشاء حركات أصناف
  const itemMovement1 = await prisma.itemMovement.upsert({
    where: {
      id: 'item1',
    },
    update: {},
    create: {
      id: 'item1',
      declarationId: declaration1.id,
      itemName: 'دواء باراسيتامول',
      quantity: 1000,
      unit: 'علبة',
      movementDate: new Date('2024-01-22'),
      movementType: 'استلام',
    },
  });

  const itemMovement2 = await prisma.itemMovement.upsert({
    where: {
      id: 'item2',
    },
    update: {},
    create: {
      id: 'item2',
      declarationId: declaration2.id,
      itemName: 'قفازات طبية',
      quantity: 5000,
      unit: 'صندوق',
      movementDate: new Date('2024-02-17'),
      movementType: 'تصدير',
    },
  });

  console.log({ itemMovement1, itemMovement2 });

  // إنشاء تفويضات
  const authorization1 = await prisma.authorization.upsert({
    where: {
      id: 'auth1',
    },
    update: {},
    create: {
      id: 'auth1',
      declarationId: declaration1.id,
      authorizationType: AuthorizationType.FOLLOW_UP,
      authorizedPerson: 'سعيد علي',
      idNumber: 'ID-12345',
      startDate: new Date('2024-01-20'),
      endDate: new Date('2024-02-20'),
      notes: 'تفويض لمتابعة البيان',
    },
  });

  const authorization2 = await prisma.authorization.upsert({
    where: {
      id: 'auth2',
    },
    update: {},
    create: {
      id: 'auth2',
      declarationId: declaration2.id,
      authorizationType: AuthorizationType.RECEIPT,
      authorizedPerson: 'فهد محمد',
      idNumber: 'ID-67890',
      startDate: new Date('2024-02-15'),
      endDate: new Date('2024-03-15'),
      notes: 'تفويض لاستلام البضائع',
    },
  });

  console.log({ authorization1, authorization2 });

  // إنشاء توكنات مبطلة للاختبار
  const invalidatedToken1 = await prisma.invalidatedToken.upsert({
    where: {
      token: 'test-access-token-1',
    },
    update: {},
    create: {
      token: 'test-access-token-1',
      tokenType: TokenType.ACCESS,
      userId: admin.id,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // بعد يوم واحد
      invalidatedAt: new Date(),
    },
  });

  const invalidatedToken2 = await prisma.invalidatedToken.upsert({
    where: {
      token: 'test-refresh-token-1',
    },
    update: {},
    create: {
      token: 'test-refresh-token-1',
      tokenType: TokenType.REFRESH,
      userId: admin.id,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // بعد أسبوع
      invalidatedAt: new Date(),
    },
  });

  console.log({ invalidatedToken1, invalidatedToken2 });

  console.log('تم إنشاء البيانات الأولية بنجاح');
}

main()
  .catch((e) => {
    console.error('حدث خطأ أثناء زراعة البيانات:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
