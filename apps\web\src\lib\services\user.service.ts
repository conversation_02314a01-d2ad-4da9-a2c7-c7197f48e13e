import apiService from './api.service';

/**
 * واجهة المستخدم
 */
export interface User {
  id: string;
  username: string;
  email?: string;
  role: string;
  firstName?: string;
  lastName?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * خدمة المستخدمين
 * توفر واجهة موحدة للتعامل مع المستخدمين
 */
class UserService {
  /**
   * الحصول على المستخدم الحالي
   * @returns وعد بمعلومات المستخدم الحالي
   */
  public async getCurrentUser(): Promise<User> {
    try {
      // الحصول على معلومات المستخدم الحالي
      const response = await apiService.get('/api/users/me');

      return response;
    } catch (error) {
      console.error('Error getting current user:', error);
      throw error;
    }
  }

  /**
   * الحصول على قائمة المستخدمين
   * @returns وعد بقائمة المستخدمين
   */
  public async getUsers(): Promise<User[]> {
    try {
      // الحصول على قائمة المستخدمين
      const response = await apiService.get('/api/users');

      return response;
    } catch (error) {
      console.error('Error getting users:', error);
      throw error;
    }
  }

  /**
   * الحصول على معلومات مستخدم
   * @param userId معرف المستخدم
   * @returns وعد بمعلومات المستخدم
   */
  public async getUser(userId: string): Promise<User> {
    try {
      // الحصول على معلومات المستخدم
      const response = await apiService.get(`/api/users/${userId}`);

      return response;
    } catch (error) {
      console.error('Error getting user:', error);
      throw error;
    }
  }

  /**
   * إنشاء مستخدم جديد
   * @param user بيانات المستخدم
   * @returns وعد بمعلومات المستخدم الجديد
   */
  public async createUser(user: Partial<User>): Promise<User> {
    try {
      // إنشاء مستخدم جديد
      const response = await apiService.post('/api/users', user);

      return response;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * تحديث معلومات مستخدم
   * @param userId معرف المستخدم
   * @param user بيانات المستخدم
   * @returns وعد بمعلومات المستخدم المحدثة
   */
  public async updateUser(userId: string, user: Partial<User>): Promise<User> {
    try {
      // تحديث معلومات المستخدم
      const response = await apiService.put(`/api/users/${userId}`, user);

      return response;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * حذف مستخدم
   * @param userId معرف المستخدم
   * @returns وعد بنتيجة الحذف
   */
  public async deleteUser(userId: string): Promise<any> {
    try {
      // حذف المستخدم
      const response = await apiService.delete(`/api/users/${userId}`);

      return response;
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * تغيير كلمة المرور
   * @param oldPassword كلمة المرور القديمة
   * @param newPassword كلمة المرور الجديدة
   * @returns وعد بنتيجة تغيير كلمة المرور
   */
  public async changePassword(oldPassword: string, newPassword: string): Promise<any> {
    try {
      // تغيير كلمة المرور
      const response = await apiService.post('/api/users/change-password', {
        oldPassword,
        newPassword,
      });

      return response;
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  }
}

// إنشاء نسخة واحدة من الخدمة
const userService = new UserService();

export default userService;
