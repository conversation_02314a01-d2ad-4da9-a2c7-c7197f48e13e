import axios from 'axios';
import { SystemSettings } from '../types/settings.types';

// واجهة طلب تحديث الإعدادات
export interface UpdateSettingsRequest {
  companyName?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
  companyWebsite?: string;
  primaryColor?: string;
  secondaryColor?: string;
  defaultFont?: string;
  defaultLanguage?: string;
  maxFileSize?: number;
  enablePrinting?: boolean;
}

/**
 * الحصول على إعدادات النظام
 */
export const getSettings = async (): Promise<SystemSettings> => {
  const response = await axios.get('/api/settings');
  return response.data;
};

/**
 * تحديث إعدادات النظام
 */
export const updateSettings = async (
  data: UpdateSettingsRequest,
  file?: File
): Promise<SystemSettings> => {
  if (file) {
    const formData = new FormData();

    // إضافة البيانات إلى النموذج
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        formData.append(key, value);
      }
    });

    // إضافة الملف إلى النموذج
    formData.append('logo', file);

    const response = await axios.put('/api/settings', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  } else {
    const response = await axios.put('/api/settings', data);
    return response.data;
  }
};
