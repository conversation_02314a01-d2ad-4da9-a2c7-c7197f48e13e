import { Request, Response, NextFunction } from 'express';
import { customFormService } from '../services/custom-form.service.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { successResponse, paginatedResponse } from '../../../core/utils/api/apiResponse.js';

export const customFormController = {
  /**
   * إنشاء نموذج مخصص جديد
   */
  createCustomForm: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على بيانات النموذج المخصص من الطلب
      const customFormData = req.body;

      // إنشاء النموذج المخصص
      const customForm = await customFormService.createCustomForm(
        customFormData,
        req.user.id
      );

      return res.status(201).json(successResponse(customForm, 'تم إنشاء النموذج المخصص بنجاح', 201));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على نموذج مخصص محدد
   */
  getCustomForm: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;

      // الحصول على النموذج المخصص
      const customForm = await customFormService.getCustomForm(id);

      return res.status(200).json(successResponse(customForm));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة النماذج المخصصة
   */
  listCustomForms: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معلمات البحث
      const { page, limit, sort, order, formType, isActive } = req.query as any;

      // الحصول على قائمة النماذج المخصصة
      const result = await customFormService.listCustomForms({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        formType,
        isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
      });

      return res.status(200).json(paginatedResponse(
        result.data,
        result.pagination.page,
        result.pagination.limit,
        result.pagination.total
      ));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث نموذج مخصص
   */
  updateCustomForm: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      const { id } = req.params;
      const customFormData = req.body;

      // تحديث النموذج المخصص
      const customForm = await customFormService.updateCustomForm(id, customFormData);

      return res.status(200).json(successResponse(customForm, 'تم تحديث النموذج المخصص بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف نموذج مخصص
   */
  deleteCustomForm: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;

      // حذف النموذج المخصص
      await customFormService.deleteCustomForm(id);

      return res.status(200).json(successResponse(null, 'تم حذف النموذج المخصص بنجاح'));
    } catch (error) {
      next(error);
    }
  },
};
