#!/usr/bin/env node

/**
 * سكريبت لإصلاح مسارات الاستيراد في ملفات TypeScript
 * يضيف امتدادات .js لجميع الاستيرادات النسبية
 */

const fs = require('fs');
const path = require('path');

// قائمة بالمجلدات التي نريد معالجتها
const foldersToProcess = [
  'src/core',
  'src/modules'
];

// أنماط الاستيراد التي نريد إصلاحها
const importPatterns = [
  // استيراد عادي
  /from\s+['"](\.\/.+?)['"];?/g,
  /from\s+['"](\.\.\/.+?)['"];?/g,
  // استيراد ديناميكي
  /import\s*\(\s*['"](\.\/.+?)['"]?\s*\)/g,
  /import\s*\(\s*['"](\.\.\/.+?)['"]?\s*\)/g,
  // استيراد مباشر
  /import\s+.*?\s+from\s+['"](\.\/.+?)['"];?/g,
  /import\s+.*?\s+from\s+['"](\.\.\/.+?)['"];?/g,
  // استيراد بدون from
  /import\s+['"](\.\/.+?)['"];?/g,
  /import\s+['"](\.\.\/.+?)['"];?/g,
];

/**
 * يتحقق من وجود ملف TypeScript أو JavaScript
 */
function fileExists(filePath) {
  const extensions = ['.ts', '.tsx', '.js', '.jsx'];
  for (const ext of extensions) {
    if (fs.existsSync(filePath + ext)) {
      return true;
    }
  }
  return false;
}

/**
 * يحول مسار الاستيراد ليشمل امتداد .js
 */
function fixImportPath(importPath, currentFileDir) {
  // تجاهل الاستيرادات من node_modules
  if (!importPath.startsWith('./') && !importPath.startsWith('../')) {
    return importPath;
  }

  // إذا كان المسار يحتوي على امتداد بالفعل، لا نغيره
  if (path.extname(importPath)) {
    return importPath;
  }

  // حل المسار النسبي
  const resolvedPath = path.resolve(currentFileDir, importPath);

  // تحقق من وجود الملف
  if (fileExists(resolvedPath)) {
    return importPath + '.js';
  }

  // تحقق من وجود ملف index
  const indexPath = path.join(resolvedPath, 'index');
  if (fileExists(indexPath)) {
    return importPath + '/index.js';
  }

  // إذا لم نجد الملف، أضف .js على أي حال
  return importPath + '.js';
}

/**
 * يعالج محتوى ملف واحد
 */
function processFileContent(content, filePath) {
  const currentFileDir = path.dirname(filePath);
  let modifiedContent = content;
  let hasChanges = false;

  // معالجة كل نمط استيراد
  for (const pattern of importPatterns) {
    modifiedContent = modifiedContent.replace(pattern, (match, importPath) => {
      const fixedPath = fixImportPath(importPath, currentFileDir);
      if (fixedPath !== importPath) {
        hasChanges = true;
        console.log(`  ${importPath} -> ${fixedPath}`);
      }
      return match.replace(importPath, fixedPath);
    });
  }

  return { content: modifiedContent, hasChanges };
}

/**
 * يعالج ملف واحد
 */
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const result = processFileContent(content, filePath);

    if (result.hasChanges) {
      fs.writeFileSync(filePath, result.content, 'utf8');
      return true;
    }
    return false;
  } catch (error) {
    console.error(`خطأ في معالجة الملف ${filePath}:`, error.message);
    return false;
  }
}

/**
 * يعالج جميع ملفات TypeScript في مجلد
 */
function processDirectory(dirPath) {
  const files = fs.readdirSync(dirPath, { withFileTypes: true });
  let processedCount = 0;

  for (const file of files) {
    const fullPath = path.join(dirPath, file.name);

    if (file.isDirectory()) {
      // معالجة المجلدات الفرعية
      processedCount += processDirectory(fullPath);
    } else if (file.isFile() && (file.name.endsWith('.ts') || file.name.endsWith('.tsx'))) {
      // معالجة ملفات TypeScript
      console.log(`معالجة: ${fullPath}`);
      if (processFile(fullPath)) {
        processedCount++;
      }
    }
  }

  return processedCount;
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔧 بدء إصلاح مسارات الاستيراد...\n');

  let totalProcessed = 0;

  for (const folder of foldersToProcess) {
    const fullPath = path.resolve(folder);

    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  المجلد غير موجود: ${fullPath}`);
      continue;
    }

    console.log(`📁 معالجة المجلد: ${folder}`);
    const processed = processDirectory(fullPath);
    totalProcessed += processed;
    console.log(`✅ تم معالجة ${processed} ملف في ${folder}\n`);
  }

  console.log(`🎉 انتهى! تم إصلاح ${totalProcessed} ملف إجمالي.`);

  if (totalProcessed > 0) {
    console.log('\n📝 يُنصح بتشغيل الأوامر التالية:');
    console.log('npm run build  # للتحقق من عدم وجود أخطاء');
    console.log('npm test       # لتشغيل الاختبارات');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = { processFileContent, fixImportPath };
