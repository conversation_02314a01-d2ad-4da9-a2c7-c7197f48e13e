import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { createStore } from '../../../app/store/store';
import ClientsPage from '../pages/ClientsPage';
import ClientFormPage from '../pages/ClientFormPage';
import ClientDetailsPage from '../pages/ClientDetailsPage';

// Mock React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

// Mock Redux Store
const store = createStore();

// Mock React Router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ id: '123' }),
    useNavigate: () => vi.fn(),
  };
});

// Mock React Query Hooks
vi.mock('../hooks/useClients', () => ({
  useClients: () => ({
    data: {
      data: [
        {
          id: '123',
          clientNumber: 1001,
          taxNumber: '*********',
          name: 'عميل اختبار',
          companyName: 'شركة اختبار',
          phone: '**********',
          email: '<EMAIL>',
          address: 'عنوان اختبار',
          contactPerson: 'شخص اختبار',
          contactPhone: '**********',
          notes: 'ملاحظات اختبار',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: { name: 'مستخدم اختبار' },
        },
      ],
      pagination: {
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      },
    },
    isLoading: false,
    isError: false,
  }),
  useClient: () => ({
    data: {
      id: '123',
      clientNumber: 1001,
      taxNumber: '*********',
      name: 'عميل اختبار',
      companyName: 'شركة اختبار',
      phone: '**********',
      email: '<EMAIL>',
      address: 'عنوان اختبار',
      contactPerson: 'شخص اختبار',
      contactPhone: '**********',
      notes: 'ملاحظات اختبار',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: { name: 'مستخدم اختبار' },
    },
    isLoading: false,
    isError: false,
  }),
  useCreateClient: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useUpdateClient: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useDeleteClient: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
}));

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: vi.fn(),
    },
  }),
}));

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn().mockReturnValue('2023-01-01'),
  arSA: {},
}));

// Wrapper Component
const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>{children}</BrowserRouter>
    </QueryClientProvider>
  </Provider>
);

describe('Clients Feature', () => {
  describe('ClientsPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <ClientsPage />
        </Wrapper>
      );
    });

    it('renders the clients page title', () => {
      expect(screen.getByText('clients.title')).toBeInTheDocument();
    });

    it('renders the clients table', () => {
      expect(screen.getByText('clients.clientNumber')).toBeInTheDocument();
      expect(screen.getByText('clients.taxNumber')).toBeInTheDocument();
      expect(screen.getByText('clients.name')).toBeInTheDocument();
      expect(screen.getByText('clients.companyName')).toBeInTheDocument();
    });

    it('renders the create client button', () => {
      expect(screen.getByText('clients.create')).toBeInTheDocument();
    });

    it('shows search field', () => {
      const searchField = screen.getByPlaceholderText('common.search');
      expect(searchField).toBeInTheDocument();
    });
  });

  describe('ClientFormPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <ClientFormPage />
        </Wrapper>
      );
    });

    it('renders the client form', () => {
      expect(screen.getByText('clients.taxNumber')).toBeInTheDocument();
      expect(screen.getByText('clients.name')).toBeInTheDocument();
      expect(screen.getByText('clients.companyName')).toBeInTheDocument();
      expect(screen.getByText('clients.phone')).toBeInTheDocument();
      expect(screen.getByText('clients.email')).toBeInTheDocument();
    });

    it('renders the save button', () => {
      expect(screen.getByText('common.save')).toBeInTheDocument();
    });

    it('renders the cancel button', () => {
      expect(screen.getByText('common.cancel')).toBeInTheDocument();
    });
  });

  describe('ClientDetailsPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <ClientDetailsPage />
        </Wrapper>
      );
    });

    it('renders the client details', () => {
      expect(screen.getByText('clients.details')).toBeInTheDocument();
      expect(screen.getByText('clients.clientNumber')).toBeInTheDocument();
      expect(screen.getByText('clients.taxNumber')).toBeInTheDocument();
      expect(screen.getByText('clients.name')).toBeInTheDocument();
      expect(screen.getByText('clients.companyName')).toBeInTheDocument();
    });

    it('renders the edit button', () => {
      expect(screen.getByText('common.edit')).toBeInTheDocument();
    });

    it('renders the delete button', () => {
      expect(screen.getByText('common.delete')).toBeInTheDocument();
    });

    it('renders the back button', () => {
      expect(screen.getByText('common.back')).toBeInTheDocument();
    });
  });
});
