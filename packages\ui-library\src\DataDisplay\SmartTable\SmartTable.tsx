import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  IconButton,
  Box,
  Typography,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';

export interface Column<T> {
  /**
   * معرف العمود
   */
  id: string;
  
  /**
   * عنوان العمود
   */
  label: string;
  
  /**
   * دالة لعرض قيمة الخلية
   */
  render?: (row: T) => React.ReactNode;
  
  /**
   * هل العمود قابل للفرز
   */
  sortable?: boolean;
  
  /**
   * عرض العمود
   */
  width?: string | number;
  
  /**
   * محاذاة العمود
   */
  align?: 'left' | 'center' | 'right';
}

export interface SmartTableProps<T> {
  /**
   * أعمدة الجدول
   */
  columns: Column<T>[];
  
  /**
   * بيانات الجدول
   */
  data: T[];
  
  /**
   * إجمالي عدد العناصر
   */
  totalCount: number;
  
  /**
   * رقم الصفحة الحالية
   */
  page: number;
  
  /**
   * عدد العناصر في الصفحة
   */
  rowsPerPage: number;
  
  /**
   * معالج تغيير الصفحة
   */
  onPageChange: (page: number) => void;
  
  /**
   * معالج تغيير عدد العناصر في الصفحة
   */
  onRowsPerPageChange: (rowsPerPage: number) => void;
  
  /**
   * معالج النقر على زر التعديل
   */
  onEdit?: (row: T) => void;
  
  /**
   * معالج النقر على زر الحذف
   */
  onDelete?: (row: T) => void;
  
  /**
   * معالج النقر على زر العرض
   */
  onView?: (row: T) => void;
  
  /**
   * هل الجدول في حالة تحميل
   */
  isLoading?: boolean;
  
  /**
   * عنوان الجدول
   */
  title?: string;
  
  /**
   * رسالة عند عدم وجود بيانات
   */
  emptyMessage?: string;
  
  /**
   * هل يعرض أزرار الإجراءات
   */
  showActions?: boolean;
}

/**
 * مكون الجدول الذكي
 */
export function SmartTable<T extends { id: string }>({
  columns,
  data,
  totalCount,
  page,
  rowsPerPage,
  onPageChange,
  onRowsPerPageChange,
  onEdit,
  onDelete,
  onView,
  isLoading = false,
  title,
  emptyMessage = 'لا توجد بيانات',
  showActions = true,
}: SmartTableProps<T>) {
  const handleChangePage = (_: unknown, newPage: number) => {
    onPageChange(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    onRowsPerPageChange(parseInt(event.target.value, 10));
    onPageChange(0);
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      {title && (
        <Box sx={{ p: 2, borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
          <Typography variant="h6" component="div">
            {title}
          </Typography>
        </Box>
      )}
      
      <TableContainer sx={{ maxHeight: 440 }}>
        <Table stickyHeader aria-label="sticky table">
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || 'right'}
                  style={{ width: column.width }}
                >
                  {column.label}
                </TableCell>
              ))}
              {showActions && (onEdit || onDelete || onView) && (
                <TableCell align="center" style={{ width: 150 }}>
                  الإجراءات
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={columns.length + (showActions ? 1 : 0)} align="center">
                  <CircularProgress size={40} />
                </TableCell>
              </TableRow>
            ) : data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length + (showActions ? 1 : 0)} align="center">
                  {emptyMessage}
                </TableCell>
              </TableRow>
            ) : (
              data.map((row) => (
                <TableRow hover role="checkbox" tabIndex={-1} key={row.id}>
                  {columns.map((column) => (
                    <TableCell key={column.id} align={column.align || 'right'}>
                      {column.render ? column.render(row) : (row as any)[column.id]}
                    </TableCell>
                  ))}
                  {showActions && (onEdit || onDelete || onView) && (
                    <TableCell align="center">
                      {onView && (
                        <Tooltip title="عرض">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => onView(row)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      {onEdit && (
                        <Tooltip title="تعديل">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => onEdit(row)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      {onDelete && (
                        <Tooltip title="حذف">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => onDelete(row)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={totalCount}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="عدد الصفوف في الصفحة:"
        labelDisplayedRows={({ from, to, count }) => `${from}-${to} من ${count}`}
      />
    </Paper>
  );
}
