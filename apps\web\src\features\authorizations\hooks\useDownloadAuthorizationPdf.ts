import { useMutation } from '@tanstack/react-query';
import { api } from '@/lib/api/api';

export const useDownloadAuthorizationPdf = () => {
  return useMutation({
    mutationFn: async (id: string): Promise<Blob> => {
      const response = await api.downloadFile(`/api/authorizations/${id}/pdf`);
      return response;
    },
    onSuccess: (blob, id) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `authorization-${id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
  });
};
