import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  Typography,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  PictureAsPdf as PdfIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import {
  useGuarantee,
  useDeleteGuarantee,
  useDownloadGuaranteePdf,
  useUpdateGuaranteeReturnStatus
} from '../hooks/useGuarantees';
import { GuaranteeType, GuaranteeNature } from '../types/guarantee.types';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

const GuaranteeDetailsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // حالة الحوار
  const [returnDialogOpen, setReturnDialogOpen] = useState(false);
  const [returnDate, setReturnDate] = useState<Date | null>(new Date());

  // استخدام خطافات الضمانات
  const { data: guarantee, isLoading, isError } = useGuarantee(id || '');
  const deleteMutation = useDeleteGuarantee();
  const downloadPdfMutation = useDownloadGuaranteePdf();
  const updateReturnStatusMutation = useUpdateGuaranteeReturnStatus();

  // التعامل مع تعديل الضمان
  const handleEdit = () => {
    navigate(`/guarantees/${id}/edit`);
  };

  // التعامل مع حذف الضمان
  const handleDelete = async () => {
    if (window.confirm(t('guarantees.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id || '');
        navigate('/guarantees');
      } catch (error) {
        console.error('Error deleting guarantee:', error);
      }
    }
  };

  // التعامل مع العودة إلى قائمة الضمانات
  const handleBack = () => {
    navigate('/guarantees');
  };

  // التعامل مع تحميل ملف PDF
  const handleDownloadPdf = async () => {
    try {
      const blob = await downloadPdfMutation.mutateAsync(id || '');
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `guarantee-${id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF:', error);
    }
  };

  // التعامل مع فتح حوار تحديث حالة الاسترجاع
  const handleOpenReturnDialog = () => {
    setReturnDialogOpen(true);
  };

  // التعامل مع إغلاق حوار تحديث حالة الاسترجاع
  const handleCloseReturnDialog = () => {
    setReturnDialogOpen(false);
  };

  // التعامل مع تحديث حالة الاسترجاع
  const handleUpdateReturnStatus = async () => {
    try {
      await updateReturnStatusMutation.mutateAsync({
        id: id || '',
        isReturned: true,
        returnDate: returnDate?.toISOString(),
      });
      setReturnDialogOpen(false);
    } catch (error) {
      console.error('Error updating return status:', error);
    }
  };

  // عرض رسالة التحميل
  if (isLoading) {
    return (
      <Container maxWidth="lg">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // عرض رسالة الخطأ
  if (isError || !guarantee) {
    return (
      <Container maxWidth="lg">
        <Box textAlign="center" py={4}>
          <Typography variant="h6" color="error">
            {t('common.errorOccurred')}
          </Typography>
          <Button variant="contained" onClick={handleBack} sx={{ mt: 2 }}>
            {t('common.back')}
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('guarantees.details')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('guarantees.detailsDescription')}
        </Typography>
      </Box>

      <Box mb={3} display="flex" justifyContent="flex-end">
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{ mr: 1 }}
        >
          {t('common.back')}
        </Button>
        <Button
          variant="outlined"
          startIcon={<EditIcon />}
          onClick={handleEdit}
          sx={{ mr: 1 }}
        >
          {t('common.edit')}
        </Button>
        <Button
          variant="outlined"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={handleDelete}
        >
          {t('common.delete')}
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h5">
                {t('guarantees.number')}: {guarantee.data.guaranteeNumber}
              </Typography>
              <Chip
                label={
                  guarantee.data.guaranteeType === GuaranteeType.RETURNABLE
                    ? t('guarantees.returnable')
                    : t('guarantees.nonReturnable')
                }
                color={guarantee.data.guaranteeType === GuaranteeType.RETURNABLE ? 'primary' : 'secondary'}
              />
            </Box>

            <Divider sx={{ my: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('guarantees.guaranteeNature')}
                </Typography>
                <Typography variant="body1">
                  {t(`guarantees.${guarantee.data.guaranteeNature.toLowerCase()}`)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('guarantees.amount')}
                </Typography>
                <Typography variant="body1">
                  {guarantee.data.amount} {guarantee.data.currency}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('guarantees.issueDate')}
                </Typography>
                <Typography variant="body1">
                  {format(new Date(guarantee.data.issueDate), 'PPP', { locale: arSA })}
                </Typography>
              </Grid>

              {guarantee.data.expiryDate && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('guarantees.expiryDate')}
                  </Typography>
                  <Typography variant="body1">
                    {format(new Date(guarantee.data.expiryDate), 'PPP', { locale: arSA })}
                  </Typography>
                </Grid>
              )}

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('guarantees.returnStatus')}
                </Typography>
                <Box display="flex" alignItems="center">
                  {guarantee.data.isReturned ? (
                    <Chip
                      icon={<CheckCircleIcon />}
                      label={t('guarantees.returned')}
                      color="success"
                      size="small"
                    />
                  ) : (
                    <Chip
                      icon={<CancelIcon />}
                      label={t('guarantees.notReturned')}
                      color="error"
                      size="small"
                    />
                  )}
                </Box>
              </Grid>

              {guarantee.data.isReturned && guarantee.data.returnDate && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('guarantees.returnDate')}
                  </Typography>
                  <Typography variant="body1">
                    {format(new Date(guarantee.data.returnDate), 'PPP', { locale: arSA })}
                  </Typography>
                </Grid>
              )}

              {guarantee.data.declarationNumber && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('declarations.number')}
                  </Typography>
                  <Typography variant="body1">
                    {guarantee.data.declarationNumber}
                  </Typography>
                </Grid>
              )}

              {guarantee.data.clientName && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('clients.name')}
                  </Typography>
                  <Typography variant="body1">
                    {guarantee.data.clientName}
                  </Typography>
                </Grid>
              )}

              {guarantee.data.notes && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('guarantees.notes')}
                  </Typography>
                  <Typography variant="body1">
                    {guarantee.data.notes}
                  </Typography>
                </Grid>
              )}
            </Grid>

            <Divider sx={{ my: 2 }} />

            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Box>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('common.createdAt')}
                </Typography>
                <Typography variant="body2">
                  {format(new Date(guarantee.data.createdAt), 'PPP', { locale: arSA })}
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('common.updatedAt')}
                </Typography>
                <Typography variant="body2">
                  {format(new Date(guarantee.data.updatedAt), 'PPP', { locale: arSA })}
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('common.actions')}
              </Typography>

              {guarantee.data.pdfFile && (
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<PdfIcon />}
                  onClick={handleDownloadPdf}
                  sx={{ mb: 2 }}
                >
                  {t('common.downloadPdf')}
                </Button>
              )}

              {guarantee.data.guaranteeType === GuaranteeType.RETURNABLE && !guarantee.data.isReturned && (
                <Button
                  fullWidth
                  variant="contained"
                  color="success"
                  startIcon={<CheckCircleIcon />}
                  onClick={handleOpenReturnDialog}
                >
                  {t('guarantees.markAsReturned')}
                </Button>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* حوار تحديث حالة الاسترجاع */}
      <Dialog open={returnDialogOpen} onClose={handleCloseReturnDialog}>
        <DialogTitle>{t('guarantees.markAsReturned')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('guarantees.markAsReturnedDescription')}
          </DialogContentText>
          <Box mt={2}>
            <DatePicker
              label={t('guarantees.returnDate')}
              value={returnDate}
              onChange={(date) => {
                if (date && 'toDate' in date && typeof date.toDate === 'function') {
                  setReturnDate(date.toDate());
                } else {
                  setReturnDate(date as Date | null);
                }
              }}
              slotProps={{
                textField: {
                  fullWidth: true,
                },
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseReturnDialog}>{t('common.cancel')}</Button>
          <Button onClick={handleUpdateReturnStatus} color="primary">
            {t('common.confirm')}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default GuaranteeDetailsPage;
