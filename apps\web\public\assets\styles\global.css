/* تعريف الخطوط */
@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('/assets/fonts/Amiri-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('/assets/fonts/Amiri-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cairo';
  src: url('/assets/fonts/Cairo-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cairo';
  src: url('/assets/fonts/Cairo-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('/assets/fonts/Tajawal-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('/assets/fonts/Tajawal-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

/* إعدادات عامة */
:root {
  --primary-color: #1976d2;
  --secondary-color: #dc004e;
  --background-color: #f5f5f5;
  --text-color: #333333;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --font-family-primary: 'Tajawal', 'Arial', sans-serif;
  --font-family-secondary: 'Cairo', 'Arial', sans-serif;
  --font-family-text: 'Amiri', 'Times New Roman', serif;
}

/* إعادة تعيين */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  direction: rtl;
}

body {
  font-family: var(--font-family-primary);
  color: var(--text-color);
  background-color: var(--background-color);
  line-height: 1.5;
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-secondary);
  font-weight: bold;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.75rem;
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
}

/* النصوص */
p {
  margin-bottom: 1rem;
}

/* الروابط */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--secondary-color);
}

/* الأزرار */
button {
  font-family: var(--font-family-primary);
  cursor: pointer;
}

/* النماذج */
input, select, textarea {
  font-family: var(--font-family-primary);
}

/* الجداول */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

th, td {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
}

th {
  background-color: var(--primary-color);
  color: white;
  font-weight: bold;
  text-align: right;
}

/* الحاويات */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* الصفوف والأعمدة */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -1rem;
}

.col {
  flex: 1;
  padding: 0 1rem;
}

/* البطاقات */
.card {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card-header {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1rem;
}

.card-body {
  margin-bottom: 1rem;
}

.card-footer {
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

/* الألوان */
.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--secondary-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.bg-primary {
  background-color: var(--primary-color);
  color: white;
}

.bg-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.bg-success {
  background-color: var(--success-color);
  color: white;
}

.bg-warning {
  background-color: var(--warning-color);
  color: white;
}

.bg-error {
  background-color: var(--error-color);
  color: white;
}

/* المسافات */
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 1rem; }
.mr-4 { margin-right: 1.5rem; }
.mr-5 { margin-right: 3rem; }

.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 1rem; }
.ml-4 { margin-left: 1.5rem; }
.ml-5 { margin-left: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* الاستجابة */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }

  .row {
    flex-direction: column;
  }

  .col {
    width: 100%;
    margin-bottom: 1rem;
  }

  /* تحسينات للجداول على الأجهزة المحمولة */
  table, thead, tbody, th, td, tr {
    display: block;
  }

  thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  tr {
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
  }

  td {
    border: none;
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding-right: 50%;
    text-align: right;
  }

  td:before {
    position: absolute;
    right: 6px;
    width: 45%;
    padding-right: 10px;
    white-space: nowrap;
    font-weight: bold;
  }

  /* تحسينات للنماذج على الأجهزة المحمولة */
  input, select, textarea {
    width: 100%;
    margin-bottom: 1rem;
  }

  /* تحسينات للأزرار على الأجهزة المحمولة */
  button {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  /* تحسينات للبطاقات على الأجهزة المحمولة */
  .card {
    padding: 1rem;
  }
}

/* طباعة */
@media print {
  body {
    background-color: white;
  }

  .no-print {
    display: none;
  }
}
