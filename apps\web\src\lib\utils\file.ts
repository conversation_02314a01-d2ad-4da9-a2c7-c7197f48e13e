/**
 * تنسيق حجم الملف بالوحدة المناسبة (بايت، كيلوبايت، ميجابايت، جيجابايت)
 * @param bytes حجم الملف بالبايت
 * @returns حجم الملف المنسق مع الوحدة المناسبة
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 بايت';

  const k = 1024;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
