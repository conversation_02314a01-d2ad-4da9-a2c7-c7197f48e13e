import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { createStore } from '../../../app/store/store';
import PermitsPage from '../pages/PermitsPage';
import { server } from '../../../test/integration-setup';
import { http, HttpResponse } from 'msw';
import { PermitType } from '../types/permit.types';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { arSA } from 'date-fns/locale';

// Mock React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

// Mock Redux Store
const store = createStore();

// Mock React Router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
  };
});

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: vi.fn(),
    },
  }),
}));

// Wrapper Component
const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={arSA}>
        <BrowserRouter>{children}</BrowserRouter>
      </LocalizationProvider>
    </QueryClientProvider>
  </Provider>
);

describe('Permits Integration Tests', () => {
  beforeEach(() => {
    // إعداد معالجات وهمية للطلبات
    server.use(
      http.get('http://localhost:3001/api/permits', ({ request }) => {
        return HttpResponse.json({
          success: true,
          data: [
            {
              id: '123',
              permitNumber: 1001,
              permitType: PermitType.ENTRY,
              issueDate: new Date().toISOString(),
              expiryDate: new Date().toISOString(),
              notes: 'Test notes',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              createdBy: { name: 'Test User' },
            },
            {
              id: '456',
              permitNumber: 1002,
              permitType: PermitType.EXIT,
              issueDate: new Date().toISOString(),
              expiryDate: new Date().toISOString(),
              notes: 'Another test notes',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              createdBy: { name: 'Test User' },
            },
          ],
          pagination: {
            total: 2,
            page: 1,
            limit: 10,
            totalPages: 1,
          },
        }, { status: 200 });
      }),

      http.delete('http://localhost:3001/api/permits/:id', () => {
        return HttpResponse.json({
          success: true,
          message: 'تم حذف التصريح بنجاح',
        }, { status: 200 });
      })
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should load and display permits', async () => {
    render(
      <Wrapper>
        <PermitsPage />
      </Wrapper>
    );

    // التحقق من عرض عنوان الصفحة
    expect(screen.getByText('permits.title')).toBeInTheDocument();

    // انتظار تحميل البيانات
    await waitFor(() => {
      expect(screen.getByText('1001')).toBeInTheDocument();
      expect(screen.getByText('1002')).toBeInTheDocument();
    });
  });

  it('should filter permits by type', async () => {
    // إعداد معالج وهمي للتصفية
    server.use(
      http.get('http://localhost:3001/api/permits', ({ request }) => {
        const url = new URL(request.url);
        const permitType = url.searchParams.get('permitType');

        if (permitType === 'ENTRY') {
          return HttpResponse.json({
            success: true,
            data: [
              {
                id: '123',
                permitNumber: 1001,
                permitType: PermitType.ENTRY,
                issueDate: new Date().toISOString(),
                expiryDate: new Date().toISOString(),
                notes: 'Test notes',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                createdBy: { name: 'Test User' },
              },
            ],
            pagination: {
              total: 1,
              page: 1,
              limit: 10,
              totalPages: 1,
            },
          }, { status: 200 });
        }

        return HttpResponse.json({
          success: true,
          data: [
            {
              id: '123',
              permitNumber: 1001,
              permitType: PermitType.ENTRY,
              issueDate: new Date().toISOString(),
              expiryDate: new Date().toISOString(),
              notes: 'Test notes',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              createdBy: { name: 'Test User' },
            },
            {
              id: '456',
              permitNumber: 1002,
              permitType: PermitType.EXIT,
              issueDate: new Date().toISOString(),
              expiryDate: new Date().toISOString(),
              notes: 'Another test notes',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              createdBy: { name: 'Test User' },
            },
          ],
          pagination: {
            total: 2,
            page: 1,
            limit: 10,
            totalPages: 1,
          },
        }, { status: 200 });
      })
    );

    render(
      <Wrapper>
        <PermitsPage />
      </Wrapper>
    );

    // فتح مرشحات البحث
    const filterButton = screen.getByText('common.filters');
    fireEvent.click(filterButton);

    // انتظار ظهور مرشحات البحث
    await waitFor(() => {
      expect(screen.getByText('permits.type')).toBeInTheDocument();
    });

    // اختيار نوع التصريح
    const typeSelect = screen.getByLabelText('permits.type');
    fireEvent.mouseDown(typeSelect);

    // انتظار ظهور قائمة الخيارات
    await waitFor(() => {
      const entryOption = screen.getByText('permits.types.ENTRY');
      fireEvent.click(entryOption);
    });

    // انتظار تحديث البيانات
    await waitFor(() => {
      expect(screen.getByText('1001')).toBeInTheDocument();
      expect(screen.queryByText('1002')).not.toBeInTheDocument();
    });
  });

  it('should handle permit deletion', async () => {
    // Mock window.confirm
    const confirmSpy = vi.spyOn(window, 'confirm');
    confirmSpy.mockImplementation(() => true);

    render(
      <Wrapper>
        <PermitsPage />
      </Wrapper>
    );

    // انتظار تحميل البيانات
    await waitFor(() => {
      expect(screen.getByText('1001')).toBeInTheDocument();
    });

    // العثور على زر الحذف والنقر عليه
    const deleteButtons = screen.getAllByTitle('common.delete');
    fireEvent.click(deleteButtons[0]);

    // التحقق من ظهور رسالة التأكيد
    expect(confirmSpy).toHaveBeenCalledWith('permits.confirmDelete');

    // انتظار اكتمال عملية الحذف
    await waitFor(() => {
      // يمكن التحقق من استدعاء واجهة برمجة التطبيقات أو تحديث البيانات
      expect(true).toBeTruthy();
    });

    // تنظيف
    confirmSpy.mockRestore();
  });
});
