import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  Typography,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationOnIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { useClient, useDeleteClient } from '../hooks/useClients';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const ClientDetailsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  
  // استخدام خطافات العميل
  const { data: client, isLoading, isError } = useClient(id || '');
  const deleteMutation = useDeleteClient();
  
  // التعامل مع تعديل العميل
  const handleEdit = () => {
    navigate(`/clients/${id}/edit`);
  };
  
  // التعامل مع حذف العميل
  const handleDelete = async () => {
    if (window.confirm(t('clients.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id || '');
        navigate('/clients');
      } catch (error) {
        console.error('Error deleting client:', error);
      }
    }
  };
  
  // التعامل مع العودة إلى قائمة العملاء
  const handleBack = () => {
    navigate('/clients');
  };
  
  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (isError || !client) {
    return (
      <Container maxWidth="md">
        <Box textAlign="center" my={4}>
          <Typography variant="h5" color="error" gutterBottom>
            {t('common.errorLoading')}
          </Typography>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            {t('common.backToList')}
          </Button>
        </Box>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('clients.details')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('clients.detailsDescription')}
        </Typography>
      </Box>
      
      <Box mb={3} display="flex" justifyContent="flex-end">
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{ mr: 1 }}
        >
          {t('common.back')}
        </Button>
        <Button
          variant="outlined"
          startIcon={<EditIcon />}
          onClick={handleEdit}
          sx={{ mr: 1 }}
        >
          {t('common.edit')}
        </Button>
        <Button
          variant="outlined"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={handleDelete}
        >
          {t('common.delete')}
        </Button>
      </Box>
      
      <Paper>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('clients.clientNumber')}
              </Typography>
              <Typography variant="body1">
                {client.clientNumber || '-'}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('clients.taxNumber')}
              </Typography>
              <Typography variant="body1">
                {client.taxNumber}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('clients.name')}
              </Typography>
              <Typography variant="body1">
                {client.name}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('clients.companyName')}
              </Typography>
              <Typography variant="body1">
                {client.companyName || '-'}
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box display="flex" alignItems="center">
                <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle2" color="textSecondary">
                  {t('clients.phone')}
                </Typography>
              </Box>
              <Typography variant="body1">
                {client.phone || '-'}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box display="flex" alignItems="center">
                <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle2" color="textSecondary">
                  {t('clients.email')}
                </Typography>
              </Box>
              <Typography variant="body1">
                {client.email || '-'}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box display="flex" alignItems="center">
                <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle2" color="textSecondary">
                  {t('clients.contactPerson')}
                </Typography>
              </Box>
              <Typography variant="body1">
                {client.contactPerson || '-'}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Box display="flex" alignItems="center">
                <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle2" color="textSecondary">
                  {t('clients.contactPhone')}
                </Typography>
              </Box>
              <Typography variant="body1">
                {client.contactPhone || '-'}
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Box display="flex" alignItems="center">
                <LocationOnIcon sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle2" color="textSecondary">
                  {t('clients.address')}
                </Typography>
              </Box>
              <Typography variant="body1">
                {client.address || '-'}
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" color="textSecondary">
                {t('clients.notes')}
              </Typography>
              <Typography variant="body1">
                {client.notes || '-'}
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" color="textSecondary">
                {t('common.createdBy')}
              </Typography>
              <Typography variant="body1">
                {client.createdBy?.name || '-'}
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('common.createdAt')}
              </Typography>
              <Typography variant="body1">
                {format(new Date(client.createdAt), 'yyyy-MM-dd HH:mm', { locale: arSA })}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Paper>
    </Container>
  );
};

export default ClientDetailsPage;
