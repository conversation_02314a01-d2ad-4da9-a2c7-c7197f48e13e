import { useState, useEffect } from 'react';
import { Box, Typography, Button, CircularProgress, Paper, Divider, List, ListItem, ListItemText } from '@mui/material';
import apiService from '@lib/services/api.service';
import { useApi } from '@lib/hooks/useApi';

/**
 * مكون اختبار الاتصال بالواجهة الخلفية
 * يستخدم للتحقق من صحة الاتصال بين الواجهة الأمامية والخلفية
 */
const ApiTest = () => {
  const [status, setStatus] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [endpoints, setEndpoints] = useState<string[]>([]);
  const api = useApi();

  // دالة للتحقق من حالة الخادم
  const checkServerStatus = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiService.get('/health');
      setStatus(response.status);

      // جمع قائمة بنقاط النهاية المتاحة
      setEndpoints([
        '/api/auth/login',
        '/api/declarations',
        '/api/item-movements',
        '/api/authorizations',
        '/api/releases',
        '/api/permits',
        '/api/guarantees',
        '/api/receipts',
        '/api/clients',
        '/api/documents',
        '/api/advanced-search',
        '/api/reports',
      ]);
    } catch (err) {
      console.error('Error checking server status:', err);
      setError('فشل الاتصال بالخادم. يرجى التحقق من تشغيل الخادم الخلفي.');
      setEndpoints([]);
    } finally {
      setLoading(false);
    }
  };

  // التحقق من حالة الخادم عند تحميل المكون
  useEffect(() => {
    checkServerStatus();
  }, []);

  return (
    <Paper elevation={3} sx={{ p: 3, maxWidth: 800, mx: 'auto', mt: 4 }}>
      <Typography variant="h5" gutterBottom align="center">
        اختبار الاتصال بالواجهة الخلفية
      </Typography>

      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', my: 3 }}>
        {loading ? (
          <CircularProgress size={24} />
        ) : status ? (
          <Typography color="success.main" sx={{ mb: 2 }}>
            حالة الخادم: {status === 'ok' ? 'متصل' : status}
          </Typography>
        ) : error ? (
          <Typography color="error" sx={{ mb: 2 }}>
            {error}
          </Typography>
        ) : null}

        <Button
          variant="contained"
          onClick={checkServerStatus}
          disabled={loading}
        >
          التحقق من الاتصال
        </Button>
      </Box>

      <Divider sx={{ my: 2 }} />

      {endpoints.length > 0 && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            نقاط النهاية المتاحة:
          </Typography>
          <List dense>
            {endpoints.map((endpoint) => (
              <ListItem key={endpoint}>
                <ListItemText primary={endpoint} />
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      <Divider sx={{ my: 2 }} />

      <Box sx={{ mt: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          معلومات التكوين:
        </Typography>
        <Typography variant="body2">
          عنوان API: {apiService.getBaseURL()}
        </Typography>
        <Typography variant="body2">
          وضع التشغيل: {import.meta.env.MODE}
        </Typography>
      </Box>
    </Paper>
  );
};

export default ApiTest;
