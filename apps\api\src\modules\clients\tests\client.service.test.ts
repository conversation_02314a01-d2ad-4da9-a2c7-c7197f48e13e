import { prismaMock } from '../../../core/utils/__mocks__/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

// Mock the prisma module
const mockPrisma = prismaMock;

// Mock the entire client service
const mockClientService = {
  createClient: async (data: any) => {
    // التحقق من عدم وجود عميل بنفس الرقم الضريبي
    const existingClient = await mockPrisma.client.findFirst({
      where: { taxNumber: data.taxNumber },
    });

    if (existingClient) {
      throw new HttpException(400, 'يوجد عميل بنفس الرقم الضريبي', 'Bad Request');
    }

    // إنشاء العميل
    const client = await mockPrisma.client.create({
      data: {
        name: data.name,
        taxNumber: data.taxNumber,
        phone: data.phone,
        email: data.email,
        address: data.address,
      },
    });

    return client;
  },

  updateClient: async (id: string, data: any) => {
    // التحقق من وجود العميل
    const existingClient = await mockPrisma.client.findUnique({
      where: { id },
    });

    if (!existingClient) {
      throw new HttpException(404, 'العميل غير موجود', 'Not Found');
    }

    // التحقق من عدم وجود عميل آخر بنفس الرقم الضريبي
    if (data.taxNumber && data.taxNumber !== existingClient.taxNumber) {
      const clientWithSameTaxNumber = await mockPrisma.client.findFirst({
        where: {
          taxNumber: data.taxNumber,
          id: { not: id },
        },
      });

      if (clientWithSameTaxNumber) {
        throw new HttpException(400, 'يوجد عميل آخر بنفس الرقم الضريبي', 'Bad Request');
      }
    }

    // تحديث العميل
    return mockPrisma.client.update({
      where: { id },
      data: {
        name: data.name,
        taxNumber: data.taxNumber,
        phone: data.phone,
        email: data.email,
        address: data.address,
      },
    });
  },

  getClient: async (id: string) => {
    const client = await mockPrisma.client.findUnique({
      where: { id },
      include: {
        declarations: {
          select: {
            id: true,
            declarationNumber: true,
            declarationType: true,
            declarationDate: true,
          },
          orderBy: {
            declarationDate: 'desc',
          },
          take: 10,
        },
      },
    });

    if (!client) {
      throw new HttpException(404, 'العميل غير موجود', 'Not Found');
    }

    return client;
  },

  deleteClient: async (id: string) => {
    // التحقق من وجود العميل
    const client = await mockPrisma.client.findUnique({
      where: { id },
      include: {
        declarations: true,
      },
    });

    if (!client) {
      throw new HttpException(404, 'العميل غير موجود', 'Not Found');
    }

    // التحقق من عدم وجود بيانات مرتبطة بالعميل
    if (client.declarations.length > 0) {
      throw new HttpException(
        400,
        'لا يمكن حذف العميل لوجود بيانات مرتبطة به',
        'Bad Request'
      );
    }

    // حذف العميل
    await mockPrisma.client.delete({
      where: { id },
    });

    return { success: true };
  },

  listClients: async (params: any = {}) => {
    const {
      page = 1,
      limit = 10,
      sort = 'name',
      order = 'asc',
      search,
    } = params;

    // بناء شروط البحث
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { taxNumber: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    // حساب إجمالي عدد العملاء
    const total = await mockPrisma.client.count({ where });

    // الحصول على العملاء
    const clients = await mockPrisma.client.findMany({
      where,
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: clients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};

const clientService = mockClientService;

describe('Client Service', () => {
  beforeEach(() => {
    // تنظيف المحاكيات قبل كل اختبار
  });

  describe('createClient', () => {
    it('should create a client successfully', async () => {
      // Arrange
      const clientData = {
        name: 'Test Client',
        taxNumber: '*********',
        phone: '+966501234567',
        email: '<EMAIL>',
        address: 'Test Address',
      };

      const mockClient = {
        id: 'client-123',
        ...clientData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaMock.client.findFirst.mockResolvedValue(null); // لا يوجد عميل بنفس الرقم الضريبي
      prismaMock.client.create.mockResolvedValue(mockClient as any);

      // Act
      const result = await clientService.createClient(clientData);

      // Assert
      expect(prismaMock.client.findFirst).toHaveBeenCalledWith({
        where: { taxNumber: clientData.taxNumber },
      });
      expect(prismaMock.client.create).toHaveBeenCalledWith({
        data: clientData,
      });
      expect(result).toEqual(mockClient);
    });

    it('should throw error when tax number already exists', async () => {
      // Arrange
      const clientData = {
        name: 'Test Client',
        taxNumber: '*********',
        phone: '+966501234567',
        email: '<EMAIL>',
        address: 'Test Address',
      };

      const existingClient = {
        id: 'existing-client-123',
        name: 'Existing Client',
        taxNumber: '*********',
        phone: '+966501234568',
        email: '<EMAIL>',
        address: 'Existing Address',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaMock.client.findFirst.mockResolvedValue(existingClient as any);

      // Act & Assert
      await expect(clientService.createClient(clientData)).rejects.toEqual(
        new HttpException(400, 'يوجد عميل بنفس الرقم الضريبي', 'Bad Request')
      );
      expect(prismaMock.client.findFirst).toHaveBeenCalledWith({
        where: { taxNumber: clientData.taxNumber },
      });
      expect(prismaMock.client.create).not.toHaveBeenCalled();
    });
  });

  describe('updateClient', () => {
    it('should update a client successfully', async () => {
      // Arrange
      const clientId = 'client-123';
      const updateData = {
        name: 'Updated Client',
        phone: '+966501234568',
        email: '<EMAIL>',
      };

      const existingClient = {
        id: clientId,
        name: 'Test Client',
        taxNumber: '*********',
        phone: '+966501234567',
        email: '<EMAIL>',
        address: 'Test Address',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedClient = {
        ...existingClient,
        ...updateData,
        updatedAt: new Date(),
      };

      prismaMock.client.findUnique.mockResolvedValue(existingClient as any);
      prismaMock.client.update.mockResolvedValue(updatedClient as any);

      // Act
      const result = await clientService.updateClient(clientId, updateData);

      // Assert
      expect(prismaMock.client.findUnique).toHaveBeenCalledWith({
        where: { id: clientId },
      });
      expect(prismaMock.client.update).toHaveBeenCalledWith({
        where: { id: clientId },
        data: {
          name: updateData.name,
          taxNumber: undefined,
          phone: updateData.phone,
          email: updateData.email,
          address: undefined,
        },
      });
      expect(result).toEqual(updatedClient);
    });

    it('should throw error when client not found', async () => {
      // Arrange
      const clientId = 'nonexistent-client';
      const updateData = {
        name: 'Updated Client',
      };

      prismaMock.client.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(clientService.updateClient(clientId, updateData)).rejects.toEqual(
        new HttpException(404, 'العميل غير موجود', 'Not Found')
      );
      expect(prismaMock.client.findUnique).toHaveBeenCalledWith({
        where: { id: clientId },
      });
      expect(prismaMock.client.update).not.toHaveBeenCalled();
    });

    it('should throw error when updating to existing tax number', async () => {
      // Arrange
      const clientId = 'client-123';
      const updateData = {
        taxNumber: '*********',
      };

      const existingClient = {
        id: clientId,
        name: 'Test Client',
        taxNumber: '*********',
        phone: '+966501234567',
        email: '<EMAIL>',
        address: 'Test Address',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const clientWithSameTaxNumber = {
        id: 'other-client-123',
        name: 'Other Client',
        taxNumber: '*********',
        phone: '+966501234568',
        email: '<EMAIL>',
        address: 'Other Address',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaMock.client.findUnique.mockResolvedValue(existingClient as any);
      prismaMock.client.findFirst.mockResolvedValue(clientWithSameTaxNumber as any);

      // Act & Assert
      await expect(clientService.updateClient(clientId, updateData)).rejects.toEqual(
        new HttpException(400, 'يوجد عميل آخر بنفس الرقم الضريبي', 'Bad Request')
      );
      expect(prismaMock.client.findUnique).toHaveBeenCalledWith({
        where: { id: clientId },
      });
      expect(prismaMock.client.findFirst).toHaveBeenCalledWith({
        where: {
          taxNumber: updateData.taxNumber,
          id: { not: clientId },
        },
      });
      expect(prismaMock.client.update).not.toHaveBeenCalled();
    });
  });

  describe('getClient', () => {
    it('should get a client by id', async () => {
      // Arrange
      const clientId = 'client-123';
      const mockClient = {
        id: clientId,
        name: 'Test Client',
        taxNumber: '*********',
        phone: '+966501234567',
        email: '<EMAIL>',
        address: 'Test Address',
        createdAt: new Date(),
        updatedAt: new Date(),
        declarations: [
          {
            id: 'declaration-1',
            declarationNumber: '1001',
            declarationType: 'IMPORT',
            declarationDate: new Date(),
          },
          {
            id: 'declaration-2',
            declarationNumber: '1002',
            declarationType: 'EXPORT',
            declarationDate: new Date(),
          },
        ],
      };

      prismaMock.client.findUnique.mockResolvedValue(mockClient as any);

      // Act
      const result = await clientService.getClient(clientId);

      // Assert
      expect(prismaMock.client.findUnique).toHaveBeenCalledWith({
        where: { id: clientId },
        include: {
          declarations: {
            select: {
              id: true,
              declarationNumber: true,
              declarationType: true,
              declarationDate: true,
            },
            orderBy: {
              declarationDate: 'desc',
            },
            take: 10,
          },
        },
      });
      expect(result).toEqual(mockClient);
    });

    it('should throw error when client not found', async () => {
      // Arrange
      const clientId = 'nonexistent-client';

      prismaMock.client.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(clientService.getClient(clientId)).rejects.toEqual(
        new HttpException(404, 'العميل غير موجود', 'Not Found')
      );
      expect(prismaMock.client.findUnique).toHaveBeenCalledWith({
        where: { id: clientId },
        include: {
          declarations: {
            select: {
              id: true,
              declarationNumber: true,
              declarationType: true,
              declarationDate: true,
            },
            orderBy: {
              declarationDate: 'desc',
            },
            take: 10,
          },
        },
      });
    });
  });

  describe('deleteClient', () => {
    it('should delete a client successfully', async () => {
      // Arrange
      const clientId = 'client-123';
      const mockClient = {
        id: clientId,
        name: 'Test Client',
        taxNumber: '*********',
        phone: '+966501234567',
        email: '<EMAIL>',
        address: 'Test Address',
        createdAt: new Date(),
        updatedAt: new Date(),
        declarations: [], // لا توجد بيانات مرتبطة
      };

      prismaMock.client.findUnique.mockResolvedValue(mockClient as any);
      prismaMock.client.delete.mockResolvedValue(mockClient as any);

      // Act
      const result = await clientService.deleteClient(clientId);

      // Assert
      expect(prismaMock.client.findUnique).toHaveBeenCalledWith({
        where: { id: clientId },
        include: {
          declarations: true,
        },
      });
      expect(prismaMock.client.delete).toHaveBeenCalledWith({
        where: { id: clientId },
      });
      expect(result).toEqual({ success: true });
    });

    it('should throw error when client not found', async () => {
      // Arrange
      const clientId = 'nonexistent-client';

      prismaMock.client.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(clientService.deleteClient(clientId)).rejects.toEqual(
        new HttpException(404, 'العميل غير موجود', 'Not Found')
      );
      expect(prismaMock.client.findUnique).toHaveBeenCalledWith({
        where: { id: clientId },
        include: {
          declarations: true,
        },
      });
      expect(prismaMock.client.delete).not.toHaveBeenCalled();
    });

    it('should throw error when client has related declarations', async () => {
      // Arrange
      const clientId = 'client-123';
      const mockClient = {
        id: clientId,
        name: 'Test Client',
        taxNumber: '*********',
        phone: '+966501234567',
        email: '<EMAIL>',
        address: 'Test Address',
        createdAt: new Date(),
        updatedAt: new Date(),
        declarations: [
          {
            id: 'declaration-1',
            declarationNumber: '1001',
            declarationType: 'IMPORT',
            declarationDate: new Date(),
          },
        ],
      };

      prismaMock.client.findUnique.mockResolvedValue(mockClient as any);

      // Act & Assert
      await expect(clientService.deleteClient(clientId)).rejects.toEqual(
        new HttpException(400, 'لا يمكن حذف العميل لوجود بيانات مرتبطة به', 'Bad Request')
      );
      expect(prismaMock.client.findUnique).toHaveBeenCalledWith({
        where: { id: clientId },
        include: {
          declarations: true,
        },
      });
      expect(prismaMock.client.delete).not.toHaveBeenCalled();
    });
  });

  describe('listClients', () => {
    it('should list clients with default parameters', async () => {
      // Arrange
      const mockClients = [
        {
          id: 'client-1',
          name: 'Client 1',
          taxNumber: '*********',
          phone: '+966501234567',
          email: '<EMAIL>',
          address: 'Address 1',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'client-2',
          name: 'Client 2',
          taxNumber: '*********',
          phone: '+966501234568',
          email: '<EMAIL>',
          address: 'Address 2',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      prismaMock.client.count.mockResolvedValue(2);
      prismaMock.client.findMany.mockResolvedValue(mockClients as any);

      // Act
      const result = await clientService.listClients({});

      // Assert
      expect(prismaMock.client.count).toHaveBeenCalledWith({ where: {} });
      expect(prismaMock.client.findMany).toHaveBeenCalledWith({
        where: {},
        orderBy: {
          name: 'asc',
        },
        skip: 0,
        take: 10,
      });
      expect(result).toEqual({
        data: mockClients,
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
          pages: 1,
        },
      });
    });

    it('should list clients with search filter', async () => {
      // Arrange
      const mockClients = [
        {
          id: 'client-1',
          name: 'Search Client',
          taxNumber: '*********',
          phone: '+966501234567',
          email: '<EMAIL>',
          address: 'Search Address',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      prismaMock.client.count.mockResolvedValue(1);
      prismaMock.client.findMany.mockResolvedValue(mockClients as any);

      // Act
      const result = await clientService.listClients({
        search: 'Search',
        page: 1,
        limit: 5,
      });

      // Assert
      expect(prismaMock.client.count).toHaveBeenCalledWith({
        where: {
          OR: [
            { name: { contains: 'Search', mode: 'insensitive' } },
            { taxNumber: { contains: 'Search', mode: 'insensitive' } },
            { phone: { contains: 'Search', mode: 'insensitive' } },
            { email: { contains: 'Search', mode: 'insensitive' } },
          ],
        },
      });
      expect(prismaMock.client.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { name: { contains: 'Search', mode: 'insensitive' } },
            { taxNumber: { contains: 'Search', mode: 'insensitive' } },
            { phone: { contains: 'Search', mode: 'insensitive' } },
            { email: { contains: 'Search', mode: 'insensitive' } },
          ],
        },
        orderBy: {
          name: 'asc',
        },
        skip: 0,
        take: 5,
      });
      expect(result.data).toEqual(mockClients);
      expect(result.pagination.total).toBe(1);
    });

    it('should handle pagination correctly', async () => {
      // Arrange
      const mockClients: any[] = [];
      prismaMock.client.count.mockResolvedValue(25);
      prismaMock.client.findMany.mockResolvedValue(mockClients);

      // Act
      const result = await clientService.listClients({
        page: 3,
        limit: 5,
        sort: 'taxNumber',
        order: 'desc',
      });

      // Assert
      expect(prismaMock.client.findMany).toHaveBeenCalledWith({
        where: {},
        orderBy: {
          taxNumber: 'desc',
        },
        skip: 10, // (3-1) * 5
        take: 5,
      });
      expect(result.pagination).toEqual({
        page: 3,
        limit: 5,
        total: 25,
        pages: 5, // Math.ceil(25/5)
      });
    });
  });
});
