/**
 * أنواع النماذج المخصصة
 */

// نوع الحقل
export type FieldType =
  | 'text'
  | 'number'
  | 'date'
  | 'select'
  | 'checkbox'
  | 'textarea'
  | 'file';

// نوع حقل النموذج (بديل لـ FieldType)
export enum FormFieldType {
  TEXT = 'text',
  NUMBER = 'number',
  DATE = 'date',
  SELECT = 'select',
  CHECKBOX = 'checkbox',
  TEXTAREA = 'textarea',
  FILE = 'file',
}

// واجهة خيار القائمة المنسدلة
export interface SelectOption {
  value: string;
  label: string;
}

// واجهة حقل النموذج
export interface FormField {
  id: string;
  name: string;
  label: string;
  type: FieldType;
  required: boolean;
  order: number;
  placeholder?: string;
  defaultValue?: string | number | boolean;
  options?: SelectOption[];
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  disabled?: boolean;
  hidden?: boolean;
  helpText?: string;
}

// نوع النموذج
export type FormType =
  | 'declarations'
  | 'item_movements'
  | 'authorizations'
  | 'releases'
  | 'permits'
  | 'guarantees'
  | 'receipts'
  | 'clients'
  | 'documents';

// واجهة النموذج المخصص
export interface CustomForm {
  id: string;
  name: string;
  description?: string;
  formType: FormType;
  fields: FormField[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

// واجهة قيم نموذج النموذج المخصص
export interface CustomFormFormValues {
  name: string;
  description?: string;
  formType: FormType;
  fields: FormField[];
  isActive: boolean;
}

// واجهة استجابة النموذج المخصص
export interface CustomFormResponse {
  data: CustomForm;
}

// واجهة استجابة قائمة النماذج المخصصة
export interface CustomFormsResponse {
  data: CustomForm[];
}
