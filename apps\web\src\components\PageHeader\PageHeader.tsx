import React from 'react';
import { Box, Typography, Divider, Paper } from '@mui/material';

interface PageHeaderProps {
  /**
   * عنوان الصفحة
   */
  title: string;
  
  /**
   * العنوان الفرعي للصفحة (اختياري)
   */
  subtitle?: string;
  
  /**
   * زر العودة أو أي أزرار أخرى (اختياري)
   */
  backButton?: React.ReactNode;
  
  /**
   * أزرار إضافية (اختياري)
   */
  actions?: React.ReactNode;
  
  /**
   * محتوى إضافي (اختياري)
   */
  children?: React.ReactNode;
}

/**
 * مكون رأس الصفحة
 * يستخدم لعرض عنوان الصفحة والأزرار الرئيسية
 */
export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  backButton,
  actions,
  children,
}) => {
  return (
    <Paper
      elevation={0}
      sx={{
        p: 3,
        mb: 3,
        backgroundColor: 'background.paper',
        borderRadius: 2,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'flex-start', sm: 'center' },
          mb: subtitle || children ? 2 : 0,
        }}
      >
        <Box>
          <Typography variant="h4" component="h1" gutterBottom={!!subtitle}>
            {title}
          </Typography>
          {subtitle && (
            <Typography variant="subtitle1" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
        
        <Box
          sx={{
            display: 'flex',
            gap: 2,
            mt: { xs: 2, sm: 0 },
            flexWrap: 'wrap',
          }}
        >
          {backButton}
          {actions}
        </Box>
      </Box>
      
      {children && (
        <>
          <Divider sx={{ my: 2 }} />
          {children}
        </>
      )}
    </Paper>
  );
};
