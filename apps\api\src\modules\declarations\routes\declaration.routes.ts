import { Router } from 'express';
import multer from 'multer';
import { declarationController } from '../controllers/declaration.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import {
  createDeclarationSchema,
  updateDeclarationSchema,
  getDeclarationSchema,
  deleteDeclarationSchema,
  listDeclarationsSchema,
} from '../schemas/declaration.schema.js';
import { config } from '../../../core/config/app.config.js';

export const declarationRoutes = Router();

// إعداد Multer لرفع الملفات
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: config.upload.maxFileSize,
  },
  fileFilter: (req, file, cb) => {
    // قبول ملفات PDF فقط
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم. يرجى رفع ملف PDF فقط.'));
    }
  },
});

// مسارات البيانات
declarationRoutes.get(
  '/',
  authMiddleware,
  validateRequest(listDeclarationsSchema),
  declarationController.listDeclarations
);

declarationRoutes.post(
  '/',
  authMiddleware,
  upload.single('file'),
  validateRequest(createDeclarationSchema),
  declarationController.createDeclaration
);

declarationRoutes.get(
  '/:id',
  authMiddleware,
  validateRequest(getDeclarationSchema),
  declarationController.getDeclaration
);

declarationRoutes.put(
  '/:id',
  authMiddleware,
  upload.single('file'),
  validateRequest(updateDeclarationSchema),
  declarationController.updateDeclaration
);

declarationRoutes.delete(
  '/:id',
  authMiddleware,
  validateRequest(deleteDeclarationSchema),
  declarationController.deleteDeclaration
);
