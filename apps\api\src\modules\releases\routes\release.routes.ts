import { Router } from 'express';
import multer from 'multer';
import { releaseController } from '../controllers/release.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import {
  createReleaseSchema,
  updateReleaseSchema,
  getReleaseSchema,
  deleteReleaseSchema,
  listReleasesSchema,
} from '../schemas/release.schema.js';
import { config } from '../../../core/config/app.config.js';

export const releaseRoutes = Router();

// إعداد Multer لرفع الملفات
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: config.upload.maxFileSize,
  },
  fileFilter: (req, file, cb) => {
    // قبول ملفات PDF فقط
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم. يرجى رفع ملف PDF فقط.'));
    }
  },
});

// مسارات الإفراجات
releaseRoutes.get(
  '/',
  authMiddleware,
  validateRequest(listReleasesSchema),
  releaseController.listReleases
);

releaseRoutes.post(
  '/',
  authMiddleware,
  upload.single('file'),
  validateRequest(createReleaseSchema),
  releaseController.createRelease
);

releaseRoutes.get(
  '/:id',
  authMiddleware,
  validateRequest(getReleaseSchema),
  releaseController.getRelease
);

releaseRoutes.put(
  '/:id',
  authMiddleware,
  upload.single('file'),
  validateRequest(updateReleaseSchema),
  releaseController.updateRelease
);

releaseRoutes.delete(
  '/:id',
  authMiddleware,
  validateRequest(deleteReleaseSchema),
  releaseController.deleteRelease
);
