import { z } from 'zod';

// مخطط البحث المتقدم
export const advancedSearchSchema = z.object({
  query: z.object({
    page: z.string().optional().transform((val) => (val ? parseInt(val) : 1)),
    limit: z.string().optional().transform((val) => (val ? parseInt(val) : 10)),
    sort: z.string().optional().default('createdAt'),
    order: z.enum(['asc', 'desc']).optional().default('desc'),
    searchType: z.enum([
      'ALL',
      'DECLARATIONS',
      'ITEM_MOVEMENTS',
      'AUTHORIZATIONS',
      'RELEASES',
      'PERMITS',
      'GUARANTEES',
      'RECEIPTS',
      'CLIENTS',
      'DOCUMENTS',
    ]).optional().default('ALL'),
    keyword: z.string().optional(),
    taxNumber: z.string().optional(),
    clientId: z.string().uuid().optional(),
    declarationNumber: z.string().optional().transform((val) => (val ? parseInt(val) : undefined)),
    invoiceNumber: z.string().optional().transform((val) => (val ? parseInt(val) : undefined)),
    declarationType: z.enum(['IMPORT', 'EXPORT']).optional(),
    goodsType: z.enum([
      'HUMAN_MEDICINE',
      'LABORATORY_SOLUTIONS',
      'MEDICAL_SUPPLIES',
      'SUGAR_STRIPS',
      'MEDICAL_DEVICES',
      'MISCELLANEOUS',
    ]).optional(),
    fromDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    toDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
  }),
});
