import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  Grid,
  MenuItem,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useAuthorization, useCreateAuthorization, useUpdateAuthorization } from '../hooks/useAuthorizations';
import { AuthorizationType, AuthorizationFormValues } from '../types/authorization.types';

// مخطط التحقق من صحة نموذج التفويض
const authorizationFormSchema = z.object({
  clientName: z.string().optional(),
  taxNumber: z.string().min(1, { message: 'الرقم الضريبي مطلوب' }),
  authorizationType: z.nativeEnum(AuthorizationType, {
    errorMap: () => ({ message: 'نوع التفويض مطلوب' }),
  }),
  startDate: z.date({
    required_error: 'تاريخ بداية التفويض مطلوب',
    invalid_type_error: 'تاريخ بداية التفويض مطلوب',
  }).nullable().refine(val => val !== null, {
    message: 'تاريخ بداية التفويض مطلوب',
  }),
  endDate: z.date({
    required_error: 'تاريخ نهاية التفويض مطلوب',
    invalid_type_error: 'تاريخ نهاية التفويض مطلوب',
  }).nullable().refine(val => val !== null, {
    message: 'تاريخ نهاية التفويض مطلوب',
  }),
  clientId: z.string().optional(),
});

const AuthorizationFormPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  // استخدام خطافات البيانات
  const { data: authorization, isLoading: isLoadingAuthorization } = useAuthorization(id || '');
  const createMutation = useCreateAuthorization();
  const updateMutation = useUpdateAuthorization();

  // حالة الملف
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);

  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<AuthorizationFormValues>({
    resolver: zodResolver(authorizationFormSchema),
    defaultValues: {
      clientName: '',
      taxNumber: '',
      authorizationType: AuthorizationType.FOLLOW_UP,
      startDate: null,
      endDate: null,
      clientId: undefined,
    },
  });

  // تحميل بيانات التفويض عند التعديل
  useEffect(() => {
    if (isEditMode && authorization) {
      reset({
        clientName: authorization.clientName || '',
        taxNumber: authorization.taxNumber,
        authorizationType: authorization.authorizationType,
        startDate: new Date(authorization.startDate),
        endDate: new Date(authorization.endDate),
        clientId: authorization.clientId,
      });

      // تعيين رابط ملف PDF إذا كان موجودًا
      if (authorization.pdfFile) {
        setPdfUrl(`/api/files/${authorization.pdfFile}`);
      }
    }
  }, [isEditMode, authorization, reset]);

  // التعامل مع تغيير الملف
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];

      // التحقق من حجم الملف (الحد الأقصى 100 ميجابايت)
      const maxSize = 100 * 1024 * 1024; // 100 ميجابايت بالبايت
      if (file.size > maxSize) {
        alert(t('common.fileSizeExceeded', { maxSize: '100MB' }));
        return;
      }

      // التحقق من نوع الملف
      if (file.type !== 'application/pdf') {
        alert(t('common.invalidFileType', { type: 'PDF' }));
        return;
      }

      setSelectedFile(file);

      // إنشاء URL مؤقت للملف المحدد
      const fileUrl = URL.createObjectURL(file);
      setPdfUrl(fileUrl);
    }
  };

  // التعامل مع تقديم النموذج
  const onSubmit = async (data: AuthorizationFormValues) => {
    try {
      if (isEditMode && id) {
        // تحديث التفويض
        await updateMutation.mutateAsync({
          id,
          data: {
            clientName: data.clientName,
            taxNumber: data.taxNumber,
            authorizationType: data.authorizationType,
            startDate: data.startDate?.toISOString() || '',
            endDate: data.endDate?.toISOString() || '',
            clientId: data.clientId,
          },
          file: selectedFile || undefined,
        });
      } else {
        // إنشاء تفويض جديد
        await createMutation.mutateAsync({
          data: {
            clientName: data.clientName,
            taxNumber: data.taxNumber,
            authorizationType: data.authorizationType,
            startDate: data.startDate?.toISOString() || '',
            endDate: data.endDate?.toISOString() || '',
            clientId: data.clientId,
          },
          file: selectedFile || undefined,
        });
      }

      // العودة إلى صفحة قائمة التفويضات
      navigate('/authorizations');
    } catch (error) {
      console.error('Error submitting authorization:', error);
    }
  };

  // عرض حالة التحميل
  if (isEditMode && isLoadingAuthorization) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Box display="flex" alignItems="center" mb={3}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/authorizations')}
            sx={{ mr: 2 }}
          >
            {t('common.back')}
          </Button>
          <Typography variant="h4">
            {isEditMode ? t('authorizations.edit') : t('authorizations.create')}
          </Typography>
        </Box>

        <Paper>
          <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="clientName"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('authorizations.clientName')}
                        fullWidth
                        error={!!errors.clientName}
                        helperText={errors.clientName?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="taxNumber"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('authorizations.taxNumber')}
                        fullWidth
                        required
                        error={!!errors.taxNumber}
                        helperText={errors.taxNumber?.message}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="authorizationType"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        select
                        label={t('authorizations.type')}
                        fullWidth
                        required
                        error={!!errors.authorizationType}
                        helperText={errors.authorizationType?.message}
                      >
                        <MenuItem value={AuthorizationType.FOLLOW_UP}>
                          {t('authorizations.types.FOLLOW_UP')}
                        </MenuItem>
                        <MenuItem value={AuthorizationType.CLEARANCE}>
                          {t('authorizations.types.CLEARANCE')}
                        </MenuItem>
                        <MenuItem value={AuthorizationType.RECEIPT}>
                          {t('authorizations.types.RECEIPT')}
                        </MenuItem>
                        <MenuItem value={AuthorizationType.FULL}>
                          {t('authorizations.types.FULL')}
                        </MenuItem>
                      </TextField>
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="startDate"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        label={t('authorizations.startDate')}
                        value={field.value}
                        onChange={(date) => field.onChange(date)}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            required: true,
                            error: !!errors.startDate,
                            helperText: errors.startDate?.message,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="endDate"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        label={t('authorizations.endDate')}
                        value={field.value}
                        onChange={(date) => field.onChange(date)}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            required: true,
                            error: !!errors.endDate,
                            helperText: errors.endDate?.message,
                          },
                        }}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    {t('common.attachments')}
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<UploadIcon />}
                    >
                      {t('common.uploadFile')}
                      <input
                        type="file"
                        accept="application/pdf"
                        hidden
                        onChange={handleFileChange}
                        // السماح بملفات كبيرة (100 ميجابايت)
                        max-size="104857600"
                      />
                    </Button>
                    {selectedFile && (
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        {t('common.selectedFile')}: {selectedFile.name}
                      </Typography>
                    )}
                  </Box>

                  {pdfUrl && (
                    <Box sx={{ mt: 2, border: '1px solid #ddd', borderRadius: 1 }}>
                      <iframe
                        src={pdfUrl}
                        width="100%"
                        height="500px"
                        title="PDF Preview"
                        style={{ border: 'none' }}
                      />
                    </Box>
                  )}
                </Grid>
              </Grid>

              <Box display="flex" justifyContent="flex-end" mt={3}>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/authorizations')}
                  sx={{ mr: 1 }}
                >
                  {t('common.cancel')}
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <CircularProgress size={24} />
                  ) : (
                    t('common.save')
                  )}
                </Button>
              </Box>
            </CardContent>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default AuthorizationFormPage;
