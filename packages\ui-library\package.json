{"name": "ui-library", "version": "0.0.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "scripts": {"build": "tsup src/index.tsx --format cjs,esm --dts --external react", "dev": "tsup src/index.tsx --format cjs,esm --dts --external react --watch", "lint": "eslint \"src/**/*.{ts,tsx}\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "peerDependencies": {"@mui/material": "^5.15.11", "react": "^18.2.0", "react-dom": "^18.2.0"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.11", "@mui/material": "^5.15.11", "@mui/x-data-grid": "^6.19.5", "react-hook-form": "^7.50.1", "shared-types": "workspace:*"}, "devDependencies": {"@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "eslint": "^8.56.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tsup": "^8.0.2", "typescript": "^5.3.3"}}