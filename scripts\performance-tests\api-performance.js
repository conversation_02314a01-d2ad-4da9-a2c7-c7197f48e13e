/**
 * سكريبت لاختبارات أداء واجهة برمجة التطبيقات
 *
 * يستخدم هذا السكريبت autocannon لإجراء اختبارات التحميل على نقاط النهاية في واجهة برمجة التطبيقات
 * وإنشاء تقارير الأداء.
 *
 * الاستخدام:
 * node api-performance.js [endpoint] [--auth]
 *
 * أمثلة:
 * node api-performance.js /api/declarations
 * node api-performance.js /api/permits --auth
 */

const autocannon = require('autocannon');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const exec = promisify(require('child_process').exec);
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// الإعدادات الافتراضية
const DEFAULT_CONFIG = {
  url: 'http://localhost:3001', // عنوان URL الأساسي لواجهة برمجة التطبيقات
  connections: 10, // عدد الاتصالات المتزامنة
  duration: 10, // مدة الاختبار بالثواني
  headers: {
    'Content-Type': 'application/json',
  },
};

// الحصول على رمز المصادقة
async function getAuthToken() {
  try {
    const response = await fetch(`${DEFAULT_CONFIG.url}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: process.env.TEST_USERNAME || 'admin',
        password: process.env.TEST_PASSWORD || 'admin123',
      }),
    });

    const data = await response.json();
    return data.token;
  } catch (error) {
    console.error('خطأ في الحصول على رمز المصادقة:', error);
    process.exit(1);
  }
}

// الدالة الرئيسية
async function runPerformanceTest() {
  // الحصول على وسيطات سطر الأوامر
  const args = process.argv.slice(2);
  const endpoint = args[0] || '/api/declarations';
  const needsAuth = args.includes('--auth');

  console.log(`تنفيذ اختبار الأداء لـ: ${endpoint}`);
  console.log(`المصادقة: ${needsAuth ? 'نعم' : 'لا'}`);

  // تكوين الاختبار
  const config = {
    ...DEFAULT_CONFIG,
    url: `${DEFAULT_CONFIG.url}${endpoint}`,
  };

  // إضافة رمز المصادقة إذا لزم الأمر
  if (needsAuth) {
    const token = await getAuthToken();
    config.headers['Authorization'] = `Bearer ${token}`;
  }

  // إنشاء مجلد للتقارير إذا لم يكن موجودًا
  const reportsDir = path.join(__dirname, 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir);
  }

  // تنفيذ الاختبار
  console.log('بدء اختبار التحميل...');
  const result = await autocannon(config);

  // إنشاء اسم ملف للتقرير
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const endpointName = endpoint.replace(/\//g, '-').replace(/^-/, '');
  const reportFile = path.join(reportsDir, `${endpointName}-${timestamp}.json`);

  // حفظ النتائج
  fs.writeFileSync(reportFile, JSON.stringify(result, null, 2));
  console.log(`تم حفظ التقرير في: ${reportFile}`);

  // عرض ملخص
  console.log('\nملخص الاختبار:');
  console.log(`الطلبات في الثانية: ${result.requests.average}`);
  console.log(`متوسط زمن الاستجابة: ${result.latency.average} مللي ثانية`);
  console.log(`متوسط معدل نقل البيانات: ${result.throughput.average} بايت/ثانية`);
  console.log(`الأخطاء: ${result.errors}`);
}

// تنفيذ الدالة الرئيسية
runPerformanceTest().catch(err => {
  console.error('خطأ في تنفيذ اختبار الأداء:', err);
  process.exit(1);
});
