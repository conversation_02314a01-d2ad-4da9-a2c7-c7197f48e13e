import { prismaMock } from '../../../core/utils/__mocks__/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

// Mock the release service
const mockReleaseService = {
  createRelease: async (data: any, userId: string, file?: any) => {
    // Check if declaration exists
    if (data.declarationId === 'nonexistent-declaration') {
      throw new HttpException(404, 'البيان غير موجود', 'Not Found');
    }

    // Mock implementation
    const releaseNumber = '1001';
    const release = {
      id: 'release-1',
      releaseNumber,
      issuingAuthority: data.issuingAuthority,
      invoiceNumber: data.invoiceNumber,
      invoiceDate: data.invoiceDate,
      invoiceValue: data.invoiceValue,
      approvalIssueDate: data.approvalIssueDate,
      bondStartDate: data.bondStartDate,
      bondEndDate: data.bondEndDate,
      driverPassAvailable: data.driverPassAvailable,
      pdfFile: file ? `release_${releaseNumber}.pdf` : undefined,
      declarationId: data.declarationId,
      userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    return release;
  },

  updateRelease: async (id: string, data: any, userId: string, file?: any) => {
    const existingRelease = {
      id,
      releaseNumber: '1001',
      userId: 'user-123', // Fixed userId for testing
      pdfFile: null, // إضافة pdfFile
    };

    if (existingRelease.userId !== userId) {
      throw new HttpException(403, 'غير مصرح لك بتحديث هذا الإفراج', 'Forbidden');
    }

    const updatedRelease = {
      ...existingRelease,
      ...data,
      pdfFile: file ? `release_${id}.pdf` : existingRelease.pdfFile,
      updatedAt: new Date(),
    };

    return updatedRelease;
  },

  getRelease: async (id: string) => {
    const release = {
      id,
      releaseNumber: '1001',
      issuingAuthority: 'الجمارك السعودية',
      invoiceNumber: 'INV-001',
      invoiceDate: new Date(),
      invoiceValue: 50000,
      approvalIssueDate: new Date(),
      bondStartDate: new Date(),
      bondEndDate: new Date(),
      driverPassAvailable: true,
      declarationId: 'declaration-1',
      userId: 'user-123',
    };

    return release;
  },

  deleteRelease: async (id: string, userId: string) => {
    const release = {
      id,
      userId: 'user-123', // Fixed userId for testing
    };

    if (release.userId !== userId) {
      throw new HttpException(403, 'غير مصرح لك بحذف هذا الإفراج', 'Forbidden');
    }

    return { success: true };
  },

  listReleases: async (params: any = {}) => {
    const releases = [
      {
        id: 'release-1',
        releaseNumber: '1001',
        issuingAuthority: 'الجمارك السعودية',
        invoiceValue: 50000,
        approvalIssueDate: new Date(),
        bondEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      },
      {
        id: 'release-2',
        releaseNumber: '1002',
        issuingAuthority: 'الجمارك السعودية',
        invoiceValue: 75000,
        approvalIssueDate: new Date(),
        bondEndDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago (expired)
      },
    ];

    // Filter by active status if specified
    let filteredReleases = releases;
    if (params.isActive !== undefined) {
      const now = new Date();
      filteredReleases = releases.filter(release => {
        const isActive = release.bondEndDate >= now;
        return params.isActive ? isActive : !isActive;
      });
    }

    return {
      data: filteredReleases,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 10,
        total: filteredReleases.length,
        pages: Math.ceil(filteredReleases.length / (params.limit || 10)),
      },
    };
  },
};

describe('Release Service', () => {
  beforeEach(() => {
    // تنظيف المحاكيات قبل كل اختبار
  });

  describe('createRelease', () => {
    it('should create a new release successfully', async () => {
      // Arrange
      const releaseData = {
        declarationId: 'declaration-1',
        issuingAuthority: 'الجمارك السعودية',
        invoiceNumber: 'INV-001',
        invoiceDate: new Date(),
        invoiceValue: 50000,
        approvalIssueDate: new Date(),
        bondStartDate: new Date(),
        bondEndDate: new Date(),
        driverPassAvailable: true,
      };
      const userId = 'user-123';

      // Act
      const result = await mockReleaseService.createRelease(releaseData, userId);

      // Assert
      expect(result).toHaveProperty('id');
      expect(result.issuingAuthority).toBe(releaseData.issuingAuthority);
      expect(result.invoiceNumber).toBe(releaseData.invoiceNumber);
      expect(result.invoiceValue).toBe(releaseData.invoiceValue);
      expect(result.userId).toBe(userId);
    });

    it('should throw error when declaration not found', async () => {
      // Arrange
      const releaseData = {
        declarationId: 'nonexistent-declaration',
        issuingAuthority: 'الجمارك السعودية',
      };
      const userId = 'user-123';

      // Act & Assert
      await expect(
        mockReleaseService.createRelease(releaseData, userId)
      ).rejects.toThrow(HttpException);
    });
  });

  describe('updateRelease', () => {
    it('should update release successfully', async () => {
      // Arrange
      const releaseId = 'release-1';
      const updateData = {
        issuingAuthority: 'الجمارك السعودية المحدثة',
        invoiceValue: 60000,
      };
      const userId = 'user-123';

      // Act
      const result = await mockReleaseService.updateRelease(releaseId, updateData, userId);

      // Assert
      expect(result.issuingAuthority).toBe(updateData.issuingAuthority);
      expect(result.invoiceValue).toBe(updateData.invoiceValue);
    });

    it('should throw error when user is not authorized', async () => {
      // Arrange
      const releaseId = 'release-1';
      const updateData = { invoiceValue: 60000 };
      const userId = 'different-user';

      // Act & Assert
      await expect(
        mockReleaseService.updateRelease(releaseId, updateData, userId)
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getRelease', () => {
    it('should get release by id successfully', async () => {
      // Arrange
      const releaseId = 'release-1';

      // Act
      const result = await mockReleaseService.getRelease(releaseId);

      // Assert
      expect(result).toHaveProperty('id', releaseId);
      expect(result).toHaveProperty('releaseNumber');
      expect(result).toHaveProperty('issuingAuthority');
      expect(result).toHaveProperty('invoiceValue');
    });
  });

  describe('deleteRelease', () => {
    it('should delete release successfully', async () => {
      // Arrange
      const releaseId = 'release-1';
      const userId = 'user-123';

      // Act
      const result = await mockReleaseService.deleteRelease(releaseId, userId);

      // Assert
      expect(result).toEqual({ success: true });
    });

    it('should throw error when user is not authorized to delete', async () => {
      // Arrange
      const releaseId = 'release-1';
      const userId = 'different-user';

      // Act & Assert
      await expect(
        mockReleaseService.deleteRelease(releaseId, userId)
      ).rejects.toThrow(HttpException);
    });
  });

  describe('listReleases', () => {
    it('should list releases with pagination', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };

      // Act
      const result = await mockReleaseService.listReleases(params);

      // Assert
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('pagination');
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination.page).toBe(params.page);
      expect(result.pagination.limit).toBe(params.limit);
    });

    it('should filter active releases', async () => {
      // Arrange
      const params = { isActive: true };

      // Act
      const result = await mockReleaseService.listReleases(params);

      // Assert
      expect(result.data).toHaveLength(1); // Only one active release
      expect(result.data[0].id).toBe('release-1');
    });

    it('should filter expired releases', async () => {
      // Arrange
      const params = { isActive: false };

      // Act
      const result = await mockReleaseService.listReleases(params);

      // Assert
      expect(result.data).toHaveLength(1); // Only one expired release
      expect(result.data[0].id).toBe('release-2');
    });
  });
});
