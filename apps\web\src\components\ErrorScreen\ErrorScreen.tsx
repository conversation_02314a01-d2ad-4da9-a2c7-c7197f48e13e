import React from 'react';
import { Box, Button, Typography, Paper } from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import RefreshIcon from '@mui/icons-material/Refresh';

interface ErrorScreenProps {
  /**
   * رسالة الخطأ
   */
  message?: string;
  
  /**
   * دالة تنفذ عند الضغط على زر إعادة المحاولة
   */
  onRetry?: () => void;
  
  /**
   * عنوان الخطأ
   */
  title?: string;
  
  /**
   * رمز الخطأ
   */
  errorCode?: string | number;
  
  /**
   * هل يتم عرض زر إعادة المحاولة
   * @default true
   */
  showRetryButton?: boolean;
  
  /**
   * هل يتم عرض الخلفية
   * @default true
   */
  withBackground?: boolean;
}

/**
 * مكون شاشة الخطأ
 * يستخدم لعرض رسائل الخطأ للمستخدم
 */
export const ErrorScreen: React.FC<ErrorScreenProps> = ({
  message = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
  onRetry,
  title = 'حدث خطأ',
  errorCode,
  showRetryButton = true,
  withBackground = true,
}) => {
  const content = (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 4,
        minHeight: '200px',
        width: '100%',
        textAlign: 'center',
      }}
    >
      <ErrorOutlineIcon color="error" sx={{ fontSize: 60, mb: 2 }} />
      
      {errorCode && (
        <Typography variant="h5" color="error" gutterBottom>
          {errorCode}
        </Typography>
      )}
      
      <Typography variant="h5" gutterBottom>
        {title}
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        {message}
      </Typography>
      
      {showRetryButton && onRetry && (
        <Button
          variant="contained"
          color="primary"
          startIcon={<RefreshIcon />}
          onClick={onRetry}
          sx={{ mt: 2 }}
        >
          إعادة المحاولة
        </Button>
      )}
    </Box>
  );

  if (withBackground) {
    return (
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          backgroundColor: 'background.paper',
          borderRadius: 2,
        }}
      >
        {content}
      </Paper>
    );
  }

  return content;
};
