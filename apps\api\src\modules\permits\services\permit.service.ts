import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { saveUploadedPdf } from '../../../core/utils/pdf/pdfService.js';
import { Prisma } from '@prisma/client';

interface CreatePermitInput {
  declarationId: string;
  permitDate: Date;
  permitNumber: string;
  expiryDate?: Date;
  notes?: string;
}

interface UpdatePermitInput {
  declarationId?: string;
  permitDate?: Date;
  permitNumber?: string;
  expiryDate?: Date;
  notes?: string;
}

interface ListPermitsParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationId?: string;
  fromDate?: Date;
  toDate?: Date;
  isActive?: boolean;
}

export const permitService = {
  /**
   * إنشاء تصريح جديد
   */
  createPermit: async (
    data: CreatePermitInput,
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود البيان
      const declaration = await prisma.declaration.findUnique({
        where: { id: data.declarationId },
      });

      if (!declaration) {
        throw new HttpException(404, 'البيان غير موجود', 'Not Found');
      }

      // حفظ ملف PDF إذا تم تقديمه
      let pdfFile: string | undefined;
      if (file) {
        pdfFile = saveUploadedPdf(file, 'permits', `permit_${data.permitNumber}`);
      }

      // إنشاء التصريح
      const permit = await prisma.permit.create({
        data: {
          permitDate: data.permitDate,
          permitNumber: data.permitNumber,
          expiryDate: data.expiryDate,
          notes: data.notes,
          pdfFile,
          declarationId: data.declarationId,
        },
      });

      return permit;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(400, 'رقم التصريح موجود بالفعل', 'Bad Request');
        }
      }
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء إنشاء التصريح', 'Internal Server Error');
    }
  },

  /**
   * تحديث تصريح
   */
  updatePermit: async (
    id: string,
    data: UpdatePermitInput,
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود التصريح
      const existingPermit = await prisma.permit.findUnique({
        where: { id },
      });

      if (!existingPermit) {
        throw new HttpException(404, 'التصريح غير موجود', 'Not Found');
      }

      // لا نحتاج للتحقق من صلاحية المستخدم في هذا النموذج

      // التحقق من وجود البيان إذا تم تغييره
      if (data.declarationId) {
        const declaration = await prisma.declaration.findUnique({
          where: { id: data.declarationId },
        });

        if (!declaration) {
          throw new HttpException(404, 'البيان غير موجود', 'Not Found');
        }
      }

      // حفظ ملف PDF إذا تم تقديمه
      let pdfFile = existingPermit.pdfFile;
      if (file) {
        pdfFile = saveUploadedPdf(file, 'permits', id);
      }

      // تحديث التصريح
      const permit = await prisma.permit.update({
        where: { id },
        data: {
          permitDate: data.permitDate,
          permitNumber: data.permitNumber,
          expiryDate: data.expiryDate,
          notes: data.notes,
          pdfFile,
          declarationId: data.declarationId,
        },
      });

      return permit;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء تحديث التصريح', 'Internal Server Error');
    }
  },

  /**
   * الحصول على تصريح محدد
   */
  getPermit: async (id: string) => {
    const permit = await prisma.permit.findUnique({
      where: { id },
      include: {
        declaration: true,
      },
    });

    if (!permit) {
      throw new HttpException(404, 'التصريح غير موجود', 'Not Found');
    }

    return permit;
  },

  /**
   * حذف تصريح
   */
  deletePermit: async (id: string) => {
    // التحقق من وجود التصريح
    const permit = await prisma.permit.findUnique({
      where: { id },
    });

    if (!permit) {
      throw new HttpException(404, 'التصريح غير موجود', 'Not Found');
    }

    // حذف التصريح
    await prisma.permit.delete({
      where: { id },
    });

    return { success: true };
  },

  /**
   * الحصول على قائمة التصاريح
   */
  listPermits: async (params: ListPermitsParams = {}) => {
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'desc',
      search,
      declarationId,
      fromDate,
      toDate,
    } = params;

    // بناء شروط البحث
    const where: Prisma.PermitWhereInput = {};

    if (search) {
      where.OR = [
        { permitNumber: { contains: search } },
        { notes: { contains: search } },
      ];
    }

    if (declarationId) {
      where.declarationId = declarationId;
    }

    if (fromDate && toDate) {
      where.permitDate = {
        gte: fromDate,
        lte: toDate,
      };
    } else if (fromDate) {
      where.permitDate = {
        gte: fromDate,
      };
    } else if (toDate) {
      where.permitDate = {
        lte: toDate,
      };
    }

    // لا يوجد حقل expiryDate في نموذج Permit

    // حساب إجمالي عدد التصاريح
    const total = await prisma.permit.count({ where });

    // الحصول على التصاريح
    const permits = await prisma.permit.findMany({
      where,
      include: {
        declaration: true,
      },
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: permits,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};
