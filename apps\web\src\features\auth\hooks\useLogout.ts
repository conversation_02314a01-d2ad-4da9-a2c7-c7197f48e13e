import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch } from '@app/store/store';
import { logout } from '@app/store/slices/authSlice';
import authService from '@lib/services/auth.service';
import { useToast } from '@lib/hooks/useToast';

/**
 * خطاف لتسجيل الخروج
 * يوفر وظيفة لتسجيل الخروج من النظام
 */
export const useLogout = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const toast = useToast();

  return useMutation({
    mutationFn: async () => {
      try {
        // استخدام خدمة المصادقة لتسجيل الخروج
        await authService.logout();
        return true;
      } catch (error) {
        console.error('Error logging out:', error);
        return false;
      }
    },
    onSuccess: () => {
      // تحديث حالة المصادقة
      dispatch(logout());
      
      // عرض رسالة نجاح
      toast.showSuccess('تم تسجيل الخروج بنجاح');
      
      // الانتقال إلى صفحة تسجيل الدخول
      navigate('/login');
    },
    onError: () => {
      // عرض رسالة خطأ
      toast.showError('حدث خطأ أثناء تسجيل الخروج');
      
      // تحديث حالة المصادقة على أي حال
      dispatch(logout());
      
      // الانتقال إلى صفحة تسجيل الدخول
      navigate('/login');
    },
  });
};
