// Global type definitions for the web application

declare global {
  // Jest globals for Vitest compatibility
  var jest: typeof import('vitest').vi & {
    Mock: any;
    requireActual: (module: string) => any;
  };

  // Mock IntersectionObserver for tests
  interface IntersectionObserver {
    callback?: (entries: IntersectionObserverEntry[]) => void;
  }
}

// User types
export interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

// Auth state
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  loading: boolean;
  error: string | null;
}

// API Service types
export interface ApiService {
  request: (options: any) => Promise<any>;
  get: (url: string, config?: any) => Promise<any>;
  post: (url: string, data?: any, config?: any) => Promise<any>;
  put: (url: string, data?: any, config?: any) => Promise<any>;
  patch: (url: string, data?: any, config?: any) => Promise<any>;
  delete: (url: string, config?: any) => Promise<any>;
}

// Authorization Status enum
export enum AuthorizationStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED'
}

// Report Type enum
export enum ReportType {
  DECLARATION = 'DECLARATION',
  AUTHORIZATION = 'AUTHORIZATION',
  ITEM_MOVEMENT = 'ITEM_MOVEMENT',
  GUARANTEE = 'GUARANTEE',
  PERMIT = 'PERMIT',
  RECEIPT = 'RECEIPT',
  RELEASE = 'RELEASE'
}

// Custom Form types
export interface SelectOption {
  value: string;
  label: string;
}

export interface FormField {
  id: string;
  name: string;
  type: string;
  label: string;
  required?: boolean;
  placeholder?: string;
  options?: SelectOption[];
  order: number;
}

export interface CustomForm {
  id: string;
  name: string;
  title?: string;
  description?: string;
  formType: string;
  isActive: boolean;
  fields: FormField[];
  userId: string;
  createdBy?: {
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Report Template types
export interface ReportTemplate {
  id: string;
  title: string;
  description?: string;
  type: ReportType;
  dataSource: string;
  query?: string;
  columns: any[];
  filters?: any[];
  chart?: any;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
}

// Confirm Dialog types
export interface ConfirmDialogProps {
  open: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
}

// Report Preview types
export interface ReportPreviewProps {
  title: string;
  description?: string;
  type: ReportType;
  columns: any[];
  chart?: any;
}

// Toast notification types
export interface ToastService {
  showSuccess: (message: string) => void;
  showError: (message: string) => void;
  showWarning: (message: string) => void;
  showInfo: (message: string) => void;
  closeSnackbar: (key?: any) => void;
  success: (message: string) => void;
  error: (message: string) => void;
}

// Date picker types
export type PickerValue = Date | null;

export {};
