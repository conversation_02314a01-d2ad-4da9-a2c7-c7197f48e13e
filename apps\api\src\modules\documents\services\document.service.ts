import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { saveUploadedPdf } from '../../../core/utils/pdf/pdfService.js';
import { Prisma } from '@prisma/client';

interface CreateDocumentInput {
  title: string;
  description?: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  filePath: string;
  uploadedBy: string;
}

interface UpdateDocumentInput {
  title?: string;
  description?: string;
  fileName?: string;
  fileSize?: number;
  fileType?: string;
  filePath?: string;
  uploadedBy?: string;
}

interface ListDocumentsParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  documentType?: string;
  fromDate?: Date;
  toDate?: Date;
  isExpired?: boolean;
}

export const documentService = {
  /**
   * إنشاء مستند جديد
   */
  createDocument: async (
    data: CreateDocumentInput,
    userId: string,
    file?: Express.Multer.File
  ) => {
    try {
      // حفظ ملف PDF إذا تم تقديمه
      let filePath = data.filePath;
      if (file) {
        filePath = saveUploadedPdf(file, 'documents', `doc_${Date.now()}`);
      }

      // إنشاء المستند
      const document = await prisma.document.create({
        data: {
          title: data.title,
          description: data.description,
          fileName: data.fileName,
          fileSize: data.fileSize,
          fileType: data.fileType,
          filePath: filePath,
          uploadedBy: data.uploadedBy || userId,
        },
      });

      return document;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(400, 'رقم المستند موجود بالفعل', 'Bad Request');
        }
      }
      throw new HttpException(500, 'حدث خطأ أثناء إنشاء المستند', 'Internal Server Error');
    }
  },

  /**
   * تحديث مستند
   */
  updateDocument: async (
    id: string,
    data: UpdateDocumentInput,
    userId: string,
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود المستند
      const existingDocument = await prisma.document.findUnique({
        where: { id },
      });

      if (!existingDocument) {
        throw new HttpException(404, 'المستند غير موجود', 'Not Found');
      }

      // التحقق من صلاحية المستخدم
      if (existingDocument.uploadedBy !== userId) {
        throw new HttpException(403, 'غير مصرح لك بتحديث هذا المستند', 'Forbidden');
      }

      // حفظ ملف PDF إذا تم تقديمه
      let filePath = existingDocument.filePath;
      if (file) {
        filePath = saveUploadedPdf(file, 'documents', id);
      }

      // تحديث المستند
      const document = await prisma.document.update({
        where: { id },
        data: {
          title: data.title,
          description: data.description,
          fileName: data.fileName,
          fileSize: data.fileSize,
          fileType: data.fileType,
          filePath: filePath,
          uploadedBy: data.uploadedBy,
        },
      });

      return document;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء تحديث المستند', 'Internal Server Error');
    }
  },

  /**
   * الحصول على مستند محدد
   */
  getDocument: async (id: string) => {
    const document = await prisma.document.findUnique({
      where: { id },
    });

    if (!document) {
      throw new HttpException(404, 'المستند غير موجود', 'Not Found');
    }

    return document;
  },

  /**
   * حذف مستند
   */
  deleteDocument: async (id: string, userId: string) => {
    // التحقق من وجود المستند
    const document = await prisma.document.findUnique({
      where: { id },
    });

    if (!document) {
      throw new HttpException(404, 'المستند غير موجود', 'Not Found');
    }

    // التحقق من صلاحية المستخدم
    if (document.uploadedBy !== userId) {
      throw new HttpException(403, 'غير مصرح لك بحذف هذا المستند', 'Forbidden');
    }

    // حذف المستند
    await prisma.document.delete({
      where: { id },
    });

    return { success: true };
  },

  /**
   * الحصول على قائمة المستندات
   */
  listDocuments: async (params: ListDocumentsParams = {}) => {
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'desc',
      search,
      documentType,
      fromDate,
      toDate,
      isExpired,
    } = params;

    // بناء شروط البحث
    const where: Prisma.DocumentWhereInput = {};

    if (search) {
      where.OR = [
        { title: { contains: search } },
        { description: { contains: search } },
        { fileName: { contains: search } },
      ];
    }

    if (documentType) {
      (where as any).documentType = documentType;
    }

    if (fromDate && toDate) {
      where.createdAt = {
        gte: fromDate,
        lte: toDate,
      };
    } else if (fromDate) {
      where.createdAt = {
        gte: fromDate,
      };
    } else if (toDate) {
      where.createdAt = {
        lte: toDate,
      };
    }

    if (isExpired === true) {
      (where as any).expiryDate = {
        lt: new Date(),
      };
    } else if (isExpired === false) {
      (where as any).expiryDate = {
        gte: new Date(),
      };
    }

    // حساب إجمالي عدد المستندات
    const total = await prisma.document.count({ where });

    // الحصول على المستندات
    const documents = await prisma.document.findMany({
      where,
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: documents,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};
