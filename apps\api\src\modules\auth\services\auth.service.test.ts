import { authService } from './auth.service.js';
import { prisma } from '../../../core/utils/prisma.js';
import { generateToken, verifyToken } from '../../../core/utils/jwt.js';
import { tokenService } from '../../../core/utils/token.service.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import bcrypt from 'bcryptjs';

// Mock dependencies
jest.mock('../../../core/utils/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
  },
}));

jest.mock('../../../core/utils/jwt', () => ({
  generateToken: jest.fn(),
  verifyToken: jest.fn(),
}));

jest.mock('../../../core/utils/token.service', () => ({
  tokenService: {
    invalidateToken: jest.fn(),
    isTokenInvalidated: jest.fn(),
  },
}));

jest.mock('bcryptjs', () => ({
  compare: jest.fn(),
}));

jest.mock('../../../core/utils/logger', () => ({
  logger: {
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

describe('Auth Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      // Arrange
      const username = 'testuser';
      const password = 'password123';
      const mockUser = {
        id: 'user-123',
        username,
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashed-password',
        role: 'USER',
      };
      const mockToken = 'access-token-123';
      const mockRefreshToken = 'refresh-token-123';

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      (generateToken as jest.Mock).mockImplementation((payload, tokenType) => {
        return tokenType === 'refresh' ? mockRefreshToken : mockToken;
      });

      // Act
      const mockReq = { ip: '127.0.0.1', headers: { 'user-agent': 'test' } } as any;
      const result = await authService.login(username, password, mockReq);

      // Assert
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { username },
      });
      expect(bcrypt.compare).toHaveBeenCalledWith(password, mockUser.password);
      expect(generateToken).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        user: {
          id: mockUser.id,
          username: mockUser.username,
          name: mockUser.name,
          email: mockUser.email,
          role: mockUser.role,
        },
        token: mockToken,
        refreshToken: mockRefreshToken,
      });
    });

    it('should throw an error when user is not found', async () => {
      // Arrange
      const username = 'nonexistentuser';
      const password = 'password123';

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      // Act & Assert
      const mockReq = { ip: '127.0.0.1', headers: { 'user-agent': 'test' } } as any;
      await expect(authService.login(username, password, mockReq)).rejects.toThrow(HttpException);
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { username },
      });
      expect(bcrypt.compare).not.toHaveBeenCalled();
      expect(generateToken).not.toHaveBeenCalled();
    });

    it('should throw an error when password is invalid', async () => {
      // Arrange
      const username = 'testuser';
      const password = 'wrongpassword';
      const mockUser = {
        id: 'user-123',
        username,
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashed-password',
        role: 'USER',
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      // Act & Assert
      const mockReq = { ip: '127.0.0.1', headers: { 'user-agent': 'test' } } as any;
      await expect(authService.login(username, password, mockReq)).rejects.toThrow(HttpException);
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { username },
      });
      expect(bcrypt.compare).toHaveBeenCalledWith(password, mockUser.password);
      expect(generateToken).not.toHaveBeenCalled();
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully with valid refresh token', async () => {
      // Arrange
      const refreshToken = 'valid-refresh-token';
      const mockUser = {
        id: 'user-123',
        username: 'testuser',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'USER',
      };
      const mockToken = 'new-access-token';
      const mockNewRefreshToken = 'new-refresh-token';

      (verifyToken as jest.Mock).mockReturnValue({
        id: mockUser.id,
        tokenType: 'refresh',
      });
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
      (generateToken as jest.Mock).mockImplementation((_payload, tokenType) => {
        return tokenType === 'refresh' ? mockNewRefreshToken : mockToken;
      });

      // Act
      const mockReq = { ip: '127.0.0.1', headers: { 'user-agent': 'test' } } as any;
      const result = await authService.refreshToken(refreshToken, mockReq);

      // Assert
      expect(verifyToken).toHaveBeenCalledWith(refreshToken, 'refresh');
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: mockUser.id },
      });
      expect(generateToken).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        user: {
          id: mockUser.id,
          username: mockUser.username,
          name: mockUser.name,
          email: mockUser.email,
          role: mockUser.role,
        },
        token: mockToken,
        refreshToken: mockNewRefreshToken,
      });
    });

    it('should throw an error when refresh token is invalid', async () => {
      // Arrange
      const refreshToken = 'invalid-refresh-token';

      (verifyToken as jest.Mock).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act & Assert
      const mockReq = { ip: '127.0.0.1', headers: { 'user-agent': 'test' } } as any;
      await expect(authService.refreshToken(refreshToken, mockReq)).rejects.toThrow();
      expect(verifyToken).toHaveBeenCalledWith(refreshToken, 'refresh');
      expect(prisma.user.findUnique).not.toHaveBeenCalled();
      expect(generateToken).not.toHaveBeenCalled();
    });
  });

  describe('logout', () => {
    it('should logout successfully with valid token', async () => {
      // Arrange
      const token = 'valid-access-token';
      const refreshToken = 'valid-refresh-token';
      const userId = 'user-123';

      (tokenService.invalidateToken as jest.Mock).mockResolvedValue({ success: true });

      // Act
      const result = await authService.logout(token, refreshToken, userId);

      // Assert
      expect(tokenService.invalidateToken).toHaveBeenCalledWith(token, 'access', userId);
      expect(tokenService.invalidateToken).toHaveBeenCalledWith(refreshToken, 'refresh', userId);
      expect(result).toEqual({ success: true });
    });

    it('should extract userId from token if not provided', async () => {
      // Arrange
      const token = 'valid-access-token';
      const userId = 'user-123';

      (verifyToken as jest.Mock).mockReturnValue({
        id: userId,
      });
      (tokenService.invalidateToken as jest.Mock).mockResolvedValue({ success: true });

      // Act
      const result = await authService.logout(token);

      // Assert
      expect(verifyToken).toHaveBeenCalledWith(token, 'access');
      expect(tokenService.invalidateToken).toHaveBeenCalledWith(token, 'access', userId);
      expect(result).toEqual({ success: true });
    });
  });
});
