import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Client } from '@features/clients/types/client.types';

export interface ClientsState {
  selectedClient: Client | null;
  filters: {
    name?: string;
    taxNumber?: string;
    phone?: string;
  };
  pagination: {
    page: number;
    limit: number;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: ClientsState = {
  selectedClient: null,
  filters: {},
  pagination: {
    page: 0,
    limit: 10,
  },
  isLoading: false,
  error: null,
};

export const clientsSlice = createSlice({
  name: 'clients',
  initialState,
  reducers: {
    setSelectedClient: (state, action: PayloadAction<Client | null>) => {
      state.selectedClient = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<ClientsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = {};
    },
    setPagination: (state, action: PayloadAction<Partial<ClientsState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setSelectedClient,
  setFilters,
  resetFilters,
  setPagination,
  setLoading,
  setError,
} = clientsSlice.actions;

export default clientsSlice.reducer;
