import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  InputAdornment,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { usePermits, useDeletePermit } from '../hooks/usePermits';
import { PermitType } from '../types/permit.types';
import { PermitSearchParams } from '../api/permits.api';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const PermitsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // حالة البحث والتصفية
  const [search, setSearch] = useState('');
  const [permitType, setPermitType] = useState<PermitType | ''>('');
  const [fromDate, setFromDate] = useState<Date | null>(null);
  const [toDate, setToDate] = useState<Date | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);

  // إعداد معلمات البحث
  const searchParams: PermitSearchParams = {
    page: page + 1,
    limit,
    search: search || undefined,
    permitType: permitType || undefined,
    fromDate: fromDate || undefined,
    toDate: toDate || undefined,
  };

  // استخدام خطاف الحصول على قائمة التصاريح
  const { data, isLoading, isError } = usePermits(searchParams);

  // استخدام خطاف حذف تصريح
  const deleteMutation = useDeletePermit();

  // التعامل مع تغيير الصفحة
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  // التعامل مع تغيير عدد العناصر في الصفحة
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLimit(parseInt(event.target.value, 10));
    setPage(0);
  };

  // التعامل مع البحث
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(0);
  };

  // التعامل مع إعادة تعيين التصفية
  const handleResetFilters = () => {
    setSearch('');
    setPermitType('');
    setFromDate(null);
    setToDate(null);
    setPage(0);
  };

  // التعامل مع حذف تصريح
  const handleDeletePermit = async (id: string) => {
    if (window.confirm(t('permits.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id);
      } catch (error) {
        console.error('Error deleting permit:', error);
      }
    }
  };

  // التعامل مع عرض تصريح
  const handleViewPermit = (id: string) => {
    navigate(`/permits/${id}`);
  };

  // التعامل مع تعديل تصريح
  const handleEditPermit = (id: string) => {
    navigate(`/permits/${id}/edit`);
  };

  // التعامل مع إنشاء تصريح جديد
  const handleCreatePermit = () => {
    navigate('/permits/new');
  };

  return (
    <Container maxWidth="xl">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('permits.title')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('permits.description')}
        </Typography>
      </Box>

      <Box mb={4} display="flex" justifyContent="space-between" alignItems="center">
        <TextField
          placeholder={t('common.search')}
          value={search}
          onChange={handleSearch}
          variant="outlined"
          size="small"
          sx={{ width: 300 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: search && (
              <InputAdornment position="end">
                <IconButton size="small" onClick={() => setSearch('')}>
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />

        <Box>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={() => setShowFilters(!showFilters)}
            sx={{ mr: 1 }}
          >
            {t('common.filters')}
          </Button>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreatePermit}
          >
            {t('permits.create')}
          </Button>
        </Box>
      </Box>

      {showFilters && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                select
                fullWidth
                label={t('permits.type')}
                value={permitType}
                onChange={(e) => setPermitType(e.target.value as PermitType | '')}
              >
                <MenuItem value="">{t('common.all')}</MenuItem>
                <MenuItem value={PermitType.ENTRY}>{t('permits.types.ENTRY')}</MenuItem>
                <MenuItem value={PermitType.EXIT}>{t('permits.types.EXIT')}</MenuItem>
                <MenuItem value={PermitType.SPECIAL}>{t('permits.types.SPECIAL')}</MenuItem>
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label={t('common.fromDate')}
                value={fromDate}
                onChange={(date) => {
                  if (date && 'toDate' in date && typeof date.toDate === 'function') {
                    setFromDate(date.toDate());
                  } else {
                    setFromDate(date as Date | null);
                  }
                }}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <DatePicker
                label={t('common.toDate')}
                value={toDate}
                onChange={(date) => {
                  if (date && 'toDate' in date && typeof date.toDate === 'function') {
                    setToDate(date.toDate());
                  } else {
                    setToDate(date as Date | null);
                  }
                }}
                slotProps={{ textField: { fullWidth: true } }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={3} display="flex" alignItems="center">
              <Button
                variant="outlined"
                onClick={handleResetFilters}
                fullWidth
              >
                {t('common.resetFilters')}
              </Button>
            </Grid>
          </Grid>
        </Paper>
      )}

      {isLoading ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      ) : isError ? (
        <Box textAlign="center" my={4}>
          <Typography color="error">{t('common.errorLoading')}</Typography>
          <Button
            variant="outlined"
            onClick={() => window.location.reload()}
            sx={{ mt: 2 }}
          >
            {t('common.retry')}
          </Button>
        </Box>
      ) : (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('permits.number')}</TableCell>
                  <TableCell>{t('permits.type')}</TableCell>
                  <TableCell>{t('permits.issueDate')}</TableCell>
                  <TableCell>{t('permits.expiryDate')}</TableCell>
                  <TableCell>{t('permits.declaration')}</TableCell>
                  <TableCell>{t('permits.client')}</TableCell>
                  <TableCell align="center">{t('common.actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data?.data.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      {t('permits.noPermits')}
                    </TableCell>
                  </TableRow>
                ) : (
                  data?.data.map((permit: any) => (
                    <TableRow key={permit.id}>
                      <TableCell>{permit.permitNumber}</TableCell>
                      <TableCell>
                        <Chip
                          label={t(`permits.types.${permit.permitType}`)}
                          color={
                            permit.permitType === PermitType.ENTRY
                              ? 'primary'
                              : permit.permitType === PermitType.EXIT
                              ? 'secondary'
                              : 'default'
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {format(new Date(permit.issueDate), 'yyyy-MM-dd', { locale: arSA })}
                      </TableCell>
                      <TableCell>
                        {format(new Date(permit.expiryDate), 'yyyy-MM-dd', { locale: arSA })}
                      </TableCell>
                      <TableCell>
                        {permit.declaration?.declarationNumber || '-'}
                      </TableCell>
                      <TableCell>
                        {permit.client?.name || permit.client?.companyName || '-'}
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title={t('common.view')}>
                          <IconButton
                            size="small"
                            onClick={() => handleViewPermit(permit.id)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.edit')}>
                          <IconButton
                            size="small"
                            onClick={() => handleEditPermit(permit.id)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.delete')}>
                          <IconButton
                            size="small"
                            onClick={() => handleDeletePermit(permit.id)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            component="div"
            count={data?.pagination.total || 0}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={limit}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 25, 50]}
            labelRowsPerPage={t('common.rowsPerPage')}
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} ${t('common.of')} ${count}`
            }
          />
        </Paper>
      )}
    </Container>
  );
};

export default PermitsPage;
