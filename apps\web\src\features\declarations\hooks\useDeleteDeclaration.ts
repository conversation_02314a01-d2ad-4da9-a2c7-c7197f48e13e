import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api/api';

export const useDeleteDeclaration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      await api.delete(`/api/declarations/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['declarations'] });
    },
  });
};
