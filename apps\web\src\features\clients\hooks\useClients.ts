import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getClients,
  getClient,
  createClient,
  updateClient,
  deleteClient,
  ClientSearchParams,
  CreateClientRequest,
  UpdateClientRequest,
} from '../api/clients.api';
import { useToast } from '@lib/hooks/useToast';
import { useTranslation } from 'react-i18next';

// خطاف للحصول على قائمة العملاء
export const useClients = (params: ClientSearchParams = {}) => {
  return useQuery({
    queryKey: ['clients', params],
    queryFn: () => getClients(params),
  });
};

// خطاف للحصول على عميل محدد
export const useClient = (id: string) => {
  return useQuery({
    queryKey: ['client', id],
    queryFn: () => getClient(id),
    enabled: !!id,
  });
};

// خطاف لإنشاء عميل جديد
export const useCreateClient = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (data: CreateClientRequest) => createClient(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clients'] });
      showSuccess(t('clients.createSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

// خطاف لتحديث عميل
export const useUpdateClient = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateClientRequest }) =>
      updateClient(id, data),
    onSuccess: (_: any, variables: { id: string; data: UpdateClientRequest }) => {
      queryClient.invalidateQueries({ queryKey: ['clients'] });
      queryClient.invalidateQueries({ queryKey: ['client', variables.id] });
      showSuccess(t('clients.updateSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

// خطاف لحذف عميل
export const useDeleteClient = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => deleteClient(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['clients'] });
      showSuccess(t('clients.deleteSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};
