import apiService from './api.service';

/**
 * خدمة الملفات
 * توفر واجهة موحدة للتعامل مع الملفات
 */
class FileService {
  /**
   * رفع ملف
   * @param file الملف المراد رفعه
   * @param path المسار الذي سيتم رفع الملف إليه
   * @returns وعد بمعلومات الملف المرفوع
   */
  public async uploadFile(file: File, path: string = 'documents'): Promise<any> {
    try {
      // إنشاء FormData
      const formData = new FormData();
      formData.append('file', file);
      formData.append('path', path);

      // رفع الملف
      const response = await apiService.post('/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  /**
   * رفع عدة ملفات
   * @param files الملفات المراد رفعها
   * @param path المسار الذي سيتم رفع الملفات إليه
   * @returns وعد بمعلومات الملفات المرفوعة
   */
  public async uploadFiles(files: File[], path: string = 'documents'): Promise<any> {
    try {
      // إنشاء FormData
      const formData = new FormData();
      
      // إضافة الملفات
      files.forEach((file, index) => {
        formData.append(`files[${index}]`, file);
      });
      
      formData.append('path', path);

      // رفع الملفات
      const response = await apiService.post('/api/upload/multiple', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response;
    } catch (error) {
      console.error('Error uploading files:', error);
      throw error;
    }
  }

  /**
   * تنزيل ملف
   * @param fileId معرف الملف
   * @returns وعد بالملف
   */
  public async downloadFile(fileId: string): Promise<Blob> {
    try {
      // تنزيل الملف
      const response = await apiService.get(`/api/download/${fileId}`, {
        responseType: 'blob',
      });

      return response;
    } catch (error) {
      console.error('Error downloading file:', error);
      throw error;
    }
  }

  /**
   * حذف ملف
   * @param fileId معرف الملف
   * @returns وعد بنتيجة الحذف
   */
  public async deleteFile(fileId: string): Promise<any> {
    try {
      // حذف الملف
      const response = await apiService.delete(`/api/files/${fileId}`);

      return response;
    } catch (error) {
      console.error('Error deleting file:', error);
      throw error;
    }
  }

  /**
   * الحصول على معلومات ملف
   * @param fileId معرف الملف
   * @returns وعد بمعلومات الملف
   */
  public async getFileInfo(fileId: string): Promise<any> {
    try {
      // الحصول على معلومات الملف
      const response = await apiService.get(`/api/files/${fileId}`);

      return response;
    } catch (error) {
      console.error('Error getting file info:', error);
      throw error;
    }
  }

  /**
   * الحصول على قائمة الملفات
   * @param path المسار
   * @returns وعد بقائمة الملفات
   */
  public async getFiles(path: string = 'documents'): Promise<any> {
    try {
      // الحصول على قائمة الملفات
      const response = await apiService.get(`/api/files?path=${path}`);

      return response;
    } catch (error) {
      console.error('Error getting files:', error);
      throw error;
    }
  }
}

// إنشاء نسخة واحدة من الخدمة
const fileService = new FileService();

export default fileService;
