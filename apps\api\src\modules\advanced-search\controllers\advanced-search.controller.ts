import { Request, Response, NextFunction } from 'express';
import { advancedSearchService } from '../services/advanced-search.service.js';
import { paginatedResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const advancedSearchController = {
  /**
   * البحث المتقدم
   * يقوم بمعالجة طلب البحث المتقدم وإرجاع النتائج
   */
  search: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معلمات البحث
      const {
        page,
        limit,
        sort,
        order,
        searchType,
        keyword,
        taxNumber,
        clientId,
        declarationNumber,
        invoiceNumber,
        declarationType,
        goodsType,
        fromDate,
        toDate,
      } = req.query as any;

      // تنفيذ البحث
      const result = await advancedSearchService.search({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        searchType,
        keyword,
        taxNumber,
        clientId,
        declarationNumber: declarationNumber ? parseInt(declarationNumber) : undefined,
        invoiceNumber: invoiceNumber ? parseInt(invoiceNumber) : undefined,
        declarationType,
        goodsType,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined,
      });

      // إرجاع النتائج
      return res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  },
};
