import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  InputAdornment,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { useClients, useDeleteClient } from '../hooks/useClients';
import { ClientSearchParams } from '../api/clients.api';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const ClientsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // حالة البحث والتصفية
  const [search, setSearch] = useState('');
  const [taxNumber, setTaxNumber] = useState('');
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);

  // إعداد معلمات البحث
  const searchParams: ClientSearchParams = {
    page: page + 1,
    limit,
    search: search || undefined,
    taxNumber: taxNumber || undefined,
  };

  // استخدام خطاف الحصول على قائمة العملاء
  const { data, isLoading, isError } = useClients(searchParams);

  // استخدام خطاف حذف عميل
  const deleteMutation = useDeleteClient();

  // التعامل مع تغيير الصفحة
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  // التعامل مع تغيير عدد العناصر في الصفحة
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLimit(parseInt(event.target.value, 10));
    setPage(0);
  };

  // التعامل مع البحث
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    setPage(0);
  };

  // التعامل مع البحث بالرقم الضريبي
  const handleTaxNumberSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTaxNumber(event.target.value);
    setPage(0);
  };

  // التعامل مع إعادة تعيين التصفية
  const handleResetFilters = () => {
    setSearch('');
    setTaxNumber('');
    setPage(0);
  };

  // التعامل مع حذف عميل
  const handleDeleteClient = async (id: string) => {
    if (window.confirm(t('clients.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id);
      } catch (error) {
        console.error('Error deleting client:', error);
      }
    }
  };

  // التعامل مع عرض عميل
  const handleViewClient = (id: string) => {
    navigate(`/clients/${id}`);
  };

  // التعامل مع تعديل عميل
  const handleEditClient = (id: string) => {
    navigate(`/clients/${id}/edit`);
  };

  // التعامل مع إنشاء عميل جديد
  const handleCreateClient = () => {
    navigate('/clients/new');
  };

  return (
    <Container maxWidth="xl">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('clients.title')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('clients.description')}
        </Typography>
      </Box>

      <Box mb={4} display="flex" justifyContent="space-between" alignItems="center">
        <TextField
          placeholder={t('common.search')}
          value={search}
          onChange={handleSearch}
          variant="outlined"
          size="small"
          sx={{ width: 300 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: search && (
              <InputAdornment position="end">
                <IconButton size="small" onClick={() => setSearch('')}>
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />

        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateClient}
        >
          {t('clients.create')}
        </Button>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={t('clients.taxNumber')}
              value={taxNumber}
              onChange={handleTaxNumberSearch}
              placeholder={t('clients.searchByTaxNumber')}
            />
          </Grid>

          <Grid item xs={12} sm={6} display="flex" alignItems="center">
            <Button
              variant="outlined"
              onClick={handleResetFilters}
              fullWidth
            >
              {t('common.resetFilters')}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {isLoading ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      ) : isError ? (
        <Box textAlign="center" my={4}>
          <Typography color="error">{t('common.errorLoading')}</Typography>
          <Button
            variant="outlined"
            onClick={() => window.location.reload()}
            sx={{ mt: 2 }}
          >
            {t('common.retry')}
          </Button>
        </Box>
      ) : (
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('clients.clientNumber')}</TableCell>
                  <TableCell>{t('clients.taxNumber')}</TableCell>
                  <TableCell>{t('clients.name')}</TableCell>
                  <TableCell>{t('clients.companyName')}</TableCell>
                  <TableCell>{t('clients.phone')}</TableCell>
                  <TableCell>{t('clients.email')}</TableCell>
                  <TableCell align="center">{t('common.actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data?.data.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      {t('clients.noClients')}
                    </TableCell>
                  </TableRow>
                ) : (
                  data?.data.map((client: any) => (
                    <TableRow key={client.id}>
                      <TableCell>{client.clientNumber}</TableCell>
                      <TableCell>{client.taxNumber}</TableCell>
                      <TableCell>{client.name}</TableCell>
                      <TableCell>{client.companyName || '-'}</TableCell>
                      <TableCell>{client.phone || '-'}</TableCell>
                      <TableCell>{client.email || '-'}</TableCell>
                      <TableCell align="center">
                        <Tooltip title={t('common.view')}>
                          <IconButton
                            size="small"
                            onClick={() => handleViewClient(client.id)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.edit')}>
                          <IconButton
                            size="small"
                            onClick={() => handleEditClient(client.id)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.delete')}>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteClient(client.id)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            component="div"
            count={data?.pagination.total || 0}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={limit}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 25, 50]}
            labelRowsPerPage={t('common.rowsPerPage')}
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} ${t('common.of')} ${count}`
            }
          />
        </Paper>
      )}
    </Container>
  );
};

export default ClientsPage;
