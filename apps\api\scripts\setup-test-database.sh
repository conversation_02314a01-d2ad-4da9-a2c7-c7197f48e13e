#!/bin/bash

# 🗄️ سكريبت إعداد قاعدة بيانات الاختبار (SQLite) - مشروع AlnoorArch

echo "🚀 بدء إعداد قاعدة بيانات الاختبار (SQLite)..."

# الانتقال إلى مجلد API
cd "$(dirname "$0")/.." || exit 1

echo "📋 معلومات قاعدة البيانات:"
echo "   النوع: SQLite"
echo "   الملف: test.db"
echo "   المسار: $(pwd)/test.db"

# حذف قاعدة البيانات القديمة إذا كانت موجودة
if [ -f "test.db" ]; then
    echo "🗑️  حذف قاعدة البيانات القديمة..."
    rm test.db
fi

# تعيين متغير البيئة لقاعدة البيانات
export DATABASE_URL="file:./test.db"
export NODE_ENV="test"

echo "✅ متغيرات البيئة جاهزة"

# نسخ schema الاختبار
echo "📋 إعداد Prisma schema للاختبار..."

# نسخ احتياطية من schema الأصلي
if [ ! -f "prisma/schema.prisma.backup" ]; then
    cp prisma/schema.prisma prisma/schema.prisma.backup
    echo "✅ تم إنشاء نسخة احتياطية من schema الأصلي"
fi

# استخدام schema الاختبار
cp prisma/schema.test.prisma prisma/schema.prisma
echo "✅ تم تطبيق schema الاختبار"

# توليد Prisma Client
echo "🔧 توليد Prisma Client..."

npx prisma generate

if [ $? -eq 0 ]; then
    echo "✅ تم توليد Prisma Client بنجاح"
else
    echo "❌ فشل في توليد Prisma Client"
    exit 1
fi

# تشغيل migrations
echo "🔄 إنشاء قاعدة البيانات وتشغيل migrations..."

npx prisma db push

if [ $? -eq 0 ]; then
    echo "✅ تم إنشاء قاعدة البيانات وتشغيل migrations بنجاح"
else
    echo "❌ فشل في إنشاء قاعدة البيانات"
    exit 1
fi

# التحقق من إنشاء قاعدة البيانات
if [ -f "test.db" ]; then
    echo "✅ تم إنشاء ملف قاعدة البيانات test.db"
    echo "📊 حجم الملف: $(du -h test.db | cut -f1)"
else
    echo "❌ لم يتم إنشاء ملف قاعدة البيانات"
    exit 1
fi

# اختبار نهائي
echo "🧪 اختبار نهائي للإعداد..."

# تشغيل اختبار بسيط للتأكد من عمل النظام
npm test -- --testPathPattern="permit.service.test.ts" --silent

if [ $? -eq 0 ]; then
    echo "✅ الاختبار النهائي نجح - النظام جاهز!"
else
    echo "⚠️  الاختبار النهائي فشل - سنحاول مرة أخرى..."

    # محاولة تشغيل جميع الاختبارات Unit
    npm test -- --testPathPattern="service.test.ts" --silent

    if [ $? -eq 0 ]; then
        echo "✅ اختبارات Unit نجحت!"
    else
        echo "⚠️  بعض الاختبارات قد تحتاج مراجعة إضافية"
    fi
fi

# استعادة schema الأصلي
echo "🔄 استعادة schema الأصلي..."
cp prisma/schema.prisma.backup prisma/schema.prisma
npx prisma generate > /dev/null 2>&1

echo ""
echo "🎉 انتهى إعداد قاعدة بيانات الاختبار!"
echo ""
echo "📝 ملاحظات:"
echo "   - قاعدة البيانات: test.db (SQLite)"
echo "   - لتشغيل الاختبارات: npm test"
echo "   - لحذف قاعدة البيانات: rm test.db"
echo "   - schema الأصلي محفوظ في: prisma/schema.prisma.backup"
echo ""
