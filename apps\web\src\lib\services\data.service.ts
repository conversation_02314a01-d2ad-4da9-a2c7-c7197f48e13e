import apiService from './api.service';

/**
 * خدمة البيانات
 * توفر واجهة موحدة للتعامل مع البيانات
 */
class DataService {
  /**
   * الحصول على قائمة العناصر
   * @param endpoint نقطة النهاية
   * @param params معلمات الاستعلام
   * @returns وعد بقائمة العناصر
   */
  public async getList<T>(endpoint: string, params?: Record<string, any>): Promise<T[]> {
    try {
      // إنشاء سلسلة الاستعلام
      const queryString = params ? `?${new URLSearchParams(params).toString()}` : '';
      
      // الحصول على قائمة العناصر
      const response = await apiService.get<T[]>(`${endpoint}${queryString}`);
      
      return response;
    } catch (error) {
      console.error(`Error getting list from ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * الحصول على عنصر واحد
   * @param endpoint نقطة النهاية
   * @param id معرف العنصر
   * @returns وعد بالعنصر
   */
  public async getOne<T>(endpoint: string, id: string): Promise<T> {
    try {
      // الحصول على العنصر
      const response = await apiService.get<T>(`${endpoint}/${id}`);
      
      return response;
    } catch (error) {
      console.error(`Error getting item from ${endpoint}/${id}:`, error);
      throw error;
    }
  }

  /**
   * إنشاء عنصر جديد
   * @param endpoint نقطة النهاية
   * @param data بيانات العنصر
   * @returns وعد بالعنصر الجديد
   */
  public async create<T>(endpoint: string, data: any): Promise<T> {
    try {
      // إنشاء العنصر
      const response = await apiService.post<T>(endpoint, data);
      
      return response;
    } catch (error) {
      console.error(`Error creating item at ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * تحديث عنصر
   * @param endpoint نقطة النهاية
   * @param id معرف العنصر
   * @param data بيانات العنصر
   * @returns وعد بالعنصر المحدث
   */
  public async update<T>(endpoint: string, id: string, data: any): Promise<T> {
    try {
      // تحديث العنصر
      const response = await apiService.put<T>(`${endpoint}/${id}`, data);
      
      return response;
    } catch (error) {
      console.error(`Error updating item at ${endpoint}/${id}:`, error);
      throw error;
    }
  }

  /**
   * حذف عنصر
   * @param endpoint نقطة النهاية
   * @param id معرف العنصر
   * @returns وعد بنتيجة الحذف
   */
  public async delete<T>(endpoint: string, id: string): Promise<T> {
    try {
      // حذف العنصر
      const response = await apiService.delete<T>(`${endpoint}/${id}`);
      
      return response;
    } catch (error) {
      console.error(`Error deleting item at ${endpoint}/${id}:`, error);
      throw error;
    }
  }

  /**
   * تنفيذ إجراء على عنصر
   * @param endpoint نقطة النهاية
   * @param id معرف العنصر
   * @param action الإجراء
   * @param data بيانات الإجراء
   * @returns وعد بنتيجة الإجراء
   */
  public async executeAction<T>(endpoint: string, id: string, action: string, data?: any): Promise<T> {
    try {
      // تنفيذ الإجراء
      const response = await apiService.post<T>(`${endpoint}/${id}/${action}`, data);
      
      return response;
    } catch (error) {
      console.error(`Error executing action ${action} at ${endpoint}/${id}:`, error);
      throw error;
    }
  }
}

// إنشاء نسخة واحدة من الخدمة
const dataService = new DataService();

export default dataService;
