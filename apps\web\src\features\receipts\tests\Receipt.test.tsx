import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { createStore } from '../../../app/store/store';
import ReceiptsPage from '../pages/ReceiptsPage';
import ReceiptFormPage from '../pages/ReceiptFormPage';
import ReceiptDetailsPage from '../pages/ReceiptDetailsPage';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { arSA } from 'date-fns/locale';

// Mock React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

// Mock Redux Store
const store = createStore();

// Mock React Router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ id: '123' }),
    useNavigate: () => vi.fn(),
  };
});

// Mock API Hooks
vi.mock('../hooks/useReceipts', () => ({
  useReceipts: () => ({
    data: {
      data: [
        {
          id: '123',
          receiptNumber: 'R-001',
          receiptDate: '2023-01-01T00:00:00.000Z',
          receiverName: 'Test Receiver',
          receiverPhone: '123456789',
          receiverIdNumber: 'ID123456',
          itemsDescription: 'Test items',
          notes: 'Test notes',
          pdfFile: 'test.pdf',
          declarationId: '456',
          declarationNumber: 123,
          clientName: 'Test Client',
          createdAt: '2023-01-01T00:00:00.000Z',
          updatedAt: '2023-01-01T00:00:00.000Z',
        },
      ],
      pagination: {
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      },
    },
    isLoading: false,
    isError: false,
    refetch: vi.fn(),
  }),
  useReceipt: () => ({
    data: {
      id: '123',
      receiptNumber: 'R-001',
      receiptDate: '2023-01-01T00:00:00.000Z',
      receiverName: 'Test Receiver',
      receiverPhone: '123456789',
      receiverIdNumber: 'ID123456',
      itemsDescription: 'Test items',
      notes: 'Test notes',
      pdfFile: 'test.pdf',
      declarationId: '456',
      declarationNumber: 123,
      clientName: 'Test Client',
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: '2023-01-01T00:00:00.000Z',
    },
    isLoading: false,
    isError: false,
  }),
  useCreateReceipt: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useUpdateReceipt: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useDeleteReceipt: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useDownloadReceiptPdf: () => ({
    mutateAsync: vi.fn().mockResolvedValue(new Blob()),
    isLoading: false,
  }),
}));

// Mock Declarations Hook
vi.mock('../../declarations/hooks/useDeclarations', () => ({
  useDeclarations: () => ({
    data: {
      data: [
        {
          id: '456',
          declarationNumber: 123,
          clientName: 'Test Client',
        },
      ],
      pagination: {
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      },
    },
    isLoading: false,
    isError: false,
  }),
}));

// Mock Toast Hook
vi.mock('../../../lib/hooks/useToast', () => ({
  useToast: () => ({
    showSuccess: vi.fn(),
    showError: vi.fn(),
  }),
}));

// Wrapper Component
const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={arSA}>
        <BrowserRouter>{children}</BrowserRouter>
      </LocalizationProvider>
    </QueryClientProvider>
  </Provider>
);

describe('Receipts Feature', () => {
  describe('ReceiptsPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <ReceiptsPage />
        </Wrapper>
      );
    });

    it('renders the receipts page title', () => {
      expect(screen.getByText('receipts.title')).toBeInTheDocument();
    });

    it('renders the receipts list', () => {
      expect(screen.getByText('R-001')).toBeInTheDocument();
      expect(screen.getByText('Test Receiver')).toBeInTheDocument();
    });

    it('renders the create receipt button', () => {
      expect(screen.getByText('receipts.create')).toBeInTheDocument();
    });
  });

  describe('ReceiptFormPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <ReceiptFormPage />
        </Wrapper>
      );
    });

    it('renders the receipt form', () => {
      expect(screen.getByText('receipts.create')).toBeInTheDocument();
      expect(screen.getByText('receipts.createDescription')).toBeInTheDocument();
    });

    it('renders form fields', () => {
      expect(screen.getByText('receipts.declaration')).toBeInTheDocument();
      expect(screen.getByText('receipts.receiptNumber')).toBeInTheDocument();
      expect(screen.getByText('receipts.receiptDate')).toBeInTheDocument();
      expect(screen.getByText('receipts.receiverName')).toBeInTheDocument();
      expect(screen.getByText('receipts.receiverPhone')).toBeInTheDocument();
      expect(screen.getByText('receipts.receiverIdNumber')).toBeInTheDocument();
      expect(screen.getByText('receipts.itemsDescription')).toBeInTheDocument();
      expect(screen.getByText('receipts.notes')).toBeInTheDocument();
      expect(screen.getByText('common.uploadPdf')).toBeInTheDocument();
    });
  });

  describe('ReceiptDetailsPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <ReceiptDetailsPage />
        </Wrapper>
      );
    });

    it('renders the receipt details', () => {
      expect(screen.getByText('receipts.details')).toBeInTheDocument();
      expect(screen.getByText('receipts.detailsDescription')).toBeInTheDocument();
      expect(screen.getByText('R-001')).toBeInTheDocument();
    });

    it('renders receipt information', () => {
      expect(screen.getByText('receipts.receiptDate')).toBeInTheDocument();
      expect(screen.getByText('receipts.receiverName')).toBeInTheDocument();
      expect(screen.getByText('Test Receiver')).toBeInTheDocument();
      expect(screen.getByText('receipts.receiverPhone')).toBeInTheDocument();
      expect(screen.getByText('123456789')).toBeInTheDocument();
      expect(screen.getByText('receipts.receiverIdNumber')).toBeInTheDocument();
      expect(screen.getByText('ID123456')).toBeInTheDocument();
      expect(screen.getByText('receipts.itemsDescription')).toBeInTheDocument();
      expect(screen.getByText('Test items')).toBeInTheDocument();
      expect(screen.getByText('receipts.notes')).toBeInTheDocument();
      expect(screen.getByText('Test notes')).toBeInTheDocument();
    });

    it('renders action buttons', () => {
      expect(screen.getByText('common.back')).toBeInTheDocument();
      expect(screen.getByText('common.edit')).toBeInTheDocument();
      expect(screen.getByText('common.delete')).toBeInTheDocument();
      expect(screen.getByText('common.downloadPdf')).toBeInTheDocument();
    });
  });
});
