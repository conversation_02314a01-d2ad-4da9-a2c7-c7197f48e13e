import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api/api';
import { CustomForm } from '@/types/global';

interface CreateCustomFormRequest {
  name: string;
  description?: string;
  formType: string;
  isActive: boolean;
  fields: any[];
}

export const useCreateCustomForm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateCustomFormRequest): Promise<CustomForm> => {
      const response = await api.post('/api/custom-forms', data);
      return response as CustomForm;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customForms'] });
    },
  });
};
