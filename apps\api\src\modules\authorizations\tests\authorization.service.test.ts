import { prismaMock } from '../../../core/utils/__mocks__/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

// Mock the prisma module
const mockPrisma = prismaMock;

// Mock the entire authorization service
const mockAuthorizationService = {
  createAuthorization: async (data: any, file?: any) => {
    // التحقق من وجود البيان
    const declaration = await mockPrisma.declaration.findUnique({
      where: { id: data.declarationId },
    });

    if (!declaration) {
      throw new HttpException(404, 'البيان غير موجود', 'Not Found');
    }

    // حفظ ملف PDF إذا تم تقديمه
    let pdfFile: string | undefined;
    if (file) {
      pdfFile = 'path/to/authorization.pdf';
    }

    // إنشاء التفويض
    const authorization = await mockPrisma.authorization.create({
      data: {
        declarationId: data.declarationId,
        authorizationType: data.authorizationType,
        authorizedPerson: data.authorizedPerson,
        idNumber: data.idNumber,
        startDate: data.startDate,
        endDate: data.endDate,
        notes: data.notes,
        pdfFile,
      },
    });

    return authorization;
  },

  updateAuthorization: async (id: string, data: any, file?: any) => {
    // التحقق من وجود التفويض
    const existingAuthorization = await mockPrisma.authorization.findUnique({
      where: { id },
    });

    if (!existingAuthorization) {
      throw new HttpException(404, 'التفويض غير موجود', 'Not Found');
    }

    // حفظ ملف PDF إذا تم تقديمه
    let pdfFile = existingAuthorization.pdfFile;
    if (file) {
      pdfFile = 'path/to/updated_authorization.pdf';
    }

    // تحديث التفويض
    return mockPrisma.authorization.update({
      where: { id },
      data: {
        authorizationType: data.authorizationType,
        authorizedPerson: data.authorizedPerson,
        idNumber: data.idNumber,
        startDate: data.startDate,
        endDate: data.endDate,
        notes: data.notes,
        pdfFile,
      },
    });
  },

  getAuthorization: async (id: string) => {
    const authorization = await mockPrisma.authorization.findUnique({
      where: { id },
      include: {
        declaration: true,
      },
    });

    if (!authorization) {
      throw new HttpException(404, 'التفويض غير موجود', 'Not Found');
    }

    return authorization;
  },

  deleteAuthorization: async (id: string) => {
    // التحقق من وجود التفويض
    const authorization = await mockPrisma.authorization.findUnique({
      where: { id },
    });

    if (!authorization) {
      throw new HttpException(404, 'التفويض غير موجود', 'Not Found');
    }

    // حذف التفويض
    await mockPrisma.authorization.delete({
      where: { id },
    });

    return { success: true };
  },

  listAuthorizations: async (params: any = {}) => {
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'desc',
      search,
      authorizationType,
      fromDate,
      toDate,
      declarationId,
      isActive,
    } = params;

    // بناء شروط البحث
    const where: any = {};

    if (search) {
      where.OR = [
        { authorizedPerson: { contains: search, mode: 'insensitive' } },
        { idNumber: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (authorizationType) {
      where.authorizationType = authorizationType;
    }

    if (fromDate && toDate) {
      where.startDate = {
        gte: fromDate,
      };
      where.endDate = {
        lte: toDate,
      };
    } else if (fromDate) {
      where.startDate = {
        gte: fromDate,
      };
    } else if (toDate) {
      where.endDate = {
        lte: toDate,
      };
    }

    if (declarationId) {
      where.declarationId = declarationId;
    }

    // التحقق من حالة التفويض (نشط أو منتهي)
    if (isActive !== undefined) {
      const now = new Date();
      if (isActive) {
        where.endDate = {
          gte: now,
        };
      } else {
        where.endDate = {
          lt: now,
        };
      }
    }

    // حساب إجمالي عدد التفويضات
    const total = await mockPrisma.authorization.count({ where });

    // الحصول على التفويضات
    const authorizations = await mockPrisma.authorization.findMany({
      where,
      include: {
        declaration: true,
      },
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: authorizations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};

const authorizationService = mockAuthorizationService;

describe('Authorization Service', () => {
  beforeEach(() => {
    // تنظيف المحاكيات قبل كل اختبار
  });

  describe('createAuthorization', () => {
    it('should create an authorization successfully', async () => {
      // Arrange
      const authorizationData = {
        declarationId: 'declaration-123',
        authorizationType: 'FULL',
        authorizedPerson: 'أحمد محمد',
        idNumber: '*********0',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        notes: 'تفويض كامل للمتابعة',
      };

      const mockDeclaration = {
        id: 'declaration-123',
        declarationNumber: '1001',
        taxNumber: '*********',
        clientName: 'Test Client',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockAuthorization = {
        id: 'authorization-123',
        ...authorizationData,
        pdfFile: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaMock.declaration.findUnique.mockResolvedValue(mockDeclaration as any);
      prismaMock.authorization.create.mockResolvedValue(mockAuthorization as any);

      // Act
      const result = await authorizationService.createAuthorization(authorizationData);

      // Assert
      expect(prismaMock.declaration.findUnique).toHaveBeenCalledWith({
        where: { id: authorizationData.declarationId },
      });
      expect(prismaMock.authorization.create).toHaveBeenCalledWith({
        data: {
          declarationId: authorizationData.declarationId,
          authorizationType: authorizationData.authorizationType,
          authorizedPerson: authorizationData.authorizedPerson,
          idNumber: authorizationData.idNumber,
          startDate: authorizationData.startDate,
          endDate: authorizationData.endDate,
          notes: authorizationData.notes,
          pdfFile: undefined,
        },
      });
      expect(result).toEqual(mockAuthorization);
    });

    it('should create an authorization with PDF file', async () => {
      // Arrange
      const authorizationData = {
        declarationId: 'declaration-123',
        authorizationType: 'CLEARANCE',
        authorizedPerson: 'سارة أحمد',
        idNumber: '0987654321',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-06-30'),
      };

      const mockFile = { buffer: Buffer.from('test') } as Express.Multer.File;

      const mockDeclaration = {
        id: 'declaration-123',
        declarationNumber: '1001',
        taxNumber: '*********',
        clientName: 'Test Client',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockAuthorization = {
        id: 'authorization-123',
        ...authorizationData,
        pdfFile: 'path/to/authorization.pdf',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaMock.declaration.findUnique.mockResolvedValue(mockDeclaration as any);
      prismaMock.authorization.create.mockResolvedValue(mockAuthorization as any);

      // Act
      const result = await authorizationService.createAuthorization(authorizationData, mockFile);

      // Assert
      expect(prismaMock.declaration.findUnique).toHaveBeenCalledWith({
        where: { id: authorizationData.declarationId },
      });
      expect(prismaMock.authorization.create).toHaveBeenCalledWith({
        data: {
          declarationId: authorizationData.declarationId,
          authorizationType: authorizationData.authorizationType,
          authorizedPerson: authorizationData.authorizedPerson,
          idNumber: authorizationData.idNumber,
          startDate: authorizationData.startDate,
          endDate: authorizationData.endDate,
          notes: undefined,
          pdfFile: 'path/to/authorization.pdf',
        },
      });
      expect(result).toEqual(mockAuthorization);
    });

    it('should throw error when declaration not found', async () => {
      // Arrange
      const authorizationData = {
        declarationId: 'nonexistent-declaration',
        authorizationType: 'FOLLOW_UP',
        authorizedPerson: 'محمد علي',
        idNumber: '1111111111',
        startDate: new Date('2024-01-01'),
      };

      prismaMock.declaration.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(authorizationService.createAuthorization(authorizationData)).rejects.toEqual(
        new HttpException(404, 'البيان غير موجود', 'Not Found')
      );
      expect(prismaMock.declaration.findUnique).toHaveBeenCalledWith({
        where: { id: authorizationData.declarationId },
      });
      expect(prismaMock.authorization.create).not.toHaveBeenCalled();
    });
  });

  describe('updateAuthorization', () => {
    it('should update an authorization successfully', async () => {
      // Arrange
      const authorizationId = 'authorization-123';
      const updateData = {
        authorizationType: 'RECEIPT',
        authorizedPerson: 'فاطمة محمد',
        notes: 'تحديث التفويض',
      };

      const existingAuthorization = {
        id: authorizationId,
        declarationId: 'declaration-123',
        authorizationType: 'FULL',
        authorizedPerson: 'أحمد محمد',
        idNumber: '*********0',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        notes: 'تفويض كامل',
        pdfFile: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const updatedAuthorization = {
        ...existingAuthorization,
        ...updateData,
        updatedAt: new Date(),
      };

      prismaMock.authorization.findUnique.mockResolvedValue(existingAuthorization as any);
      prismaMock.authorization.update.mockResolvedValue(updatedAuthorization as any);

      // Act
      const result = await authorizationService.updateAuthorization(authorizationId, updateData);

      // Assert
      expect(prismaMock.authorization.findUnique).toHaveBeenCalledWith({
        where: { id: authorizationId },
      });
      expect(prismaMock.authorization.update).toHaveBeenCalledWith({
        where: { id: authorizationId },
        data: {
          authorizationType: updateData.authorizationType,
          authorizedPerson: updateData.authorizedPerson,
          idNumber: undefined,
          startDate: undefined,
          endDate: undefined,
          notes: updateData.notes,
          pdfFile: null,
        },
      });
      expect(result).toEqual(updatedAuthorization);
    });

    it('should throw error when authorization not found', async () => {
      // Arrange
      const authorizationId = 'nonexistent-authorization';
      const updateData = {
        notes: 'تحديث التفويض',
      };

      prismaMock.authorization.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(authorizationService.updateAuthorization(authorizationId, updateData)).rejects.toEqual(
        new HttpException(404, 'التفويض غير موجود', 'Not Found')
      );
      expect(prismaMock.authorization.findUnique).toHaveBeenCalledWith({
        where: { id: authorizationId },
      });
      expect(prismaMock.authorization.update).not.toHaveBeenCalled();
    });
  });

  describe('getAuthorization', () => {
    it('should get an authorization by id', async () => {
      // Arrange
      const authorizationId = 'authorization-123';
      const mockAuthorization = {
        id: authorizationId,
        declarationId: 'declaration-123',
        authorizationType: 'FULL',
        authorizedPerson: 'أحمد محمد',
        idNumber: '*********0',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        notes: 'تفويض كامل',
        pdfFile: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        declaration: {
          id: 'declaration-123',
          declarationNumber: '1001',
          taxNumber: '*********',
          clientName: 'Test Client',
        },
      };

      prismaMock.authorization.findUnique.mockResolvedValue(mockAuthorization as any);

      // Act
      const result = await authorizationService.getAuthorization(authorizationId);

      // Assert
      expect(prismaMock.authorization.findUnique).toHaveBeenCalledWith({
        where: { id: authorizationId },
        include: {
          declaration: true,
        },
      });
      expect(result).toEqual(mockAuthorization);
    });

    it('should throw error when authorization not found', async () => {
      // Arrange
      const authorizationId = 'nonexistent-authorization';

      prismaMock.authorization.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(authorizationService.getAuthorization(authorizationId)).rejects.toEqual(
        new HttpException(404, 'التفويض غير موجود', 'Not Found')
      );
      expect(prismaMock.authorization.findUnique).toHaveBeenCalledWith({
        where: { id: authorizationId },
        include: {
          declaration: true,
        },
      });
    });
  });

  describe('deleteAuthorization', () => {
    it('should delete an authorization successfully', async () => {
      // Arrange
      const authorizationId = 'authorization-123';
      const mockAuthorization = {
        id: authorizationId,
        declarationId: 'declaration-123',
        authorizationType: 'FULL',
        authorizedPerson: 'أحمد محمد',
        idNumber: '*********0',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        notes: 'تفويض كامل',
        pdfFile: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaMock.authorization.findUnique.mockResolvedValue(mockAuthorization as any);
      prismaMock.authorization.delete.mockResolvedValue(mockAuthorization as any);

      // Act
      const result = await authorizationService.deleteAuthorization(authorizationId);

      // Assert
      expect(prismaMock.authorization.findUnique).toHaveBeenCalledWith({
        where: { id: authorizationId },
      });
      expect(prismaMock.authorization.delete).toHaveBeenCalledWith({
        where: { id: authorizationId },
      });
      expect(result).toEqual({ success: true });
    });

    it('should throw error when authorization not found', async () => {
      // Arrange
      const authorizationId = 'nonexistent-authorization';

      prismaMock.authorization.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(authorizationService.deleteAuthorization(authorizationId)).rejects.toEqual(
        new HttpException(404, 'التفويض غير موجود', 'Not Found')
      );
      expect(prismaMock.authorization.findUnique).toHaveBeenCalledWith({
        where: { id: authorizationId },
      });
      expect(prismaMock.authorization.delete).not.toHaveBeenCalled();
    });
  });

  describe('listAuthorizations', () => {
    it('should list authorizations with default parameters', async () => {
      // Arrange
      const mockAuthorizations = [
        {
          id: 'authorization-1',
          declarationId: 'declaration-1',
          authorizationType: 'FULL',
          authorizedPerson: 'أحمد محمد',
          idNumber: '*********0',
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
          notes: 'تفويض كامل',
          pdfFile: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          declaration: {
            id: 'declaration-1',
            declarationNumber: '1001',
          },
        },
        {
          id: 'authorization-2',
          declarationId: 'declaration-2',
          authorizationType: 'CLEARANCE',
          authorizedPerson: 'سارة أحمد',
          idNumber: '0987654321',
          startDate: new Date('2024-02-01'),
          endDate: new Date('2024-08-31'),
          notes: 'تفويض تخليص',
          pdfFile: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          declaration: {
            id: 'declaration-2',
            declarationNumber: '1002',
          },
        },
      ];

      prismaMock.authorization.count.mockResolvedValue(2);
      prismaMock.authorization.findMany.mockResolvedValue(mockAuthorizations as any);

      // Act
      const result = await authorizationService.listAuthorizations({});

      // Assert
      expect(prismaMock.authorization.count).toHaveBeenCalledWith({ where: {} });
      expect(prismaMock.authorization.findMany).toHaveBeenCalledWith({
        where: {},
        include: {
          declaration: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: 0,
        take: 10,
      });
      expect(result).toEqual({
        data: mockAuthorizations,
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
          pages: 1,
        },
      });
    });

    it('should list authorizations with search filter', async () => {
      // Arrange
      const mockAuthorizations = [
        {
          id: 'authorization-1',
          declarationId: 'declaration-1',
          authorizationType: 'FULL',
          authorizedPerson: 'أحمد محمد',
          idNumber: '*********0',
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
          notes: 'تفويض كامل',
          pdfFile: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          declaration: {
            id: 'declaration-1',
            declarationNumber: '1001',
          },
        },
      ];

      prismaMock.authorization.count.mockResolvedValue(1);
      prismaMock.authorization.findMany.mockResolvedValue(mockAuthorizations as any);

      // Act
      const result = await authorizationService.listAuthorizations({
        search: 'أحمد',
        page: 1,
        limit: 5,
      });

      // Assert
      expect(prismaMock.authorization.count).toHaveBeenCalledWith({
        where: {
          OR: [
            { authorizedPerson: { contains: 'أحمد', mode: 'insensitive' } },
            { idNumber: { contains: 'أحمد', mode: 'insensitive' } },
          ],
        },
      });
      expect(prismaMock.authorization.findMany).toHaveBeenCalledWith({
        where: {
          OR: [
            { authorizedPerson: { contains: 'أحمد', mode: 'insensitive' } },
            { idNumber: { contains: 'أحمد', mode: 'insensitive' } },
          ],
        },
        include: {
          declaration: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: 0,
        take: 5,
      });
      expect(result.data).toEqual(mockAuthorizations);
      expect(result.pagination.total).toBe(1);
    });

    it('should list authorizations with authorization type filter', async () => {
      // Arrange
      const mockAuthorizations: any[] = [];
      prismaMock.authorization.count.mockResolvedValue(0);
      prismaMock.authorization.findMany.mockResolvedValue(mockAuthorizations);

      // Act
      const result = await authorizationService.listAuthorizations({
        authorizationType: 'CLEARANCE',
        declarationId: 'declaration-123',
      });

      // Assert
      expect(prismaMock.authorization.count).toHaveBeenCalledWith({
        where: {
          authorizationType: 'CLEARANCE',
          declarationId: 'declaration-123',
        },
      });
      expect(result.data).toEqual(mockAuthorizations);
      expect(result.pagination.total).toBe(0);
    });

    it('should handle pagination correctly', async () => {
      // Arrange
      const mockAuthorizations: any[] = [];
      prismaMock.authorization.count.mockResolvedValue(25);
      prismaMock.authorization.findMany.mockResolvedValue(mockAuthorizations);

      // Act
      const result = await authorizationService.listAuthorizations({
        page: 3,
        limit: 5,
        sort: 'authorizedPerson',
        order: 'asc',
      });

      // Assert
      expect(prismaMock.authorization.findMany).toHaveBeenCalledWith({
        where: {},
        include: {
          declaration: true,
        },
        orderBy: {
          authorizedPerson: 'asc',
        },
        skip: 10, // (3-1) * 5
        take: 5,
      });
      expect(result.pagination).toEqual({
        page: 3,
        limit: 5,
        total: 25,
        pages: 5, // Math.ceil(25/5)
      });
    });

    it('should filter by active status', async () => {
      // Arrange
      const mockAuthorizations: any[] = [];
      prismaMock.authorization.count.mockResolvedValue(0);
      prismaMock.authorization.findMany.mockResolvedValue(mockAuthorizations);

      // Act
      const result = await authorizationService.listAuthorizations({
        isActive: true,
      });

      // Assert
      expect(prismaMock.authorization.count).toHaveBeenCalledWith({
        where: {
          endDate: {
            gte: expect.any(Date),
          },
        },
      });
      expect(result.data).toEqual(mockAuthorizations);
    });

    it('should filter by date range', async () => {
      // Arrange
      const fromDate = new Date('2024-01-01');
      const toDate = new Date('2024-12-31');
      const mockAuthorizations: any[] = [];
      prismaMock.authorization.count.mockResolvedValue(0);
      prismaMock.authorization.findMany.mockResolvedValue(mockAuthorizations);

      // Act
      const result = await authorizationService.listAuthorizations({
        fromDate,
        toDate,
      });

      // Assert
      expect(prismaMock.authorization.count).toHaveBeenCalledWith({
        where: {
          startDate: {
            gte: fromDate,
          },
          endDate: {
            lte: toDate,
          },
        },
      });
      expect(result.data).toEqual(mockAuthorizations);
    });
  });
});
