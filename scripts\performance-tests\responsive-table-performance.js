/**
 * سكريبت اختبار أداء الجدول المتجاوب
 * يستخدم Lighthouse لقياس أداء الجدول المتجاوب على مختلف الأجهزة
 */
const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// تكوين الاختبار
const config = {
  // الصفحات التي سيتم اختبارها
  pages: [
    { name: 'declarations', url: '/declarations', auth: true },
    { name: 'guarantees', url: '/guarantees', auth: true },
    { name: 'receipts', url: '/receipts', auth: true },
    { name: 'documents', url: '/documents', auth: true },
    { name: 'advanced-search', url: '/advanced-search', auth: true },
  ],
  
  // أجهزة المحاكاة
  devices: [
    { name: 'desktop', formFactor: 'desktop', width: 1350, height: 940 },
    { name: 'tablet', formFactor: 'mobile', width: 768, height: 1024 },
    { name: 'mobile', formFactor: 'mobile', width: 360, height: 640 },
  ],
  
  // مجلد النتائج
  outputDir: path.join(__dirname, 'results'),
  
  // عنوان الخادم المحلي
  baseUrl: 'http://localhost:3000',
  
  // بيانات تسجيل الدخول
  auth: {
    username: 'admin',
    password: 'admin123',
  },
};

// إنشاء مجلد النتائج إذا لم يكن موجودًا
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

/**
 * تشغيل اختبار Lighthouse
 * @param {string} url - عنوان URL للصفحة
 * @param {Object} options - خيارات الاختبار
 * @returns {Promise<Object>} - نتائج الاختبار
 */
async function runLighthouse(url, options) {
  // إطلاق Chrome
  const chrome = await chromeLauncher.launch({
    chromeFlags: ['--headless', '--disable-gpu', '--no-sandbox'],
  });
  
  // تكوين Lighthouse
  const lighthouseOptions = {
    logLevel: 'info',
    output: 'json',
    onlyCategories: ['performance', 'accessibility', 'best-practices'],
    port: chrome.port,
    formFactor: options.formFactor,
    screenEmulation: {
      width: options.width,
      height: options.height,
      deviceScaleFactor: options.formFactor === 'desktop' ? 1 : 2,
      mobile: options.formFactor === 'mobile',
    },
  };
  
  // تشغيل الاختبار
  const results = await lighthouse(url, lighthouseOptions);
  
  // إغلاق Chrome
  await chrome.kill();
  
  return results.lhr;
}

/**
 * تسجيل الدخول إلى التطبيق
 * @returns {Promise<string>} - ملف تعريف الارتباط للجلسة
 */
async function login() {
  console.log('تسجيل الدخول إلى التطبيق...');
  
  // تنفيذ طلب تسجيل الدخول باستخدام curl
  const command = `curl -s -c cookies.txt -X POST ${config.baseUrl}/api/auth/login -H "Content-Type: application/json" -d '{"username":"${config.auth.username}","password":"${config.auth.password}"}'`;
  
  try {
    execSync(command);
    console.log('تم تسجيل الدخول بنجاح');
    return fs.readFileSync('cookies.txt', 'utf8');
  } catch (error) {
    console.error('فشل تسجيل الدخول:', error);
    throw error;
  }
}

/**
 * تشغيل اختبارات الأداء
 */
async function runPerformanceTests() {
  console.log('بدء اختبارات أداء الجدول المتجاوب...');
  
  // تسجيل الدخول إذا كان مطلوبًا
  let cookies = '';
  if (config.pages.some(page => page.auth)) {
    cookies = await login();
  }
  
  // نتائج الاختبارات
  const results = {
    timestamp: new Date().toISOString(),
    summary: {},
    details: {},
  };
  
  // تشغيل الاختبارات لكل صفحة وجهاز
  for (const page of config.pages) {
    results.details[page.name] = {};
    
    for (const device of config.devices) {
      console.log(`اختبار ${page.name} على ${device.name}...`);
      
      // تكوين عنوان URL
      const url = `${config.baseUrl}${page.url}`;
      
      try {
        // تشغيل الاختبار
        const result = await runLighthouse(url, device);
        
        // حفظ النتائج
        results.details[page.name][device.name] = {
          performance: result.categories.performance.score * 100,
          accessibility: result.categories.accessibility.score * 100,
          bestPractices: result.categories['best-practices'].score * 100,
          firstContentfulPaint: result.audits['first-contentful-paint'].numericValue,
          largestContentfulPaint: result.audits['largest-contentful-paint'].numericValue,
          totalBlockingTime: result.audits['total-blocking-time'].numericValue,
          cumulativeLayoutShift: result.audits['cumulative-layout-shift'].numericValue,
          speedIndex: result.audits['speed-index'].numericValue,
        };
        
        console.log(`  أداء: ${results.details[page.name][device.name].performance.toFixed(0)}%`);
      } catch (error) {
        console.error(`فشل اختبار ${page.name} على ${device.name}:`, error);
        results.details[page.name][device.name] = { error: error.message };
      }
    }
  }
  
  // حساب ملخص النتائج
  results.summary = calculateSummary(results.details);
  
  // حفظ النتائج
  const outputFile = path.join(config.outputDir, `responsive-table-performance-${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(outputFile, JSON.stringify(results, null, 2));
  
  console.log(`تم حفظ النتائج في ${outputFile}`);
  
  // إنشاء تقرير HTML
  generateHtmlReport(results, outputFile.replace('.json', '.html'));
  
  console.log('اكتملت اختبارات الأداء');
}

/**
 * حساب ملخص النتائج
 * @param {Object} details - تفاصيل النتائج
 * @returns {Object} - ملخص النتائج
 */
function calculateSummary(details) {
  const summary = {
    desktop: { performance: 0, accessibility: 0, bestPractices: 0, count: 0 },
    tablet: { performance: 0, accessibility: 0, bestPractices: 0, count: 0 },
    mobile: { performance: 0, accessibility: 0, bestPractices: 0, count: 0 },
  };
  
  // حساب متوسط النتائج لكل جهاز
  Object.values(details).forEach(pageResults => {
    Object.entries(pageResults).forEach(([device, metrics]) => {
      if (!metrics.error) {
        summary[device].performance += metrics.performance;
        summary[device].accessibility += metrics.accessibility;
        summary[device].bestPractices += metrics.bestPractices;
        summary[device].count++;
      }
    });
  });
  
  // حساب المتوسط
  Object.values(summary).forEach(deviceSummary => {
    if (deviceSummary.count > 0) {
      deviceSummary.performance /= deviceSummary.count;
      deviceSummary.accessibility /= deviceSummary.count;
      deviceSummary.bestPractices /= deviceSummary.count;
    }
  });
  
  return summary;
}

/**
 * إنشاء تقرير HTML
 * @param {Object} results - نتائج الاختبارات
 * @param {string} outputFile - مسار ملف الإخراج
 */
function generateHtmlReport(results, outputFile) {
  // إنشاء محتوى HTML
  let html = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>تقرير أداء الجدول المتجاوب</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; direction: rtl; }
        h1, h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; }
        .good { background-color: #d4edda; }
        .average { background-color: #fff3cd; }
        .poor { background-color: #f8d7da; }
        .summary { margin-bottom: 30px; }
        .details { margin-bottom: 30px; }
      </style>
    </head>
    <body>
      <h1>تقرير أداء الجدول المتجاوب</h1>
      <p>تاريخ الاختبار: ${new Date(results.timestamp).toLocaleString('ar-SA')}</p>
      
      <div class="summary">
        <h2>ملخص النتائج</h2>
        <table>
          <tr>
            <th>الجهاز</th>
            <th>الأداء</th>
            <th>إمكانية الوصول</th>
            <th>أفضل الممارسات</th>
          </tr>
  `;
  
  // إضافة ملخص النتائج
  Object.entries(results.summary).forEach(([device, metrics]) => {
    if (metrics.count > 0) {
      html += `
        <tr>
          <td>${getDeviceName(device)}</td>
          <td class="${getScoreClass(metrics.performance)}">${metrics.performance.toFixed(0)}%</td>
          <td class="${getScoreClass(metrics.accessibility)}">${metrics.accessibility.toFixed(0)}%</td>
          <td class="${getScoreClass(metrics.bestPractices)}">${metrics.bestPractices.toFixed(0)}%</td>
        </tr>
      `;
    }
  });
  
  html += `
        </table>
      </div>
      
      <div class="details">
        <h2>تفاصيل النتائج</h2>
  `;
  
  // إضافة تفاصيل النتائج لكل صفحة
  Object.entries(results.details).forEach(([page, pageResults]) => {
    html += `
        <h3>${getPageName(page)}</h3>
        <table>
          <tr>
            <th>الجهاز</th>
            <th>الأداء</th>
            <th>إمكانية الوصول</th>
            <th>أفضل الممارسات</th>
            <th>FCP (ms)</th>
            <th>LCP (ms)</th>
            <th>TBT (ms)</th>
            <th>CLS</th>
          </tr>
    `;
    
    Object.entries(pageResults).forEach(([device, metrics]) => {
      if (!metrics.error) {
        html += `
          <tr>
            <td>${getDeviceName(device)}</td>
            <td class="${getScoreClass(metrics.performance)}">${metrics.performance.toFixed(0)}%</td>
            <td class="${getScoreClass(metrics.accessibility)}">${metrics.accessibility.toFixed(0)}%</td>
            <td class="${getScoreClass(metrics.bestPractices)}">${metrics.bestPractices.toFixed(0)}%</td>
            <td>${metrics.firstContentfulPaint.toFixed(0)}</td>
            <td>${metrics.largestContentfulPaint.toFixed(0)}</td>
            <td>${metrics.totalBlockingTime.toFixed(0)}</td>
            <td>${metrics.cumulativeLayoutShift.toFixed(3)}</td>
          </tr>
        `;
      } else {
        html += `
          <tr>
            <td>${getDeviceName(device)}</td>
            <td colspan="7" class="poor">خطأ: ${metrics.error}</td>
          </tr>
        `;
      }
    });
    
    html += `
        </table>
    `;
  });
  
  html += `
      </div>
      
      <div class="recommendations">
        <h2>التوصيات</h2>
        <ul>
          <li>تحسين أداء الجدول المتجاوب على الأجهزة المحمولة من خلال تقليل عدد الأعمدة المعروضة في البطاقات.</li>
          <li>تحسين وقت التحميل الأولي من خلال تقليل حجم الصور وتحسين تحميل الصفحات.</li>
          <li>تحسين إمكانية الوصول من خلال إضافة نصوص بديلة للصور وتحسين تباين الألوان.</li>
          <li>تحسين أفضل الممارسات من خلال تحسين أمان الموقع وتحسين استخدام الموارد.</li>
        </ul>
      </div>
    </body>
    </html>
  `;
  
  // حفظ التقرير
  fs.writeFileSync(outputFile, html);
  console.log(`تم إنشاء تقرير HTML في ${outputFile}`);
}

/**
 * الحصول على اسم الجهاز
 * @param {string} device - معرف الجهاز
 * @returns {string} - اسم الجهاز
 */
function getDeviceName(device) {
  const names = {
    desktop: 'سطح المكتب',
    tablet: 'الجهاز اللوحي',
    mobile: 'الهاتف المحمول',
  };
  
  return names[device] || device;
}

/**
 * الحصول على اسم الصفحة
 * @param {string} page - معرف الصفحة
 * @returns {string} - اسم الصفحة
 */
function getPageName(page) {
  const names = {
    declarations: 'البيانات',
    guarantees: 'الضمانات',
    receipts: 'الاستلامات',
    documents: 'المستندات',
    'advanced-search': 'البحث المتقدم',
  };
  
  return names[page] || page;
}

/**
 * الحصول على فئة النتيجة
 * @param {number} score - النتيجة
 * @returns {string} - فئة النتيجة
 */
function getScoreClass(score) {
  if (score >= 90) return 'good';
  if (score >= 50) return 'average';
  return 'poor';
}

// تشغيل الاختبارات
runPerformanceTests().catch(error => {
  console.error('حدث خطأ أثناء تشغيل اختبارات الأداء:', error);
  process.exit(1);
});
