#!/bin/bash

# سكريبت فحص الأمان
# يتحقق من الإعدادات الأمنية ويحذر من المشاكل المحتملة

echo "🔒 بدء فحص الأمان..."

SECURITY_ISSUES=0
WARNINGS=0

# ألوان للإخراج
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

# دالة لطباعة الأخطاء
print_error() {
    echo -e "${RED}❌ $1${NC}"
    ((SECURITY_ISSUES++))
}

# دالة لطباعة التحذيرات
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((WARNINGS++))
}

# دالة لطباعة النجاح
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo "🔍 فحص ملفات البيئة..."

# فحص ملف .env الرئيسي
if [ -f ".env" ]; then
    # فحص كلمة مرور قاعدة البيانات
    if grep -q "POSTGRES_PASSWORD=admin" .env; then
        print_error "كلمة مرور قاعدة البيانات ضعيفة في .env"
    elif grep -q "POSTGRES_PASSWORD=.*" .env; then
        if [ ${#POSTGRES_PASSWORD} -lt 20 ]; then
            print_warning "كلمة مرور قاعدة البيانات قصيرة في .env"
        else
            print_success "كلمة مرور قاعدة البيانات قوية في .env"
        fi
    fi
    
    # فحص مفاتيح JWT
    if grep -q "JWT_SECRET=your-secret-key" .env; then
        print_error "مفتاح JWT افتراضي في .env"
    elif grep -q "JWT_SECRET=.*" .env; then
        JWT_SECRET=$(grep "JWT_SECRET=" .env | cut -d'=' -f2)
        if [ ${#JWT_SECRET} -lt 32 ]; then
            print_warning "مفتاح JWT قصير في .env"
        else
            print_success "مفتاح JWT قوي في .env"
        fi
    fi
    
    # فحص مفتاح التجديد
    if grep -q "JWT_REFRESH_SECRET=your-refresh-secret" .env; then
        print_error "مفتاح تجديد JWT افتراضي في .env"
    elif grep -q "JWT_REFRESH_SECRET=.*" .env; then
        print_success "مفتاح تجديد JWT محدث في .env"
    fi
else
    print_error "ملف .env غير موجود"
fi

# فحص ملف .env للـ API
if [ -f "apps/api/.env" ]; then
    if grep -q "POSTGRES_PASSWORD=admin" apps/api/.env; then
        print_error "كلمة مرور قاعدة البيانات ضعيفة في apps/api/.env"
    else
        print_success "كلمة مرور قاعدة البيانات محدثة في apps/api/.env"
    fi
else
    print_warning "ملف apps/api/.env غير موجود"
fi

# فحص ملف الاختبار
if [ -f "apps/api/.env.test" ]; then
    if grep -q "alnoor_db" apps/api/.env.test; then
        print_error "بيئة الاختبار تستخدم نفس قاعدة بيانات التطوير"
    else
        print_success "بيئة الاختبار تستخدم قاعدة بيانات منفصلة"
    fi
else
    print_warning "ملف apps/api/.env.test غير موجود"
fi

echo ""
echo "🔍 فحص صلاحيات الملفات..."

# فحص صلاحيات ملفات البيئة
for env_file in .env apps/api/.env apps/api/.env.test; do
    if [ -f "$env_file" ]; then
        perms=$(stat -c "%a" "$env_file" 2>/dev/null || stat -f "%A" "$env_file" 2>/dev/null)
        if [ "$perms" = "600" ] || [ "$perms" = "644" ]; then
            print_success "صلاحيات $env_file آمنة ($perms)"
        else
            print_warning "صلاحيات $env_file قد تكون غير آمنة ($perms)"
        fi
    fi
done

echo ""
echo "🔍 فحص التبعيات..."

# فحص التبعيات الأمنية
if command -v npm &> /dev/null; then
    echo "🔍 فحص الثغرات الأمنية في التبعيات..."
    if npm audit --audit-level=high --silent; then
        print_success "لا توجد ثغرات أمنية عالية الخطورة"
    else
        print_warning "توجد ثغرات أمنية في التبعيات - تشغيل npm audit للتفاصيل"
    fi
fi

echo ""
echo "🔍 فحص ملفات Git..."

# التحقق من عدم تتبع ملفات البيئة
if git check-ignore .env >/dev/null 2>&1; then
    print_success "ملف .env متجاهل في Git"
else
    print_error "ملف .env غير متجاهل في Git - خطر أمني!"
fi

# فحص السجلات في Git
if git check-ignore apps/api/logs/ >/dev/null 2>&1; then
    print_success "مجلد السجلات متجاهل في Git"
else
    print_warning "مجلد السجلات قد يكون مُتتبع في Git"
fi

echo ""
echo "📊 ملخص فحص الأمان:"
echo "   🔴 مشاكل أمنية حرجة: $SECURITY_ISSUES"
echo "   🟡 تحذيرات: $WARNINGS"

if [ $SECURITY_ISSUES -eq 0 ] && [ $WARNINGS -eq 0 ]; then
    print_success "جميع الفحوصات الأمنية نجحت!"
    exit 0
elif [ $SECURITY_ISSUES -eq 0 ]; then
    echo -e "${YELLOW}⚠️  توجد تحذيرات أمنية يُنصح بمعالجتها${NC}"
    exit 1
else
    echo -e "${RED}❌ توجد مشاكل أمنية حرجة يجب إصلاحها فوراً!${NC}"
    exit 2
fi
