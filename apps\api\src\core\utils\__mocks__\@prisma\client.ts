// Mock لـ @prisma/client للاختبارات
import { randomUUID } from 'crypto';

// قاعدة بيانات وهمية في الذاكرة للاختبارات
const mockDatabase: { [key: string]: any[] } = {
  user: [],
  declaration: [],
  authorization: [],
  guarantee: [],
  permit: [],
  receipt: [],
  release: [],
  client: [],
  document: [],
  itemMovement: [],
  driver: [],
  session: [],
  invalidatedToken: [],
  loginAttempt: [],
  auditLog: [],
  customForm: [],
  reportTemplate: [],
  systemSettings: [],
  returnableGuarantee: [],
  nonReturnableGuarantee: [],
  officeDocument: [],
};

// تعريف دالة mock بسيطة للاختبارات
const mockFn = (tableName: string) => {
  const fn = (...args: any[]) => {
    const operation = fn.name || 'unknown';

    // عملية create
    if (args.length > 0 && args[0] && args[0].data) {
      const result = {
        id: randomUUID(), // استخدام UUID صحيح بدلاً من mock-id
        ...args[0].data,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // إضافة isActive: true للمستخدمين إذا لم يكن محدد
      if (args[0].data.username && result.isActive === undefined) {
        result.isActive = true;
      }

      // حفظ في قاعدة البيانات الوهمية
      if (!mockDatabase[tableName]) {
        mockDatabase[tableName] = [];
      }
      mockDatabase[tableName].push(result);



      return Promise.resolve(result);
    }

    // عملية findUnique
    if (args.length > 0 && args[0] && args[0].where) {
      const where = args[0].where;
      const table = mockDatabase[tableName] || [];

      // البحث بناءً على المعايير
      const found = table.find((item: any) => {
        return Object.keys(where).every(key => item[key] === where[key]);
      });



      return Promise.resolve(found || null);
    }

    // عملية findMany
    if (args.length === 0 || (args[0] && !args[0].data && !args[0].where)) {
      const table = mockDatabase[tableName] || [];
      return Promise.resolve(table);
    }

    // عمليات أخرى
    return Promise.resolve({
      id: randomUUID(), // استخدام UUID صحيح
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  };

  // إضافة دالة delete منفصلة
  fn.delete = (...args: any[]) => {
    if (args.length > 0 && args[0] && args[0].where) {
      const where = args[0].where;
      const table = mockDatabase[tableName] || [];

      const index = table.findIndex((item: any) => {
        return Object.keys(where).every(key => item[key] === where[key]);
      });

      if (index !== -1) {
        const deletedItem = table[index];
        table.splice(index, 1);
        return Promise.resolve(deletedItem);
      }
    }
    return Promise.resolve(null);
  };

  fn.mockResolvedValue = (value: any) => {
    fn.mockImplementation(() => Promise.resolve(value));
    return fn;
  };
  fn.mockRejectedValue = (error: any) => {
    fn.mockImplementation(() => Promise.reject(error));
    return fn;
  };
  fn.mockImplementation = (impl: any) => {
    Object.assign(fn, impl);
    return fn;
  };
  fn.mockReturnValue = (value: any) => {
    fn.mockImplementation(() => value);
    return fn;
  };
  fn.mockClear = () => fn;
  fn.mockReset = () => fn;
  return fn;
};

export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  MANAGER = 'MANAGER'
}

export enum TokenType {
  ACCESS = 'ACCESS',
  REFRESH = 'REFRESH'
}

export enum DeclarationType {
  IMPORT = 'IMPORT',
  EXPORT = 'EXPORT'
}

export enum GoodsType {
  HUMAN_MEDICINE = 'HUMAN_MEDICINE',
  LABORATORY_SOLUTIONS = 'LABORATORY_SOLUTIONS',
  MEDICAL_SUPPLIES = 'MEDICAL_SUPPLIES',
  SUGAR_STRIPS = 'SUGAR_STRIPS',
  MEDICAL_DEVICES = 'MEDICAL_DEVICES',
  MISCELLANEOUS = 'MISCELLANEOUS'
}

export enum AuthorizationType {
  FOLLOW_UP = 'FOLLOW_UP',
  CLEARANCE = 'CLEARANCE',
  RECEIPT = 'RECEIPT',
  FULL = 'FULL'
}

export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  SAR = 'SAR'
}

export enum GuaranteeStatus {
  ACTIVE = 'ACTIVE',
  RETURNED = 'RETURNED',
  EXPIRED = 'EXPIRED'
}

export enum LoginStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  LOCKED = 'LOCKED',
  SUSPICIOUS = 'SUSPICIOUS'
}

// Mock Prisma namespace
export namespace Prisma {
  export type UserCreateInput = any;
  export type UserUpdateInput = any;
  export type UserWhereInput = any;
  export type UserWhereUniqueInput = any;
  export type UserOrderByInput = any;
  export type UserSelect = any;
  export type UserInclude = any;

  export type DeclarationCreateInput = any;
  export type DeclarationUpdateInput = any;
  export type DeclarationWhereInput = any;
  export type DeclarationWhereUniqueInput = any;
  export type DeclarationOrderByInput = any;
  export type DeclarationSelect = any;
  export type DeclarationInclude = any;

  export type AuthorizationCreateInput = any;
  export type AuthorizationUpdateInput = any;
  export type AuthorizationWhereInput = any;
  export type AuthorizationWhereUniqueInput = any;
  export type AuthorizationOrderByInput = any;
  export type AuthorizationSelect = any;
  export type AuthorizationInclude = any;

  export type GuaranteeCreateInput = any;
  export type GuaranteeUpdateInput = any;
  export type GuaranteeWhereInput = any;
  export type GuaranteeWhereUniqueInput = any;
  export type GuaranteeOrderByInput = any;
  export type GuaranteeSelect = any;
  export type GuaranteeInclude = any;

  export type PermitCreateInput = any;
  export type PermitUpdateInput = any;
  export type PermitWhereInput = any;
  export type PermitWhereUniqueInput = any;
  export type PermitOrderByInput = any;
  export type PermitSelect = any;
  export type PermitInclude = any;

  export type ReceiptCreateInput = any;
  export type ReceiptUpdateInput = any;
  export type ReceiptWhereInput = any;
  export type ReceiptWhereUniqueInput = any;
  export type ReceiptOrderByInput = any;
  export type ReceiptSelect = any;
  export type ReceiptInclude = any;

  export type ReleaseCreateInput = any;
  export type ReleaseUpdateInput = any;
  export type ReleaseWhereInput = any;
  export type ReleaseWhereUniqueInput = any;
  export type ReleaseOrderByInput = any;
  export type ReleaseSelect = any;
  export type ReleaseInclude = any;

  export type ClientCreateInput = any;
  export type ClientUpdateInput = any;
  export type ClientWhereInput = any;
  export type ClientWhereUniqueInput = any;
  export type ClientOrderByInput = any;
  export type ClientSelect = any;
  export type ClientInclude = any;

  export type DocumentCreateInput = any;
  export type DocumentUpdateInput = any;
  export type DocumentWhereInput = any;
  export type DocumentWhereUniqueInput = any;
  export type DocumentOrderByInput = any;
  export type DocumentSelect = any;
  export type DocumentInclude = any;

  export type ItemMovementCreateInput = any;
  export type ItemMovementUpdateInput = any;
  export type ItemMovementWhereInput = any;
  export type ItemMovementWhereUniqueInput = any;
  export type ItemMovementOrderByInput = any;
  export type ItemMovementSelect = any;
  export type ItemMovementInclude = any;

  export type DriverCreateInput = any;
  export type DriverUpdateInput = any;
  export type DriverWhereInput = any;
  export type DriverWhereUniqueInput = any;
  export type DriverOrderByInput = any;
  export type DriverSelect = any;
  export type DriverInclude = any;

  export type SessionCreateInput = any;
  export type SessionUpdateInput = any;
  export type SessionWhereInput = any;
  export type SessionWhereUniqueInput = any;
  export type SessionOrderByInput = any;
  export type SessionSelect = any;
  export type SessionInclude = any;

  export type InvalidatedTokenCreateInput = any;
  export type InvalidatedTokenUpdateInput = any;
  export type InvalidatedTokenWhereInput = any;
  export type InvalidatedTokenWhereUniqueInput = any;
  export type InvalidatedTokenOrderByInput = any;
  export type InvalidatedTokenSelect = any;
  export type InvalidatedTokenInclude = any;

  export type LoginAttemptCreateInput = any;
  export type LoginAttemptUpdateInput = any;
  export type LoginAttemptWhereInput = any;
  export type LoginAttemptWhereUniqueInput = any;
  export type LoginAttemptOrderByInput = any;
  export type LoginAttemptSelect = any;
  export type LoginAttemptInclude = any;

  export type AuditLogCreateInput = any;
  export type AuditLogUpdateInput = any;
  export type AuditLogWhereInput = any;
  export type AuditLogWhereUniqueInput = any;
  export type AuditLogOrderByInput = any;
  export type AuditLogSelect = any;
  export type AuditLogInclude = any;

  export type CustomFormCreateInput = any;
  export type CustomFormUpdateInput = any;
  export type CustomFormWhereInput = any;
  export type CustomFormWhereUniqueInput = any;
  export type CustomFormOrderByInput = any;
  export type CustomFormSelect = any;
  export type CustomFormInclude = any;

  export type ReportTemplateCreateInput = any;
  export type ReportTemplateUpdateInput = any;
  export type ReportTemplateWhereInput = any;
  export type ReportTemplateWhereUniqueInput = any;
  export type ReportTemplateOrderByInput = any;
  export type ReportTemplateSelect = any;
  export type ReportTemplateInclude = any;

  export type SystemSettingsCreateInput = any;
  export type SystemSettingsUpdateInput = any;
  export type SystemSettingsWhereInput = any;
  export type SystemSettingsWhereUniqueInput = any;
  export type SystemSettingsOrderByInput = any;
  export type SystemSettingsSelect = any;
  export type SystemSettingsInclude = any;
}

// دالة لتنظيف قاعدة البيانات الوهمية
export const clearMockDatabase = () => {
  Object.keys(mockDatabase).forEach(key => {
    mockDatabase[key] = [];
  });
};

// Mock PrismaClient
export class PrismaClient {
  user = {
    create: mockFn('user'),
    findMany: mockFn('user'),
    findUnique: mockFn('user'),
    findFirst: mockFn('user'),
    update: mockFn('user'),
    delete: mockFn('user'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.user = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('user'),
    count: mockFn('user'),
  };

  declaration = {
    create: mockFn('declaration'),
    findMany: mockFn('declaration'),
    findUnique: mockFn('declaration'),
    findFirst: mockFn('declaration'),
    update: mockFn('declaration'),
    delete: mockFn('declaration'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.declaration = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('declaration'),
    count: mockFn('declaration'),
  };

  authorization = {
    create: mockFn('authorization'),
    findMany: mockFn('authorization'),
    findUnique: mockFn('authorization'),
    findFirst: mockFn('authorization'),
    update: mockFn('authorization'),
    delete: mockFn('authorization'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.authorization = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('authorization'),
    count: mockFn('authorization'),
  };

  guarantee = {
    create: mockFn('guarantee'),
    findMany: mockFn('guarantee'),
    findUnique: mockFn('guarantee'),
    findFirst: mockFn('guarantee'),
    update: mockFn('guarantee'),
    delete: mockFn('guarantee'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.guarantee = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('guarantee'),
    count: mockFn('guarantee'),
  };

  permit = {
    create: mockFn('permit'),
    findMany: mockFn('permit'),
    findUnique: mockFn('permit'),
    findFirst: mockFn('permit'),
    update: mockFn('permit'),
    delete: mockFn('permit'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.permit = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('permit'),
    count: mockFn('permit'),
  };

  receipt = {
    create: mockFn('receipt'),
    findMany: mockFn('receipt'),
    findUnique: mockFn('receipt'),
    findFirst: mockFn('receipt'),
    update: mockFn('receipt'),
    delete: mockFn('receipt'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.receipt = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('receipt'),
    count: mockFn('receipt'),
  };

  release = {
    create: mockFn('release'),
    findMany: mockFn('release'),
    findUnique: mockFn('release'),
    findFirst: mockFn('release'),
    update: mockFn('release'),
    delete: mockFn('release'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.release = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('release'),
    count: mockFn('release'),
  };

  client = {
    create: mockFn('client'),
    findMany: mockFn('client'),
    findUnique: mockFn('client'),
    findFirst: mockFn('client'),
    update: mockFn('client'),
    delete: mockFn('client'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.client = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('client'),
    count: mockFn('client'),
  };

  document = {
    create: mockFn('document'),
    findMany: mockFn('document'),
    findUnique: mockFn('document'),
    findFirst: mockFn('document'),
    update: mockFn('document'),
    delete: mockFn('document'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.document = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('document'),
    count: mockFn('document'),
  };

  itemMovement = {
    create: mockFn('itemMovement'),
    findMany: mockFn('itemMovement'),
    findUnique: mockFn('itemMovement'),
    findFirst: mockFn('itemMovement'),
    update: mockFn('itemMovement'),
    delete: mockFn('itemMovement'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.itemMovement = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('itemMovement'),
    count: mockFn('itemMovement'),
  };

  driver = {
    create: mockFn('driver'),
    findMany: mockFn('driver'),
    findUnique: mockFn('driver'),
    findFirst: mockFn('driver'),
    update: mockFn('driver'),
    delete: mockFn('driver'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.driver = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('driver'),
    count: mockFn('driver'),
  };

  session = {
    create: mockFn('session'),
    findMany: mockFn('session'),
    findUnique: mockFn('session'),
    findFirst: mockFn('session'),
    update: mockFn('session'),
    delete: mockFn('session'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.session = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('session'),
    count: mockFn('session'),
  };

  invalidatedToken = {
    create: mockFn('invalidatedToken'),
    findMany: mockFn('invalidatedToken'),
    findUnique: mockFn('invalidatedToken'),
    findFirst: mockFn('invalidatedToken'),
    update: mockFn('invalidatedToken'),
    delete: mockFn('invalidatedToken'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.invalidatedToken = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('invalidatedToken'),
    count: mockFn('invalidatedToken'),
  };

  loginAttempt = {
    create: mockFn('loginAttempt'),
    findMany: mockFn('loginAttempt'),
    findUnique: mockFn('loginAttempt'),
    findFirst: mockFn('loginAttempt'),
    update: mockFn('loginAttempt'),
    delete: mockFn('loginAttempt'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.loginAttempt = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('loginAttempt'),
    count: mockFn('loginAttempt'),
  };

  auditLog = {
    create: mockFn('auditLog'),
    findMany: mockFn('auditLog'),
    findUnique: mockFn('auditLog'),
    findFirst: mockFn('auditLog'),
    update: mockFn('auditLog'),
    delete: mockFn('auditLog'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.auditLog = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('auditLog'),
    count: mockFn('auditLog'),
  };

  customForm = {
    create: mockFn('customForm'),
    findMany: mockFn('customForm'),
    findUnique: mockFn('customForm'),
    findFirst: mockFn('customForm'),
    update: mockFn('customForm'),
    delete: mockFn('customForm'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.customForm = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('customForm'),
    count: mockFn('customForm'),
  };

  reportTemplate = {
    create: mockFn('reportTemplate'),
    findMany: mockFn('reportTemplate'),
    findUnique: mockFn('reportTemplate'),
    findFirst: mockFn('reportTemplate'),
    update: mockFn('reportTemplate'),
    delete: mockFn('reportTemplate'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.reportTemplate = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('reportTemplate'),
    count: mockFn('reportTemplate'),
  };

  systemSettings = {
    create: mockFn('systemSettings'),
    findMany: mockFn('systemSettings'),
    findUnique: mockFn('systemSettings'),
    findFirst: mockFn('systemSettings'),
    update: mockFn('systemSettings'),
    delete: mockFn('systemSettings'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.systemSettings = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('systemSettings'),
    count: mockFn('systemSettings'),
  };

  returnableGuarantee = {
    create: mockFn('returnableGuarantee'),
    findMany: mockFn('returnableGuarantee'),
    findUnique: mockFn('returnableGuarantee'),
    findFirst: mockFn('returnableGuarantee'),
    update: mockFn('returnableGuarantee'),
    delete: mockFn('returnableGuarantee'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.returnableGuarantee = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('returnableGuarantee'),
    count: mockFn('returnableGuarantee'),
  };

  nonReturnableGuarantee = {
    create: mockFn('nonReturnableGuarantee'),
    findMany: mockFn('nonReturnableGuarantee'),
    findUnique: mockFn('nonReturnableGuarantee'),
    findFirst: mockFn('nonReturnableGuarantee'),
    update: mockFn('nonReturnableGuarantee'),
    delete: mockFn('nonReturnableGuarantee'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.nonReturnableGuarantee = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('nonReturnableGuarantee'),
    count: mockFn('nonReturnableGuarantee'),
  };

  officeDocument = {
    create: mockFn('officeDocument'),
    findMany: mockFn('officeDocument'),
    findUnique: mockFn('officeDocument'),
    findFirst: mockFn('officeDocument'),
    update: mockFn('officeDocument'),
    delete: mockFn('officeDocument'),
    deleteMany: (() => {
      const fn = () => {
        mockDatabase.officeDocument = [];
        return Promise.resolve({ count: 0 });
      };
      return Object.assign(fn, {
        mockResolvedValue: (value: any) => fn,
        mockRejectedValue: (error: any) => fn,
        mockImplementation: (impl: any) => fn,
        mockReturnValue: (value: any) => fn,
        mockClear: () => fn,
        mockReset: () => fn,
      });
    })(),
    upsert: mockFn('officeDocument'),
    count: mockFn('officeDocument'),
  };

  $connect = (() => {
    const fn = () => Promise.resolve();
    return Object.assign(fn, {
      mockResolvedValue: (value: any) => fn,
      mockRejectedValue: (error: any) => fn,
      mockImplementation: (impl: any) => fn,
      mockReturnValue: (value: any) => fn,
      mockClear: () => fn,
      mockReset: () => fn,
    });
  })();

  $disconnect = (() => {
    const fn = () => Promise.resolve();
    return Object.assign(fn, {
      mockResolvedValue: (value: any) => fn,
      mockRejectedValue: (error: any) => fn,
      mockImplementation: (impl: any) => fn,
      mockReturnValue: (value: any) => fn,
      mockClear: () => fn,
      mockReset: () => fn,
    });
  })();

  $transaction = (() => {
    const fn = (callback: any) => callback(this);
    return Object.assign(fn, {
      mockResolvedValue: (value: any) => fn,
      mockRejectedValue: (error: any) => fn,
      mockImplementation: (impl: any) => fn,
      mockReturnValue: (value: any) => fn,
      mockClear: () => fn,
      mockReset: () => fn,
    });
  })();

  $executeRaw = (() => {
    const fn = () => Promise.resolve(0);
    return Object.assign(fn, {
      mockResolvedValue: (value: any) => fn,
      mockRejectedValue: (error: any) => fn,
      mockImplementation: (impl: any) => fn,
      mockReturnValue: (value: any) => fn,
      mockClear: () => fn,
      mockReset: () => fn,
    });
  })();

  $queryRaw = (() => {
    const fn = () => Promise.resolve([]);
    return Object.assign(fn, {
      mockResolvedValue: (value: any) => fn,
      mockRejectedValue: (error: any) => fn,
      mockImplementation: (impl: any) => fn,
      mockReturnValue: (value: any) => fn,
      mockClear: () => fn,
      mockReset: () => fn,
    });
  })();

  $on = (() => {
    const fn = () => {};
    return Object.assign(fn, {
      mockResolvedValue: (value: any) => fn,
      mockRejectedValue: (error: any) => fn,
      mockImplementation: (impl: any) => fn,
      mockReturnValue: (value: any) => fn,
      mockClear: () => fn,
      mockReset: () => fn,
    });
  })();

  $use = (() => {
    const fn = () => {};
    return Object.assign(fn, {
      mockResolvedValue: (value: any) => fn,
      mockRejectedValue: (error: any) => fn,
      mockImplementation: (impl: any) => fn,
      mockReturnValue: (value: any) => fn,
      mockClear: () => fn,
      mockReset: () => fn,
    });
  })();
}

// تصدير Prisma namespace
export const Prisma = {
  // Prisma enums
  UserRole: UserRole,

  // Prisma types
  validator: (schema: any) => schema,

  // Prisma client extensions
  defineExtension: (extension: any) => extension,

  // Prisma error types
  PrismaClientKnownRequestError: class extends Error {
    code: string;
    constructor(message: string, code: string) {
      super(message);
      this.code = code;
      this.name = 'PrismaClientKnownRequestError';
    }
  },

  PrismaClientUnknownRequestError: class extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'PrismaClientUnknownRequestError';
    }
  },

  PrismaClientRustPanicError: class extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'PrismaClientRustPanicError';
    }
  },

  PrismaClientInitializationError: class extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'PrismaClientInitializationError';
    }
  },

  PrismaClientValidationError: class extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'PrismaClientValidationError';
    }
  },

  // Prisma query types
  SortOrder: {
    asc: 'asc',
    desc: 'desc'
  },

  // Prisma scalar types
  JsonValue: null,
  JsonObject: {},
  JsonArray: [],

  // Prisma field types
  StringFilter: {},
  IntFilter: {},
  DateTimeFilter: {},
  BoolFilter: {},

  // Prisma relation types
  UserWhereInput: {},
  UserOrderByInput: {},
  UserCreateInput: {},
  UserUpdateInput: {},

  // Additional Prisma types for all models
  DeclarationWhereInput: {},
  AuthorizationWhereInput: {},
  GuaranteeWhereInput: {},
  PermitWhereInput: {},
  ReceiptWhereInput: {},
  ReleaseWhereInput: {},
  ClientWhereInput: {},
  DocumentWhereInput: {},
  ItemMovementWhereInput: {},
  DriverWhereInput: {},
  SessionWhereInput: {},
  InvalidatedTokenWhereInput: {},
  LoginAttemptWhereInput: {},
  AuditLogWhereInput: {},
  CustomFormWhereInput: {},
  ReportTemplateWhereInput: {},
  SystemSettingsWhereInput: {},
};

// تصدير افتراضي للـ PrismaClient
export default PrismaClient;
