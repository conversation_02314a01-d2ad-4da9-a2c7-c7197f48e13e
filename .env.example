# إعدادات البيئة
NODE_ENV=production

# إعدادات قاعدة البيانات
# ⚠️ تحذير: يجب تغيير كلمة المرور قبل الاستخدام
POSTGRES_USER=postgres
POSTGRES_PASSWORD=CHANGE_THIS_SECURE_PASSWORD_BEFORE_USE
POSTGRES_DB=alnoor_db
DATABASE_URL="postgresql://postgres:CHANGE_THIS_SECURE_PASSWORD_BEFORE_USE@localhost:5432/alnoor_db"

# إعدادات API
API_PORT=3001
API_URL=https://api.alnoorarch.com

# إعدادات الواجهة الأمامية
VITE_API_URL=https://api.alnoorarch.com

# إعدادات JWT
# ⚠️ تحذير: يجب إنشاء مفاتيح قوية قبل الاستخدام
# استخدم: openssl rand -base64 64
JWT_SECRET=GENERATE_STRONG_JWT_SECRET_HERE
JWT_EXPIRES_IN=1d
JWT_REFRESH_SECRET=GENERATE_STRONG_REFRESH_SECRET_HERE
JWT_REFRESH_EXPIRES_IN=7d

# إعدادات الملفات المرفوعة
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600 # 100MB

# إعدادات CORS
CORS_ORIGIN=https://alnoorarch.com

# إعدادات الأمان
BCRYPT_ROUNDS=12
SESSION_TIMEOUT=3600000 # 1 hour in milliseconds

# إعدادات السجلات
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
