import { Request, Response } from 'express';
import { logger } from '../../../core/utils/logger.js';
import { prisma } from '../../../core/utils/prisma.js';

/**
 * التحقق من حالة الخادم
 * @param req طلب HTTP
 * @param res استجابة HTTP
 */
export const checkHealth = async (req: Request, res: Response) => {
  try {
    // التحقق من اتصال قاعدة البيانات
    const dbStatus = await checkDatabaseConnection();

    // إرجاع حالة الخادم
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: dbStatus,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    });
  } catch (error) {
    logger.error('Health check failed', { error });

    res.status(500).json({
      status: 'error',
      message: 'Health check failed',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * التحقق من اتصال قاعدة البيانات
 * @returns حالة اتصال قاعدة البيانات
 */
const checkDatabaseConnection = async () => {
  try {
    // محاولة تنفيذ استعلام بسيط
    await prisma.$queryRaw`SELECT 1`;

    return {
      status: 'connected',
      message: 'Database connection is healthy',
    };
  } catch (error) {
    logger.error('Database connection check failed', { error });

    return {
      status: 'error',
      message: 'Database connection failed',
    };
  }
};
