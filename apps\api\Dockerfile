# استخدام صورة Node.js الرسمية كصورة أساسية
FROM node:18-alpine AS base

# تثبيت التبعيات
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# نسخ ملفات الإعتماديات
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml* ./
COPY apps/api/package.json ./apps/api/package.json
COPY packages/shared-types/package.json ./packages/shared-types/package.json

# تثبيت الإعتماديات
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# بناء التطبيق
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# بناء التطبيق
RUN npm install -g pnpm
RUN pnpm build:api

# إنشاء صورة للتشغيل
FROM base AS runner
WORKDIR /app

# تثبيت الإعتماديات الإنتاجية فقط
RUN npm install -g pnpm
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=builder /app/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=builder /app/apps/api/package.json ./apps/api/package.json
COPY --from=builder /app/packages/shared-types/package.json ./packages/shared-types/package.json
RUN pnpm install --prod --frozen-lockfile

# نسخ الملفات المبنية
COPY --from=builder /app/apps/api/dist ./apps/api/dist
COPY --from=builder /app/packages/shared-types/dist ./packages/shared-types/dist

# نسخ ملفات Prisma
COPY --from=builder /app/apps/api/prisma ./apps/api/prisma

# إنشاء مجلد للملفات المرفوعة
RUN mkdir -p /app/uploads
VOLUME /app/uploads

# تعريف متغيرات البيئة
ENV NODE_ENV production
ENV PORT 3001

# تعريض المنفذ
EXPOSE 3001

# تشغيل التطبيق
CMD ["node", "apps/api/dist/server.js"]
