{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "isolatedModules": true, "outDir": "dist", "rootDir": "src", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@core/*": ["src/core/*"], "@modules/*": ["src/modules/*"]}, "allowJs": true}, "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}