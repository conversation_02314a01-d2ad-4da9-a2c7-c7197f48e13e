import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Container,
  Typography,
  Divider,
  Alert,
  CircularProgress,
  Pagination,
} from '@mui/material';
import { AdvancedSearchForm } from '../components/AdvancedSearchForm';
import { SearchResults } from '../components/SearchResults';
import { useAdvancedSearch } from '../hooks/useAdvancedSearch';
import { AdvancedSearchFormValues } from '../types/advanced-search.types';
import { AdvancedSearchParams, SearchType } from '../api/advanced-search.api';

/**
 * صفحة البحث المتقدم
 */
const AdvancedSearchPage = () => {
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useState<AdvancedSearchParams>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [hasSearched, setHasSearched] = useState(false);
  
  // استخدام خطاف البحث المتقدم
  const { data, isLoading, isError, error } = useAdvancedSearch({
    ...searchParams,
    page: currentPage,
    limit: 10,
  });
  
  // التعامل مع تقديم نموذج البحث
  const handleSearch = (formValues: AdvancedSearchFormValues) => {
    // تحويل قيم النموذج إلى معلمات البحث
    const params: AdvancedSearchParams = {
      searchType: formValues.searchType,
      keyword: formValues.keyword,
      taxNumber: formValues.taxNumber,
      clientId: formValues.clientId,
      declarationNumber: formValues.declarationNumber !== '' ? formValues.declarationNumber as number : undefined,
      invoiceNumber: formValues.invoiceNumber !== '' ? formValues.invoiceNumber as number : undefined,
      declarationType: formValues.declarationType,
      goodsType: formValues.goodsType,
      fromDate: formValues.fromDate ? formValues.fromDate.toISOString() : undefined,
      toDate: formValues.toDate ? formValues.toDate.toISOString() : undefined,
    };
    
    // تحديث معلمات البحث وإعادة تعيين الصفحة الحالية
    setSearchParams(params);
    setCurrentPage(1);
    setHasSearched(true);
  };
  
  // التعامل مع تغيير الصفحة
  const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
    setCurrentPage(page);
  };
  
  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('advancedSearch.title')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('advancedSearch.description')}
        </Typography>
      </Box>
      
      <Box mb={4}>
        <AdvancedSearchForm onSubmit={handleSearch} isLoading={isLoading} />
      </Box>
      
      {isError && (
        <Alert severity="error" sx={{ mb: 4 }}>
          {error instanceof Error ? error.message : t('common.errorOccurred')}
        </Alert>
      )}
      
      {isLoading && (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress />
        </Box>
      )}
      
      {hasSearched && !isLoading && data && (
        <Box>
          <SearchResults
            results={data.data}
            searchType={searchParams.searchType || SearchType.ALL}
          />
          
          {data.pagination.total > 0 && (
            <Box display="flex" justifyContent="center" mt={4}>
              <Pagination
                count={data.pagination.pages}
                page={currentPage}
                onChange={handlePageChange}
                color="primary"
                showFirstButton
                showLastButton
              />
            </Box>
          )}
        </Box>
      )}
    </Container>
  );
};

export default AdvancedSearchPage;
