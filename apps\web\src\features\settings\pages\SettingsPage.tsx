import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { LazyImage } from '../../../components';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Switch,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import {
  Save as SaveIcon,
  Upload as UploadIcon,
  Palette as PaletteIcon,
  Business as BusinessIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useSettings, useUpdateSettings } from '../hooks/useSettings';
import { SettingsFormValues } from '../types/settings.types';

// مخطط التحقق من صحة نموذج الإعدادات
const settingsSchema = z.object({
  companyName: z.string().min(1, 'اسم الشركة مطلوب'),
  companyAddress: z.string().optional(),
  companyPhone: z.string().optional(),
  companyEmail: z.string().email('البريد الإلكتروني غير صالح').optional().or(z.literal('')),
  companyWebsite: z.string().url('رابط الموقع غير صالح').optional().or(z.literal('')),
  primaryColor: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'اللون غير صالح'),
  secondaryColor: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'اللون غير صالح'),
  defaultFont: z.string(),
  defaultLanguage: z.string(),
  maxFileSize: z.number().min(1, 'الحجم يجب أن يكون أكبر من 0'),
  enablePrinting: z.boolean(),
});

// واجهة خصائص تبويب
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// مكون تبويب
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const SettingsPage = () => {
  const { t, i18n } = useTranslation();
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);

  // استخدام خطافات الإعدادات
  const { data: settings, isLoading } = useSettings();
  const updateMutation = useUpdateSettings();

  // التعامل مع تغيير التبويب
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<SettingsFormValues>({
    resolver: zodResolver(settingsSchema),
    defaultValues: {
      companyName: '',
      companyAddress: '',
      companyPhone: '',
      companyEmail: '',
      companyWebsite: '',
      primaryColor: '#1976d2',
      secondaryColor: '#dc004e',
      defaultFont: 'Tajawal',
      defaultLanguage: 'ar',
      maxFileSize: *********, // 100MB
      enablePrinting: true,
    },
  });

  // تحديث قيم النموذج عند تحميل الإعدادات
  useEffect(() => {
    if (settings) {
      reset({
        companyName: settings.companyName,
        companyAddress: settings.companyAddress || '',
        companyPhone: settings.companyPhone || '',
        companyEmail: settings.companyEmail || '',
        companyWebsite: settings.companyWebsite || '',
        primaryColor: settings.primaryColor,
        secondaryColor: settings.secondaryColor,
        defaultFont: settings.defaultFont,
        defaultLanguage: settings.defaultLanguage,
        maxFileSize: settings.maxFileSize,
        enablePrinting: settings.enablePrinting,
      });

      if (settings.companyLogo) {
        setLogoPreview(`${settings.companyLogo}`);
      }
    }
  }, [settings, reset]);

  // التعامل مع تغيير الشعار
  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setLogoFile(file);
      setLogoPreview(URL.createObjectURL(file));
    }
  };

  // التعامل مع تقديم النموذج
  const onSubmit = async (data: SettingsFormValues) => {
    try {
      await updateMutation.mutateAsync({
        data: {
          companyName: data.companyName,
          companyAddress: data.companyAddress,
          companyPhone: data.companyPhone,
          companyEmail: data.companyEmail,
          companyWebsite: data.companyWebsite,
          primaryColor: data.primaryColor,
          secondaryColor: data.secondaryColor,
          defaultFont: data.defaultFont,
          defaultLanguage: data.defaultLanguage,
          maxFileSize: data.maxFileSize,
          enablePrinting: data.enablePrinting,
        },
        file: logoFile || undefined,
      });

      // تحديث لغة التطبيق إذا تم تغييرها
      if (data.defaultLanguage !== i18n.language) {
        i18n.changeLanguage(data.defaultLanguage);
      }

      // تطبيق الألوان والخط على CSS
      document.documentElement.style.setProperty('--primary-color', data.primaryColor);
      document.documentElement.style.setProperty('--secondary-color', data.secondaryColor);
      document.body.style.fontFamily = `'${data.defaultFont}', 'Arial', sans-serif`;
    } catch (error) {
      console.error('Error updating settings:', error);
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('settings.title')}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <form onSubmit={handleSubmit(onSubmit)}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="settings tabs">
              <Tab label={t('settings.companyInfo')} icon={<BusinessIcon />} iconPosition="start" />
              <Tab label={t('settings.appearance')} icon={<PaletteIcon />} iconPosition="start" />
              <Tab label={t('settings.systemSettings')} icon={<SettingsIcon />} iconPosition="start" />
            </Tabs>
          </Box>

          {/* تبويب معلومات الشركة */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              {/* اسم الشركة */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="companyName"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('settings.companyName')}
                      fullWidth
                      error={!!errors.companyName}
                      helperText={errors.companyName?.message}
                    />
                  )}
                />
              </Grid>

              {/* عنوان الشركة */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="companyAddress"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('settings.companyAddress')}
                      fullWidth
                      error={!!errors.companyAddress}
                      helperText={errors.companyAddress?.message}
                    />
                  )}
                />
              </Grid>

              {/* هاتف الشركة */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="companyPhone"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('settings.companyPhone')}
                      fullWidth
                      error={!!errors.companyPhone}
                      helperText={errors.companyPhone?.message}
                    />
                  )}
                />
              </Grid>

              {/* بريد الشركة الإلكتروني */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="companyEmail"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('settings.companyEmail')}
                      fullWidth
                      error={!!errors.companyEmail}
                      helperText={errors.companyEmail?.message}
                    />
                  )}
                />
              </Grid>

              {/* موقع الشركة */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="companyWebsite"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('settings.companyWebsite')}
                      fullWidth
                      error={!!errors.companyWebsite}
                      helperText={errors.companyWebsite?.message}
                    />
                  )}
                />
              </Grid>

              {/* شعار الشركة */}
              <Grid item xs={12} md={6}>
                <Box>
                  <Typography variant="subtitle1" gutterBottom>
                    {t('settings.companyLogo')}
                  </Typography>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: 2,
                    }}
                  >
                    {logoPreview && (
                      <LazyImage
                        src={logoPreview}
                        alt="Company Logo"
                        width={150}
                        height={150}
                        sx={{
                          objectFit: 'contain',
                          border: '1px solid #ddd',
                          borderRadius: 1,
                        }}
                      />
                    )}
                    <Button
                      variant="outlined"
                      component="label"
                      startIcon={<UploadIcon />}
                    >
                      {t('common.upload')}
                      <input
                        type="file"
                        hidden
                        accept="image/*"
                        onChange={handleLogoChange}
                      />
                    </Button>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </TabPanel>

          {/* تبويب المظهر */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              {/* اللون الرئيسي */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="primaryColor"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('settings.primaryColor')}
                      fullWidth
                      type="color"
                      error={!!errors.primaryColor}
                      helperText={errors.primaryColor?.message}
                      InputLabelProps={{ shrink: true }}
                    />
                  )}
                />
              </Grid>

              {/* اللون الثانوي */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="secondaryColor"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('settings.secondaryColor')}
                      fullWidth
                      type="color"
                      error={!!errors.secondaryColor}
                      helperText={errors.secondaryColor?.message}
                      InputLabelProps={{ shrink: true }}
                    />
                  )}
                />
              </Grid>

              {/* الخط الافتراضي */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="defaultFont"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.defaultFont}>
                      <InputLabel>{t('settings.defaultFont')}</InputLabel>
                      <Select {...field} label={t('settings.defaultFont')}>
                        <MenuItem value="Tajawal">Tajawal</MenuItem>
                        <MenuItem value="Cairo">Cairo</MenuItem>
                        <MenuItem value="Amiri">Amiri</MenuItem>
                      </Select>
                      {errors.defaultFont && (
                        <FormHelperText>{errors.defaultFont.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              {/* اللغة الافتراضية */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="defaultLanguage"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.defaultLanguage}>
                      <InputLabel>{t('settings.defaultLanguage')}</InputLabel>
                      <Select {...field} label={t('settings.defaultLanguage')}>
                        <MenuItem value="ar">العربية</MenuItem>
                        <MenuItem value="en">English</MenuItem>
                      </Select>
                      {errors.defaultLanguage && (
                        <FormHelperText>{errors.defaultLanguage.message}</FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* تبويب إعدادات النظام */}
          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              {/* الحد الأقصى لحجم الملف */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="maxFileSize"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('settings.maxFileSize')}
                      fullWidth
                      type="number"
                      error={!!errors.maxFileSize}
                      helperText={errors.maxFileSize?.message || t('settings.maxFileSizeHelp')}
                      InputProps={{
                        endAdornment: <Typography variant="caption">بايت</Typography>,
                      }}
                    />
                  )}
                />
              </Grid>

              {/* تفعيل الطباعة */}
              <Grid item xs={12} md={6}>
                <Controller
                  name="enablePrinting"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Switch
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                        />
                      }
                      label={t('settings.enablePrinting')}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* زر الحفظ */}
          <Box display="flex" justifyContent="flex-end" mt={3}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              disabled={updateMutation.isPending}
            >
              {updateMutation.isPending ? (
                <CircularProgress size={24} />
              ) : (
                t('common.save')
              )}
            </Button>
          </Box>
        </form>
      </Paper>
    </Container>
  );
};

export default SettingsPage;
