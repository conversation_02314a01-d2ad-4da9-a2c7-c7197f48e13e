import React from 'react';
import { Box, Paper, Typography } from '@mui/material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { ReportType } from '../types/report-type';

interface ReportPreviewProps {
  data?: any;
  columns: any[];
  type: ReportType;
  title: string;
  description?: string;
  chart?: any;
}

/**
 * مكون معاينة التقرير
 * يعرض معاينة للتقرير بناءً على نوعه
 */
export const ReportPreview: React.FC<ReportPreviewProps> = ({
  data = [],
  columns = [],
  type = ReportType.TABLE,
  title,
  description,
  chart,
}) => {
  // تحويل الأعمدة إلى تنسيق DataGrid
  const gridColumns: GridColDef[] = columns.map((col) => ({
    field: col.name,
    headerName: col.label,
    width: col.width || 150,
    sortable: col.sortable !== false,
    filterable: col.filterable !== false,
  }));

  // بيانات وهمية للعرض
  const sampleData = Array.from({ length: 5 }, (_, index) => {
    const row: Record<string, any> = { id: index };
    columns.forEach((col) => {
      if (col.type === 'number') {
        row[col.name] = Math.floor(Math.random() * 1000);
      } else if (col.type === 'date') {
        row[col.name] = new Date().toISOString().split('T')[0];
      } else if (col.type === 'boolean') {
        row[col.name] = Math.random() > 0.5;
      } else {
        row[col.name] = `قيمة ${col.name} ${index + 1}`;
      }
    });
    return row;
  });

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        {title}
      </Typography>
      {description && (
        <Typography variant="body2" color="text.secondary" gutterBottom>
          {description}
        </Typography>
      )}

      <Box sx={{ height: 400, width: '100%' }}>
        {type === ReportType.TABLE && (
          <DataGrid
            rows={data.length > 0 ? data : sampleData}
            columns={gridColumns}
            pageSizeOptions={[5, 10, 25]}
            initialState={{
              pagination: {
                paginationModel: { pageSize: 5, page: 0 },
              },
            }}
            disableRowSelectionOnClick
          />
        )}

        {type !== ReportType.TABLE && (
          <Box
            sx={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '1px dashed #ccc',
              borderRadius: 1,
            }}
          >
            <Typography variant="body1" color="text.secondary">
              معاينة الرسم البياني غير متاحة حاليًا
            </Typography>
          </Box>
        )}
      </Box>
    </Paper>
  );
};
