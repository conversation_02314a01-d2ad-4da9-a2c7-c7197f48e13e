#!/usr/bin/env node

/**
 * سكريبت لحل merge conflicts تلقائياً
 * يختار النسخة HEAD في جميع الحالات
 */

const fs = require('fs');
const path = require('path');

// قائمة الملفات التي تحتوي على merge conflicts
const conflictFiles = [
  'src/modules/advanced-search/services/advanced-search.service.ts',
  'src/modules/auth/controllers/auth.controller.ts',
  'src/modules/auth/services/auth.service.ts',
  'src/modules/items-movement/tests/item-movement.service.test.ts'
];

/**
 * حل merge conflicts في ملف واحد
 */
function resolveConflictsInFile(filePath) {
  console.log(`حل merge conflicts في: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`الملف غير موجود: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let hasConflicts = false;

  // البحث عن merge conflicts وحلها
  const conflictPattern = /<<<<<<< HEAD\n([\s\S]*?)\n=======\n([\s\S]*?)\n>>>>>>> [a-f0-9]+/g;
  
  content = content.replace(conflictPattern, (match, headContent, branchContent) => {
    hasConflicts = true;
    console.log(`  - تم العثور على conflict، اختيار HEAD version`);
    return headContent;
  });

  if (hasConflicts) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  ✅ تم حل conflicts في ${filePath}`);
    return true;
  } else {
    console.log(`  ℹ️  لا توجد conflicts في ${filePath}`);
    return false;
  }
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔧 بدء حل merge conflicts...\n');
  
  let totalResolved = 0;
  
  conflictFiles.forEach(file => {
    if (resolveConflictsInFile(file)) {
      totalResolved++;
    }
    console.log(''); // سطر فارغ
  });
  
  console.log(`\n✅ تم حل conflicts في ${totalResolved} ملف من أصل ${conflictFiles.length}`);
  
  if (totalResolved > 0) {
    console.log('\n📝 يُنصح بتشغيل الاختبارات للتأكد من عمل الكود بشكل صحيح:');
    console.log('npm run test');
    console.log('npm run build');
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = { resolveConflictsInFile };
