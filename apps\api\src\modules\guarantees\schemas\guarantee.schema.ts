import { z } from 'zod';

// مخطط إنشاء ضمان جديد
export const createGuaranteeSchema = z.object({
  body: z.object({
    declarationId: z.string({
      required_error: 'معرف البيان مطلوب',
    }).uuid(),
    guaranteeType: z.enum(['RETURNABLE', 'NON_RETURNABLE'], {
      required_error: 'نوع الضمان مطلوب',
    }),
    guaranteeNature: z.enum(['DOCUMENTS', 'FINANCIAL'], {
      required_error: 'طبيعة الضمان مطلوبة',
    }),
    guaranteeNumber: z.string({
      required_error: 'رقم الضمان مطلوب',
    }),
    issueDate: z.string({
      required_error: 'تاريخ الإصدار مطلوب',
    }).transform(val => new Date(val)),
    expiryDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    amount: z.number({
      required_error: 'مبلغ الضمان مطلوب',
    }),
    currency: z.enum(['USD', 'EUR', 'GBP', 'SAR'], {
      required_error: 'عملة الضمان مطلوبة',
    }),
    notes: z.string().optional(),
  }),
});

// مخطط تحديث ضمان
export const updateGuaranteeSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف الضمان مطلوب',
    }),
  }),
  body: z.object({
    declarationId: z.string().uuid().optional(),
    guaranteeType: z.enum(['RETURNABLE', 'NON_RETURNABLE']).optional(),
    guaranteeNature: z.enum(['DOCUMENTS', 'FINANCIAL']).optional(),
    guaranteeNumber: z.string().optional(),
    issueDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    expiryDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    amount: z.number().optional(),
    currency: z.enum(['USD', 'EUR', 'GBP', 'SAR']).optional(),
    notes: z.string().optional(),
    returnDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    isReturned: z.boolean().optional(),
  }),
});

// مخطط الحصول على ضمان محدد
export const getGuaranteeSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف الضمان مطلوب',
    }),
  }),
});

// مخطط حذف ضمان
export const deleteGuaranteeSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف الضمان مطلوب',
    }),
  }),
});

// مخطط قائمة الضمانات
export const listGuaranteesSchema = z.object({
  query: z.object({
    page: z.string().optional().transform((val) => (val ? parseInt(val) : 1)),
    limit: z.string().optional().transform((val) => (val ? parseInt(val) : 10)),
    sort: z.string().optional().default('createdAt'),
    order: z.enum(['asc', 'desc']).optional().default('desc'),
    search: z.string().optional(),
    declarationId: z.string().uuid().optional(),
    guaranteeType: z.enum(['RETURNABLE', 'NON_RETURNABLE']).optional(),
    guaranteeNature: z.enum(['DOCUMENTS', 'FINANCIAL']).optional(),
    fromDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    toDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    isReturned: z.string().optional().transform((val) => {
      if (val === 'true') return true;
      if (val === 'false') return false;
      return undefined;
    }),
    isActive: z.string().optional().transform((val) => {
      if (val === 'true') return true;
      if (val === 'false') return false;
      return undefined;
    }),
  }),
});
