import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  Typography,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import { useRelease, useDeleteRelease, useDownloadReleasePdf } from '../hooks/useReleases';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const ReleaseDetailsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // استخدام خطافات الإفراج
  const { data: release, isLoading, isError } = useRelease(id || '');
  const deleteMutation = useDeleteRelease();
  const downloadPdfMutation = useDownloadReleasePdf();

  // التعامل مع تعديل الإفراج
  const handleEdit = () => {
    navigate(`/releases/${id}/edit`);
  };

  // التعامل مع حذف الإفراج
  const handleDelete = async () => {
    if (window.confirm(t('releases.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id || '');
        navigate('/releases');
      } catch (error) {
        console.error('Error deleting release:', error);
      }
    }
  };

  // التعامل مع العودة إلى قائمة الإفراجات
  const handleBack = () => {
    navigate('/releases');
  };

  // التعامل مع عرض ملف PDF
  const handleViewPdf = () => {
    if (release?.pdfFile) {
      downloadPdfMutation.mutate(id || '');
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (isError || !release) {
    return (
      <Container maxWidth="md">
        <Box textAlign="center" my={4}>
          <Typography variant="h5" color="error" gutterBottom>
            {t('common.errorLoading')}
          </Typography>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            {t('common.backToList')}
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('releases.details')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('releases.detailsDescription')}
        </Typography>
      </Box>

      <Box mb={3} display="flex" justifyContent="flex-end">
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{ mr: 1 }}
        >
          {t('common.back')}
        </Button>
        <Button
          variant="outlined"
          startIcon={<EditIcon />}
          onClick={handleEdit}
          sx={{ mr: 1 }}
        >
          {t('common.edit')}
        </Button>
        <Button
          variant="outlined"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={handleDelete}
        >
          {t('common.delete')}
        </Button>
      </Box>

      <Paper>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('releases.number')}
              </Typography>
              <Typography variant="body1">
                {release.releaseNumber || '-'}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('releases.type')}
              </Typography>
              <Chip
                label={t(`releases.types.${release.releaseType}`)}
                color={release.releaseType === 'PERMANENT' ? 'primary' : 'default'}
                size="small"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('releases.startDate')}
              </Typography>
              <Typography variant="body1">
                {format(new Date(release.startDate), 'yyyy-MM-dd', { locale: arSA })}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('releases.endDate')}
              </Typography>
              <Typography variant="body1">
                {format(new Date(release.endDate), 'yyyy-MM-dd', { locale: arSA })}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('releases.declaration')}
              </Typography>
              <Typography variant="body1">
                {release.declaration?.declarationNumber || '-'}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('releases.client')}
              </Typography>
              <Typography variant="body1">
                {release.client?.name || release.client?.companyName || '-'}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('releases.notes')}
              </Typography>
              <Typography variant="body1">
                {release.notes || '-'}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" color="textSecondary">
                {t('common.createdBy')}
              </Typography>
              <Typography variant="body1">
                {release.createdBy?.name || '-'}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('common.createdAt')}
              </Typography>
              <Typography variant="body1">
                {format(new Date(release.createdAt), 'yyyy-MM-dd HH:mm', { locale: arSA })}
              </Typography>
            </Grid>

            {release.pdfFile && (
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Box display="flex" alignItems="center">
                  <Button
                    variant="outlined"
                    startIcon={<PdfIcon />}
                    onClick={handleViewPdf}
                  >
                    {t('common.viewPdf')}
                  </Button>
                </Box>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Paper>
    </Container>
  );
};

export default ReleaseDetailsPage;
