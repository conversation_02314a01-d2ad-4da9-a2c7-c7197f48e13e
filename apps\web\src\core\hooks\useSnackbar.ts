// 🔔 Hook للإشعارات - مشروع AlnoorArch

import { useState, useCallback } from 'react';

export type SnackbarSeverity = 'success' | 'error' | 'warning' | 'info';

export interface SnackbarState {
  open: boolean;
  message: string;
  severity: SnackbarSeverity;
  autoHideDuration?: number;
}

/**
 * Hook لإدارة حالة الإشعارات
 */
export const useSnackbar = () => {
  const [snackbarState, setSnackbarState] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info',
    autoHideDuration: 6000,
  });

  /**
   * إظهار إشعار
   */
  const showSnackbar = useCallback((
    message: string,
    severity: SnackbarSeverity = 'info',
    autoHideDuration: number = 6000
  ) => {
    setSnackbarState({
      open: true,
      message,
      severity,
      autoHideDuration,
    });
  }, []);

  /**
   * إخفاء الإشعار
   */
  const hideSnackbar = useCallback(() => {
    setSnackbarState(prev => ({ ...prev, open: false }));
  }, []);

  /**
   * إظهار إشعار نجاح
   */
  const showSuccess = useCallback((message: string, autoHideDuration?: number) => {
    showSnackbar(message, 'success', autoHideDuration);
  }, [showSnackbar]);

  /**
   * إظهار إشعار خطأ
   */
  const showError = useCallback((message: string, autoHideDuration?: number) => {
    showSnackbar(message, 'error', autoHideDuration);
  }, [showSnackbar]);

  /**
   * إظهار إشعار تحذير
   */
  const showWarning = useCallback((message: string, autoHideDuration?: number) => {
    showSnackbar(message, 'warning', autoHideDuration);
  }, [showSnackbar]);

  /**
   * إظهار إشعار معلومات
   */
  const showInfo = useCallback((message: string, autoHideDuration?: number) => {
    showSnackbar(message, 'info', autoHideDuration);
  }, [showSnackbar]);

  /**
   * إظهار إشعار نجاح العملية
   */
  const showOperationSuccess = useCallback((operation: string) => {
    showSuccess(`تم ${operation} بنجاح`);
  }, [showSuccess]);

  /**
   * إظهار إشعار فشل العملية
   */
  const showOperationError = useCallback((operation: string, error?: string) => {
    const message = error ? `فشل في ${operation}: ${error}` : `فشل في ${operation}`;
    showError(message);
  }, [showError]);

  /**
   * إظهار إشعار حفظ ناجح
   */
  const showSaveSuccess = useCallback(() => {
    showSuccess('تم الحفظ بنجاح');
  }, [showSuccess]);

  /**
   * إظهار إشعار حذف ناجح
   */
  const showDeleteSuccess = useCallback(() => {
    showSuccess('تم الحذف بنجاح');
  }, [showSuccess]);

  /**
   * إظهار إشعار تحديث ناجح
   */
  const showUpdateSuccess = useCallback(() => {
    showSuccess('تم التحديث بنجاح');
  }, [showSuccess]);

  /**
   * إظهار إشعار إنشاء ناجح
   */
  const showCreateSuccess = useCallback(() => {
    showSuccess('تم الإنشاء بنجاح');
  }, [showSuccess]);

  /**
   * إظهار إشعار تحميل فاشل
   */
  const showLoadError = useCallback((error?: string) => {
    const message = error ? `فشل في تحميل البيانات: ${error}` : 'فشل في تحميل البيانات';
    showError(message);
  }, [showError]);

  /**
   * إظهار إشعار اتصال فاشل
   */
  const showConnectionError = useCallback(() => {
    showError('فشل في الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت.');
  }, [showError]);

  /**
   * إظهار إشعار عدم وجود صلاحية
   */
  const showUnauthorizedError = useCallback(() => {
    showError('ليس لديك صلاحية للوصول إلى هذا المورد.');
  }, [showError]);

  /**
   * إظهار إشعار انتهاء الجلسة
   */
  const showSessionExpired = useCallback(() => {
    showWarning('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
  }, [showWarning]);

  /**
   * إظهار إشعار تحديث متاح
   */
  const showUpdateAvailable = useCallback(() => {
    showInfo('يتوفر تحديث جديد للتطبيق.');
  }, [showInfo]);

  return {
    snackbarState,
    showSnackbar,
    hideSnackbar,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showOperationSuccess,
    showOperationError,
    showSaveSuccess,
    showDeleteSuccess,
    showUpdateSuccess,
    showCreateSuccess,
    showLoadError,
    showConnectionError,
    showUnauthorizedError,
    showSessionExpired,
    showUpdateAvailable,
  };
};
