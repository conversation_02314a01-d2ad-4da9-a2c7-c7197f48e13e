import { z } from 'zod';

// مخطط إنشاء عميل جديد
export const createClientSchema = z.object({
  body: z.object({
    name: z.string({
      required_error: 'اسم العميل مطلوب',
    }),
    taxNumber: z.string({
      required_error: 'الرقم الضريبي مطلوب',
    }),
    phone: z.string().optional(),
    email: z.string().email().optional(),
    address: z.string().optional(),
  }),
});

// مخطط تحديث عميل
export const updateClientSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف العميل مطلوب',
    }),
  }),
  body: z.object({
    name: z.string().optional(),
    taxNumber: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().email().optional(),
    address: z.string().optional(),
  }),
});

// مخطط الحصول على عميل محدد
export const getClientSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف العميل مطلوب',
    }),
  }),
});

// مخطط حذف عميل
export const deleteClientSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف العميل مطلوب',
    }),
  }),
});

// مخطط قائمة العملاء
export const listClientsSchema = z.object({
  query: z.object({
    page: z.string().optional().transform((val) => (val ? parseInt(val) : 1)),
    limit: z.string().optional().transform((val) => (val ? parseInt(val) : 10)),
    sort: z.string().optional().default('name'),
    order: z.enum(['asc', 'desc']).optional().default('asc'),
    search: z.string().optional(),
  }),
});
