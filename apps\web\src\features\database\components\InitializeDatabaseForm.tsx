import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Paper, Typography } from '@mui/material';
import { RestartAlt as RestartAltIcon } from '@mui/icons-material';
import { useInitializeDatabase } from '../hooks/useDatabase';
import { useTranslation } from 'react-i18next';
import { useConfirm } from '@lib/hooks/useConfirm';

/**
 * مكون تهيئة قاعدة البيانات
 */
const InitializeDatabaseForm: React.FC = () => {
  const { t } = useTranslation();
  const initializeDatabaseMutation = useInitializeDatabase();
  const { showConfirm } = useConfirm();

  // التعامل مع تهيئة قاعدة البيانات
  const handleInitializeDatabase = async () => {
    const confirmed = await showConfirm({
      title: t('database.confirmInitializeDatabase'),
      message: t('database.confirmInitializeDatabaseMessage'),
      confirmButtonText: t('database.initialize'),
      cancelButtonText: t('common.cancel'),
      confirmButtonColor: 'error',
    });

    if (confirmed) {
      initializeDatabaseMutation.mutate();
    }
  };

  return (
    <Paper sx={{ p: 2, mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        {t('database.initializeDatabase')}
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        {t('database.initializeDatabaseDescription')}
      </Typography>
      <Typography variant="body2" color="error" paragraph>
        {t('database.initializeDatabaseWarning')}
      </Typography>
      <Box display="flex" justifyContent="flex-end">
        <Button
          variant="contained"
          color="error"
          startIcon={<RestartAltIcon />}
          onClick={handleInitializeDatabase}
          disabled={initializeDatabaseMutation.isPending}
        >
          {initializeDatabaseMutation.isPending
            ? t('common.processing')
            : t('database.initializeDatabase')}
        </Button>
      </Box>
    </Paper>
  );
};

export default InitializeDatabaseForm;
