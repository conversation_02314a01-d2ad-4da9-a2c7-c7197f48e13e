import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Alert,
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  TextField,
  Typography,
} from '@mui/material';
import { useAppDispatch } from '../../../app/store/store';
import { loginStart, loginSuccess, loginFailure } from '../../../app/store/slices/authSlice';
import { useLogin } from '../hooks/useLogin';

// Define form schema with Zod
const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

type LoginFormValues = z.infer<typeof loginSchema>;

const LoginPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [error, setError] = useState<string | null>(null);

  const { mutate: login, isPending } = useLogin();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
      rememberMe: false,
    },
  });

  const onSubmit = (data: LoginFormValues) => {
    setError(null);
    dispatch(loginStart());

    login(
      {
        username: data.username,
        password: data.password,
      },
      {
        onSuccess: (response) => {
          dispatch(
            loginSuccess({
              user: response.user,
              token: response.token,
              refreshToken: response.refreshToken,
            })
          );
          navigate('/dashboard');
        },
        onError: (error) => {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          setError(errorMessage);
          dispatch(loginFailure(errorMessage));
        },
      }
    );
  };

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
      <Typography component="h1" variant="h5" align="center" gutterBottom>
        {t('auth.login')}
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Controller
        name="username"
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            margin="normal"
            required
            fullWidth
            id="username"
            label={t('auth.username')}
            autoComplete="username"
            autoFocus
            error={!!errors.username}
            helperText={errors.username?.message}
          />
        )}
      />

      <Controller
        name="password"
        control={control}
        render={({ field }) => (
          <TextField
            {...field}
            margin="normal"
            required
            fullWidth
            id="password"
            label={t('auth.password')}
            type="password"
            autoComplete="current-password"
            error={!!errors.password}
            helperText={errors.password?.message}
          />
        )}
      />

      <Controller
        name="rememberMe"
        control={control}
        render={({ field }) => (
          <FormControlLabel
            control={<Checkbox {...field} color="primary" />}
            label={t('auth.rememberMe')}
          />
        )}
      />

      <Button
        type="submit"
        fullWidth
        variant="contained"
        sx={{ mt: 3, mb: 2 }}
        disabled={isPending}
      >
        {isPending ? t('common.loading') : t('auth.login')}
      </Button>
    </Box>
  );
};

export default LoginPage;
