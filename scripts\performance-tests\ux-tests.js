/**
 * سكريبت لاختبارات تجربة المستخدم
 * 
 * يستخدم هذا السكريبت Puppeteer لاختبار تجربة المستخدم وإنشاء تقارير.
 * 
 * الاستخدام:
 * node ux-tests.js [test] [--headless]
 * 
 * أمثلة:
 * node ux-tests.js login
 * node ux-tests.js declaration-flow --headless
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const exec = promisify(require('child_process').exec);
const dotenv = require('dotenv');

// تحميل متغيرات البيئة
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

// الإعدادات الافتراضية
const DEFAULT_CONFIG = {
  baseUrl: 'http://localhost:3000', // عنوان URL الأساسي لواجهة المستخدم
  headless: false, // تشغيل المتصفح مع واجهة رسومية افتراضيًا
  timeout: 30000, // المهلة بالمللي ثانية
  viewport: { width: 1920, height: 1080 }, // حجم نافذة العرض
  slowMo: 50, // إبطاء العمليات لتسهيل المشاهدة
};

// تسجيل الدخول إلى التطبيق
async function login(page) {
  try {
    await page.goto(`${DEFAULT_CONFIG.baseUrl}/login`, { waitUntil: 'networkidle0' });
    
    // ملء نموذج تسجيل الدخول
    await page.type('input[name="username"]', process.env.TEST_USERNAME || 'admin');
    await page.type('input[name="password"]', process.env.TEST_PASSWORD || 'admin123');
    
    // النقر على زر تسجيل الدخول
    await Promise.all([
      page.click('button[type="submit"]'),
      page.waitForNavigation({ waitUntil: 'networkidle0' }),
    ]);
    
    return true;
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    return false;
  }
}

// التقاط لقطة شاشة
async function takeScreenshot(page, name) {
  const screenshotsDir = path.join(__dirname, 'reports', 'screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const screenshotPath = path.join(screenshotsDir, `${name}-${timestamp}.png`);
  await page.screenshot({ path: screenshotPath, fullPage: true });
  
  return screenshotPath;
}

// اختبار تسجيل الدخول
async function testLogin(browser) {
  console.log('بدء اختبار تسجيل الدخول...');
  
  const page = await browser.newPage();
  await page.setViewport(DEFAULT_CONFIG.viewport);
  
  try {
    // الانتقال إلى صفحة تسجيل الدخول
    await page.goto(`${DEFAULT_CONFIG.baseUrl}/login`, { waitUntil: 'networkidle0' });
    
    // التقاط لقطة شاشة لصفحة تسجيل الدخول
    const loginScreenshot = await takeScreenshot(page, 'login-page');
    console.log(`تم التقاط لقطة شاشة لصفحة تسجيل الدخول: ${loginScreenshot}`);
    
    // ملء نموذج تسجيل الدخول
    await page.type('input[name="username"]', process.env.TEST_USERNAME || 'admin');
    await page.type('input[name="password"]', process.env.TEST_PASSWORD || 'admin123');
    
    // النقر على زر تسجيل الدخول
    await Promise.all([
      page.click('button[type="submit"]'),
      page.waitForNavigation({ waitUntil: 'networkidle0' }),
    ]);
    
    // التقاط لقطة شاشة بعد تسجيل الدخول
    const dashboardScreenshot = await takeScreenshot(page, 'dashboard-after-login');
    console.log(`تم التقاط لقطة شاشة للوحة التحكم بعد تسجيل الدخول: ${dashboardScreenshot}`);
    
    // التحقق من نجاح تسجيل الدخول
    const isLoggedIn = await page.evaluate(() => {
      return document.body.textContent.includes('لوحة التحكم') || 
             document.body.textContent.includes('Dashboard');
    });
    
    if (isLoggedIn) {
      console.log('✅ تم تسجيل الدخول بنجاح');
    } else {
      console.error('❌ فشل تسجيل الدخول');
    }
    
    return isLoggedIn;
  } catch (error) {
    console.error('خطأ في اختبار تسجيل الدخول:', error);
    return false;
  } finally {
    await page.close();
  }
}

// اختبار سير عمل البيانات
async function testDeclarationFlow(browser) {
  console.log('بدء اختبار سير عمل البيانات...');
  
  const page = await browser.newPage();
  await page.setViewport(DEFAULT_CONFIG.viewport);
  
  try {
    // تسجيل الدخول
    const loginSuccess = await login(page);
    if (!loginSuccess) {
      throw new Error('فشل تسجيل الدخول');
    }
    
    // الانتقال إلى صفحة البيانات
    await page.goto(`${DEFAULT_CONFIG.baseUrl}/declarations`, { waitUntil: 'networkidle0' });
    
    // التقاط لقطة شاشة لصفحة البيانات
    const declarationsScreenshot = await takeScreenshot(page, 'declarations-page');
    console.log(`تم التقاط لقطة شاشة لصفحة البيانات: ${declarationsScreenshot}`);
    
    // النقر على زر إضافة بيان جديد
    await Promise.all([
      page.click('button:has-text("إضافة بيان جديد"), button:has-text("Add New Declaration")'),
      page.waitForNavigation({ waitUntil: 'networkidle0' }),
    ]);
    
    // التقاط لقطة شاشة لنموذج إضافة بيان
    const newDeclarationScreenshot = await takeScreenshot(page, 'new-declaration-form');
    console.log(`تم التقاط لقطة شاشة لنموذج إضافة بيان: ${newDeclarationScreenshot}`);
    
    // ملء نموذج البيان
    await page.type('input[name="declarationNumber"]', Math.floor(Math.random() * 10000).toString());
    await page.type('input[name="taxNumber"]', '*********');
    await page.type('input[name="clientName"]', 'عميل اختبار');
    await page.type('input[name="gatewayEntryNumber"]', Math.floor(Math.random() * 10000).toString());
    
    // اختيار نوع البيان
    await page.click('div[role="button"]'); // فتح القائمة المنسدلة
    await page.click('li[data-value="IMPORT"]'); // اختيار استيراد
    
    // التقاط لقطة شاشة بعد ملء النموذج
    const filledFormScreenshot = await takeScreenshot(page, 'filled-declaration-form');
    console.log(`تم التقاط لقطة شاشة بعد ملء النموذج: ${filledFormScreenshot}`);
    
    // إرسال النموذج
    await Promise.all([
      page.click('button[type="submit"]'),
      page.waitForNavigation({ waitUntil: 'networkidle0' }),
    ]);
    
    // التقاط لقطة شاشة بعد إرسال النموذج
    const afterSubmitScreenshot = await takeScreenshot(page, 'after-declaration-submit');
    console.log(`تم التقاط لقطة شاشة بعد إرسال النموذج: ${afterSubmitScreenshot}`);
    
    // التحقق من نجاح إضافة البيان
    const isSuccess = await page.evaluate(() => {
      return document.body.textContent.includes('تم إضافة البيان بنجاح') || 
             document.body.textContent.includes('Declaration added successfully');
    });
    
    if (isSuccess) {
      console.log('✅ تم إضافة البيان بنجاح');
    } else {
      console.error('❌ فشل إضافة البيان');
    }
    
    return isSuccess;
  } catch (error) {
    console.error('خطأ في اختبار سير عمل البيانات:', error);
    return false;
  } finally {
    await page.close();
  }
}

// الدالة الرئيسية
async function runUXTest() {
  // الحصول على وسيطات سطر الأوامر
  const args = process.argv.slice(2);
  const testName = args[0] || 'login';
  const headless = args.includes('--headless');
  
  console.log(`تنفيذ اختبار تجربة المستخدم: ${testName}`);
  console.log(`وضع headless: ${headless ? 'نعم' : 'لا'}`);
  
  // إنشاء مجلد للتقارير إذا لم يكن موجودًا
  const reportsDir = path.join(__dirname, 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir);
  }
  
  // إطلاق المتصفح
  const browser = await puppeteer.launch({
    headless,
    defaultViewport: DEFAULT_CONFIG.viewport,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    slowMo: DEFAULT_CONFIG.slowMo,
  });
  
  try {
    let result = false;
    
    // تنفيذ الاختبار المطلوب
    switch (testName) {
      case 'login':
        result = await testLogin(browser);
        break;
      case 'declaration-flow':
        result = await testDeclarationFlow(browser);
        break;
      default:
        console.error(`اختبار غير معروف: ${testName}`);
        break;
    }
    
    // إنشاء تقرير
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const reportFile = path.join(reportsDir, `ux-test-${testName}-${timestamp}.json`);
    
    const report = {
      testName,
      timestamp: new Date().toISOString(),
      result,
      config: {
        baseUrl: DEFAULT_CONFIG.baseUrl,
        headless,
        viewport: DEFAULT_CONFIG.viewport,
      },
    };
    
    // حفظ التقرير
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    console.log(`تم حفظ التقرير في: ${reportFile}`);
    
    console.log(`\nنتيجة الاختبار: ${result ? '✅ نجاح' : '❌ فشل'}`);
  } finally {
    // إغلاق المتصفح
    await browser.close();
  }
}

// تنفيذ الدالة الرئيسية
runUXTest().catch(err => {
  console.error('خطأ في تنفيذ اختبار تجربة المستخدم:', err);
  process.exit(1);
});
