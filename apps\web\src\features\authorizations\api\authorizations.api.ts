import axios from 'axios';
import { Authorization, AuthorizationType } from '../types/authorization.types';

// واجهة طلب إنشاء التفويض
export interface CreateAuthorizationRequest {
  clientName?: string;
  taxNumber: string;
  authorizationType: AuthorizationType;
  startDate: string;
  endDate: string;
  clientId?: string;
}

// واجهة طلب تحديث التفويض
export interface UpdateAuthorizationRequest {
  clientName?: string;
  taxNumber?: string;
  authorizationType?: AuthorizationType;
  startDate?: string;
  endDate?: string;
  clientId?: string;
}

// واجهة معلمات البحث عن التفويضات
export interface AuthorizationSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  authorizationType?: AuthorizationType;
  fromDate?: Date;
  toDate?: Date;
  clientId?: string;
  isActive?: boolean;
}

// الحصول على قائمة التفويضات
export const getAuthorizations = async (params: AuthorizationSearchParams = {}) => {
  const { data } = await axios.get('/api/authorizations', { params });
  return data;
};

// الحصول على تفويض محدد
export const getAuthorization = async (id: string) => {
  const { data } = await axios.get(`/api/authorizations/${id}`);
  return data.data;
};

// إنشاء تفويض جديد
export const createAuthorization = async (authorizationData: CreateAuthorizationRequest, file?: File) => {
  const formData = new FormData();
  
  // إضافة بيانات التفويض إلى النموذج
  formData.append('clientName', authorizationData.clientName || '');
  formData.append('taxNumber', authorizationData.taxNumber);
  formData.append('authorizationType', authorizationData.authorizationType);
  formData.append('startDate', authorizationData.startDate);
  formData.append('endDate', authorizationData.endDate);
  
  if (authorizationData.clientId) {
    formData.append('clientId', authorizationData.clientId);
  }
  
  // إضافة الملف إذا تم تقديمه
  if (file) {
    formData.append('file', file);
  }
  
  const { data } = await axios.post('/api/authorizations', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return data.data;
};

// تحديث تفويض
export const updateAuthorization = async (id: string, authorizationData: UpdateAuthorizationRequest, file?: File) => {
  const formData = new FormData();
  
  // إضافة بيانات التفويض إلى النموذج
  if (authorizationData.clientName !== undefined) {
    formData.append('clientName', authorizationData.clientName);
  }
  
  if (authorizationData.taxNumber !== undefined) {
    formData.append('taxNumber', authorizationData.taxNumber);
  }
  
  if (authorizationData.authorizationType !== undefined) {
    formData.append('authorizationType', authorizationData.authorizationType);
  }
  
  if (authorizationData.startDate !== undefined) {
    formData.append('startDate', authorizationData.startDate);
  }
  
  if (authorizationData.endDate !== undefined) {
    formData.append('endDate', authorizationData.endDate);
  }
  
  if (authorizationData.clientId !== undefined) {
    formData.append('clientId', authorizationData.clientId);
  }
  
  // إضافة الملف إذا تم تقديمه
  if (file) {
    formData.append('file', file);
  }
  
  const { data } = await axios.put(`/api/authorizations/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return data.data;
};

// حذف تفويض
export const deleteAuthorization = async (id: string) => {
  const { data } = await axios.delete(`/api/authorizations/${id}`);
  return data;
};
