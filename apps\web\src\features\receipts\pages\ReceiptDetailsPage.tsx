import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  Typography,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import { useReceipt, useDeleteReceipt, useDownloadReceiptPdf } from '../hooks/useReceipts';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const ReceiptDetailsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // استخدام خطافات الاستلامات
  const { data: receiptResponse, isLoading, isError } = useReceipt(id || '');
  const deleteMutation = useDeleteReceipt();
  const downloadPdfMutation = useDownloadReceiptPdf();

  // استخراج بيانات الاستلام من الاستجابة
  const receipt = receiptResponse?.data;

  // التعامل مع تعديل الاستلام
  const handleEdit = () => {
    navigate(`/receipts/${id}/edit`);
  };

  // التعامل مع حذف الاستلام
  const handleDelete = async () => {
    if (window.confirm(t('receipts.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id || '');
        navigate('/receipts');
      } catch (error) {
        console.error('Error deleting receipt:', error);
      }
    }
  };

  // التعامل مع العودة إلى قائمة الاستلامات
  const handleBack = () => {
    navigate('/receipts');
  };

  // التعامل مع تحميل ملف PDF
  const handleDownloadPdf = async () => {
    try {
      const blob = await downloadPdfMutation.mutateAsync(id || '');
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `receipt-${id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF:', error);
    }
  };

  // عرض رسالة التحميل
  if (isLoading) {
    return (
      <Container maxWidth="lg">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // عرض رسالة الخطأ
  if (isError || !receipt) {
    return (
      <Container maxWidth="lg">
        <Box textAlign="center" py={4}>
          <Typography variant="h6" color="error">
            {t('common.errorOccurred')}
          </Typography>
          <Button variant="contained" onClick={handleBack} sx={{ mt: 2 }}>
            {t('common.back')}
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('receipts.details')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('receipts.detailsDescription')}
        </Typography>
      </Box>

      <Box mb={3} display="flex" justifyContent="flex-end">
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{ mr: 1 }}
        >
          {t('common.back')}
        </Button>
        <Button
          variant="outlined"
          startIcon={<EditIcon />}
          onClick={handleEdit}
          sx={{ mr: 1 }}
        >
          {t('common.edit')}
        </Button>
        <Button
          variant="outlined"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={handleDelete}
        >
          {t('common.delete')}
        </Button>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h5">
                {t('receipts.number')}: {receipt.receiptNumber || receipt.id}
              </Typography>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('receipts.receiptDate')}
                </Typography>
                <Typography variant="body1">
                  {format(new Date(receipt.receiptDate || receipt.createdAt), 'PPP', { locale: arSA })}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('receipts.receiverName')}
                </Typography>
                <Typography variant="body1">
                  {receipt.receiverName || 'غير محدد'}
                </Typography>
              </Grid>

              {receipt.receiverPhone && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('receipts.receiverPhone')}
                  </Typography>
                  <Typography variant="body1">
                    {receipt.receiverPhone}
                  </Typography>
                </Grid>
              )}

              {receipt.receiverIdNumber && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('receipts.receiverIdNumber')}
                  </Typography>
                  <Typography variant="body1">
                    {receipt.receiverIdNumber}
                  </Typography>
                </Grid>
              )}

              {receipt.declarationNumber && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('declarations.number')}
                  </Typography>
                  <Typography variant="body1">
                    {receipt.declarationNumber}
                  </Typography>
                </Grid>
              )}

              {receipt.clientName && (
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('clients.name')}
                  </Typography>
                  <Typography variant="body1">
                    {receipt.clientName}
                  </Typography>
                </Grid>
              )}

              {receipt.itemsDescription && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('receipts.itemsDescription')}
                  </Typography>
                  <Typography variant="body1">
                    {receipt.itemsDescription}
                  </Typography>
                </Grid>
              )}

              {receipt.notes && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">
                    {t('receipts.notes')}
                  </Typography>
                  <Typography variant="body1">
                    {receipt.notes}
                  </Typography>
                </Grid>
              )}
            </Grid>

            <Divider sx={{ my: 2 }} />

            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Box>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('common.createdAt')}
                </Typography>
                <Typography variant="body2">
                  {format(new Date(receipt.createdAt), 'PPP', { locale: arSA })}
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('common.updatedAt')}
                </Typography>
                <Typography variant="body2">
                  {format(new Date(receipt.updatedAt), 'PPP', { locale: arSA })}
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('common.actions')}
              </Typography>

              {receipt.pdfFile && (
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<PdfIcon />}
                  onClick={handleDownloadPdf}
                  sx={{ mb: 2 }}
                >
                  {t('common.downloadPdf')}
                </Button>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ReceiptDetailsPage;
