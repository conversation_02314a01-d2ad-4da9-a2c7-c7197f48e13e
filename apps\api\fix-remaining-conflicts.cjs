#!/usr/bin/env node

/**
 * سكريبت لحل merge conflicts المتبقية
 */

const fs = require('fs');

function fixItemMovementTest() {
  const filePath = 'src/modules/items-movement/tests/item-movement.service.test.ts';
  console.log(`إصلاح ${filePath}...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // إزالة جميع علامات merge conflicts المتبقية
  content = content.replace(/^=======\s*$/gm, '');
  content = content.replace(/^>>>>>>> [a-f0-9]+\s*$/gm, '');
  content = content.replace(/^<<<<<<< HEAD\s*$/gm, '');
  
  // إزالة الأسطر الفارغة المتتالية
  content = content.replace(/\n\n\n+/g, '\n\n');
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ تم إصلاح الملف');
}

function main() {
  console.log('🔧 إصلاح merge conflicts المتبقية...\n');
  fixItemMovementTest();
  console.log('\n✅ تم الانتهاء من الإصلاح');
}

if (require.main === module) {
  main();
}
