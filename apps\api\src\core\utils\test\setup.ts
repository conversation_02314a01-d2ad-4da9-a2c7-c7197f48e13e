// إعداد بيئة الاختبار محسن
import { prisma } from '../prisma.js';
import { cleanupTestData } from './auth.js';

// تعيين بيئة الاختبار
process.env.NODE_ENV = 'test';

console.log('✅ Jest setup completed - Test environment initialized');

// تنظيف البيانات قبل بدء الاختبارات
beforeAll(async () => {
  try {
    await cleanupTestData();
    console.log('✅ Test data cleaned before starting tests');
  } catch (error) {
    console.warn('⚠️ Warning: Error cleaning test data:', error);
  }
}, 30000); // زيادة المهلة الزمنية

// تنظيف خفيف بعد كل اختبار
afterEach(async () => {
  try {
    // تنظيف التوكنات المبطلة ومحاولات تسجيل الدخول فقط
    await prisma.invalidatedToken.deleteMany({});
    await prisma.loginAttempt.deleteMany({});
  } catch (error) {
    console.warn('⚠️ Warning: Error cleaning tokens after test:', error);
  }
}, 10000); // مهلة أقل للتنظيف الخفيف

// إغلاق اتصال قاعدة البيانات بعد انتهاء جميع الاختبارات
afterAll(async () => {
  try {
    await cleanupTestData();
    await prisma.$disconnect();
    console.log('✅ Database connection closed successfully');
  } catch (error) {
    console.error('❌ Error closing database connection:', error);
  }
}, 30000); // زيادة المهلة الزمنية
