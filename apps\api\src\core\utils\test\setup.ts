// إعداد بيئة الاختبار محسن
import dotenv from 'dotenv';
import path from 'path';
import { prisma } from '../prisma.js';
import { cleanupTestData } from './auth.js';

// تعيين بيئة الاختبار أولاً
process.env.NODE_ENV = 'test';

// تحميل متغيرات البيئة للاختبار
dotenv.config({
  path: path.join(process.cwd(), '.env.test'),
  override: false
});

// تحميل ملف .env الافتراضي كـ fallback
dotenv.config({
  path: path.join(process.cwd(), '.env'),
  override: false
});

// إعدادات قاعدة البيانات للاختبار - استخدام SQLite
process.env.DATABASE_URL = 'file:./prisma/test.db';

// إعدادات JWT للاختبار
if (!process.env.JWT_SECRET) {
  process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
}

if (!process.env.JWT_REFRESH_SECRET) {
  process.env.JWT_REFRESH_SECRET = 'test_jwt_refresh_secret_key_for_testing_only';
}

// تعطيل الـ logging في الاختبارات
process.env.LOG_LEVEL = 'error';

console.log('✅ Jest setup completed - Test environment initialized');

// تنظيف البيانات قبل بدء الاختبارات
beforeAll(async () => {
  try {
    await cleanupTestData();
    console.log('✅ Test data cleaned before starting tests');
  } catch (error) {
    console.warn('⚠️ Warning: Error cleaning test data:', error);
  }
}, 30000); // زيادة المهلة الزمنية

// تنظيف خفيف بعد كل اختبار
afterEach(async () => {
  try {
    // تنظيف التوكنات المبطلة ومحاولات تسجيل الدخول فقط
    await prisma.invalidatedToken.deleteMany({});
    await prisma.loginAttempt.deleteMany({});
  } catch (error) {
    console.warn('⚠️ Warning: Error cleaning tokens after test:', error);
  }
}, 10000); // مهلة أقل للتنظيف الخفيف

// إغلاق اتصال قاعدة البيانات بعد انتهاء جميع الاختبارات
afterAll(async () => {
  try {
    await cleanupTestData();
    await prisma.$disconnect();
    console.log('✅ Database connection closed successfully');
  } catch (error) {
    console.error('❌ Error closing database connection:', error);
  }
}, 30000); // زيادة المهلة الزمنية
