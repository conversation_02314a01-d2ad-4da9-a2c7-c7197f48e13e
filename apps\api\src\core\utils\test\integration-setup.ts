// إعداد بيئة اختبار التكامل
import { testPrisma } from './test-prisma-client.js';
import app from '../../../app.js';
import { createTestUser, getAuthToken } from './auth.js';
import { config } from '../../config/app.config.js';
import fs from 'fs';
import path from 'path';

// تعيين بيئة الاختبار
process.env.NODE_ENV = 'test';

// زيادة مهلة الاختبار - سيتم تعيينها في تكوين Jest

// إنشاء المجلدات اللازمة للاختبار
const setupTestFolders = () => {
  // إنشاء مجلد التحميلات إذا لم يكن موجودًا
  const uploadsDir = config.upload.dir;
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }

  // إنشاء المجلدات الفرعية
  const subDirs = [
    'declarations',
    'item-movements',
    'authorizations',
    'releases',
    'permits',
    'guarantees',
    'receipts',
    'documents',
    'reports',
  ];

  subDirs.forEach((dir) => {
    const fullPath = path.join(uploadsDir, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }
  });
};

// تنظيف قاعدة البيانات قبل الاختبارات
const cleanupDatabase = async () => {
  try {
    // حذف البيانات من الجداول بترتيب يحترم القيود الخارجية
    await testPrisma.itemMovement.deleteMany({});
    await testPrisma.receipt.deleteMany({});
    await testPrisma.returnableGuarantee.deleteMany({});
    await testPrisma.nonReturnableGuarantee.deleteMany({});
    await testPrisma.permit.deleteMany({});
    await testPrisma.release.deleteMany({});
    await testPrisma.authorization.deleteMany({});
    await testPrisma.driver.deleteMany({});
    await testPrisma.declaration.deleteMany({});
    await testPrisma.client.deleteMany({});

    // حذف محاولات تسجيل الدخول والجلسات لتجنب قفل المستخدم
    await testPrisma.loginAttempt.deleteMany({});
    await testPrisma.session.deleteMany({});
    await testPrisma.invalidatedToken.deleteMany({});
    await testPrisma.customForm.deleteMany({});
    await testPrisma.reportTemplate.deleteMany({});
    await testPrisma.officeDocument.deleteMany({});
    await testPrisma.auditLog.deleteMany({});
    await testPrisma.user.deleteMany({});

    console.log('🧹 تم تنظيف قاعدة البيانات');
  } catch (error) {
    console.warn('⚠️ تحذير: خطأ في تنظيف قاعدة البيانات:', error);
  }
};

// إنشاء بيانات الاختبار الأساسية
const setupTestData = async () => {
  try {
    // إنشاء مستخدم اختبار إذا لم يكن موجودًا
    const user = await createTestUser();

    // التأكد من أن المستخدم نشط
    await testPrisma.user.update({
      where: { id: user.id },
      data: { isActive: true }
    });

    const authToken = await getAuthToken(user);

    // إنشاء عميل اختبار
    const timestamp = Date.now();
    const client = await testPrisma.client.create({
      data: {
        name: 'عميل اختبار',
        taxNumber: `TX${timestamp}`,
        phone: '0500000000',
        email: `test_${timestamp}@example.com`,
        address: 'عنوان اختبار',
      },
    });

    return {
      user,
      authToken,
      client,
    };
  } catch (error) {
    console.error('خطأ في إعداد بيانات الاختبار:', error);
    throw error;
  }
};

// تنظيف الملفات المؤقتة بعد الاختبارات
const cleanupFiles = (files: string[]): void => {
  files.forEach((file) => {
    if (fs.existsSync(file)) {
      fs.unlinkSync(file);
    }
  });
};

// إغلاق اتصال قاعدة البيانات بعد انتهاء جميع الاختبارات
afterAll(async () => {
  await testPrisma.$disconnect();
});

export {
  app,
  setupTestFolders,
  cleanupDatabase,
  setupTestData,
  cleanupFiles,
};
