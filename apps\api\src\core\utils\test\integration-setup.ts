// إعداد بيئة اختبار التكامل
import { testPrisma } from './test-prisma-client.js';
import app from '../../../app.js';
import { createTestUser, getAuthToken } from './auth.js';
import { config } from '../../config/app.config.js';
import fs from 'fs';
import path from 'path';

// تعيين بيئة الاختبار
process.env.NODE_ENV = 'test';

// زيادة مهلة الاختبار - سيتم تعيينها في تكوين Jest

// إنشاء المجلدات اللازمة للاختبار
const setupTestFolders = () => {
  // إنشاء مجلد التحميلات إذا لم يكن موجودًا
  const uploadsDir = config.upload.dir;
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
  }

  // إنشاء المجلدات الفرعية
  const subDirs = [
    'declarations',
    'item-movements',
    'authorizations',
    'releases',
    'permits',
    'guarantees',
    'receipts',
    'documents',
    'reports',
  ];

  subDirs.forEach((dir) => {
    const fullPath = path.join(uploadsDir, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }
  });
};

// تنظيف قاعدة البيانات قبل الاختبارات
const cleanupDatabase = async () => {
  try {
    // حذف البيانات من الجداول بترتيب يحترم القيود الخارجية
    // إزالة الجداول غير الموجودة في schema الاختبار
    const cleanupOperations = [
      () => testPrisma.itemMovement.deleteMany({}),
      () => testPrisma.receipt.deleteMany({}),
      () => testPrisma.permit.deleteMany({}),
      () => testPrisma.release.deleteMany({}),
      () => testPrisma.authorization.deleteMany({}),
      () => testPrisma.driver.deleteMany({}),
      () => testPrisma.declaration.deleteMany({}),
      () => testPrisma.client.deleteMany({}),
      () => testPrisma.loginAttempt.deleteMany({}),
      () => testPrisma.session.deleteMany({}),
      () => testPrisma.invalidatedToken.deleteMany({}),
      () => testPrisma.customForm.deleteMany({}),
      () => testPrisma.reportTemplate.deleteMany({}),
      () => testPrisma.auditLog.deleteMany({}),
      () => testPrisma.user.deleteMany({})
    ];

    // تنفيذ عمليات التنظيف مع معالجة الأخطاء
    for (const operation of cleanupOperations) {
      try {
        await operation();
      } catch (error) {
        console.warn('تحذير: خطأ في عملية تنظيف:', error instanceof Error ? error.message : String(error));
      }
    }

    console.log('🧹 تم تنظيف قاعدة البيانات');
  } catch (error) {
    console.warn('⚠️ تحذير: خطأ عام في تنظيف قاعدة البيانات:', error);
  }
};

// إنشاء بيانات الاختبار الأساسية
const setupTestData = async () => {
  try {
    // إنشاء مستخدم اختبار إذا لم يكن موجودًا
    const user = await createTestUser();

    // التأكد من أن المستخدم نشط
    await testPrisma.user.update({
      where: { id: user.id },
      data: { isActive: true }
    });

    const authToken = await getAuthToken(user);

    // إنشاء عميل اختبار
    const timestamp = Date.now();
    const client = await testPrisma.client.create({
      data: {
        clientNumber: 'C001',
        taxNumber: `TX${timestamp}`,
        clientName: 'عميل اختبار',
        companyName: 'شركة الاختبار',
        phone: '0500000000',
        email: `test_${timestamp}@example.com`,
        address: 'عنوان اختبار',
      },
    });

    return {
      user,
      authToken,
      client,
    };
  } catch (error) {
    console.error('خطأ في إعداد بيانات الاختبار:', error);
    throw error;
  }
};

// تنظيف الملفات المؤقتة بعد الاختبارات
const cleanupFiles = (files: string[]): void => {
  files.forEach((file) => {
    if (fs.existsSync(file)) {
      fs.unlinkSync(file);
    }
  });
};

// إغلاق اتصال قاعدة البيانات بعد انتهاء جميع الاختبارات
afterAll(async () => {
  await testPrisma.$disconnect();
});

export {
  app,
  setupTestFolders,
  cleanupDatabase,
  setupTestData,
  cleanupFiles,
};
