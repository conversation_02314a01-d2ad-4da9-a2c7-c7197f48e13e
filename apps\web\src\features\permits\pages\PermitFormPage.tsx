import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  Grid,
  MenuItem,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Upload as UploadIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { usePermit, useCreatePermit, useUpdatePermit } from '../hooks/usePermits';
import { PermitType, PermitFormValues } from '../types/permit.types';

// مخطط التحقق من صحة نموذج التصريح
const permitFormSchema = z.object({
  permitType: z.nativeEnum(PermitType, {
    errorMap: () => ({ message: 'نوع التصريح مطلوب' }),
  }),
  issueDate: z.date({
    required_error: 'تاريخ إصدار التصريح مطلوب',
    invalid_type_error: 'تاريخ إصدار التصريح مطلوب',
  }).nullable().refine(val => val !== null, {
    message: 'تاريخ إصدار التصريح مطلوب',
  }),
  expiryDate: z.date({
    required_error: 'تاريخ انتهاء التصريح مطلوب',
    invalid_type_error: 'تاريخ انتهاء التصريح مطلوب',
  }).nullable().refine(val => val !== null, {
    message: 'تاريخ انتهاء التصريح مطلوب',
  }),
  notes: z.string().optional(),
  declarationId: z.string().optional(),
  clientId: z.string().optional(),
  file: z.any().optional(),
});

const PermitFormPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  // حالة الملف المحدد
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);

  // استخدام خطافات التصريح
  const { data: permit, isLoading: isLoadingPermit } = usePermit(id || '');
  const createMutation = useCreatePermit();
  const updateMutation = useUpdatePermit();

  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<PermitFormValues>({
    resolver: zodResolver(permitFormSchema),
    defaultValues: {
      permitType: PermitType.ENTRY,
      permitDate: null,
      issueDate: null,
      expiryDate: null,
      notes: '',
      declarationId: '',
      file: null,
    },
  });

  // تحديث النموذج عند تحميل بيانات التصريح
  useEffect(() => {
    if (isEditMode && permit) {
      reset({
        permitType: permit.permitType,
        permitDate: new Date(permit.permitDate),
        issueDate: permit.issueDate ? new Date(permit.issueDate) : null,
        expiryDate: new Date(permit.expiryDate),
        notes: permit.notes || '',
        declarationId: permit.declarationId,
        file: null,
      });

      if (permit.pdfFile) {
        setPdfUrl(`/api/permits/pdf/${permit.id}`);
      }
    }
  }, [isEditMode, permit, reset]);

  // التعامل مع تغيير الملف
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);

      // إنشاء عنوان URL للملف المحدد
      const fileUrl = URL.createObjectURL(files[0]);
      setPdfUrl(fileUrl);
    }
  };

  // التعامل مع تقديم النموذج
  const onSubmit = async (data: PermitFormValues) => {
    try {
      if (isEditMode && id) {
        // تحديث التصريح
        await updateMutation.mutateAsync({
          id,
          data: {
            permitType: data.permitType,
            permitDate: data.permitDate?.toISOString() || '',
            issueDate: data.issueDate?.toISOString() || '',
            expiryDate: data.expiryDate?.toISOString() || '',
            notes: data.notes,
            declarationId: data.declarationId,
          },
          file: selectedFile || undefined,
        });
      } else {
        // إنشاء تصريح جديد
        await createMutation.mutateAsync({
          data: {
            permitType: data.permitType,
            permitDate: data.permitDate?.toISOString() || '',
            issueDate: data.issueDate?.toISOString() || '',
            expiryDate: data.expiryDate?.toISOString() || '',
            notes: data.notes,
            declarationId: data.declarationId,
          },
          file: selectedFile || undefined,
        });
      }

      // العودة إلى صفحة قائمة التصاريح
      navigate('/permits');
    } catch (error) {
      console.error('Error submitting permit form:', error);
    }
  };

  // التعامل مع إلغاء النموذج
  const handleCancel = () => {
    navigate('/permits');
  };

  if (isEditMode && isLoadingPermit) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {isEditMode ? t('permits.edit') : t('permits.create')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {isEditMode ? t('permits.editDescription') : t('permits.createDescription')}
        </Typography>
      </Box>

      <Paper>
        <Box component="form" onSubmit={handleSubmit(onSubmit as any)} noValidate>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="permitType"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      select
                      label={t('permits.type')}
                      fullWidth
                      required
                      error={!!errors.permitType}
                      helperText={errors.permitType?.message}
                    >
                      <MenuItem value={PermitType.ENTRY}>
                        {t('permits.types.ENTRY')}
                      </MenuItem>
                      <MenuItem value={PermitType.EXIT}>
                        {t('permits.types.EXIT')}
                      </MenuItem>
                      <MenuItem value={PermitType.SPECIAL}>
                        {t('permits.types.SPECIAL')}
                      </MenuItem>
                    </TextField>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="declarationId"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('permits.declaration')}
                      fullWidth
                      error={!!errors.declarationId}
                      helperText={errors.declarationId?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="issueDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label={t('permits.issueDate')}
                      value={field.value}
                      onChange={field.onChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          required: true,
                          error: !!errors.issueDate,
                          helperText: errors.issueDate?.message,
                        },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="expiryDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label={t('permits.expiryDate')}
                      value={field.value}
                      onChange={field.onChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          required: true,
                          error: !!errors.expiryDate,
                          helperText: errors.expiryDate?.message,
                        },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('permits.notes')}
                      fullWidth
                      multiline
                      rows={4}
                      error={!!errors.notes}
                      helperText={errors.notes?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  {t('permits.uploadPdf')}
                </Typography>
                <Box
                  border={1}
                  borderRadius={1}
                  borderColor="divider"
                  p={2}
                  textAlign="center"
                >
                  <input
                    type="file"
                    accept="application/pdf"
                    id="pdf-upload"
                    style={{ display: 'none' }}
                    onChange={handleFileChange}
                  />
                  <label htmlFor="pdf-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      startIcon={<UploadIcon />}
                    >
                      {t('common.selectFile')}
                    </Button>
                  </label>

                  {selectedFile && (
                    <Box mt={2} textAlign="left">
                      <Typography variant="body2">
                        {t('common.selectedFile')}: {selectedFile.name}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {t('common.fileSize')}: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </Typography>
                    </Box>
                  )}

                  {pdfUrl && (
                    <Box mt={2}>
                      <Button
                        variant="outlined"
                        startIcon={<PdfIcon />}
                        onClick={() => window.open(pdfUrl, '_blank')}
                      >
                        {t('common.viewPdf')}
                      </Button>
                    </Box>
                  )}
                </Box>
              </Grid>
            </Grid>
          </CardContent>

          <Divider />

          <Box p={2} display="flex" justifyContent="flex-end">
            <Button
              variant="outlined"
              onClick={handleCancel}
              startIcon={<ArrowBackIcon />}
              sx={{ mr: 1 }}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="submit"
              variant="contained"
              startIcon={<SaveIcon />}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <CircularProgress size={24} />
              ) : isEditMode ? (
                t('common.update')
              ) : (
                t('common.save')
              )}
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default PermitFormPage;
