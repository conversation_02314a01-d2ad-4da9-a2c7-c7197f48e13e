import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { createStore } from '../../../app/store/store';
import GuaranteesPage from '../pages/GuaranteesPage';
import GuaranteeFormPage from '../pages/GuaranteeFormPage';
import GuaranteeDetailsPage from '../pages/GuaranteeDetailsPage';
import { GuaranteeType, GuaranteeNature, GuaranteeCurrency } from '../types/guarantee.types';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { arSA } from 'date-fns/locale';

// Mock React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

// Mock Redux Store
const store = createStore();

// Mock React Router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ id: '123' }),
    useNavigate: () => vi.fn(),
  };
});

// Mock API Hooks
vi.mock('../hooks/useGuarantees', () => ({
  useGuarantees: () => ({
    data: {
      data: [
        {
          id: '123',
          guaranteeType: GuaranteeType.RETURNABLE,
          guaranteeNature: GuaranteeNature.DOCUMENTS,
          guaranteeNumber: 'G-001',
          issueDate: '2023-01-01T00:00:00.000Z',
          expiryDate: '2023-12-31T00:00:00.000Z',
          amount: 1000,
          currency: GuaranteeCurrency.SAR,
          notes: 'Test notes',
          pdfFile: 'test.pdf',
          declarationId: '456',
          declarationNumber: 123,
          clientName: 'Test Client',
          isReturned: false,
          createdAt: '2023-01-01T00:00:00.000Z',
          updatedAt: '2023-01-01T00:00:00.000Z',
        },
      ],
      pagination: {
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      },
    },
    isLoading: false,
    isError: false,
    refetch: vi.fn(),
  }),
  useGuarantee: () => ({
    data: {
      id: '123',
      guaranteeType: GuaranteeType.RETURNABLE,
      guaranteeNature: GuaranteeNature.DOCUMENTS,
      guaranteeNumber: 'G-001',
      issueDate: '2023-01-01T00:00:00.000Z',
      expiryDate: '2023-12-31T00:00:00.000Z',
      amount: 1000,
      currency: GuaranteeCurrency.SAR,
      notes: 'Test notes',
      pdfFile: 'test.pdf',
      declarationId: '456',
      declarationNumber: 123,
      clientName: 'Test Client',
      isReturned: false,
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: '2023-01-01T00:00:00.000Z',
    },
    isLoading: false,
    isError: false,
  }),
  useCreateGuarantee: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useUpdateGuarantee: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useDeleteGuarantee: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useUpdateGuaranteeReturnStatus: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useDownloadGuaranteePdf: () => ({
    mutateAsync: vi.fn().mockResolvedValue(new Blob()),
    isLoading: false,
  }),
}));

// Mock Declarations Hook
vi.mock('../../declarations/hooks/useDeclarations', () => ({
  useDeclarations: () => ({
    data: {
      data: [
        {
          id: '456',
          declarationNumber: 123,
          clientName: 'Test Client',
        },
      ],
      pagination: {
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      },
    },
    isLoading: false,
    isError: false,
  }),
}));

// Mock Toast Hook
vi.mock('../../../lib/hooks/useToast', () => ({
  useToast: () => ({
    showSuccess: vi.fn(),
    showError: vi.fn(),
  }),
}));

// Wrapper Component
const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={arSA}>
        <BrowserRouter>{children}</BrowserRouter>
      </LocalizationProvider>
    </QueryClientProvider>
  </Provider>
);

describe('Guarantees Feature', () => {
  describe('GuaranteesPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <GuaranteesPage />
        </Wrapper>
      );
    });

    it('renders the guarantees page title', () => {
      expect(screen.getByText('guarantees.title')).toBeInTheDocument();
    });

    it('renders the guarantees list', () => {
      expect(screen.getByText('G-001')).toBeInTheDocument();
      expect(screen.getByText('guarantees.returnable')).toBeInTheDocument();
      expect(screen.getByText('guarantees.documents')).toBeInTheDocument();
    });

    it('renders the create guarantee button', () => {
      expect(screen.getByText('guarantees.create')).toBeInTheDocument();
    });
  });

  describe('GuaranteeFormPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <GuaranteeFormPage />
        </Wrapper>
      );
    });

    it('renders the guarantee form', () => {
      expect(screen.getByText('guarantees.create')).toBeInTheDocument();
      expect(screen.getByText('guarantees.createDescription')).toBeInTheDocument();
    });

    it('renders form fields', () => {
      expect(screen.getByText('guarantees.declaration')).toBeInTheDocument();
      expect(screen.getByText('guarantees.guaranteeType')).toBeInTheDocument();
      expect(screen.getByText('guarantees.guaranteeNature')).toBeInTheDocument();
      expect(screen.getByText('guarantees.guaranteeNumber')).toBeInTheDocument();
      expect(screen.getByText('guarantees.issueDate')).toBeInTheDocument();
      expect(screen.getByText('guarantees.expiryDate')).toBeInTheDocument();
      expect(screen.getByText('guarantees.amount')).toBeInTheDocument();
      expect(screen.getByText('guarantees.currency')).toBeInTheDocument();
      expect(screen.getByText('guarantees.notes')).toBeInTheDocument();
      expect(screen.getByText('common.uploadPdf')).toBeInTheDocument();
    });
  });

  describe('GuaranteeDetailsPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <GuaranteeDetailsPage />
        </Wrapper>
      );
    });

    it('renders the guarantee details', () => {
      expect(screen.getByText('guarantees.details')).toBeInTheDocument();
      expect(screen.getByText('guarantees.detailsDescription')).toBeInTheDocument();
      expect(screen.getByText('G-001')).toBeInTheDocument();
      expect(screen.getByText('guarantees.returnable')).toBeInTheDocument();
    });

    it('renders guarantee information', () => {
      expect(screen.getByText('guarantees.guaranteeNature')).toBeInTheDocument();
      expect(screen.getByText('guarantees.amount')).toBeInTheDocument();
      expect(screen.getByText('1000 SAR')).toBeInTheDocument();
      expect(screen.getByText('guarantees.issueDate')).toBeInTheDocument();
      expect(screen.getByText('guarantees.returnStatus')).toBeInTheDocument();
      expect(screen.getByText('guarantees.notReturned')).toBeInTheDocument();
    });

    it('renders action buttons', () => {
      expect(screen.getByText('common.back')).toBeInTheDocument();
      expect(screen.getByText('common.edit')).toBeInTheDocument();
      expect(screen.getByText('common.delete')).toBeInTheDocument();
      expect(screen.getByText('common.downloadPdf')).toBeInTheDocument();
      expect(screen.getByText('guarantees.markAsReturned')).toBeInTheDocument();
    });
  });
});
