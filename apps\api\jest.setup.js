// إعداد Jest - تحميل متغيرات البيئة للاختبار
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// تعيين بيئة الاختبار أولاً
process.env.NODE_ENV = 'test';

// تحميل ملف .env للاختبار
dotenv.config({
  path: path.join(__dirname, '.env.test'),
  override: false
});

// تحميل ملف .env الافتراضي كـ fallback
dotenv.config({
  path: path.join(__dirname, '.env'),
  override: false
});

// إعدادات قاعدة البيانات للاختبار - استخدام SQLite
// فرض استخدام SQLite للاختبارات
process.env.DATABASE_URL = 'file:./prisma/test.db';

// إعداد Prisma Client للاختبارات
process.env.PRISMA_SCHEMA_PATH = './prisma/schema.test.prisma';

// إنشاء قاعدة البيانات للاختبارات
import { execSync } from 'child_process';
import { existsSync, unlinkSync } from 'fs';

const setupTestDatabase = () => {
  try {
    const dbPath = path.join(process.cwd(), 'prisma', 'test.db');

    // حذف قاعدة البيانات السابقة إن وجدت
    if (existsSync(dbPath)) {
      unlinkSync(dbPath);
    }

    // تطبيق المخطط باستخدام schema.test.prisma
    execSync('npx prisma db push --schema=./prisma/schema.test.prisma --force-reset', {
      stdio: 'pipe',
      cwd: process.cwd(),
      env: { ...process.env, DATABASE_URL: `file:${dbPath}` }
    });

    console.log('✅ تم إعداد قاعدة بيانات الاختبار');
  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة بيانات الاختبار:', error);
  }
};

// إعداد قاعدة البيانات
setupTestDatabase();

// إعدادات JWT للاختبار - يجب أن تتطابق مع .env.test
if (!process.env.JWT_SECRET) {
  process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
}

if (!process.env.JWT_REFRESH_SECRET) {
  process.env.JWT_REFRESH_SECRET = 'test_jwt_refresh_secret_key_for_testing_only';
}

// تعطيل الـ logging في الاختبارات
process.env.LOG_LEVEL = 'error';

console.log('Jest setup completed - Environment variables loaded for testing');
