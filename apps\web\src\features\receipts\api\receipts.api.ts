import apiService from '../../../lib/services/api.service';
import {
  Receipt,
  CreateReceiptDto,
  UpdateReceiptDto,
  ReceiptSearchParams
} from '../types/receipt.types';
import { PaginatedResponse } from '@/types/api.types';

/**
 * إنشاء استلام جديد
 */
export const createReceipt = async (data: CreateReceiptDto, file?: File) => {
  const formData = new FormData();
  formData.append('data', JSON.stringify(data));

  if (file) {
    formData.append('file', file);
  }

  const response = await apiService.post<{ data: Receipt }>('/receipts', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response;
};

/**
 * تحديث استلام
 */
export const updateReceipt = async (id: string, data: UpdateReceiptDto, file?: File) => {
  const formData = new FormData();
  formData.append('data', JSON.stringify(data));

  if (file) {
    formData.append('file', file);
  }

  const response = await apiService.put<{ data: Receipt }>(`/receipts/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response;
};

/**
 * حذف استلام
 */
export const deleteReceipt = async (id: string) => {
  const response = await apiService.delete<{ success: boolean }>(`/receipts/${id}`);
  return response.success;
};

/**
 * الحصول على استلام محدد
 */
export const getReceipt = async (id: string) => {
  const response = await apiService.get<{ data: Receipt }>(`/receipts/${id}`);
  return response;
};

/**
 * الحصول على قائمة الاستلامات
 */
export const getReceipts = async (params: ReceiptSearchParams = {}) => {
  const response = await apiService.get<PaginatedResponse<Receipt>>('/receipts', { params });
  return response;
};

/**
 * تحميل ملف PDF للاستلام
 */
export const downloadReceiptPdf = async (id: string) => {
  const response = await apiService.get<Blob>(`/receipts/pdf/${id}`, {
    responseType: 'blob',
  });

  return response;
};
