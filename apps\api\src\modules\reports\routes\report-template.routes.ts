import { Router } from 'express';
import { reportTemplateController } from '../controllers/report-template.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import {
  createReportTemplateSchema,
  updateReportTemplateSchema,
  getReportTemplateSchema,
  deleteReportTemplateSchema,
  listReportTemplatesSchema,
  getDefaultTemplateSchema,
} from '../schemas/report-template.schema.js';

export const reportTemplateRoutes = Router();

/**
 * @route GET /api/report-templates
 * @desc الحصول على قائمة قوالب التقارير
 * @access خاص (مصادقة مطلوبة)
 */
reportTemplateRoutes.get(
  '/',
  authMiddleware,
  validateRequest(listReportTemplatesSchema),
  reportTemplateController.listReportTemplates
);

/**
 * @route POST /api/report-templates
 * @desc إنشاء قالب تقرير جديد
 * @access خاص (مصادقة مطلوبة)
 */
reportTemplateRoutes.post(
  '/',
  authMiddleware,
  validateRequest(createReportTemplateSchema),
  reportTemplateController.createReportTemplate
);

/**
 * @route GET /api/report-templates/:id
 * @desc الحصول على قالب تقرير محدد
 * @access خاص (مصادقة مطلوبة)
 */
reportTemplateRoutes.get(
  '/:id',
  authMiddleware,
  validateRequest(getReportTemplateSchema),
  reportTemplateController.getReportTemplate
);

/**
 * @route PUT /api/report-templates/:id
 * @desc تحديث قالب تقرير
 * @access خاص (مصادقة مطلوبة)
 */
reportTemplateRoutes.put(
  '/:id',
  authMiddleware,
  validateRequest(updateReportTemplateSchema),
  reportTemplateController.updateReportTemplate
);

/**
 * @route DELETE /api/report-templates/:id
 * @desc حذف قالب تقرير
 * @access خاص (مصادقة مطلوبة)
 */
reportTemplateRoutes.delete(
  '/:id',
  authMiddleware,
  validateRequest(deleteReportTemplateSchema),
  reportTemplateController.deleteReportTemplate
);

/**
 * @route GET /api/report-templates/default/:reportType
 * @desc الحصول على القالب الافتراضي لنوع تقرير معين
 * @access خاص (مصادقة مطلوبة)
 */
reportTemplateRoutes.get(
  '/default/:reportType',
  authMiddleware,
  validateRequest(getDefaultTemplateSchema),
  reportTemplateController.getDefaultTemplate
);
