import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Box,
  Button,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  LinearProgress,
  Chip,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  InsertDriveFile as FileIcon,
  Delete as DeleteIcon,
  PictureAsPdf as PdfIcon,
  Image as ImageIcon,
  Description as DocIcon,
} from '@mui/icons-material';

interface FileUploadProps {
  /**
   * دالة تنفذ عند تغيير الملفات
   */
  onChange: (files: File[]) => void;
  
  /**
   * الملفات المحددة
   */
  value?: File[];
  
  /**
   * أنواع الملفات المسموح بها
   * @default undefined (كل الأنواع)
   */
  accept?: Record<string, string[]>;
  
  /**
   * الحد الأقصى لحجم الملف بالبايت
   * @default 104857600 (100 ميجابايت)
   */
  maxSize?: number;
  
  /**
   * هل يسمح بتحميل ملفات متعددة
   * @default false
   */
  multiple?: boolean;
  
  /**
   * نص المساعدة
   */
  helperText?: string;
  
  /**
   * رسالة الخطأ
   */
  error?: string;
  
  /**
   * هل المكون معطل
   * @default false
   */
  disabled?: boolean;
}

/**
 * مكون تحميل الملفات
 * يستخدم لتحميل الملفات بواسطة السحب والإفلات أو النقر
 */
export const FileUpload: React.FC<FileUploadProps> = ({
  onChange,
  value = [],
  accept,
  maxSize = 104857600, // 100 MB
  multiple = false,
  helperText,
  error,
  disabled = false,
}) => {
  const [progress, setProgress] = useState<number | null>(null);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      // محاكاة تقدم التحميل
      setProgress(0);
      const interval = setInterval(() => {
        setProgress((prev) => {
          if (prev === null) return 0;
          if (prev >= 100) {
            clearInterval(interval);
            return null;
          }
          return prev + 10;
        });
      }, 200);

      // تحديث الملفات
      if (multiple) {
        onChange([...value, ...acceptedFiles]);
      } else {
        onChange(acceptedFiles);
      }
    },
    [multiple, onChange, value]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept,
    maxSize,
    multiple,
    disabled,
  });

  const handleRemoveFile = (index: number) => {
    const newFiles = [...value];
    newFiles.splice(index, 1);
    onChange(newFiles);
  };

  const getFileIcon = (file: File) => {
    const fileType = file.type;
    if (fileType.includes('pdf')) {
      return <PdfIcon color="error" />;
    } else if (fileType.includes('image')) {
      return <ImageIcon color="primary" />;
    } else {
      return <DocIcon color="info" />;
    }
  };

  const formatFileSize = (size: number) => {
    if (size < 1024) {
      return `${size} B`;
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)} KB`;
    } else {
      return `${(size / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper
        {...getRootProps()}
        sx={{
          p: 3,
          border: '2px dashed',
          borderColor: error
            ? 'error.main'
            : isDragActive
            ? 'primary.main'
            : 'divider',
          borderRadius: 2,
          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',
          cursor: disabled ? 'not-allowed' : 'pointer',
          opacity: disabled ? 0.6 : 1,
          transition: 'all 0.2s ease',
          '&:hover': {
            borderColor: !disabled && !error ? 'primary.main' : undefined,
            backgroundColor: !disabled ? 'action.hover' : undefined,
          },
        }}
      >
        <input {...getInputProps()} />
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center',
          }}
        >
          <CloudUploadIcon
            sx={{ fontSize: 48, color: 'primary.main', mb: 2 }}
          />
          <Typography variant="h6" gutterBottom>
            {isDragActive
              ? 'أفلت الملفات هنا'
              : 'اسحب وأفلت الملفات هنا، أو انقر للاختيار'}
          </Typography>
          {helperText && (
            <Typography variant="body2" color="text.secondary">
              {helperText}
            </Typography>
          )}
          {error && (
            <Typography variant="body2" color="error" sx={{ mt: 1 }}>
              {error}
            </Typography>
          )}
          <Button
            variant="outlined"
            color="primary"
            disabled={disabled}
            sx={{ mt: 2 }}
            onClick={(e) => e.stopPropagation()}
          >
            اختر ملفات
          </Button>
          {accept && (
            <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {Object.entries(accept).map(([key, values]) => (
                <Chip
                  key={key}
                  label={values.join(', ')}
                  size="small"
                  variant="outlined"
                />
              ))}
            </Box>
          )}
        </Box>
      </Paper>

      {progress !== null && (
        <Box sx={{ width: '100%', mt: 2 }}>
          <LinearProgress variant="determinate" value={progress} />
        </Box>
      )}

      {value.length > 0 && (
        <List sx={{ mt: 2 }}>
          {value.map((file, index) => (
            <ListItem key={index} divider={index < value.length - 1}>
              <ListItemIcon>{getFileIcon(file)}</ListItemIcon>
              <ListItemText
                primary={file.name}
                secondary={formatFileSize(file.size)}
              />
              <ListItemSecondaryAction>
                <IconButton
                  edge="end"
                  aria-label="delete"
                  onClick={() => handleRemoveFile(index)}
                  disabled={disabled}
                >
                  <DeleteIcon />
                </IconButton>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
      )}
    </Box>
  );
};
