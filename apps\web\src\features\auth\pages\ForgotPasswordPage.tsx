import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Link } from 'react-router-dom';
import { Button, TextField, Typography, Paper, Box, Container, Alert } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { AuthLayout } from '../layouts/AuthLayout';
import { useAuth } from '../hooks/useAuth';

// تعريف مخطط التحقق من صحة البيانات
const forgotPasswordSchema = z.object({
  email: z.string().email('البريد الإلكتروني غير صالح').min(1, 'البريد الإلكتروني مطلوب'),
});

// نوع بيانات النموذج
type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

/**
 * صفحة نسيت كلمة المرور
 */
const ForgotPasswordPage: React.FC = () => {
  const { t } = useTranslation();
  const { forgotPassword } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // إعداد نموذج إعادة تعيين كلمة المرور
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  // معالجة تقديم النموذج
  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // استدعاء خدمة نسيت كلمة المرور
      await forgotPassword(data.email);

      // عرض رسالة النجاح
      setSuccess(true);
    } catch (err) {
      // عرض رسالة الخطأ
      setError(err instanceof Error ? err.message : 'حدث خطأ أثناء إرسال طلب إعادة تعيين كلمة المرور');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AuthLayout>
      <Container maxWidth="sm">
        <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
          <Typography variant="h5" component="h1" align="center" gutterBottom>
            {t('auth.forgotPassword.title')}
          </Typography>

          {success ? (
            <Box sx={{ mt: 2 }}>
              <Alert severity="success">
                {t('auth.forgotPassword.successMessage')}
              </Alert>
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Link to="/login">
                  <Button variant="text" color="primary">
                    {t('auth.forgotPassword.backToLogin')}
                  </Button>
                </Link>
              </Box>
            </Box>
          ) : (
            <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}

              <Typography variant="body2" sx={{ mb: 3 }}>
                {t('auth.forgotPassword.instructions')}
              </Typography>

              <TextField
                margin="normal"
                required
                fullWidth
                id="email"
                label={t('auth.email')}
                {...register('email')}
                error={!!errors.email}
                helperText={errors.email?.message}
                autoComplete="email"
                autoFocus
                disabled={isSubmitting}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                color="primary"
                sx={{ mt: 3, mb: 2 }}
                disabled={isSubmitting}
              >
                {isSubmitting
                  ? t('auth.forgotPassword.submitting')
                  : t('auth.forgotPassword.submit')}
              </Button>

              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Link to="/login">
                  <Button variant="text" color="primary">
                    {t('auth.forgotPassword.backToLogin')}
                  </Button>
                </Link>
              </Box>
            </Box>
          )}
        </Paper>
      </Container>
    </AuthLayout>
  );
};

export default ForgotPasswordPage;
