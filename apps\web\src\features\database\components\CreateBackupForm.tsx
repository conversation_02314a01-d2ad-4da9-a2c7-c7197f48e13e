import React from 'react';
import { Box, Button, Paper, Typography } from '@mui/material';
import { Backup as BackupIcon } from '@mui/icons-material';
import { useCreateBackup } from '../hooks/useDatabase';
import { useTranslation } from 'react-i18next';

/**
 * مكون إنشاء نسخة احتياطية
 */
const CreateBackupForm: React.FC = () => {
  const { t } = useTranslation();
  const createBackupMutation = useCreateBackup();

  // التعامل مع إنشاء نسخة احتياطية
  const handleCreateBackup = () => {
    createBackupMutation.mutate();
  };

  return (
    <Paper sx={{ p: 2, mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        {t('database.createBackup')}
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        {t('database.createBackupDescription')}
      </Typography>
      <Box display="flex" justifyContent="flex-end">
        <Button
          variant="contained"
          color="primary"
          startIcon={<BackupIcon />}
          onClick={handleCreateBackup}
          disabled={createBackupMutation.isPending}
        >
          {createBackupMutation.isPending
            ? t('common.processing')
            : t('database.createBackup')}
        </Button>
      </Box>
    </Paper>
  );
};

export default CreateBackupForm;
