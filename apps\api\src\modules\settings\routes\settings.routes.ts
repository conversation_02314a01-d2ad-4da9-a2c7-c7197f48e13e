import { Router } from 'express';
import { settingsController } from '../controllers/settings.controller.js';
import { authMiddleware, adminMiddleware } from '../../../core/middleware/auth.middleware.js';

export const settingsRoutes = Router();

/**
 * @route GET /api/settings
 * @desc الحصول على إعدادات النظام
 * @access خاص (مصادقة مطلوبة)
 */
settingsRoutes.get(
  '/',
  authMiddleware,
  settingsController.getSettings
);

/**
 * @route PUT /api/settings
 * @desc تحديث إعدادات النظام
 * @access خاص (مصادقة مطلوبة + مدير النظام فقط)
 */
settingsRoutes.put(
  '/',
  authMiddleware,
  adminMiddleware,
  settingsController.updateSettings
);
