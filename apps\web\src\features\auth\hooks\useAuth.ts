import { useAppSelector } from '@app/store/store';
import { User } from '@lib/services/user.service';
import { useMutation } from '@tanstack/react-query';
import authService from '@lib/services/auth.service';

/**
 * واجهة حالة المصادقة
 */
interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>;
}

/**
 * خطاف للتحقق من حالة المصادقة
 * يوفر معلومات حول حالة المصادقة الحالية
 */
export const useAuth = (): AuthState => {
  const { user, token, refreshToken, isAuthenticated, isLoading } = useAppSelector((state) => state.auth);

  // دالة نسيت كلمة المرور
  const forgotPassword = async (email: string): Promise<void> => {
    try {
      // استدعاء خدمة نسيت كلمة المرور
      await authService.forgotPassword(email);
    } catch (error) {
      console.error('Error in forgotPassword:', error);
      throw error;
    }
  };

  // دالة إعادة تعيين كلمة المرور
  const resetPassword = async (token: string, newPassword: string): Promise<void> => {
    try {
      // استدعاء خدمة إعادة تعيين كلمة المرور
      await authService.resetPassword(token, newPassword);
    } catch (error) {
      console.error('Error in resetPassword:', error);
      throw error;
    }
  };

  // دالة تحديث الملف الشخصي
  const updateProfile = async (data: Partial<User>): Promise<void> => {
    try {
      // استدعاء خدمة تحديث الملف الشخصي
      await authService.updateProfile(data);
    } catch (error) {
      console.error('Error in updateProfile:', error);
      throw error;
    }
  };

  // دالة تحديث كلمة المرور
  const updatePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    try {
      // استدعاء خدمة تحديث كلمة المرور
      await authService.updatePassword(currentPassword, newPassword);
    } catch (error) {
      console.error('Error in updatePassword:', error);
      throw error;
    }
  };

  return {
    user,
    token,
    refreshToken,
    isAuthenticated,
    isLoading,
    forgotPassword,
    resetPassword,
    updateProfile,
    updatePassword,
  };
};
