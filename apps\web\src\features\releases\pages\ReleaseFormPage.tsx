import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  Grid,
  MenuItem,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon,
  Upload as UploadIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useRelease, useCreateRelease, useUpdateRelease } from '../hooks/useReleases';
import { ReleaseType, ReleaseFormValues } from '../types/release.types';

// مخطط التحقق من صحة نموذج الإفراج
const releaseFormSchema = z.object({
  releaseType: z.nativeEnum(ReleaseType, {
    errorMap: () => ({ message: 'نوع الإفراج مطلوب' }),
  }),
  startDate: z.date({
    required_error: 'تاريخ بداية الإفراج مطلوب',
    invalid_type_error: 'تاريخ بداية الإفراج مطلوب',
  }).nullable().refine(val => val !== null, {
    message: 'تاريخ بداية الإفراج مطلوب',
  }),
  endDate: z.date({
    required_error: 'تاريخ نهاية الإفراج مطلوب',
    invalid_type_error: 'تاريخ نهاية الإفراج مطلوب',
  }).nullable().refine(val => val !== null, {
    message: 'تاريخ نهاية الإفراج مطلوب',
  }),
  notes: z.string().optional(),
  declarationId: z.string().optional(),
  clientId: z.string().optional(),
  file: z.any().optional(),
});

const ReleaseFormPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  
  // حالة الملف المحدد
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  
  // استخدام خطافات الإفراج
  const { data: release, isLoading: isLoadingRelease } = useRelease(id || '');
  const createMutation = useCreateRelease();
  const updateMutation = useUpdateRelease();
  
  // إعداد نموذج React Hook Form
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<ReleaseFormValues>({
    resolver: zodResolver(releaseFormSchema),
    defaultValues: {
      releaseType: ReleaseType.TEMPORARY,
      startDate: null,
      endDate: null,
      notes: '',
      declarationId: undefined,
      clientId: undefined,
      file: null,
    },
  });
  
  // تحديث النموذج عند تحميل بيانات الإفراج
  useEffect(() => {
    if (isEditMode && release) {
      reset({
        releaseType: release.releaseType,
        startDate: new Date(release.startDate),
        endDate: new Date(release.endDate),
        notes: release.notes,
        declarationId: release.declarationId,
        clientId: release.clientId,
      });
      
      if (release.pdfFile) {
        setPdfUrl(`/api/releases/pdf/${release.id}`);
      }
    }
  }, [isEditMode, release, reset]);
  
  // التعامل مع تغيير الملف
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      setSelectedFile(files[0]);
      
      // إنشاء عنوان URL للملف المحدد
      const fileUrl = URL.createObjectURL(files[0]);
      setPdfUrl(fileUrl);
    }
  };
  
  // التعامل مع تقديم النموذج
  const onSubmit = async (data: ReleaseFormValues) => {
    try {
      if (isEditMode && id) {
        // تحديث الإفراج
        await updateMutation.mutateAsync({
          id,
          data: {
            releaseType: data.releaseType,
            startDate: data.startDate?.toISOString() || '',
            endDate: data.endDate?.toISOString() || '',
            notes: data.notes,
            declarationId: data.declarationId,
            clientId: data.clientId,
          },
          file: selectedFile || undefined,
        });
      } else {
        // إنشاء إفراج جديد
        await createMutation.mutateAsync({
          data: {
            releaseType: data.releaseType,
            startDate: data.startDate?.toISOString() || '',
            endDate: data.endDate?.toISOString() || '',
            notes: data.notes,
            declarationId: data.declarationId,
            clientId: data.clientId,
          },
          file: selectedFile || undefined,
        });
      }
      
      // العودة إلى صفحة قائمة الإفراجات
      navigate('/releases');
    } catch (error) {
      console.error('Error submitting release form:', error);
    }
  };
  
  // التعامل مع إلغاء النموذج
  const handleCancel = () => {
    navigate('/releases');
  };
  
  if (isEditMode && isLoadingRelease) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {isEditMode ? t('releases.edit') : t('releases.create')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {isEditMode ? t('releases.editDescription') : t('releases.createDescription')}
        </Typography>
      </Box>
      
      <Paper>
        <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="releaseType"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      select
                      label={t('releases.type')}
                      fullWidth
                      required
                      error={!!errors.releaseType}
                      helperText={errors.releaseType?.message}
                    >
                      <MenuItem value={ReleaseType.TEMPORARY}>
                        {t('releases.types.TEMPORARY')}
                      </MenuItem>
                      <MenuItem value={ReleaseType.PERMANENT}>
                        {t('releases.types.PERMANENT')}
                      </MenuItem>
                    </TextField>
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="declarationId"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('releases.declaration')}
                      fullWidth
                      error={!!errors.declarationId}
                      helperText={errors.declarationId?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="startDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label={t('releases.startDate')}
                      value={field.value}
                      onChange={field.onChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          required: true,
                          error: !!errors.startDate,
                          helperText: errors.startDate?.message,
                        },
                      }}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Controller
                  name="endDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label={t('releases.endDate')}
                      value={field.value}
                      onChange={field.onChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          required: true,
                          error: !!errors.endDate,
                          helperText: errors.endDate?.message,
                        },
                      }}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12}>
                <Controller
                  name="notes"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('releases.notes')}
                      fullWidth
                      multiline
                      rows={4}
                      error={!!errors.notes}
                      helperText={errors.notes?.message}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  {t('releases.uploadPdf')}
                </Typography>
                <Box
                  border={1}
                  borderRadius={1}
                  borderColor="divider"
                  p={2}
                  textAlign="center"
                >
                  <input
                    type="file"
                    accept="application/pdf"
                    id="pdf-upload"
                    style={{ display: 'none' }}
                    onChange={handleFileChange}
                  />
                  <label htmlFor="pdf-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      startIcon={<UploadIcon />}
                    >
                      {t('common.selectFile')}
                    </Button>
                  </label>
                  
                  {selectedFile && (
                    <Box mt={2} textAlign="left">
                      <Typography variant="body2">
                        {t('common.selectedFile')}: {selectedFile.name}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {t('common.fileSize')}: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                      </Typography>
                    </Box>
                  )}
                  
                  {pdfUrl && (
                    <Box mt={2}>
                      <Button
                        variant="outlined"
                        startIcon={<PdfIcon />}
                        onClick={() => window.open(pdfUrl, '_blank')}
                      >
                        {t('common.viewPdf')}
                      </Button>
                    </Box>
                  )}
                </Box>
              </Grid>
            </Grid>
          </CardContent>
          
          <Divider />
          
          <Box p={2} display="flex" justifyContent="flex-end">
            <Button
              variant="outlined"
              onClick={handleCancel}
              startIcon={<ArrowBackIcon />}
              sx={{ mr: 1 }}
            >
              {t('common.cancel')}
            </Button>
            <Button
              type="submit"
              variant="contained"
              startIcon={<SaveIcon />}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <CircularProgress size={24} />
              ) : isEditMode ? (
                t('common.update')
              ) : (
                t('common.save')
              )}
            </Button>
          </Box>
        </Box>
      </Paper>
    </Container>
  );
};

export default ReleaseFormPage;
