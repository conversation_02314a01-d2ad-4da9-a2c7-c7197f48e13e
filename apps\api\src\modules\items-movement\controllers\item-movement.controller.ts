import { Request, Response, NextFunction } from 'express';
import { itemMovementService } from '../services/item-movement.service.js';
import { successResponse, paginatedResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const itemMovementController = {
  /**
   * إنشاء حركة صنف جديدة
   */
  createItemMovement: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على بيانات حركة الصنف من الطلب
      const itemMovementData = req.body;

      // إنشاء حركة الصنف
      const itemMovement = await itemMovementService.createItemMovement(
        itemMovementData
      );

      return res.status(201).json(successResponse(itemMovement, 'تم إنشاء حركة الصنف بنجاح', 201));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث حركة صنف
   */
  updateItemMovement: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف حركة الصنف من المعلمات
      const { id } = req.params;

      // الحصول على بيانات حركة الصنف من الطلب
      const itemMovementData = req.body;

      // تحديث حركة الصنف
      const itemMovement = await itemMovementService.updateItemMovement(
        id,
        itemMovementData
      );

      return res.status(200).json(successResponse(itemMovement, 'تم تحديث حركة الصنف بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على حركة صنف محددة
   */
  getItemMovement: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معرف حركة الصنف من المعلمات
      const { id } = req.params;

      // الحصول على حركة الصنف
      const itemMovement = await itemMovementService.getItemMovement(id);

      return res.status(200).json(successResponse(itemMovement, 'تم الحصول على حركة الصنف بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف حركة صنف
   */
  deleteItemMovement: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف حركة الصنف من المعلمات
      const { id } = req.params;

      // حذف حركة الصنف
      const deletedItemMovement = await itemMovementService.deleteItemMovement(id);

      return res.status(200).json(successResponse(deletedItemMovement, 'تم حذف حركة الصنف بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة حركات الأصناف
   */
  listItemMovements: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معلمات البحث
      const { page, limit, sort, order, search, declarationId, fromDate, toDate } =
        req.query as any;

      // الحصول على قائمة حركات الأصناف
      const result = await itemMovementService.listItemMovements({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        search,
        declarationId,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined,
      });

      return res
        .status(200)
        .json(
          paginatedResponse(
            result.data,
            result.pagination.page,
            result.pagination.limit,
            result.pagination.total,
            'تم الحصول على قائمة حركات الأصناف بنجاح'
          )
        );
    } catch (error) {
      next(error);
    }
  },
};
