import { ReactNode, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../store/store';
import { loginSuccess, logout } from '../store/slices/authSlice';
import authService from '@lib/services/auth.service';
import { useToast } from '@lib/hooks/useToast';

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const dispatch = useAppDispatch();
  const { token } = useAppSelector((state) => state.auth);
  const toast = useToast();

  useEffect(() => {
    // التحقق من وجود توكن في التخزين المحلي عند تحميل التطبيق
    const storedToken = localStorage.getItem('token');
    const storedRefreshToken = localStorage.getItem('refreshToken');

    if (storedToken && storedRefreshToken && !token) {
      // محاولة تجديد التوكن
      authService.refreshToken()
        .then(async (response) => {
          try {
            // إذا كانت استجابة تجديد التوكن لا تحتوي على معلومات المستخدم، نحاول الحصول عليها
            if (!response.user) {
              // استيراد خدمة المستخدم
              const userService = (await import('@lib/services/user.service')).default;
              // الحصول على معلومات المستخدم الحالي
              const user = await userService.getCurrentUser();

              // تحديث حالة المصادقة بالتوكن الجديد ومعلومات المستخدم
              dispatch(loginSuccess({
                user,
                token: response.token,
                refreshToken: response.refreshToken
              }));
            } else {
              // تحديث حالة المصادقة بالتوكن الجديد ومعلومات المستخدم
              dispatch(loginSuccess({
                user: response.user,
                token: response.token,
                refreshToken: response.refreshToken
              }));
            }
          } catch (error) {
            // إذا فشل الحصول على معلومات المستخدم، تسجيل الخروج
            dispatch(logout());
            toast.showError('حدث خطأ أثناء الحصول على معلومات المستخدم. يرجى تسجيل الدخول مرة أخرى.');
          }
        })
        .catch(() => {
          // إذا فشل تجديد التوكن، تسجيل الخروج
          dispatch(logout());
          toast.showError('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
        });
    }
  }, [dispatch, token, toast]);

  return <>{children}</>;
};

export default AuthProvider;
