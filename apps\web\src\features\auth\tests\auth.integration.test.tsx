import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { server, createMockHandlers } from '@/test/integration-setup';
import Login from '../components/Login';

// إنشاء theme للاختبار
const theme = createTheme({
  direction: 'rtl',
});

// مكون wrapper للاختبارات
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            {children}
          </LocalizationProvider>
        </ThemeProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Auth Integration Tests', () => {
  beforeEach(() => {
    // إعداد mock handlers للاختبارات
    createMockHandlers();
  });

  it('should login successfully with valid credentials', async () => {
    const mockOnLogin = vi.fn().mockResolvedValue(undefined);

    render(
      <TestWrapper>
        <Login onLogin={mockOnLogin} />
      </TestWrapper>
    );

    // العثور على حقول الإدخال
    const usernameInput = screen.getByLabelText(/اسم المستخدم/i);
    const passwordInput = screen.getByLabelText(/كلمة المرور/i);
    const loginButton = screen.getByRole('button', { name: /تسجيل الدخول/i });

    // إدخال بيانات صحيحة
    fireEvent.change(usernameInput, { target: { value: 'test_user' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });

    // النقر على زر تسجيل الدخول
    fireEvent.click(loginButton);

    // التحقق من استدعاء دالة تسجيل الدخول
    await waitFor(() => {
      expect(mockOnLogin).toHaveBeenCalledWith({
        username: 'test_user',
        password: 'password123',
      });
    });
  });

  it('should show validation errors for empty fields', async () => {
    const mockOnLogin = vi.fn();

    render(
      <TestWrapper>
        <Login onLogin={mockOnLogin} />
      </TestWrapper>
    );

    const loginButton = screen.getByRole('button', { name: /تسجيل الدخول/i });

    // النقر على زر تسجيل الدخول بدون إدخال بيانات
    fireEvent.click(loginButton);

    // التحقق من ظهور رسائل الخطأ
    await waitFor(() => {
      expect(screen.getByText(/اسم المستخدم مطلوب/i)).toBeInTheDocument();
      expect(screen.getByText(/كلمة المرور مطلوبة/i)).toBeInTheDocument();
    });

    // التحقق من عدم استدعاء دالة تسجيل الدخول
    expect(mockOnLogin).not.toHaveBeenCalled();
  });

  it('should show loading state during login', async () => {
    const mockOnLogin = vi.fn().mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 1000))
    );

    render(
      <TestWrapper>
        <Login onLogin={mockOnLogin} loading={true} />
      </TestWrapper>
    );

    // التحقق من ظهور حالة التحميل
    expect(screen.getByText(/جاري تسجيل الدخول/i)).toBeInTheDocument();
    
    // التحقق من تعطيل الحقول أثناء التحميل
    const usernameInput = screen.getByLabelText(/اسم المستخدم/i);
    const passwordInput = screen.getByLabelText(/كلمة المرور/i);
    const loginButton = screen.getByRole('button', { name: /جاري تسجيل الدخول/i });

    expect(usernameInput).toBeDisabled();
    expect(passwordInput).toBeDisabled();
    expect(loginButton).toBeDisabled();
  });

  it('should display error message when login fails', async () => {
    const errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';

    render(
      <TestWrapper>
        <Login onLogin={vi.fn()} error={errorMessage} />
      </TestWrapper>
    );

    // التحقق من ظهور رسالة الخطأ
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });
});
