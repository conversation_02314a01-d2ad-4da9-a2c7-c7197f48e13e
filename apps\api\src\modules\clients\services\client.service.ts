import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { Prisma } from '@prisma/client';

interface CreateClientInput {
  name: string;
  taxNumber: string;
  phone?: string;
  email?: string;
  address?: string;
}

interface UpdateClientInput {
  name?: string;
  taxNumber?: string;
  phone?: string;
  email?: string;
  address?: string;
}

interface ListClientsParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
}

export const clientService = {
  /**
   * إنشاء عميل جديد
   */
  createClient: async (data: CreateClientInput) => {
    try {
      // التحقق من عدم وجود عميل بنفس الرقم الضريبي
      const existingClient = await prisma.client.findFirst({
        where: { taxNumber: data.taxNumber },
      });

      if (existingClient) {
        throw new HttpException(400, 'يوجد عميل بنفس الرقم الضريبي', 'Bad Request');
      }

      // في النموذج الجديد، لا يوجد حقل clientNumber

      // إنشاء العميل
      const client = await prisma.client.create({
        data: {
          name: data.name,
          taxNumber: data.taxNumber,
          phone: data.phone,
          email: data.email,
          address: data.address,
        },
      });

      return client;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(400, 'يوجد عميل بنفس الرقم الضريبي', 'Bad Request');
        }
      }
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء إنشاء العميل', 'Internal Server Error');
    }
  },

  /**
   * تحديث عميل
   */
  updateClient: async (id: string, data: UpdateClientInput) => {
    try {
      // التحقق من وجود العميل
      const existingClient = await prisma.client.findUnique({
        where: { id },
      });

      if (!existingClient) {
        throw new HttpException(404, 'العميل غير موجود', 'Not Found');
      }

      // التحقق من عدم وجود عميل آخر بنفس الرقم الضريبي
      if (data.taxNumber && data.taxNumber !== existingClient.taxNumber) {
        const clientWithSameTaxNumber = await prisma.client.findFirst({
          where: {
            taxNumber: data.taxNumber,
            id: { not: id },
          },
        });

        if (clientWithSameTaxNumber) {
          throw new HttpException(400, 'يوجد عميل آخر بنفس الرقم الضريبي', 'Bad Request');
        }
      }

      // تحديث العميل
      const client = await prisma.client.update({
        where: { id },
        data: {
          name: data.name,
          taxNumber: data.taxNumber,
          phone: data.phone,
          email: data.email,
          address: data.address,
        },
      });

      return client;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(400, 'يوجد عميل آخر بنفس الرقم الضريبي', 'Bad Request');
        }
      }
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء تحديث العميل', 'Internal Server Error');
    }
  },

  /**
   * الحصول على عميل محدد
   */
  getClient: async (id: string) => {
    const client = await prisma.client.findUnique({
      where: { id },
      include: {
        declarations: {
          select: {
            id: true,
            declarationNumber: true,
            declarationType: true,
            declarationDate: true,
          },
          orderBy: {
            declarationDate: 'desc',
          },
          take: 10,
        },
      },
    });

    if (!client) {
      throw new HttpException(404, 'العميل غير موجود', 'Not Found');
    }

    return client;
  },

  /**
   * حذف عميل
   */
  deleteClient: async (id: string) => {
    try {
      // التحقق من وجود العميل
      const client = await prisma.client.findUnique({
        where: { id },
        include: {
          declarations: true,
        },
      });

      if (!client) {
        throw new HttpException(404, 'العميل غير موجود', 'Not Found');
      }

      // التحقق من عدم وجود بيانات مرتبطة بالعميل
      if (client.declarations.length > 0) {
        throw new HttpException(
          400,
          'لا يمكن حذف العميل لوجود بيانات مرتبطة به',
          'Bad Request'
        );
      }

      // حذف العميل
      await prisma.client.delete({
        where: { id },
      });

      return { success: true };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء حذف العميل', 'Internal Server Error');
    }
  },

  /**
   * الحصول على قائمة العملاء
   */
  listClients: async (params: ListClientsParams = {}) => {
    const {
      page = 1,
      limit = 10,
      sort = 'name',
      order = 'asc',
      search,
    } = params;

    // بناء شروط البحث
    const where: Prisma.ClientWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { taxNumber: { contains: search } },
        { phone: { contains: search } },
        { email: { contains: search } },
      ];
    }

    // حساب إجمالي عدد العملاء
    const total = await prisma.client.count({ where });

    // الحصول على العملاء
    const clients = await prisma.client.findMany({
      where,
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: clients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};
