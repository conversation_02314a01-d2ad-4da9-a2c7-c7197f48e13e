import { Request, Response, NextFunction } from 'express';
import { settingsService } from '../services/settings.service.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { successResponse } from '../../../core/utils/api/apiResponse.js';
import { config } from '../../../core/config/app.config.js';
import path from 'path';
import fs from 'fs';
import multer from 'multer';

// تكوين multer لرفع الملفات
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(config.upload.dir, 'logos');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    cb(null, 'logo-' + uniqueSuffix + ext);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: config.upload.maxFileSize,
  },
  fileFilter: (req, file, cb) => {
    // التحقق من نوع الملف (صورة فقط)
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('يجب أن يكون الملف صورة') as any, false);
    }
  },
}).single('logo');

export const settingsController = {
  /**
   * الحصول على إعدادات النظام
   */
  getSettings: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على إعدادات النظام
      const settings = await settingsService.getSettings();

      return res.status(200).json(successResponse(settings));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث إعدادات النظام
   */
  updateSettings: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // التحقق من أن المستخدم مدير
      if (req.user.role !== 'ADMIN') {
        throw new HttpException(403, 'غير مسموح', 'Forbidden');
      }

      // استخدام multer لمعالجة رفع الملفات
      upload(req, res, async (err) => {
        if (err) {
          return next(new HttpException(400, err.message, 'Bad Request'));
        }

        try {
          // الحصول على بيانات الإعدادات من الطلب
          const {
            companyName,
            companyAddress,
            companyPhone,
            companyEmail,
            companyWebsite,
            primaryColor,
            secondaryColor,
            defaultFont,
            defaultLanguage,
            maxFileSize,
            enablePrinting,
          } = req.body;

          // إنشاء كائن التحديث
          const updateData: any = {};

          // إضافة البيانات إلى كائن التحديث إذا كانت موجودة
          if (companyName !== undefined) updateData.companyName = companyName;
          if (companyAddress !== undefined) updateData.companyAddress = companyAddress;
          if (companyPhone !== undefined) updateData.companyPhone = companyPhone;
          if (companyEmail !== undefined) updateData.companyEmail = companyEmail;
          if (companyWebsite !== undefined) updateData.companyWebsite = companyWebsite;
          if (primaryColor !== undefined) updateData.primaryColor = primaryColor;
          if (secondaryColor !== undefined) updateData.secondaryColor = secondaryColor;
          if (defaultFont !== undefined) updateData.defaultFont = defaultFont;
          if (defaultLanguage !== undefined) updateData.defaultLanguage = defaultLanguage;
          if (maxFileSize !== undefined) updateData.maxFileSize = parseInt(maxFileSize);
          if (enablePrinting !== undefined) updateData.enablePrinting = enablePrinting === 'true';

          // إضافة مسار الشعار إذا تم رفع ملف
          if (req.file) {
            updateData.companyLogo = `/uploads/logos/${req.file.filename}`;
          }

          // تحديث إعدادات النظام
          const settings = await settingsService.updateSettings(updateData);

          return res.status(200).json(successResponse(settings, 'تم تحديث الإعدادات بنجاح'));
        } catch (error) {
          next(error);
        }
      });
    } catch (error) {
      next(error);
    }
  },
};
