export enum ItemMovementStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum ItemMovementType {
  IN = 'IN',
  OUT = 'OUT',
  TRANSFER = 'TRANSFER',
  ADJUSTMENT = 'ADJUSTMENT'
}

export interface ItemMovementFormData {
  itemName: string;
  movementType: ItemMovementType;
  quantity: number;
  date: Date;
  notes?: string;
  declarationId?: string;
  status: ItemMovementStatus;
}
