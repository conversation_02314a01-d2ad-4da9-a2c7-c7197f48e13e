generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./prisma/test.db"
}

model User {
  id              String           @id @default(uuid())
  username        String           @unique
  email           String           @unique
  password        String
  name            String
  role            UserRole         @default(USER)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  isActive        Boolean          @default(true)
  customForms     CustomForm[]
  declarations    Declaration[]
  reportTemplates ReportTemplate[]
  sessions        Session[]

  @@map("users")
}

model Client {
  id           String        @id @default(uuid())
  name         String
  taxNumber    String        @unique
  phone        String?
  email        String?
  address      String?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  declarations Declaration[]

  @@index([name])
  @@index([phone])
  @@index([email])
  @@index([createdAt])
  @@map("clients")
}

model Declaration {
  id                      String                   @id @default(uuid())
  declarationNumber       String                   @unique
  taxNumber               String
  clientName              String
  companyName             String?
  policyNumber            String?
  invoiceNumber           String?
  gatewayEntryNumber      String
  declarationType         DeclarationType
  declarationDate         DateTime
  count                   Int?
  weight                  Float?
  goodsType               GoodsType?
  itemsCount              Int?
  entryDate               DateTime?
  exitDate                DateTime?
  pdfFile                 String?
  clientId                String?
  userId                  String?
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  authorizations Authorization[]
  client         Client?         @relation(fields: [clientId], references: [id])
  createdBy      User?           @relation(fields: [userId], references: [id])
  drivers        Driver[]
  guarantees     Guarantee[]
  itemMovements  ItemMovement[]
  permits        Permit[]
  receipts       Receipt[]
  releases       Release[]

  @@index([taxNumber])
  @@index([clientName])
  @@index([companyName])
  @@index([invoiceNumber])
  @@index([declarationType])
  @@index([declarationDate])
  @@index([goodsType])
  @@index([clientId])
  @@index([userId])
  @@index([createdAt])
  @@index([entryDate])
  @@index([exitDate])
  @@map("declarations")
}

model Driver {
  id            String      @id @default(uuid())
  declarationId String
  driverName    String
  truckNumber   String
  trailerNumber String?
  driverPhone   String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  declaration   Declaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("drivers")
}

model ItemMovement {
  id            String      @id @default(uuid())
  declarationId String
  itemName      String
  quantity      Int
  unit          String
  movementDate  DateTime
  movementType  String
  notes         String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  declaration   Declaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@index([declarationId])
  @@index([itemName])
  @@index([movementDate])
  @@index([movementType])
  @@index([createdAt])
  @@map("item_movements")
}

model Authorization {
  id                String            @id @default(uuid())
  declarationId     String
  authorizationType AuthorizationType
  authorizedPerson  String
  idNumber          String
  startDate         DateTime
  endDate           DateTime?
  notes             String?
  pdfFile           String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  declaration       Declaration       @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("authorizations")
}

model Release {
  id               String      @id @default(uuid())
  releaseNumber    String      @unique
  issuingAuthority String
  invoiceNumber    String
  invoiceDate      DateTime?
  invoiceValue     Float?
  approvalDate     DateTime
  releaseStartDate DateTime
  releaseEndDate   DateTime
  driverPermit     Boolean     @default(false)
  pdfFile          String?
  declarationId    String
  createdAt        DateTime    @default(now())
  updatedAt        DateTime    @updatedAt
  declaration      Declaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@index([invoiceNumber])
  @@index([approvalDate])
  @@index([releaseStartDate])
  @@index([releaseEndDate])
  @@index([declarationId])
  @@index([createdAt])
  @@map("releases")
}

model Permit {
  id            String      @id @default(uuid())
  declarationId String
  permitNumber  String      @unique
  permitDate    DateTime
  expiryDate    DateTime?
  notes         String?
  pdfFile       String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  declaration   Declaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("permits")
}

model Guarantee {
  id              String          @id @default(uuid())
  declarationId   String
  guaranteeNumber String          @unique
  amount          Float
  currency        Currency
  issueDate       DateTime
  expiryDate      DateTime?
  status          GuaranteeStatus @default(ACTIVE)
  notes           String?
  pdfFile         String?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  declaration     Declaration     @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@index([declarationId])
  @@index([status])
  @@index([issueDate])
  @@index([expiryDate])
  @@index([amount])
  @@map("guarantees")
}

model Receipt {
  id            String      @id @default(uuid())
  declarationId String
  receiptNumber String      @unique
  receiptDate   DateTime
  receivedBy    String
  notes         String?
  pdfFile       String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  declaration   Declaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@index([declarationId])
  @@index([receiptDate])
  @@index([receivedBy])
  @@index([createdAt])
  @@map("receipts")
}

model Document {
  id          String   @id @default(uuid())
  title       String
  description String?
  fileName    String
  fileSize    Int
  fileType    String
  filePath    String
  uploadedBy  String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("documents")
}

model InvalidatedToken {
  id            String    @id @default(uuid())
  token         String    @unique
  tokenType     TokenType
  userId        String
  expiresAt     DateTime
  invalidatedAt DateTime  @default(now())

  @@index([expiresAt])
  @@map("invalidated_tokens")
}

model Session {
  id           String   @id @default(uuid())
  userId       String
  ipAddress    String?
  userAgent    String?
  deviceInfo   String?
  isActive     Boolean  @default(true)
  lastActivity DateTime @default(now())
  createdAt    DateTime @default(now())
  expiresAt    DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expiresAt])
  @@map("sessions")
}

model LoginAttempt {
  id            String      @id @default(uuid())
  username      String
  ipAddress     String?
  userAgent     String?
  status        LoginStatus
  attemptTime   DateTime    @default(now())
  failureReason String?

  @@index([username])
  @@index([ipAddress])
  @@index([attemptTime])
  @@map("login_attempts")
}

model AuditLog {
  id        String   @id @default(uuid())
  action    String
  userId    String
  details   Json?
  createdAt DateTime @default(now())

  @@map("audit_logs")
}

model CustomForm {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  formType    String
  fields      Json
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String
  user        User     @relation(fields: [userId], references: [id])

  @@map("custom_forms")
}

model ReportTemplate {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  reportType  String
  template    Json
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String
  user        User     @relation(fields: [userId], references: [id])

  @@map("report_templates")
}

model SystemSettings {
  id              String   @id @default("default")
  companyName     String   @default("نظام النور للأرشفة")
  companyLogo     String?
  companyAddress  String?
  companyPhone    String?
  companyEmail    String?
  companyWebsite  String?
  primaryColor    String   @default("#1976d2")
  secondaryColor  String   @default("#dc004e")
  defaultFont     String   @default("Tajawal")
  defaultLanguage String   @default("ar")
  maxFileSize     Int      @default(*********)
  enablePrinting  Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("system_settings")
}

enum UserRole {
  ADMIN
  USER
  MANAGER
}

enum DeclarationType {
  IMPORT
  EXPORT
}

enum GoodsType {
  HUMAN_MEDICINE
  LABORATORY_SOLUTIONS
  MEDICAL_SUPPLIES
  SUGAR_STRIPS
  MEDICAL_DEVICES
  MISCELLANEOUS
}



enum AuthorizationType {
  FOLLOW_UP
  CLEARANCE
  RECEIPT
  FULL
}



enum Currency {
  USD
  EUR
  GBP
  SAR
}

enum GuaranteeStatus {
  ACTIVE
  RETURNED
  EXPIRED
}

enum TokenType {
  ACCESS
  REFRESH
}

enum LoginStatus {
  SUCCESS
  FAILED
  LOCKED
  SUSPICIOUS
}
