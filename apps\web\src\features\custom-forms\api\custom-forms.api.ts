import axios from 'axios';
import { CustomForm, CustomFormFormValues } from '../types/custom-form.types';

/**
 * واجهة معلمات البحث للنماذج المخصصة
 */
export interface CustomFormsParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  formType?: string;
  isActive?: boolean;
}

/**
 * واجهة استجابة قائمة النماذج المخصصة
 */
export interface CustomFormsResponse {
  data: CustomForm[];
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

/**
 * الحصول على قائمة النماذج المخصصة
 */
export const getCustomForms = async (params: CustomFormsParams = {}): Promise<CustomFormsResponse> => {
  const response = await axios.get('/api/custom-forms', { params });
  return response.data;
};

/**
 * الحصول على نموذج مخصص محدد
 */
export const getCustomForm = async (id: string): Promise<CustomForm> => {
  const response = await axios.get(`/api/custom-forms/${id}`);
  return response.data.data;
};

/**
 * إنشاء نموذج مخصص جديد
 */
export const createCustomForm = async (data: CustomFormFormValues): Promise<CustomForm> => {
  const response = await axios.post('/api/custom-forms', data);
  return response.data.data;
};

/**
 * تحديث نموذج مخصص
 */
export const updateCustomForm = async (id: string, data: Partial<CustomFormFormValues>): Promise<CustomForm> => {
  const response = await axios.put(`/api/custom-forms/${id}`, data);
  return response.data.data;
};

/**
 * حذف نموذج مخصص
 */
export const deleteCustomForm = async (id: string): Promise<void> => {
  await axios.delete(`/api/custom-forms/${id}`);
};
