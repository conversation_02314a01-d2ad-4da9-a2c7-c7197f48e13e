// إعداد محسن للاختبارات التكاملية
import dotenv from 'dotenv';
import { testPrisma } from './test-prisma-client.js';
import { clearMockDatabase } from '../__mocks__/@prisma/client.js';
import { generateToken } from '../jwt.js';
import bcrypt from 'bcryptjs';
import { execSync } from 'child_process';
import { existsSync, unlinkSync } from 'fs';
import path from 'path';

// تعيين بيئة الاختبار أولاً
process.env.NODE_ENV = 'test';

// تحميل متغيرات البيئة للاختبار
dotenv.config({
  path: path.join(process.cwd(), '.env.test'),
  override: false
});

// تحميل ملف .env الافتراضي كـ fallback
dotenv.config({
  path: path.join(process.cwd(), '.env'),
  override: false
});

// إعدادات JWT للاختبار
if (!process.env.JWT_SECRET) {
  process.env.JWT_SECRET = 'test_jwt_secret_key_for_testing_only';
}

if (!process.env.JWT_REFRESH_SECRET) {
  process.env.JWT_REFRESH_SECRET = 'test_jwt_refresh_secret_key_for_testing_only';
}

// تعطيل الـ logging في الاختبارات
process.env.LOG_LEVEL = 'error';

/**
 * إعداد قاعدة البيانات للاختبارات التكاملية
 */
export const setupIntegrationDatabase = async () => {
  try {
    // إعداد قاعدة بيانات SQLite للاختبارات
    const dbPath = path.join(process.cwd(), 'prisma', 'test.db');

    // حذف قاعدة البيانات السابقة إن وجدت مع معالجة أفضل للأخطاء
    if (existsSync(dbPath)) {
      try {
        // محاولة إغلاق أي اتصالات مفتوحة أولاً
        if (testPrisma) {
          await testPrisma.$disconnect();
        }

        // انتظار قصير للتأكد من إغلاق الاتصالات
        await new Promise(resolve => setTimeout(resolve, 100));

        unlinkSync(dbPath);
        console.log('🗑️ تم حذف قاعدة البيانات السابقة');
      } catch (error) {
        console.warn('⚠️ تحذير: لا يمكن حذف قاعدة البيانات السابقة، سيتم المتابعة:', error instanceof Error ? error.message : String(error));
        // لا نرمي خطأ هنا، سنحاول المتابعة
      }
    }

    // تطبيق المخطط باستخدام schema.test.prisma
    console.log('📋 تطبيق مخطط قاعدة البيانات...');
    process.env.DATABASE_URL = `file:${dbPath}`;
    execSync('npx prisma db push --schema=./prisma/schema.test.prisma --force-reset', {
      stdio: 'pipe',
      cwd: process.cwd(),
      env: { ...process.env, DATABASE_URL: `file:${dbPath}` }
    });

    await testPrisma.$connect();
    console.log('✅ تم إعداد قاعدة بيانات SQLite للاختبارات');
  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error);
    throw error;
  }
};

/**
 * تنظيف قاعدة البيانات
 */
export const cleanupIntegrationDatabase = async (preserveSharedUser = true): Promise<void> => {
  try {
    // تنظيف قاعدة البيانات الوهمية
    clearMockDatabase();

    // التأكد من وجود اتصال نشط
    if (!testPrisma) {
      console.log('⚠️ لا يوجد اتصال بقاعدة البيانات للتنظيف');
      return;
    }

    // حذف البيانات بترتيب يحترم القيود الخارجية مع معالجة الأخطاء
    // إزالة الجداول غير الموجودة في schema الاختبار
    const cleanupOperations = [
      () => testPrisma.itemMovement.deleteMany(),
      () => testPrisma.authorization.deleteMany(),
      () => testPrisma.release.deleteMany(),
      () => testPrisma.permit.deleteMany(),
      () => testPrisma.receipt.deleteMany(),
      () => testPrisma.driver.deleteMany(),
      () => testPrisma.declaration.deleteMany(),
      () => testPrisma.client.deleteMany(),
      () => testPrisma.session.deleteMany(),
      () => testPrisma.customForm.deleteMany(),
      () => testPrisma.reportTemplate.deleteMany(),
      () => testPrisma.loginAttempt.deleteMany(),
      () => testPrisma.invalidatedToken.deleteMany(),
      () => testPrisma.auditLog.deleteMany()
    ];

    // تنفيذ عمليات التنظيف مع معالجة الأخطاء
    for (const operation of cleanupOperations) {
      try {
        await operation();
      } catch (error) {
        console.warn('تحذير: خطأ في عملية تنظيف:', error instanceof Error ? error.message : String(error));
      }
    }

    // حذف المستخدمين مع الحفاظ على المستخدم المشترك إذا طُلب ذلك
    try {
      if (preserveSharedUser && sharedTestUser) {
        await testPrisma.user.deleteMany({
          where: {
            id: {
              not: sharedTestUser.id
            }
          }
        });
        console.log('🧹 تم تنظيف قاعدة البيانات مع الحفاظ على المستخدم المشترك');
      } else {
        await testPrisma.user.deleteMany();
        sharedTestUser = null; // إعادة تعيين المستخدم المشترك
        console.log('🧹 تم تنظيف قاعدة البيانات بالكامل');
      }
    } catch (error) {
      console.warn('تحذير: خطأ في حذف المستخدمين:', error instanceof Error ? error.message : String(error));
    }
  } catch (error) {
    console.warn('تحذير: خطأ عام في تنظيف قاعدة البيانات:', error);
  }
};

// متغير لحفظ المستخدم المشترك
let sharedTestUser: any = null;
// متغير لحفظ كلمة المرور الخام للمستخدم المشترك
const sharedTestUserPassword: string = 'Test@123';

/**
 * الحصول على كلمة المرور الخام للمستخدم المشترك
 */
export const getSharedTestUserPassword = (): string => {
  return sharedTestUserPassword;
};

/**
 * إنشاء مستخدم اختبار مشترك
 */
export const createIntegrationTestUser = async () => {
  if (!testPrisma) {
    throw new Error('قاعدة البيانات غير مهيأة');
  }

  // إذا كان المستخدم موجود بالفعل، تحقق من وجوده في قاعدة البيانات
  if (sharedTestUser) {
    try {
      const existingUser = await testPrisma.user.findUnique({
        where: { id: sharedTestUser.id }
      });
      if (existingUser) {
        // التأكد من أن كلمة المرور صحيحة
        const isPasswordValid = await bcrypt.compare(sharedTestUserPassword, existingUser.password);
        if (isPasswordValid) {
          console.log('🔄 استخدام مستخدم الاختبار الموجود:', {
            id: existingUser.id,
            username: existingUser.username
          });
          return existingUser;
        } else {
          // إعادة تعيين كلمة المرور إذا لم تكن صحيحة
          const hashedPassword = await bcrypt.hash(sharedTestUserPassword, 12);
          const updatedUser = await testPrisma.user.update({
            where: { id: existingUser.id },
            data: { password: hashedPassword, isActive: true }
          });
          console.log('🔄 تم إعادة تعيين كلمة المرور للمستخدم الموجود');
          return updatedUser;
        }
      }
    } catch (error) {
      console.warn('تحذير: فشل في التحقق من المستخدم الموجود:', error);
    }
  }

  try {
    const timestamp = Date.now();
    const username = `test_admin_${timestamp}`;

    // التأكد من تشفير كلمة المرور بشكل صحيح
    const password = sharedTestUserPassword;
    if (!password) {
      throw new Error('كلمة المرور مطلوبة');
    }

    // تنظيف أي بيانات قديمة للمستخدم
    await testPrisma.invalidatedToken.deleteMany({});
    await testPrisma.loginAttempt.deleteMany({});

    const hashedPassword = await bcrypt.hash(password, 12);

    // التحقق من أن كلمة المرور تم تشفيرها
    if (!hashedPassword) {
      throw new Error('فشل في تشفير كلمة المرور');
    }

    // التحقق من صحة التشفير
    const isHashValid = await bcrypt.compare(password, hashedPassword);
    if (!isHashValid) {
      throw new Error('فشل في التحقق من تشفير كلمة المرور');
    }

    const user = await testPrisma.user.create({
      data: {
        id: `test-user-${timestamp}`,
        username,
        password: hashedPassword,
        name: 'مستخدم اختبار',
        email: `test_${timestamp}@example.com`,
        role: 'ADMIN',
        isActive: true,
      },
    });

    // التحقق من إنشاء المستخدم بنجاح
    if (!user || !user.password) {
      throw new Error('فشل في إنشاء المستخدم أو كلمة المرور فارغة');
    }

    // التحقق النهائي من كلمة المرور
    const finalPasswordCheck = await bcrypt.compare(password, user.password);
    if (!finalPasswordCheck) {
      throw new Error('فشل في التحقق النهائي من كلمة المرور');
    }

    // حفظ المستخدم للاستخدام المشترك
    sharedTestUser = user;

    console.log('✅ تم إنشاء مستخدم الاختبار الجديد:', {
      id: user.id,
      username: user.username,
      hasPassword: !!user.password,
      passwordLength: user.password.length,
      passwordVerified: finalPasswordCheck
    });

    return user;
  } catch (error) {
    console.error('❌ خطأ في إنشاء مستخدم الاختبار:', error);
    throw error;
  }
};

/**
 * الحصول على رمز المصادقة للاختبارات
 */
export const getIntegrationAuthToken = async (user: any): Promise<string> => {
  if (!testPrisma) {
    throw new Error('قاعدة البيانات غير مهيأة');
  }

  try {
    // تنظيف البيانات المتعلقة بالمستخدم
    await testPrisma.invalidatedToken.deleteMany({
      where: { userId: user.id }
    });

    await testPrisma.loginAttempt.deleteMany({
      where: { username: user.username }
    });

    // التأكد من أن المستخدم نشط
    await testPrisma.user.update({
      where: { id: user.id },
      data: { isActive: true }
    });

    // إنشاء JWT (متوافق مع middleware المصادقة)
    const payload = {
      id: user.id,
      username: user.username,
      role: user.role
    };

    const token = generateToken(payload, 'access', '24h');
    console.log('✅ تم إنشاء رمز المصادقة للاختبار');

    return token;
  } catch (error) {
    console.error('❌ خطأ في الحصول على رمز المصادقة:', error);
    throw error;
  }
};

/**
 * إنشاء بيانات تجريبية أساسية
 */
export const seedIntegrationTestData = async () => {
  if (!testPrisma) {
    throw new Error('قاعدة البيانات غير مهيأة');
  }

  try {
    console.log('🌱 إضافة بيانات تجريبية...');

    // إنشاء مستخدم
    const user = await createIntegrationTestUser();

    // إنشاء عميل تجريبي
    const timestamp = Date.now();
    const client = await testPrisma.client.create({
      data: {
        clientNumber: 'C001',
        taxNumber: `TX${timestamp}`,
        clientName: 'شركة الاختبار',
        companyName: 'Test Company',
        phone: '+966501234567',
        email: `client_${timestamp}@test.com`,
        address: 'الرياض، المملكة العربية السعودية'
      }
    });

    // إنشاء بيان تجريبي
    const declaration = await testPrisma.declaration.create({
      data: {
        declarationNumber: `D${timestamp}`,
        taxNumber: client.taxNumber,
        clientName: client.clientName,
        companyName: client.companyName,
        policyNumber: '12345',
        invoiceNumber: '67890',
        gatewayEntryNumber: '11111',
        declarationType: 'IMPORT',
        declarationDate: new Date(),
        count: 10,
        weight: 100.5,
        goodsType: 'MEDICAL_SUPPLIES',
        itemsCount: 5,
        clientId: client.id,
        userId: user.id
      }
    });

    // إنشاء حركة صنف تجريبية
    await testPrisma.itemMovement.create({
      data: {
        movementNumber: `M${timestamp}`,
        movementDate: new Date(),
        declarationNumber: declaration.declarationNumber,
        itemNumber: 'I001',
        invoiceNumber: 'INV001',
        itemName: 'أدوية طبية',
        quantity: 100,
        packageType: 'CARTON',
        goodsType: 'MEDICAL_SUPPLIES',
        countryOfOrigin: 'USA',
        itemValue: 1000.0,
        currency: 'USD',
        totalValue: 1000.0,
        declarationId: declaration.id
      }
    });

    console.log('✅ تم إضافة البيانات التجريبية بنجاح');

    return { user, client, declaration };
  } catch (error) {
    console.error('❌ خطأ في إضافة البيانات التجريبية:', error);
    throw error;
  }
};

/**
 * إغلاق الاتصال بقاعدة البيانات
 */
export const disconnectIntegrationDatabase = async (): Promise<void> => {
  try {
    if (testPrisma) {
      await testPrisma.$disconnect();
      console.log('🔌 تم قطع الاتصال بقاعدة البيانات');

      // انتظار إضافي للتأكد من إغلاق الاتصال
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  } catch (error) {
    console.warn('تحذير: خطأ في قطع الاتصال:', error instanceof Error ? error.message : String(error));
  }
};

/**
 * الحصول على عميل Prisma للاختبارات
 */
export const getIntegrationPrismaClient = () => {
  return testPrisma;
};

// إعداد Jest hooks
beforeAll(async () => {
  await setupIntegrationDatabase();
  await cleanupIntegrationDatabase();
}, 180000); // 3 دقائق timeout

afterAll(async () => {
  await cleanupIntegrationDatabase();
  await disconnectIntegrationDatabase();
}, 60000); // دقيقة واحدة timeout

beforeEach(async () => {
  // إعادة تعيين البيانات قبل كل اختبار مع الحفاظ على المستخدم المشترك
  // لا نحذف المستخدم في beforeEach لتجنب مشاكل اختبارات المصادقة
  try {
    // حذف البيانات الأخرى فقط - إزالة الجداول غير الموجودة
    await testPrisma.itemMovement.deleteMany({});
    await testPrisma.receipt.deleteMany({});
    await testPrisma.permit.deleteMany({});
    await testPrisma.release.deleteMany({});
    await testPrisma.authorization.deleteMany({});
    await testPrisma.driver.deleteMany({});
    await testPrisma.declaration.deleteMany({});
    await testPrisma.client.deleteMany({});
    await testPrisma.loginAttempt.deleteMany({});
    await testPrisma.session.deleteMany({});
    await testPrisma.invalidatedToken.deleteMany({});
    await testPrisma.customForm.deleteMany({});
    await testPrisma.reportTemplate.deleteMany({});
    await testPrisma.auditLog.deleteMany({});
    // لا نحذف المستخدمين هنا
  } catch (error) {
    console.warn('تحذير: خطأ في تنظيف البيانات:', error);
  }
});
