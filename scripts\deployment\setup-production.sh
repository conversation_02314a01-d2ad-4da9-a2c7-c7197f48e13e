#!/bin/bash

# سكريبت إعداد بيئة الإنتاج لنظام النور للأرشفة
# يجب تشغيل هذا السكريبت كمستخدم له صلاحيات sudo

# التحقق من وجود صلاحيات sudo
if [ "$(id -u)" != "0" ]; then
   echo "يجب تشغيل هذا السكريبت كمستخدم root أو باستخدام sudo"
   exit 1
fi

# تعيين المتغيرات
APP_DIR="/opt/alnoorarch"
DOMAIN="alnoorarch.com"
EMAIL="<EMAIL>"

# تحديث النظام
echo "تحديث النظام..."
apt update
apt upgrade -y

# تثبيت المتطلبات الأساسية
echo "تثبيت المتطلبات الأساسية..."
apt install -y apt-transport-https ca-certificates curl software-properties-common gnupg lsb-release

# تثبيت Docker
echo "تثبيت Docker..."
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
apt update
apt install -y docker-ce docker-ce-cli containerd.io

# تثبيت Docker Compose
echo "تثبيت Docker Compose..."
curl -L "https://github.com/docker/compose/releases/download/v2.18.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# التحقق من تثبيت Docker و Docker Compose
docker --version
docker-compose --version

# إنشاء مجلدات النظام
echo "إنشاء مجلدات النظام..."
mkdir -p $APP_DIR
mkdir -p $APP_DIR/nginx/conf.d
mkdir -p $APP_DIR/nginx/ssl
mkdir -p $APP_DIR/nginx/logs
mkdir -p $APP_DIR/uploads
mkdir -p $APP_DIR/backups

# تثبيت Certbot للحصول على شهادة SSL
echo "تثبيت Certbot..."
apt install -y certbot

# إيقاف أي خدمات تستخدم المنفذ 80 مؤقتًا
systemctl stop nginx || true
systemctl stop apache2 || true

# الحصول على شهادة SSL
echo "الحصول على شهادة SSL..."
certbot certonly --standalone -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email $EMAIL

# نسخ الشهادات إلى مجلد Nginx
echo "نسخ شهادات SSL..."
cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem $APP_DIR/nginx/ssl/$DOMAIN.crt
cp /etc/letsencrypt/live/$DOMAIN/privkey.pem $APP_DIR/nginx/ssl/$DOMAIN.key

# إعداد Cron Job لتجديد شهادة SSL
echo "إعداد Cron Job لتجديد شهادة SSL..."
echo "0 0 1 */2 * certbot renew --quiet && cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem $APP_DIR/nginx/ssl/$DOMAIN.crt && cp /etc/letsencrypt/live/$DOMAIN/privkey.pem $APP_DIR/nginx/ssl/$DOMAIN.key && docker-compose -f $APP_DIR/docker-compose.yml restart nginx" | crontab -

# إعداد Cron Job للنسخ الاحتياطي
echo "إعداد Cron Job للنسخ الاحتياطي..."
cat > $APP_DIR/backup.sh << 'EOF'
#!/bin/bash

# تعيين التاريخ
DATE=$(date +%Y-%m-%d_%H-%M-%S)

# تحميل متغيرات البيئة
source /opt/alnoorarch/.env

# إنشاء نسخة احتياطية من قاعدة البيانات
docker-compose -f /opt/alnoorarch/docker-compose.yml exec -T postgres pg_dump -U $POSTGRES_USER $POSTGRES_DB > /opt/alnoorarch/backups/db_backup_$DATE.sql

# ضغط النسخة الاحتياطية
gzip /opt/alnoorarch/backups/db_backup_$DATE.sql

# إنشاء نسخة احتياطية من الملفات المرفوعة
tar -czf /opt/alnoorarch/backups/uploads_backup_$DATE.tar.gz /opt/alnoorarch/uploads

# حذف النسخ الاحتياطية القديمة (أكثر من 30 يومًا)
find /opt/alnoorarch/backups -type f -name "*.gz" -mtime +30 -delete
EOF

chmod +x $APP_DIR/backup.sh
echo "0 2 * * * /opt/alnoorarch/backup.sh" | crontab -

# تعديل ملف Nginx لاستخدام شهادة SSL
echo "تعديل ملف تكوين Nginx..."
sed -i "s/localhost/$DOMAIN/g" $APP_DIR/nginx/conf.d/default.conf
sed -i "s/#listen 443 ssl/listen 443 ssl/g" $APP_DIR/nginx/conf.d/default.conf
sed -i "s/#server_name/server_name/g" $APP_DIR/nginx/conf.d/default.conf
sed -i "s/#ssl_certificate/ssl_certificate/g" $APP_DIR/nginx/conf.d/default.conf
sed -i "s/#ssl_certificate_key/ssl_certificate_key/g" $APP_DIR/nginx/conf.d/default.conf
sed -i "s/alnoorarch.crt/$DOMAIN.crt/g" $APP_DIR/nginx/conf.d/default.conf
sed -i "s/alnoorarch.key/$DOMAIN.key/g" $APP_DIR/nginx/conf.d/default.conf

# إنشاء ملف .env
echo "إنشاء ملف .env..."
if [ ! -f "$APP_DIR/.env" ]; then
    cp $APP_DIR/.env.example $APP_DIR/.env
    
    # توليد كلمة مرور عشوائية لقاعدة البيانات
    DB_PASSWORD=$(openssl rand -base64 12)
    
    # توليد مفتاح سري عشوائي لـ JWT
    JWT_SECRET=$(openssl rand -base64 32)
    JWT_REFRESH_SECRET=$(openssl rand -base64 32)
    
    # تحديث ملف .env
    sed -i "s/NODE_ENV=.*/NODE_ENV=production/g" $APP_DIR/.env
    sed -i "s/POSTGRES_PASSWORD=.*/POSTGRES_PASSWORD=$DB_PASSWORD/g" $APP_DIR/.env
    sed -i "s/DATABASE_URL=.*/DATABASE_URL=postgresql:\/\/postgres:$DB_PASSWORD@postgres:5432\/alnoor_db/g" $APP_DIR/.env
    sed -i "s/JWT_SECRET=.*/JWT_SECRET=$JWT_SECRET/g" $APP_DIR/.env
    sed -i "s/JWT_REFRESH_SECRET=.*/JWT_REFRESH_SECRET=$JWT_REFRESH_SECRET/g" $APP_DIR/.env
    sed -i "s/CORS_ORIGIN=.*/CORS_ORIGIN=https:\/\/$DOMAIN/g" $APP_DIR/.env
    sed -i "s/API_URL=.*/API_URL=https:\/\/api.$DOMAIN/g" $APP_DIR/.env
    sed -i "s/VITE_API_URL=.*/VITE_API_URL=https:\/\/api.$DOMAIN/g" $APP_DIR/.env
fi

# تشغيل الحاويات
echo "تشغيل الحاويات..."
cd $APP_DIR
docker-compose up -d

# إعداد قاعدة البيانات
echo "إعداد قاعدة البيانات..."
sleep 10 # انتظار بدء تشغيل قاعدة البيانات
docker-compose exec -T api npx prisma migrate deploy

echo "تم إعداد بيئة الإنتاج بنجاح!"
echo "يمكنك الوصول إلى التطبيق من خلال: https://$DOMAIN"
