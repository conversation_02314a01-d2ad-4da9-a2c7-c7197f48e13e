import { prisma } from './prisma.js';
import { logger } from './logger.js';
import { LoginStatus } from '@prisma/client';
import { Request } from 'express';

// عدد محاولات تسجيل الدخول الفاشلة المسموح بها قبل قفل الحساب
const MAX_FAILED_ATTEMPTS = 5;
// الفترة الزمنية (بالدقائق) التي يتم فيها حساب محاولات تسجيل الدخول الفاشلة
const FAILED_ATTEMPTS_WINDOW_MINUTES = 30;

/**
 * خدمة تتبع محاولات تسجيل الدخول
 */
export const loginAttemptService = {
  /**
   * تسجيل محاولة تسجيل دخول
   * @param username اسم المستخدم
   * @param status حالة المحاولة (نجاح، فشل، إلخ)
   * @param req طلب Express
   * @param failureReason سبب الفشل (اختياري)
   * @returns معلومات محاولة تسجيل الدخول
   */
  recordLoginAttempt: async (
    username: string,
    status: LoginStatus,
    req: Request,
    failureReason?: string
  ) => {
    try {
      const ipAddress = req.ip || req.socket.remoteAddress || 'unknown';
      const userAgent = req.headers['user-agent'] || 'unknown';

      const loginAttempt = await prisma.loginAttempt.create({
        data: {
          username,
          status,
          ipAddress,
          userAgent,
          failureReason,
        },
      });

      return loginAttempt;
    } catch (error) {
      logger.error('خطأ في تسجيل محاولة تسجيل الدخول:', error);
      // لا نريد إيقاف العملية إذا فشل تسجيل المحاولة
      return null;
    }
  },

  /**
   * التحقق مما إذا كان المستخدم مقفلًا بسبب محاولات تسجيل دخول فاشلة متعددة
   * @param username اسم المستخدم
   * @returns true إذا كان المستخدم مقفلًا، false إذا كان غير مقفل
   */
  isUserLocked: async (username: string): Promise<boolean> => {
    try {
      // تعطيل نظام القفل في بيئة الاختبار أو إذا تم تعيين متغير البيئة
      if (process.env.NODE_ENV === 'test' || process.env.DISABLE_USER_LOCKING === 'true') {
        return false;
      }

      // حساب الوقت قبل FAILED_ATTEMPTS_WINDOW_MINUTES دقيقة
      const windowStart = new Date();
      windowStart.setMinutes(windowStart.getMinutes() - FAILED_ATTEMPTS_WINDOW_MINUTES);

      // عدد محاولات تسجيل الدخول الفاشلة في النافذة الزمنية
      const failedAttempts = await prisma.loginAttempt.count({
        where: {
          username,
          status: LoginStatus.FAILED,
          attemptTime: { gte: windowStart },
        },
      });

      return failedAttempts >= MAX_FAILED_ATTEMPTS;
    } catch (error) {
      logger.error('خطأ في التحقق من حالة قفل المستخدم:', error);
      // في حالة الخطأ، نفترض أن المستخدم غير مقفل
      return false;
    }
  },

  /**
   * التحقق مما إذا كانت محاولة تسجيل الدخول مشبوهة
   * @param username اسم المستخدم
   * @param req طلب Express
   * @returns true إذا كانت المحاولة مشبوهة، false إذا كانت غير مشبوهة
   */
  isSuspiciousLoginAttempt: async (username: string, req: Request): Promise<boolean> => {
    try {
      const ipAddress = req.ip || req.socket.remoteAddress || 'unknown';

      // الحصول على آخر محاولة تسجيل دخول ناجحة
      const lastSuccessfulLogin = await prisma.loginAttempt.findFirst({
        where: {
          username,
          status: LoginStatus.SUCCESS,
        },
        orderBy: { attemptTime: 'desc' },
      });

      // إذا لم تكن هناك محاولات تسجيل دخول ناجحة سابقة، فهذه ليست محاولة مشبوهة
      if (!lastSuccessfulLogin) {
        return false;
      }

      // التحقق مما إذا كان عنوان IP مختلفًا عن آخر تسجيل دخول ناجح
      if (lastSuccessfulLogin.ipAddress && lastSuccessfulLogin.ipAddress !== ipAddress) {
        // يمكن إضافة المزيد من المنطق هنا، مثل التحقق من البلد أو المنطقة
        return true;
      }

      return false;
    } catch (error) {
      logger.error('خطأ في التحقق من محاولة تسجيل الدخول المشبوهة:', error);
      // في حالة الخطأ، نفترض أن المحاولة غير مشبوهة
      return false;
    }
  },

  /**
   * الحصول على محاولات تسجيل الدخول الأخيرة للمستخدم
   * @param username اسم المستخدم
   * @param limit عدد المحاولات المراد استرجاعها
   * @returns قائمة محاولات تسجيل الدخول
   */
  getRecentLoginAttempts: async (username: string, limit = 10) => {
    try {
      return await prisma.loginAttempt.findMany({
        where: { username },
        orderBy: { attemptTime: 'desc' },
        take: limit,
      });
    } catch (error) {
      logger.error('خطأ في الحصول على محاولات تسجيل الدخول الأخيرة:', error);
      throw new Error('فشل في الحصول على محاولات تسجيل الدخول الأخيرة');
    }
  },

  /**
   * تنظيف محاولات تسجيل الدخول القديمة
   * @param daysToKeep عدد الأيام للاحتفاظ بمحاولات تسجيل الدخول
   * @returns عدد المحاولات التي تم حذفها
   */
  cleanupOldLoginAttempts: async (daysToKeep = 90) => {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await prisma.loginAttempt.deleteMany({
        where: {
          attemptTime: { lt: cutoffDate },
        },
      });

      logger.info(`تم حذف ${result.count} محاولة تسجيل دخول قديمة`);
      return { count: result.count };
    } catch (error) {
      logger.error('خطأ في تنظيف محاولات تسجيل الدخول القديمة:', error);
      throw new Error('فشل في تنظيف محاولات تسجيل الدخول القديمة');
    }
  },
};
