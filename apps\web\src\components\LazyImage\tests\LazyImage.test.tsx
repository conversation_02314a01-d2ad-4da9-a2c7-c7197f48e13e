import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import { LazyImage } from '../LazyImage';

// Mock Intersection Observer
const mockIntersectionObserver = class {
  constructor(callback: IntersectionObserverCallback) {
    this.callback = callback;
  }

  callback: IntersectionObserverCallback;
  observe = vi.fn();
  unobserve = vi.fn();
  disconnect = vi.fn();
};

global.IntersectionObserver = mockIntersectionObserver as any;

describe('LazyImage', () => {
  it('should render skeleton while loading', () => {
    render(
      <LazyImage
        src="test-image.jpg"
        alt="Test Image"
        width={300}
        height={200}
      />
    );

    // Check if skeleton is rendered
    const skeleton = document.querySelector('.MuiSkeleton-root');
    expect(skeleton).toBeInTheDocument();
  });

  it('should not load image when lazy=true and not in view', () => {
    render(
      <LazyImage
        src="test-image.jpg"
        alt="Test Image"
        width={300}
        height={200}
        lazy={true}
      />
    );

    // Check if image is not loaded (it's hidden with display: none)
    const img = screen.getByAltText('Test Image');
    expect(img).toHaveStyle('display: none');
  });

  it('should load image immediately when lazy=false', () => {
    render(
      <LazyImage
        src="test-image.jpg"
        alt="Test Image"
        width={300}
        height={200}
        lazy={false}
      />
    );

    // Check if image element exists
    expect(screen.getByAltText('Test Image')).toBeInTheDocument();
  });

  it('should load image when it comes into view', () => {
    render(
      <LazyImage
        src="test-image.jpg"
        alt="Test Image"
        width={300}
        height={200}
        lazy={true}
      />
    );

    // Simulate intersection
    const observer = new IntersectionObserver(() => {});
    if (observer.callback) {
      observer.callback([
        {
          isIntersecting: true,
          target: document.createElement('div'),
        } as unknown as IntersectionObserverEntry,
      ]);
    }

    // Trigger image load event
    const img = screen.getByAltText('Test Image');
    img.dispatchEvent(new Event('load'));

    // Check if image is visible after loading
    expect(img).toBeInTheDocument();
  });

  it('should apply aspect ratio correctly', () => {
    render(
      <LazyImage
        src="test-image.jpg"
        alt="Test Image"
        aspectRatio="16/9"
        lazy={false}
      />
    );

    // Get the container element
    const container = screen.getByAltText('Test Image').closest('div');

    // Check if aspect ratio is applied (16:9 = 56.25%)
    expect(container).toHaveStyle({ paddingTop: '56.25%' });
  });
});
