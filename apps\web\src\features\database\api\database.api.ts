import axios from 'axios';

// واجهة معلومات النسخة الاحتياطية
export interface BackupInfo {
  fileName: string;
  size: number;
  createdAt: Date;
}

// واجهة استجابة النسخ الاحتياطية
export interface BackupsResponse {
  data: BackupInfo[];
}

// واجهة استجابة النسخة الاحتياطية
export interface BackupResponse {
  data: BackupInfo;
  message: string;
}

/**
 * الحصول على قائمة النسخ الاحتياطية
 */
export const getBackups = async (): Promise<BackupInfo[]> => {
  const response = await axios.get<BackupsResponse>('/api/database/backups');
  return response.data.data;
};

/**
 * إنشاء نسخة احتياطية جديدة
 */
export const createBackup = async (): Promise<BackupInfo> => {
  const response = await axios.post<BackupResponse>('/api/database/backups');
  return response.data.data;
};

/**
 * استعادة قاعدة البيانات من نسخة احتياطية
 */
export const restoreBackup = async (fileName: string): Promise<void> => {
  await axios.post(`/api/database/restore/${fileName}`);
};

/**
 * حذف نسخة احتياطية
 */
export const deleteBackup = async (fileName: string): Promise<void> => {
  await axios.delete(`/api/database/backups/${fileName}`);
};

/**
 * تنزيل نسخة احتياطية
 */
export const downloadBackup = (fileName: string): void => {
  window.open(`/api/database/download/${fileName}`, '_blank');
};

/**
 * تصدير قاعدة البيانات
 */
export const exportDatabase = (): void => {
  window.open('/api/database/export', '_blank');
};

/**
 * تهيئة قاعدة البيانات
 */
export const initializeDatabase = async (): Promise<void> => {
  await axios.post('/api/database/initialize');
};
