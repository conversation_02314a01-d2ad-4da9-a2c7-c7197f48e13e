import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { saveUploadedPdf } from '../../../core/utils/pdf/pdfService.js';
import { Prisma } from '@prisma/client';

interface CreateAuthorizationInput {
  declarationId: string;
  authorizationType: 'FOLLOW_UP' | 'CLEARANCE' | 'RECEIPT' | 'FULL';
  authorizedPerson: string;
  idNumber: string;
  startDate: Date;
  endDate?: Date;
  notes?: string;
}

interface UpdateAuthorizationInput {
  authorizationType?: 'FOLLOW_UP' | 'CLEARANCE' | 'RECEIPT' | 'FULL';
  authorizedPerson?: string;
  idNumber?: string;
  startDate?: Date;
  endDate?: Date;
  notes?: string;
}

interface ListAuthorizationsParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  authorizationType?: 'FOLLOW_UP' | 'CLEARANCE' | 'RECEIPT' | 'FULL';
  fromDate?: Date;
  toDate?: Date;
  declarationId?: string;
  isActive?: boolean;
}

export const authorizationService = {
  /**
   * إنشاء تفويض جديد
   */
  createAuthorization: async (
    data: CreateAuthorizationInput,
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود البيان
      const declaration = await prisma.declaration.findUnique({
        where: { id: data.declarationId },
      });

      if (!declaration) {
        throw new HttpException(404, 'البيان غير موجود', 'Not Found');
      }

      // حفظ ملف PDF إذا تم تقديمه
      let pdfFile: string | undefined;
      if (file) {
        pdfFile = saveUploadedPdf(file, 'authorizations', `auth_${Date.now()}`);
      }

      // إنشاء التفويض
      const authorization = await prisma.authorization.create({
        data: {
          declarationId: data.declarationId,
          authorizationType: data.authorizationType,
          authorizedPerson: data.authorizedPerson,
          idNumber: data.idNumber,
          startDate: data.startDate,
          endDate: data.endDate,
          notes: data.notes,
          pdfFile,
        },
      });

      return authorization;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(400, 'رقم التفويض موجود بالفعل', 'Bad Request');
        }
      }
      throw new HttpException(500, 'حدث خطأ أثناء إنشاء التفويض', 'Internal Server Error');
    }
  },

  /**
   * تحديث تفويض
   */
  updateAuthorization: async (
    id: string,
    data: UpdateAuthorizationInput,
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود التفويض
      const existingAuthorization = await prisma.authorization.findUnique({
        where: { id },
      });

      if (!existingAuthorization) {
        throw new HttpException(404, 'التفويض غير موجود', 'Not Found');
      }

      // حفظ ملف PDF إذا تم تقديمه
      let pdfFile = existingAuthorization.pdfFile;
      if (file) {
        pdfFile = saveUploadedPdf(file, 'authorizations', id);
      }

      // تحديث التفويض
      const authorization = await prisma.authorization.update({
        where: { id },
        data: {
          authorizationType: data.authorizationType,
          authorizedPerson: data.authorizedPerson,
          idNumber: data.idNumber,
          startDate: data.startDate,
          endDate: data.endDate,
          notes: data.notes,
          pdfFile,
        },
      });

      return authorization;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء تحديث التفويض', 'Internal Server Error');
    }
  },

  /**
   * الحصول على تفويض محدد
   */
  getAuthorization: async (id: string) => {
    const authorization = await prisma.authorization.findUnique({
      where: { id },
      include: {
        declaration: true,
      },
    });

    if (!authorization) {
      throw new HttpException(404, 'التفويض غير موجود', 'Not Found');
    }

    return authorization;
  },

  /**
   * حذف تفويض
   */
  deleteAuthorization: async (id: string) => {
    // التحقق من وجود التفويض
    const authorization = await prisma.authorization.findUnique({
      where: { id },
    });

    if (!authorization) {
      throw new HttpException(404, 'التفويض غير موجود', 'Not Found');
    }

    // حذف التفويض
    await prisma.authorization.delete({
      where: { id },
    });

    return { success: true };
  },

  /**
   * الحصول على قائمة التفويضات
   */
  listAuthorizations: async (params: ListAuthorizationsParams = {}) => {
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'desc',
      search,
      authorizationType,
      fromDate,
      toDate,
      declarationId,
      isActive,
    } = params;

    // بناء شروط البحث
    const where: Prisma.AuthorizationWhereInput = {};

    if (search) {
      where.OR = [
        { authorizedPerson: { contains: search } },
        { idNumber: { contains: search } },
      ];
    }

    if (authorizationType) {
      where.authorizationType = authorizationType;
    }

    if (fromDate && toDate) {
      where.startDate = {
        gte: fromDate,
      };
      where.endDate = {
        lte: toDate,
      };
    } else if (fromDate) {
      where.startDate = {
        gte: fromDate,
      };
    } else if (toDate) {
      where.endDate = {
        lte: toDate,
      };
    }

    if (declarationId) {
      where.declarationId = declarationId;
    }

    // التحقق من حالة التفويض (نشط أو منتهي)
    if (isActive !== undefined) {
      const now = new Date();
      if (isActive) {
        where.endDate = {
          gte: now,
        };
      } else {
        where.endDate = {
          lt: now,
        };
      }
    }

    // حساب إجمالي عدد التفويضات
    const total = await prisma.authorization.count({ where });

    // الحصول على التفويضات
    const authorizations = await prisma.authorization.findMany({
      where,
      include: {
        declaration: true,
      },
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: authorizations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};
