import React, { useState, useEffect, useRef } from 'react';
import { Box, Skeleton, BoxProps } from '@mui/material';

interface LazyImageProps extends Omit<BoxProps, 'component'> {
  /**
   * مسار الصورة
   */
  src: string;
  
  /**
   * النص البديل للصورة
   */
  alt: string;
  
  /**
   * مسارات الصور بأحجام مختلفة
   * مثال: "image-small.jpg 400w, image-medium.jpg 800w, image-large.jpg 1200w"
   */
  srcSet?: string;
  
  /**
   * أحجام الصورة لمختلف أحجام الشاشة
   * مثال: "(max-width: 600px) 400px, (max-width: 1200px) 800px, 1200px"
   */
  sizes?: string;
  
  /**
   * عرض الصورة
   */
  width?: string | number;
  
  /**
   * ارتفاع الصورة
   */
  height?: string | number;
  
  /**
   * هل يتم تحميل الصورة بشكل كسول
   * @default true
   */
  lazy?: boolean;
  
  /**
   * هامش التحميل المسبق (بالبكسل)
   * المسافة التي يجب أن تكون الصورة ضمنها من نطاق الرؤية لبدء التحميل
   * @default 200
   */
  threshold?: number;
  
  /**
   * هل يتم عرض هيكل التحميل
   * @default true
   */
  showSkeleton?: boolean;
  
  /**
   * نسبة العرض إلى الارتفاع
   * مثال: "16/9" أو "4/3" أو "1/1"
   */
  aspectRatio?: string;
}

/**
 * مكون الصورة الكسولة
 * يقوم بتحميل الصورة فقط عندما تكون في نطاق الرؤية أو قريبة منه
 */
export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  srcSet,
  sizes,
  width,
  height,
  lazy = true,
  threshold = 200,
  showSkeleton = true,
  aspectRatio,
  sx,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(!lazy);
  const imgRef = useRef<HTMLDivElement>(null);
  
  // استخدام Intersection Observer لتحديد متى تكون الصورة في نطاق الرؤية
  useEffect(() => {
    if (!lazy) {
      setIsInView(true);
      return;
    }
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: `${threshold}px`,
      }
    );
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => {
      observer.disconnect();
    };
  }, [lazy, threshold]);
  
  // معالج تحميل الصورة
  const handleImageLoad = () => {
    setIsLoaded(true);
  };
  
  // حساب نسبة العرض إلى الارتفاع
  const calculatePaddingTop = () => {
    if (!aspectRatio) return undefined;
    
    const [width, height] = aspectRatio.split('/').map(Number);
    return `${(height / width) * 100}%`;
  };
  
  return (
    <Box
      ref={imgRef}
      sx={{
        position: 'relative',
        width,
        height,
        overflow: 'hidden',
        paddingTop: calculatePaddingTop(),
        ...sx,
      }}
      {...props}
    >
      {showSkeleton && !isLoaded && (
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          animation="wave"
          sx={{
            position: aspectRatio ? 'absolute' : 'relative',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}
        />
      )}
      
      {isInView && (
        <Box
          component="img"
          src={src}
          alt={alt}
          srcSet={srcSet}
          sizes={sizes}
          onLoad={handleImageLoad}
          sx={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            display: isLoaded ? 'block' : 'none',
            position: aspectRatio ? 'absolute' : 'relative',
            top: 0,
            left: 0,
          }}
        />
      )}
    </Box>
  );
};
