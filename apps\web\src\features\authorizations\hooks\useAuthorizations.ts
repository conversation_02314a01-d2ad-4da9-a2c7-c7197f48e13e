import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getAuthorizations,
  getAuthorization,
  createAuthorization,
  updateAuthorization,
  deleteAuthorization,
  AuthorizationSearchParams,
} from '../api/authorizations.api';
import { CreateAuthorizationRequest, UpdateAuthorizationRequest } from '../api/authorizations.api';

// خطاف للحصول على قائمة التفويضات
export const useAuthorizations = (params: AuthorizationSearchParams = {}) => {
  return useQuery({
    queryKey: ['authorizations', params],
    queryFn: () => getAuthorizations(params),
  });
};

// خطاف للحصول على تفويض محدد
export const useAuthorization = (id: string) => {
  return useQuery({
    queryKey: ['authorization', id],
    queryFn: () => getAuthorization(id),
    enabled: !!id,
  });
};

// خطاف لإنشاء تفويض جديد
export const useCreateAuthorization = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ data, file }: { data: CreateAuthorizationRequest; file?: File }) => 
      createAuthorization(data, file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['authorizations'] });
    },
  });
};

// خطاف لتحديث تفويض
export const useUpdateAuthorization = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data, file }: { id: string; data: UpdateAuthorizationRequest; file?: File }) => 
      updateAuthorization(id, data, file),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['authorizations'] });
      queryClient.invalidateQueries({ queryKey: ['authorization', variables.id] });
    },
  });
};

// خطاف لحذف تفويض
export const useDeleteAuthorization = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => deleteAuthorization(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['authorizations'] });
    },
  });
};
