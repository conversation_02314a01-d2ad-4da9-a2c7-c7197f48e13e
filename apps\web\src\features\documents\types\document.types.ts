/**
 * أنواع المستندات
 */
export enum DocumentType {
  INVOICE = 'INVOICE',
  CONTRACT = 'CONTRACT',
  CERTIFICATE = 'CERTIFICATE',
  REPORT = 'REPORT',
  OTHER = 'OTHER',
}

/**
 * واجهة المستند
 */
export interface Document {
  id: string;
  documentNumber?: number;
  documentType?: DocumentType;
  documentDate?: string;
  documentValue?: number;
  title: string;
  description?: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  filePath: string;
  expiryDate?: string;
  issuedBy?: string;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * واجهة نموذج المستند
 */
export interface DocumentFormValues {
  documentNumber?: number;
  documentType?: DocumentType;
  documentDate?: Date | null;
  documentValue?: number;
  title: string;
  description?: string;
  expiryDate?: Date | null;
  issuedBy?: string;
  file?: File | null;
}
