import { Router } from 'express';
import multer from 'multer';
import { guaranteeController } from '../controllers/guarantee.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import {
  createGuaranteeSchema,
  updateGuaranteeSchema,
  getGuaranteeSchema,
  deleteGuaranteeSchema,
  listGuaranteesSchema,
} from '../schemas/guarantee.schema.js';
import { config } from '../../../core/config/app.config.js';

export const guaranteeRoutes = Router();

// إعداد Multer لرفع الملفات
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: config.upload.maxFileSize,
  },
  fileFilter: (req, file, cb) => {
    // قبول ملفات PDF فقط
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم. يرجى رفع ملف PDF فقط.'));
    }
  },
});

// مسارات الضمانات
guaranteeRoutes.get(
  '/',
  authMiddleware,
  validateRequest(listGuaranteesSchema),
  guaranteeController.listGuarantees
);

guaranteeRoutes.post(
  '/',
  authMiddleware,
  upload.single('file'),
  validateRequest(createGuaranteeSchema),
  guaranteeController.createGuarantee
);

guaranteeRoutes.get(
  '/:id',
  authMiddleware,
  validateRequest(getGuaranteeSchema),
  guaranteeController.getGuarantee
);

guaranteeRoutes.put(
  '/:id',
  authMiddleware,
  upload.single('file'),
  validateRequest(updateGuaranteeSchema),
  guaranteeController.updateGuarantee
);

guaranteeRoutes.delete(
  '/:id',
  authMiddleware,
  validateRequest(deleteGuaranteeSchema),
  guaranteeController.deleteGuarantee
);
