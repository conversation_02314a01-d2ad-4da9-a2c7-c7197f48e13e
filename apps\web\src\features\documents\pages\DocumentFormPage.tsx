import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  TextField,
  MenuItem,
  Typography,
  Alert,
} from '@mui/material';
import { ArrowBack as ArrowBackIcon, Save as SaveIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useDocument } from '../hooks/useDocument';
import { useCreateDocument } from '../hooks/useCreateDocument';
import { useUpdateDocument } from '../hooks/useUpdateDocument';
import { PageHeader } from '@components/PageHeader';
import { LoadingScreen } from '@components/LoadingScreen';
import { ErrorScreen } from '@components/ErrorScreen';
import { FileUpload } from '@components/FileUpload';
import { DocumentType } from '../types/document.types';

/**
 * مخطط التحقق من صحة نموذج المستند
 */
const documentSchema = z.object({
  documentNumber: z.string().min(1, 'رقم المستند مطلوب'),
  title: z.string().min(1, 'عنوان المستند مطلوب'),
  documentType: z.string().min(1, 'نوع المستند مطلوب'),
  documentDate: z.date(),
  expiryDate: z.date().nullable().optional(),
  issuingAuthority: z.string().optional(),
  referenceNumber: z.string().optional(),
  description: z.string().optional(),
});

/**
 * نوع بيانات نموذج المستند
 */
type DocumentFormData = z.infer<typeof documentSchema>;

/**
 * صفحة نموذج المستند (إضافة/تعديل)
 */
const DocumentFormPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;
  const [file, setFile] = useState<File | null>(null);

  // استخدام خطافات البيانات
  const { data: document, isLoading: isLoadingDocument, error: documentError } = useDocument(
    id || '',
    { enabled: isEditMode && !!id }
  );

  const createDocumentMutation = useCreateDocument();
  const updateDocumentMutation = useUpdateDocument();

  // إعداد نموذج المستند
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<DocumentFormData>({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      documentNumber: '',
      title: '',
      documentType: DocumentType.OTHER,
      documentDate: new Date(),
      expiryDate: null,
      issuingAuthority: '',
      referenceNumber: '',
      description: '',
    },
  });

  // تحديث النموذج عند تحميل البيانات في وضع التعديل
  useEffect(() => {
    if (isEditMode && document) {
      reset({
        documentNumber: document.documentNumber,
        title: document.title,
        documentType: document.documentType,
        documentDate: new Date(document.documentDate),
        expiryDate: document.expiryDate ? new Date(document.expiryDate) : null,
        issuingAuthority: document.issuingAuthority || '',
        referenceNumber: document.referenceNumber || '',
        description: document.description || '',
      });
    }
  }, [isEditMode, document, reset]);

  // معالجة تقديم النموذج
  const onSubmit = async (data: DocumentFormData) => {
    try {
      const formattedData = {
        ...data,
        documentDate: data.documentDate.toISOString(),
        expiryDate: data.expiryDate ? data.expiryDate.toISOString() : undefined,
      };

      if (isEditMode) {
        // تحديث مستند موجود
        await updateDocumentMutation.mutateAsync({
          id: id!,
          data: formattedData,
          file,
        });
      } else {
        // إنشاء مستند جديد
        await createDocumentMutation.mutateAsync({
          data: formattedData,
          file,
        });
      }

      // الانتقال إلى صفحة المستندات
      navigate('/documents');
    } catch (error) {
      console.error('Error saving document:', error);
    }
  };

  // التعامل مع تغيير الملف
  const handleFileChange = (files: File[]) => {
    setFile(files.length > 0 ? files[0] : null);
  };

  // عرض شاشة التحميل
  if (isEditMode && isLoadingDocument) {
    return <LoadingScreen />;
  }

  // عرض شاشة الخطأ
  if (isEditMode && (documentError || !document)) {
    return (
      <ErrorScreen
        message={t('documents.form.errorLoading')}
        onRetry={() => window.location.reload()}
      />
    );
  }

  return (
    <Box>
      <PageHeader
        title={
          isEditMode
            ? t('documents.form.editTitle')
            : t('documents.form.addTitle')
        }
        subtitle={
          isEditMode
            ? document?.title
            : t('documents.form.subtitle')
        }
        backButton={
          <Button
            component={Link}
            to="/documents"
            startIcon={<ArrowBackIcon />}
            variant="outlined"
          >
            {t('common.back')}
          </Button>
        }
      />

      <Paper sx={{ mb: 3 }}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              {t('documents.form.basicInfo')}
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="documentNumber"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('documents.documentNumber')}
                        fullWidth
                        required
                        error={!!errors.documentNumber}
                        helperText={errors.documentNumber?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="documentType"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        select
                        label={t('documents.documentType')}
                        fullWidth
                        required
                        error={!!errors.documentType}
                        helperText={errors.documentType?.message}
                        disabled={isSubmitting}
                      >
                        <MenuItem value={DocumentType.INVOICE}>
                          {t('documents.types.INVOICE')}
                        </MenuItem>
                        <MenuItem value={DocumentType.CONTRACT}>
                          {t('documents.types.CONTRACT')}
                        </MenuItem>
                        <MenuItem value={DocumentType.CERTIFICATE}>
                          {t('documents.types.CERTIFICATE')}
                        </MenuItem>
                        <MenuItem value={DocumentType.REPORT}>
                          {t('documents.types.REPORT')}
                        </MenuItem>
                        <MenuItem value={DocumentType.OTHER}>
                          {t('documents.types.OTHER')}
                        </MenuItem>
                      </TextField>
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('documents.title')}
                        fullWidth
                        required
                        error={!!errors.title}
                        helperText={errors.title?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="documentDate"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        label={t('documents.documentDate')}
                        value={field.value}
                        onChange={field.onChange}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            required: true,
                            error: !!errors.documentDate,
                            helperText: errors.documentDate?.message,
                          },
                        }}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="expiryDate"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        label={t('documents.expiryDate')}
                        value={field.value}
                        onChange={field.onChange}
                        slotProps={{
                          textField: {
                            fullWidth: true,
                            error: !!errors.expiryDate,
                            helperText: errors.expiryDate?.message,
                          },
                        }}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="issuingAuthority"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('documents.issuingAuthority')}
                        fullWidth
                        error={!!errors.issuingAuthority}
                        helperText={errors.issuingAuthority?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Controller
                    name="referenceNumber"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('documents.referenceNumber')}
                        fullWidth
                        error={!!errors.referenceNumber}
                        helperText={errors.referenceNumber?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('documents.description')}
                        fullWidth
                        multiline
                        rows={3}
                        error={!!errors.description}
                        helperText={errors.description?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('documents.form.attachFile')}
                  </Typography>
                  <FileUpload
                    accept={{
                      'application/pdf': ['.pdf'],
                      'application/msword': ['.doc'],
                      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
                      'image/*': ['.jpg', '.jpeg', '.png', '.gif']
                    }}
                    maxSize={100 * 1024 * 1024} // 100 MB
                    onChange={handleFileChange}
                    disabled={isSubmitting}
                  />
                  {isEditMode && document?.filePath && !file && (
                    <Alert severity="info" sx={{ mt: 1 }}>
                      {t('documents.form.existingFile')}
                    </Alert>
                  )}
                </Grid>

                <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button
                    component={Link}
                    to="/documents"
                    variant="outlined"
                    sx={{ mr: 1 }}
                    disabled={isSubmitting}
                  >
                    {t('common.cancel')}
                  </Button>
                  <Button
                    type="submit"
                    variant="contained"
                    startIcon={<SaveIcon />}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <CircularProgress size={24} />
                    ) : (
                      t('common.save')
                    )}
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </CardContent>
        </Card>
      </Paper>
    </Box>
  );
};

export default DocumentFormPage;
