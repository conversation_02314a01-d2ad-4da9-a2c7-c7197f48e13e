import { useMutation } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';

export const useDownloadDocument = () => {
  return useMutation({
    mutationFn: async (id: string): Promise<Blob> => {
      const response = await api.downloadFile(`/api/documents/${id}/download`);
      return response;
    },
    onSuccess: (blob, id) => {
      // إنشاء رابط تحميل تلقائي
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `document-${id}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
  });
};
