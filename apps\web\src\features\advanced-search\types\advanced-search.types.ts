import { SearchType, DeclarationType, GoodsType } from '../api/advanced-search.api';

/**
 * واجهة نموذج البحث المتقدم
 */
export interface AdvancedSearchFormValues {
  searchType: SearchType;
  keyword?: string;
  taxNumber?: string;
  clientId?: string;
  declarationNumber?: number | '';
  invoiceNumber?: number | '';
  declarationType?: DeclarationType;
  goodsType?: GoodsType;
  fromDate?: Date | null;
  toDate?: Date | null;
}

/**
 * واجهة نتائج البيان
 */
export interface DeclarationResult {
  id: string;
  declarationNumber: number;
  taxNumber: string;
  clientName?: string;
  companyName?: string;
  declarationType: DeclarationType;
  declarationDate?: string;
  createdAt: string;
  client?: {
    id: string;
    name: string;
    taxNumber: string;
  };
}

/**
 * واجهة نتائج حركة الصنف
 */
export interface ItemMovementResult {
  id: string;
  movementNumber: number;
  movementDate?: string;
  declarationNumber: number;
  itemName?: string;
  goodsType?: GoodsType;
  countryOfOrigin?: string;
  createdAt: string;
  declaration?: {
    id: string;
    declarationNumber: number;
    declarationType: DeclarationType;
    clientName?: string;
  };
}

/**
 * واجهة نتائج التفويض
 */
export interface AuthorizationResult {
  id: string;
  authorizationNumber?: number;
  clientName?: string;
  taxNumber: string;
  authorizationType: string;
  startDate: string;
  endDate: string;
  createdAt: string;
  client?: {
    id: string;
    name: string;
    taxNumber: string;
  };
}

/**
 * واجهة نتائج الإفراج
 */
export interface ReleaseResult {
  id: string;
  releaseNumber: number;
  invoiceNumber: number;
  approvalIssueDate: string;
  bondStartDate: string;
  bondEndDate: string;
  createdAt: string;
  declaration?: {
    id: string;
    declarationNumber: number;
    clientName?: string;
  };
}

/**
 * واجهة نتائج التصريح
 */
export interface PermitResult {
  id: string;
  permitNumber: number;
  permitType: string;
  permitDate?: string;
  expiryDate?: string;
  createdAt: string;
  declaration?: {
    id: string;
    declarationNumber: number;
    clientName?: string;
  };
}

/**
 * واجهة نتائج الضمان
 */
export interface GuaranteeResult {
  id: string;
  guaranteeNumber: string;
  guaranteeType: string;
  guaranteeNature: string;
  issueDate: string;
  expiryDate?: string;
  isReturned: boolean;
  createdAt: string;
  declaration?: {
    id: string;
    declarationNumber: number;
    clientName?: string;
  };
}

/**
 * واجهة نتائج الاستلام
 */
export interface ReceiptResult {
  id: string;
  receiptNumber: string;
  receiptDate: string;
  receiverName: string;
  createdAt: string;
  declaration?: {
    id: string;
    declarationNumber: number;
    clientName?: string;
  };
}

/**
 * واجهة نتائج العميل
 */
export interface ClientResult {
  id: string;
  clientNumber?: number;
  taxNumber: string;
  name: string;
  companyName?: string;
  createdAt: string;
}

/**
 * واجهة نتائج المستند
 */
export interface DocumentResult {
  id: string;
  documentNumber?: number;
  documentType?: string;
  documentDate?: string;
  title: string;
  createdAt: string;
}
