import React, { Suspense, lazy, ComponentType, LazyExoticComponent } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

interface LazyLoadProps {
  /**
   * المكون الذي سيتم تحميله بشكل كسول
   */
  component: LazyExoticComponent<ComponentType<any>>;
  
  /**
   * خصائص المكون
   */
  props?: Record<string, any>;
  
  /**
   * مكون التحميل
   */
  fallback?: React.ReactNode;
  
  /**
   * رسالة التحميل
   */
  loadingMessage?: string;
}

/**
 * مكون التحميل الافتراضي
 */
const DefaultLoading: React.FC<{ message?: string }> = ({ message }) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      p: 4,
    }}
  >
    <CircularProgress size={40} />
    {message && (
      <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
        {message}
      </Typography>
    )}
  </Box>
);

/**
 * مكون التحميل الكسول
 * يستخدم لتحميل المكونات بشكل كسول لتحسين أداء التطبيق
 */
export const LazyLoad: React.FC<LazyLoadProps> = ({
  component: Component,
  props = {},
  fallback,
  loadingMessage,
}) => {
  return (
    <Suspense fallback={fallback || <DefaultLoading message={loadingMessage} />}>
      <Component {...props} />
    </Suspense>
  );
};

/**
 * دالة مساعدة لإنشاء مكون كسول
 * @param factory - دالة استيراد المكون
 * @param options - خيارات التحميل الكسول
 * @returns مكون كسول
 */
export function createLazyComponent<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  options?: {
    /**
     * هل يتم تحميل المكون مسبقًا
     * @default false
     */
    preload?: boolean;
    
    /**
     * تأخير التحميل المسبق (بالمللي ثانية)
     * @default 0
     */
    preloadDelay?: number;
  }
): LazyExoticComponent<T> {
  const LazyComponent = lazy(factory);
  
  // تحميل المكون مسبقًا إذا كان مطلوبًا
  if (options?.preload) {
    const delay = options.preloadDelay || 0;
    
    setTimeout(() => {
      factory();
    }, delay);
  }
  
  return LazyComponent;
}
