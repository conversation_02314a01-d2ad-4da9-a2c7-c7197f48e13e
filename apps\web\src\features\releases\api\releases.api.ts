import axios from 'axios';
import { Release, ReleaseType } from '../types/release.types';

// واجهة طلب إنشاء الإفراج
export interface CreateReleaseRequest {
  releaseType: ReleaseType;
  startDate: string;
  endDate: string;
  notes?: string;
  declarationId?: string;
  clientId?: string;
}

// واجهة طلب تحديث الإفراج
export interface UpdateReleaseRequest {
  releaseType?: ReleaseType;
  startDate?: string;
  endDate?: string;
  notes?: string;
  declarationId?: string;
  clientId?: string;
}

// واجهة معلمات البحث عن الإفراجات
export interface ReleaseSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  releaseType?: ReleaseType;
  fromDate?: Date;
  toDate?: Date;
  declarationId?: string;
  clientId?: string;
  isActive?: boolean;
}

// الحصول على قائمة الإفراجات
export const getReleases = async (params: ReleaseSearchParams = {}) => {
  const { data } = await axios.get('/api/releases', { params });
  return data;
};

// الحصول على إفراج محدد
export const getRelease = async (id: string) => {
  const { data } = await axios.get(`/api/releases/${id}`);
  return data.data;
};

// إنشاء إفراج جديد
export const createRelease = async (
  releaseData: CreateReleaseRequest,
  file?: File
) => {
  const formData = new FormData();
  formData.append('data', JSON.stringify(releaseData));
  
  if (file) {
    formData.append('file', file);
  }
  
  const { data } = await axios.post('/api/releases', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return data.data;
};

// تحديث إفراج
export const updateRelease = async (
  id: string,
  releaseData: UpdateReleaseRequest,
  file?: File
) => {
  const formData = new FormData();
  formData.append('data', JSON.stringify(releaseData));
  
  if (file) {
    formData.append('file', file);
  }
  
  const { data } = await axios.put(`/api/releases/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return data.data;
};

// حذف إفراج
export const deleteRelease = async (id: string) => {
  const { data } = await axios.delete(`/api/releases/${id}`);
  return data;
};
