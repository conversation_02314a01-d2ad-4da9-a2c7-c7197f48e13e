import request from 'supertest';
import {
  app,
  setupTestFolders,
  cleanupDatabase,
  setupTestData,
  cleanupFiles,
} from '../../../core/utils/test/integration-setup.js';

/**
 * اختبارات وحدة النماذج المخصصة
 */
describe('Custom Forms Module', () => {
  let authToken: string;
  let userId: string;
  let customFormId: string;

  // قبل جميع الاختبارات
  beforeAll(async () => {
    setupTestFolders();
    await cleanupDatabase();

    // إنشاء بيانات الاختبار
    const { user, authToken: token } = await setupTestData();
    userId = user.id;
    authToken = token;
  });

  // بعد جميع الاختبارات
  afterAll(async () => {
    await cleanupDatabase();
    cleanupFiles([]);
  });

  // اختبار إنشاء نموذج مخصص
  it('should create a custom form', async () => {
    const customFormData = {
      name: 'نموذج اختبار',
      description: 'وصف نموذج الاختبار',
      formType: 'declarations',
      fields: [
        {
          id: 'field_1',
          name: 'testField',
          label: 'حقل اختبار',
          type: 'text',
          required: true,
          order: 0,
        },
      ],
      isActive: true,
    };

    const response = await request(app)
      .post('/api/custom-forms')
      .set('Authorization', `Bearer ${authToken}`)
      .send(customFormData)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data).toHaveProperty('id');
    expect(response.body.data.name).toBe(customFormData.name);
    expect(response.body.data.formType).toBe(customFormData.formType);
    expect(response.body.data.fields).toHaveLength(1);

    // حفظ معرف النموذج المخصص للاستخدام في الاختبارات اللاحقة
    customFormId = response.body.data.id;
  });

  // اختبار الحصول على قائمة النماذج المخصصة
  it('should get a list of custom forms', async () => {
    const response = await request(app)
      .get('/api/custom-forms')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    // التحقق من أن البيانات موجودة وهي array أو object مع data
    if (response.body.data && typeof response.body.data === 'object') {
      if (Array.isArray(response.body.data)) {
        expect(Array.isArray(response.body.data)).toBe(true);
      } else if (response.body.data.data) {
        expect(Array.isArray(response.body.data.data)).toBe(true);
      }
    }
    expect(response.body.pagination).toBeDefined();
  });

  // اختبار الحصول على نموذج مخصص محدد
  it('should get a specific custom form', async () => {
    const response = await request(app)
      .get(`/api/custom-forms/${customFormId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.id).toBe(customFormId);
    expect(response.body.data.name).toBe('نموذج اختبار');
  });

  // اختبار تحديث نموذج مخصص
  it('should update a custom form', async () => {
    const updateData = {
      name: 'نموذج اختبار محدث',
      description: 'وصف محدث لنموذج الاختبار',
      formType: 'declarations',
      fields: [
        {
          id: 'field_1',
          name: 'testField',
          label: 'حقل اختبار محدث',
          type: 'text',
          required: true,
          order: 0,
        },
      ],
      isActive: true,
    };

    const response = await request(app)
      .put(`/api/custom-forms/${customFormId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .send(updateData)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.name).toBe(updateData.name);
    expect(response.body.data.description).toBe(updateData.description);
  });

  // اختبار حذف نموذج مخصص
  it('should delete a custom form', async () => {
    const response = await request(app)
      .delete(`/api/custom-forms/${customFormId}`)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.message).toBe('تم حذف النموذج المخصص بنجاح');

    // تعيين معرف النموذج المخصص إلى قيمة فارغة لتجنب محاولة حذفه مرة أخرى في afterAll
    customFormId = '';
  });
});
