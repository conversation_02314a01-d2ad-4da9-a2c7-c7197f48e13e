// أنواع الإفراج
export enum ReleaseType {
  TEMPORARY = 'TEMPORARY',
  PERMANENT = 'PERMANENT',
}

// واجهة المستخدم
export interface User {
  id: string;
  username: string;
  name: string;
}

// واجهة العميل
export interface Client {
  id: string;
  clientNumber?: number;
  taxNumber: string;
  name: string;
  companyName?: string;
  phone?: string;
}

// واجهة البيان
export interface Declaration {
  id: string;
  declarationNumber: number;
  taxNumber: string;
  clientName?: string;
}

// واجهة الإفراج
export interface Release {
  id: string;
  releaseNumber?: number;
  releaseType: ReleaseType;
  startDate: Date;
  endDate: Date;
  notes?: string;
  pdfFile?: string;
  declarationId?: string;
  declaration?: Declaration;
  clientId?: string;
  client?: Client;
  createdBy: User;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

// واجهة قيم نموذج الإفراج
export interface ReleaseFormValues {
  releaseType: ReleaseType;
  startDate: Date | null;
  endDate: Date | null;
  notes?: string;
  declarationId?: string;
  clientId?: string;
  file?: File | null;
}

// واجهة استجابة قائمة الإفراجات
export interface ReleasesResponse {
  data: Release[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// واجهة استجابة الإفراج
export interface ReleaseResponse {
  data: Release;
}
