import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Chip,
  IconButton,
  Grid,
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { Declaration } from '../hooks/useDeclaration';

interface DeclarationListProps {
  declarations: Declaration[];
  onView?: (declaration: Declaration) => void;
  onEdit?: (declaration: Declaration) => void;
  onDelete?: (declaration: Declaration) => void;
  loading?: boolean;
}

export const DeclarationList: React.FC<DeclarationListProps> = ({
  declarations,
  onView,
  onEdit,
  onDelete,
  loading = false,
}) => {
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography>جاري التحميل...</Typography>
      </Box>
    );
  }

  if (!declarations || !declarations.length) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography color="text.secondary">
          لا توجد بيانات للعرض
        </Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={2}>
      {declarations.map((declaration) => (
        <Grid item xs={12} md={6} lg={4} key={declaration.id}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                <Typography variant="h6" component="h3">
                  {declaration.taxNumber}
                </Typography>
                <Chip
                  label={declaration.declarationType}
                  size="small"
                  color="primary"
                />
              </Box>

              {declaration.clientName && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  العميل: {declaration.clientName}
                </Typography>
              )}

              {declaration.companyName && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  الشركة: {declaration.companyName}
                </Typography>
              )}

              <Typography variant="body2" color="text.secondary" gutterBottom>
                رقم دخول البوابة: {declaration.gatewayEntryNumber}
              </Typography>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                تاريخ البيان: {new Date(declaration.declarationDate).toLocaleDateString('ar-SA')}
              </Typography>

              <Box display="flex" justifyContent="flex-end" mt={2}>
                {onView && (
                  <IconButton
                    size="small"
                    onClick={() => onView(declaration)}
                    title="عرض"
                  >
                    <ViewIcon />
                  </IconButton>
                )}
                {onEdit && (
                  <IconButton
                    size="small"
                    onClick={() => onEdit(declaration)}
                    title="تعديل"
                  >
                    <EditIcon />
                  </IconButton>
                )}
                {onDelete && (
                  <IconButton
                    size="small"
                    onClick={() => onDelete(declaration)}
                    title="حذف"
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default DeclarationList;
