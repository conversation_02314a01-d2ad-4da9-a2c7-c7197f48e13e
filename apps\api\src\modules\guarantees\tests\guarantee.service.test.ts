import { prismaMock } from '../../../core/utils/__mocks__/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

// Mock the guarantee service
const mockGuaranteeService = {
  createGuarantee: async (data: any, userId: string) => {
    // Mock implementation
    const guaranteeNumber = 'G-1001';
    const guarantee = {
      id: 'guarantee-1',
      guaranteeNumber,
      declarationId: data.declarationId || 'declaration-1', // إضافة declarationId المطلوب
      guaranteeType: data.guaranteeType || 'BANK', // إضافة guaranteeType المطلوب
      amount: data.amount,
      currency: data.currency,
      issueDate: data.issueDate,
      expiryDate: data.expiryDate,
      status: 'ACTIVE' as any,
      notes: data.notes || null,
      pdfFile: data.pdfFile || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    prismaMock.guarantee.create.mockResolvedValue(guarantee);
    return guarantee;
  },

  updateGuarantee: async (id: string, data: any, userId: string) => {
    const existingGuarantee = {
      id,
      guaranteeNumber: 'G-1001',
      declarationId: 'declaration-1',
      guaranteeType: 'BANK',
      amount: 100000,
      currency: 'SAR' as any,
      issueDate: new Date(),
      expiryDate: new Date(),
      status: 'ACTIVE' as any,
      notes: null,
      pdfFile: null,
      createdAt: new Date(),
      userId: 'user-123', // Fixed user ID
    };

    if (existingGuarantee.userId !== userId) {
      throw new HttpException(403, 'غير مصرح لك بتحديث هذا الضمان', 'Forbidden');
    }

    const updatedGuarantee = {
      ...existingGuarantee,
      ...data,
      updatedAt: new Date(),
    };

    prismaMock.guarantee.update.mockResolvedValue(updatedGuarantee);
    return updatedGuarantee;
  },

  getGuarantee: async (id: string) => {
    const guarantee = {
      id,
      guaranteeNumber: 'G-1001',
      declarationId: 'declaration-1',
      guaranteeType: 'BANK', // إضافة guaranteeType المطلوب
      amount: 100000,
      currency: 'SAR' as any,
      issueDate: new Date(),
      expiryDate: new Date(),
      status: 'ACTIVE' as any,
      notes: null,
      pdfFile: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    prismaMock.guarantee.findUnique.mockResolvedValue(guarantee);
    return guarantee;
  },

  deleteGuarantee: async (id: string, userId: string) => {
    const guarantee = {
      id,
      guaranteeNumber: 'G-1001',
      declarationId: 'declaration-1',
      guaranteeType: 'BANK', // إضافة guaranteeType
      amount: 100000,
      currency: 'SAR' as any,
      issueDate: new Date(),
      expiryDate: new Date(),
      status: 'ACTIVE' as any,
      notes: null,
      pdfFile: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Mock authorization check
    if (userId !== 'user-123') {
      throw new HttpException(403, 'غير مصرح لك بحذف هذا الضمان', 'Forbidden');
    }

    prismaMock.guarantee.delete.mockResolvedValue(guarantee);
    return { success: true };
  },

  listGuarantees: async (params: any = {}) => {
    const guarantees = [
      {
        id: 'guarantee-1',
        guaranteeNumber: 'G-1001',
        declarationId: 'declaration-1',
        guaranteeType: 'BANK', // إضافة guaranteeType
        amount: 100000,
        currency: 'SAR' as any,
        issueDate: new Date(),
        expiryDate: new Date(),
        status: 'ACTIVE' as any,
        notes: null,
        pdfFile: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'guarantee-2',
        guaranteeNumber: 'G-1002',
        declarationId: 'declaration-2',
        guaranteeType: 'INSURANCE', // إضافة guaranteeType
        amount: 50000,
        currency: 'SAR' as any,
        issueDate: new Date(),
        expiryDate: new Date(),
        status: 'EXPIRED' as any,
        notes: null,
        pdfFile: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    prismaMock.guarantee.findMany.mockResolvedValue(guarantees);
    prismaMock.guarantee.count.mockResolvedValue(guarantees.length);

    return {
      data: guarantees,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 10,
        total: guarantees.length,
        pages: Math.ceil(guarantees.length / (params.limit || 10)),
      },
    };
  },

  calculateGuaranteeValue: async (declarationId: string) => {
    // Mock calculation
    const declaration = {
      id: declarationId,
      totalValue: 500000,
    };

    const guaranteeValue = declaration.totalValue * 0.1; // 10% of declaration value

    return {
      declarationValue: declaration.totalValue,
      guaranteePercentage: 10,
      guaranteeValue,
      currency: 'SAR',
    };
  },
};

describe('Guarantee Service', () => {
  beforeEach(() => {
    // تنظيف المحاكيات قبل كل اختبار
  });

  describe('createGuarantee', () => {
    it('should create a new guarantee successfully', async () => {
      // Arrange
      const guaranteeData = {
        guaranteeType: 'BANK',
        bankName: 'البنك الأهلي السعودي',
        amount: 100000,
        currency: 'SAR',
        issueDate: new Date(),
        expiryDate: new Date(),
      };
      const userId = 'user-123';

      // Act
      const result = await mockGuaranteeService.createGuarantee(guaranteeData, userId);

      // Assert
      expect(result).toHaveProperty('id');
      expect(result.amount).toBe(guaranteeData.amount);
      expect(result.currency).toBe(guaranteeData.currency);
      expect(result.status).toBe('ACTIVE');
    });
  });

  describe('updateGuarantee', () => {
    it('should update guarantee successfully', async () => {
      // Arrange
      const guaranteeId = 'guarantee-1';
      const updateData = {
        guaranteeType: 'INSURANCE',
        bankName: 'شركة التأمين الوطنية',
        amount: 150000,
      };
      const userId = 'user-123';

      // Act
      const result = await mockGuaranteeService.updateGuarantee(guaranteeId, updateData, userId);

      // Assert
      expect(result.guaranteeType).toBe(updateData.guaranteeType);
      expect(result.bankName).toBe(updateData.bankName);
      expect(result.amount).toBe(updateData.amount);
      expect(result).toHaveProperty('updatedAt');
    });

    it('should throw error when user is not authorized', async () => {
      // Arrange
      const guaranteeId = 'guarantee-1';
      const updateData = { amount: 200000 };
      const userId = 'different-user';

      // Act & Assert
      await expect(
        mockGuaranteeService.updateGuarantee(guaranteeId, updateData, userId)
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getGuarantee', () => {
    it('should get guarantee by id successfully', async () => {
      // Arrange
      const guaranteeId = 'guarantee-1';

      // Act
      const result = await mockGuaranteeService.getGuarantee(guaranteeId);

      // Assert
      expect(result).toHaveProperty('id', guaranteeId);
      expect(result).toHaveProperty('guaranteeNumber');
      expect(result).toHaveProperty('guaranteeType');
      expect(result).toHaveProperty('amount');
      expect(result).toHaveProperty('status', 'ACTIVE');
    });
  });

  describe('deleteGuarantee', () => {
    it('should delete guarantee successfully', async () => {
      // Arrange
      const guaranteeId = 'guarantee-1';
      const userId = 'user-123';

      // Act
      const result = await mockGuaranteeService.deleteGuarantee(guaranteeId, userId);

      // Assert
      expect(result).toEqual({ success: true });
    });

    it('should throw error when user is not authorized to delete', async () => {
      // Arrange
      const guaranteeId = 'guarantee-1';
      const userId = 'different-user';

      // Act & Assert
      await expect(
        mockGuaranteeService.deleteGuarantee(guaranteeId, userId)
      ).rejects.toThrow(HttpException);
    });
  });

  describe('listGuarantees', () => {
    it('should list guarantees with pagination', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };

      // Act
      const result = await mockGuaranteeService.listGuarantees(params);

      // Assert
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('pagination');
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination.page).toBe(params.page);
      expect(result.pagination.limit).toBe(params.limit);
      expect(result.data.length).toBeGreaterThan(0);
    });
  });

  describe('calculateGuaranteeValue', () => {
    it('should calculate guarantee value correctly', async () => {
      // Arrange
      const declarationId = 'declaration-1';

      // Act
      const result = await mockGuaranteeService.calculateGuaranteeValue(declarationId);

      // Assert
      expect(result).toHaveProperty('declarationValue');
      expect(result).toHaveProperty('guaranteePercentage');
      expect(result).toHaveProperty('guaranteeValue');
      expect(result).toHaveProperty('currency');
      expect(result.guaranteeValue).toBe(result.declarationValue * 0.1);
    });
  });
});
