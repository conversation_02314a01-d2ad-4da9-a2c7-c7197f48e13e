import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FileCopy as CopyIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { useCustomForms, useDeleteCustomForm } from '../hooks/useCustomForms';
import { CustomForm, FormType } from '../types/custom-form.types';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';
import ConfirmDialog from '@lib/ui/Dialogs/ConfirmDialog';

const CustomFormsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // حالة البحث والتصفية
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const [formType, setFormType] = useState<string>('');
  const [isActive, setIsActive] = useState<string>('');

  // حالة حوار التأكيد
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [selectedFormId, setSelectedFormId] = useState<string>('');

  // استخدام خطافات النماذج المخصصة
  const { data, isLoading, refetch } = useCustomForms({
    page: page + 1,
    limit,
    formType: formType || undefined,
    isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined,
  });

  const deleteMutation = useDeleteCustomForm();

  // معالجة تغيير الصفحة
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // معالجة تغيير عدد العناصر في الصفحة
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setLimit(parseInt(event.target.value, 10));
    setPage(0);
  };

  // معالجة فتح حوار التأكيد
  const handleOpenConfirmDialog = (id: string) => {
    setSelectedFormId(id);
    setConfirmOpen(true);
  };

  // معالجة إغلاق حوار التأكيد
  const handleCloseConfirmDialog = () => {
    setConfirmOpen(false);
    setSelectedFormId('');
  };

  // معالجة حذف النموذج المخصص
  const handleDeleteForm = async () => {
    if (selectedFormId) {
      await deleteMutation.mutateAsync(selectedFormId);
      handleCloseConfirmDialog();
      refetch();
    }
  };

  // معالجة إعادة تعيين التصفية
  const handleResetFilter = () => {
    setFormType('');
    setIsActive('');
  };

  // معالجة الانتقال إلى صفحة إنشاء نموذج جديد
  const handleCreateForm = () => {
    navigate('/custom-forms/new');
  };

  // معالجة الانتقال إلى صفحة تعديل نموذج
  const handleEditForm = (id: string) => {
    navigate(`/custom-forms/edit/${id}`);
  };

  // معالجة الانتقال إلى صفحة عرض نموذج
  const handleViewForm = (id: string) => {
    navigate(`/custom-forms/${id}`);
  };

  // معالجة نسخ نموذج
  const handleCopyForm = (id: string) => {
    navigate(`/custom-forms/copy/${id}`);
  };

  // تحويل نوع النموذج إلى نص مقروء
  const getFormTypeText = (type: string) => {
    switch (type) {
      case 'declarations':
        return t('customForms.formTypes.declarations');
      case 'item_movements':
        return t('customForms.formTypes.itemMovements');
      case 'authorizations':
        return t('customForms.formTypes.authorizations');
      case 'releases':
        return t('customForms.formTypes.releases');
      case 'permits':
        return t('customForms.formTypes.permits');
      case 'guarantees':
        return t('customForms.formTypes.guarantees');
      case 'receipts':
        return t('customForms.formTypes.receipts');
      case 'clients':
        return t('customForms.formTypes.clients');
      case 'documents':
        return t('customForms.formTypes.documents');
      default:
        return type;
    }
  };

  return (
    <Container maxWidth="xl">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          {t('customForms.title')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('customForms.description')}
        </Typography>
      </Box>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>{t('customForms.formType')}</InputLabel>
                <Select
                  value={formType}
                  onChange={(e) => setFormType(e.target.value)}
                  label={t('customForms.formType')}
                >
                  <MenuItem value="">{t('common.all')}</MenuItem>
                  <MenuItem value="declarations">{t('customForms.formTypes.declarations')}</MenuItem>
                  <MenuItem value="item_movements">{t('customForms.formTypes.itemMovements')}</MenuItem>
                  <MenuItem value="authorizations">{t('customForms.formTypes.authorizations')}</MenuItem>
                  <MenuItem value="releases">{t('customForms.formTypes.releases')}</MenuItem>
                  <MenuItem value="permits">{t('customForms.formTypes.permits')}</MenuItem>
                  <MenuItem value="guarantees">{t('customForms.formTypes.guarantees')}</MenuItem>
                  <MenuItem value="receipts">{t('customForms.formTypes.receipts')}</MenuItem>
                  <MenuItem value="clients">{t('customForms.formTypes.clients')}</MenuItem>
                  <MenuItem value="documents">{t('customForms.formTypes.documents')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>{t('customForms.status')}</InputLabel>
                <Select
                  value={isActive}
                  onChange={(e) => setIsActive(e.target.value)}
                  label={t('customForms.status')}
                >
                  <MenuItem value="">{t('common.all')}</MenuItem>
                  <MenuItem value="true">{t('common.active')}</MenuItem>
                  <MenuItem value="false">{t('common.inactive')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="outlined"
                color="primary"
                onClick={handleResetFilter}
                startIcon={<SearchIcon />}
                fullWidth
              >
                {t('common.resetFilter')}
              </Button>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleCreateForm}
                startIcon={<AddIcon />}
                fullWidth
              >
                {t('customForms.createNew')}
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Paper>
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>{t('customForms.name')}</TableCell>
                    <TableCell>{t('customForms.formType')}</TableCell>
                    <TableCell>{t('customForms.fieldsCount')}</TableCell>
                    <TableCell>{t('customForms.status')}</TableCell>
                    <TableCell>{t('customForms.createdAt')}</TableCell>
                    <TableCell align="center">{t('common.actions')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data?.data.map((form: CustomForm) => (
                    <TableRow key={form.id}>
                      <TableCell>{form.name}</TableCell>
                      <TableCell>{getFormTypeText(form.formType)}</TableCell>
                      <TableCell>{form.fields.length}</TableCell>
                      <TableCell>
                        {form.isActive ? (
                          <Box sx={{ display: 'flex', alignItems: 'center', color: 'success.main' }}>
                            <CheckCircleIcon fontSize="small" sx={{ mr: 1 }} />
                            {t('common.active')}
                          </Box>
                        ) : (
                          <Box sx={{ display: 'flex', alignItems: 'center', color: 'error.main' }}>
                            <CancelIcon fontSize="small" sx={{ mr: 1 }} />
                            {t('common.inactive')}
                          </Box>
                        )}
                      </TableCell>
                      <TableCell>
                        {format(new Date(form.createdAt), 'yyyy/MM/dd', { locale: arSA })}
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title={t('common.view')}>
                          <IconButton onClick={() => handleViewForm(form.id)} color="info" size="small">
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.edit')}>
                          <IconButton onClick={() => handleEditForm(form.id)} color="primary" size="small">
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.copy')}>
                          <IconButton onClick={() => handleCopyForm(form.id)} color="secondary" size="small">
                            <CopyIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title={t('common.delete')}>
                          <IconButton onClick={() => handleOpenConfirmDialog(form.id)} color="error" size="small">
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}

                  {data?.data.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        {t('common.noData')}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              component="div"
              count={data?.pagination.total || 0}
              page={page}
              onPageChange={handleChangePage}
              rowsPerPage={limit}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage={t('common.rowsPerPage')}
              labelDisplayedRows={({ from, to, count }) =>
                `${from}-${to} ${t('common.of')} ${count}`
              }
              rowsPerPageOptions={[5, 10, 25, 50]}
            />
          </>
        )}
      </Paper>

      <ConfirmDialog
        open={confirmOpen}
        title={t('customForms.deleteConfirmTitle')}
        message={t('customForms.deleteConfirmMessage')}
        onConfirm={handleDeleteForm}
        onCancel={handleCloseConfirmDialog}
      />
    </Container>
  );
};

export default CustomFormsPage;
