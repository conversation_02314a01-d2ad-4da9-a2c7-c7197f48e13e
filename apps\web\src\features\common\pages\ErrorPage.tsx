import React from 'react';
import { Box, Button, Container, Typography, Paper } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import HomeIcon from '@mui/icons-material/Home';

interface ErrorPageProps {
  title?: string;
  message?: string;
  errorCode?: string | number;
  showHomeButton?: boolean;
}

/**
 * صفحة الخطأ العامة
 * تستخدم لعرض رسائل الخطأ للمستخدم
 */
const ErrorPage: React.FC<ErrorPageProps> = ({
  title,
  message,
  errorCode,
  showHomeButton = true
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <Container maxWidth="md">
      <Paper
        elevation={3}
        sx={{
          p: 5,
          mt: 8,
          mb: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center'
        }}
      >
        <ErrorOutlineIcon color="error" sx={{ fontSize: 80, mb: 2 }} />
        
        {errorCode && (
          <Typography variant="h2" color="error" gutterBottom>
            {errorCode}
          </Typography>
        )}
        
        <Typography variant="h4" gutterBottom>
          {title || t('error.defaultTitle', 'حدث خطأ')}
        </Typography>
        
        <Typography variant="body1" color="text.secondary" paragraph>
          {message || t('error.defaultMessage', 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقًا.')}
        </Typography>
        
        <Box sx={{ mt: 4, display: 'flex', gap: 2 }}>
          {showHomeButton && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<HomeIcon />}
              onClick={() => navigate('/')}
            >
              {t('error.backToHome', 'العودة إلى الصفحة الرئيسية')}
            </Button>
          )}
          
          <Button
            variant="outlined"
            onClick={() => window.location.reload()}
          >
            {t('error.refresh', 'تحديث الصفحة')}
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default ErrorPage;
