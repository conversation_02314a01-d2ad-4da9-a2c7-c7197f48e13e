import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Paper, Typography, Box, Divider, Tab, Tabs } from '@mui/material';
import { ReportType } from '../../../types/shared-types';
import {
  useCreateReportTemplateMutation,
  useUpdateReportTemplateMutation,
} from '../api/report-template.api';
import {
  ReportTemplate,
  ReportTemplateStructure,
} from '../types/report-template.types';
import ReportTemplateList from '../components/ReportTemplateList';
import ReportTemplateEditor from '../components/ReportTemplateEditor';
import ReportTemplatePreview from '../components/ReportTemplatePreview';
import { useSnackbar } from '@/core/hooks/useSnackbar';

/**
 * صفحة إدارة قوالب التقارير
 */
const ReportTemplatesPage = () => {
  const { t } = useTranslation();
  const { showSnackbar } = useSnackbar();

  // حالة الصفحة
  const [selectedReportType, setSelectedReportType] = useState<string>(ReportType.DECLARATION);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | undefined>(undefined);
  const [previewTemplate, setPreviewTemplate] = useState<ReportTemplateStructure | null>(null);

  // استخدام خطافات API
  const createTemplateMutation = useCreateReportTemplateMutation();
  const updateTemplateMutation = useUpdateReportTemplateMutation();

  // التعامل مع تغيير نوع التقرير
  const handleReportTypeChange = (event: React.SyntheticEvent, newValue: string) => {
    setSelectedReportType(newValue);
  };

  // التعامل مع إضافة قالب جديد
  const handleAddTemplate = () => {
    setSelectedTemplate(undefined);
    setIsEditing(true);
  };

  // التعامل مع تعديل قالب
  const handleEditTemplate = (template: ReportTemplate) => {
    setSelectedTemplate(template);
    setIsEditing(true);
  };

  // التعامل مع نسخ قالب
  const handleCopyTemplate = (template: ReportTemplate) => {
    const copiedTemplate = {
      ...template,
      id: undefined,
      name: `${template.name} (نسخة)`,
      isDefault: false,
    };
    setSelectedTemplate(copiedTemplate as any);
    setIsEditing(true);
  };

  // التعامل مع إلغاء التحرير
  const handleCancelEdit = () => {
    setIsEditing(false);
    setSelectedTemplate(undefined);
  };

  // التعامل مع حفظ القالب
  const handleSaveTemplate = async (data: any) => {
    try {
      if (selectedTemplate?.id) {
        // تحديث قالب موجود
        await updateTemplateMutation.mutateAsync({
          id: selectedTemplate.id,
          data,
        });
        showSnackbar(t('reports.templates.updateSuccess'), 'success');
      } else {
        // إنشاء قالب جديد
        await createTemplateMutation.mutateAsync(data);
        showSnackbar(t('reports.templates.createSuccess'), 'success');
      }
      setIsEditing(false);
      setSelectedTemplate(undefined);
    } catch (error) {
      console.error('Error saving template:', error);
      showSnackbar(t('reports.templates.saveError'), 'error');
    }
  };

  // التعامل مع معاينة القالب
  const handlePreviewTemplate = (template: ReportTemplateStructure) => {
    setPreviewTemplate(template);
  };

  // التعامل مع إغلاق المعاينة
  const handleClosePreview = () => {
    setPreviewTemplate(null);
  };

  return (
    <Container maxWidth="lg">
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('reports.templates.title')}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        {isEditing ? (
          // محرر قالب التقرير
          <ReportTemplateEditor
            template={selectedTemplate}
            onSave={handleSaveTemplate}
            onCancel={handleCancelEdit}
            onPreview={handlePreviewTemplate}
          />
        ) : (
          // قائمة قوالب التقارير
          <>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
              <Tabs
                value={selectedReportType}
                onChange={handleReportTypeChange}
                variant="scrollable"
                scrollButtons="auto"
                aria-label="report types tabs"
              >
                {Object.values(ReportType).map((type) => (
                  <Tab key={type} label={t(`reports.types.${type}`)} value={type} />
                ))}
              </Tabs>
            </Box>

            <ReportTemplateList
              reportType={selectedReportType}
              onAddTemplate={handleAddTemplate}
              onEditTemplate={handleEditTemplate}
              onCopyTemplate={handleCopyTemplate}
            />
          </>
        )}
      </Paper>

      {/* معاينة قالب التقرير */}
      {previewTemplate && (
        <ReportTemplatePreview
          open={!!previewTemplate}
          onClose={handleClosePreview}
          template={previewTemplate}
          reportType={selectedTemplate?.reportType || selectedReportType}
        />
      )}
    </Container>
  );
};

export default ReportTemplatesPage;
