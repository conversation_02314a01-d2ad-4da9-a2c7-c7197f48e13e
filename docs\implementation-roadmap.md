# خارطة طريق التنفيذ - نظام النور للأرشفة

## المرحلة 1: تحديث قاعدة البيانات (مكتملة ✅)

### ما تم إنجازه:
- ✅ تحديث جميع النماذج لتتوافق مع المتطلبات
- ✅ إضافة النماذج الجديدة (ReturnableGuarantee, NonReturnableGuarantee, OfficeDocument)
- ✅ إضافة Enums جديدة (PackageType, GuaranteeType, ReceiptType, DocumentType)
- ✅ تحديث العلاقات بين النماذج
- ✅ إضافة التعليقات التوضيحية باللغة العربية

## المرحلة 2: تطبيق التحديثات على قاعدة البيانات (التالي)

### الخطوات المطلوبة:

#### 2.1 إنشاء Migration جديدة
```bash
npx prisma migrate dev --name "update_schema_for_requirements" --schema=./apps/api/prisma/schema.prisma
```

#### 2.2 تحديث Prisma Client
```bash
npx prisma generate --schema=./apps/api/prisma/schema.prisma
```

#### 2.3 تحديث Seed Data
- تحديث ملفات البذر لتتوافق مع النماذج الجديدة
- إضافة بيانات تجريبية للنماذج الجديدة

## المرحلة 3: تحديث Backend APIs

### 3.1 تحديث Controllers
- **declarations.controller.ts**: إضافة دعم للحقول الجديدة
- **item-movements.controller.ts**: تحديث كامل للحقول الجديدة
- **authorizations.controller.ts**: تحديث للحقول الجديدة
- **releases.controller.ts**: تحديث كامل
- **permits.controller.ts**: تحديث للحقول الجديدة
- **guarantees.controller.ts**: إنشاء controllers منفصلة للضمانات
- **receipts.controller.ts**: تحديث للحقول الجديدة
- **clients.controller.ts**: تحديث للحقول الجديدة
- **office-documents.controller.ts**: إنشاء controller جديد

### 3.2 تحديث Services
- تحديث جميع Services لتتوافق مع النماذج الجديدة
- إضافة business logic للحسابات التلقائية
- إضافة validation للحقول الإلزامية

### 3.3 تحديث Validation Schemas
- تحديث جميع Zod schemas لتتوافق مع الحقول الجديدة
- إضافة validation للحقول الإلزامية
- إضافة validation للعلاقات بين النماذج

### 3.4 إضافة APIs جديدة
- **Search API**: للبحث المتقدم
- **Reports API**: لإنشاء التقارير
- **Export API**: للتصدير (Excel, PDF)
- **Custom Forms API**: للنماذج المخصصة
- **System Settings API**: لإعدادات النظام

## المرحلة 4: تحديث Frontend Components

### 4.1 تحديث النماذج الموجودة
- **DeclarationForm**: إضافة الحقول الجديدة ودعم السائقين المتعددين
- **ItemMovementForm**: تحديث كامل للحقول الجديدة
- **AuthorizationForm**: تحديث للحقول الجديدة
- **ReleaseForm**: تحديث كامل
- **PermitForm**: تحديث للحقول الجديدة
- **ClientForm**: تحديث للحقول الجديدة

### 4.2 إنشاء نماذج جديدة
- **ReturnableGuaranteeForm**: نموذج الضمان المسترجع
- **NonReturnableGuaranteeForm**: نموذج الضمان غير المسترجع
- **ReceiptForm**: تحديث نموذج الاستلامات
- **OfficeDocumentForm**: نموذج اوراق المكتب

### 4.3 إضافة مكونات جديدة
- **PDFViewer**: عارض PDF مع iframe
- **SearchableSelect**: قائمة قابلة للبحث باستخدام React Select
- **MultiDriverForm**: نموذج إضافة سائقين متعددين
- **ExportButtons**: أزرار التصدير (Excel, PDF)
- **AdvancedSearch**: مكون البحث المتقدم

## المرحلة 5: تطوير لوحة التحكم

### 5.1 إحصائيات ورسوم بيانية
- **Dashboard**: لوحة تحكم رئيسية مع الإحصائيات
- **Charts**: رسوم بيانية (Bar Chart, Pie Chart)
- **Statistics**: إحصائيات الأرشيف

### 5.2 إدارة المستخدمين
- **UserManagement**: إدارة المستخدمين
- **RoleManagement**: إدارة الأدوار والصلاحيات
- **PermissionsMatrix**: مصفوفة الصلاحيات

### 5.3 إعدادات النظام
- **SystemSettings**: إعدادات النظام العامة
- **DatabaseManagement**: إدارة قاعدة البيانات
- **BackupRestore**: النسخ الاحتياطي والاستعادة

## المرحلة 6: الميزات المتقدمة

### 6.1 Custom UI Builder
- **FormBuilder**: بناء النماذج المخصصة
- **FieldPalette**: لوحة الحقول المتاحة
- **CanvasArea**: منطقة التصميم
- **PropertiesPanel**: لوحة خصائص الحقول

### 6.2 Report Designer
- **ReportDesigner**: مصمم التقارير
- **TemplateEditor**: محرر قوالب التقارير
- **ChartConfigurator**: إعداد الرسوم البيانية

### 6.3 البحث المتقدم والتقارير
- **AdvancedSearch**: البحث المتقدم مع فلاتر متعددة
- **ReportGenerator**: مولد التقارير
- **ExportManager**: مدير التصدير

## الجدول الزمني المقترح

### الأسبوع 1-2: المرحلة 2 (قاعدة البيانات)
- تطبيق Migration
- تحديث Seed Data
- اختبار قاعدة البيانات

### الأسبوع 3-4: المرحلة 3 (Backend)
- تحديث Controllers والـ Services
- تحديث Validation Schemas
- إضافة APIs جديدة

### الأسبوع 5-6: المرحلة 4 (Frontend - الأساسيات)
- تحديث النماذج الموجودة
- إنشاء النماذج الجديدة
- إضافة المكونات الأساسية

### الأسبوع 7-8: المرحلة 5 (لوحة التحكم)
- تطوير لوحة التحكم
- إدارة المستخدمين
- إعدادات النظام

### الأسبوع 9-12: المرحلة 6 (الميزات المتقدمة)
- Custom UI Builder
- Report Designer
- البحث المتقدم والتقارير

## معايير النجاح

### المرحلة 2:
- ✅ Migration تعمل بنجاح
- ✅ جميع النماذج تعمل بشكل صحيح
- ✅ البيانات التجريبية متوفرة

### المرحلة 3:
- ✅ جميع APIs تعمل بشكل صحيح
- ✅ Validation يعمل كما هو مطلوب
- ✅ اختبارات الوحدة تمر بنجاح

### المرحلة 4:
- ✅ جميع النماذج تعمل بشكل صحيح
- ✅ PDF Viewer يعمل بشكل صحيح
- ✅ التصدير يعمل بشكل صحيح

### المرحلة 5:
- ✅ لوحة التحكم تعرض الإحصائيات بشكل صحيح
- ✅ إدارة المستخدمين تعمل بشكل كامل
- ✅ إعدادات النظام قابلة للتخصيص

### المرحلة 6:
- ✅ Custom UI Builder يعمل بشكل كامل
- ✅ Report Designer ينتج تقارير صحيحة
- ✅ البحث المتقدم يعطي نتائج دقيقة

## المخاطر والتحديات

### مخاطر تقنية:
- تعقيد Migration لقاعدة البيانات الموجودة
- تأثير التحديثات على البيانات الموجودة
- تعقيد Custom UI Builder

### مخاطر زمنية:
- تأخير في تطوير الميزات المتقدمة
- الحاجة لاختبارات شاملة
- تدريب المستخدمين على الميزات الجديدة

### حلول مقترحة:
- إنشاء نسخة احتياطية قبل Migration
- تطوير تدريجي مع اختبارات مستمرة
- توثيق شامل للميزات الجديدة
