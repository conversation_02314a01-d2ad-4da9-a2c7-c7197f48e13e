# استخدام صورة Node.js الرسمية كصورة أساسية
FROM node:18-alpine AS base

# تثبيت التبعيات
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# نسخ ملفات الإعتماديات
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml* ./
COPY apps/web/package.json ./apps/web/package.json
COPY packages/shared-types/package.json ./packages/shared-types/package.json
COPY packages/ui-library/package.json ./packages/ui-library/package.json

# تثبيت الإعتماديات
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# بناء التطبيق
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# بناء التطبيق
RUN npm install -g pnpm
RUN pnpm build:web

# إنشاء صورة للتشغيل
FROM nginx:alpine AS runner
WORKDIR /usr/share/nginx/html

# نسخ ملفات Nginx
COPY nginx/conf.d/default.conf /etc/nginx/conf.d/default.conf

# نسخ الملفات المبنية
COPY --from=builder /app/apps/web/dist .

# تعريض المنفذ
EXPOSE 80
EXPOSE 443

# تشغيل Nginx
CMD ["nginx", "-g", "daemon off;"]
