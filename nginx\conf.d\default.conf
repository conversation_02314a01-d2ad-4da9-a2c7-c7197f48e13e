server {
    listen 80;
    server_name alnoorarch.com www.alnoorarch.com;

    # تحويل جميع طلبات HTTP إلى HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name alnoorarch.com www.alnoorarch.com;

    # إعدادات SSL
    ssl_certificate /etc/nginx/ssl/alnoorarch.crt;
    ssl_certificate_key /etc/nginx/ssl/alnoorarch.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # إعدادات الأمان
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # إعدادات الملفات الثابتة
    location /static/ {
        alias /app/apps/web/dist/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # إعدادات الملفات المرفوعة
    location /uploads/ {
        alias /app/uploads/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # إعدادات API
    location /api/ {
        proxy_pass http://app:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # إعدادات الواجهة الأمامية
    location / {
        proxy_pass http://app:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # إعدادات الخطأ
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
