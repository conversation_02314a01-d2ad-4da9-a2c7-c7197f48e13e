import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger.js';
import { ZodError } from 'zod';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';

/**
 * فئة استثناء HTTP المخصصة
 * تستخدم لإنشاء أخطاء HTTP مخصصة مع رمز الحالة ورسالة الخطأ
 */
export class HttpException extends Error {
  statusCode: number;
  error: string;

  constructor(statusCode: number, message: string, error?: string) {
    super(message);
    this.statusCode = statusCode;
    this.error = error || 'خطأ';
  }
}

/**
 * وسيط معالجة الأخطاء
 * يعالج جميع الأخطاء في التطبيق ويحولها إلى استجابات HTTP مناسبة
 */
export const errorMiddleware = (
  err: Error,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: NextFunction
) => {
  logger.error(`${req.method} ${req.path} - ${err.message}`);

  // معالجة أخطاء التحقق من صحة Zod
  if (err instanceof ZodError) {
    const errors = err.errors.map((e) => ({
      path: e.path.join('.'),
      message: e.message,
    }));

    return res.status(400).json({
      success: false,
      statusCode: 400,
      message: 'خطأ في التحقق من صحة البيانات',
      error: 'طلب غير صالح',
      errors,
    });
  }

  // معالجة أخطاء Prisma
  if (err instanceof PrismaClientKnownRequestError) {
    // معالجة انتهاكات قيود الفريدة
    if (err.code === 'P2002') {
      const field = (err.meta?.target as string[]) || ['غير معروف'];

      return res.status(409).json({
        success: false,
        statusCode: 409,
        message: `${field.join(', ')} موجود بالفعل`,
        error: 'تعارض',
      });
    }

    // معالجة عدم العثور على السجل
    if (err.code === 'P2025') {
      return res.status(404).json({
        success: false,
        statusCode: 404,
        message: err.meta?.cause || 'لم يتم العثور على السجل',
        error: 'غير موجود',
      });
    }

    // معالجة أخطاء Prisma الأخرى
    return res.status(500).json({
      success: false,
      statusCode: 500,
      message: 'خطأ في قاعدة البيانات',
      error: 'خطأ في الخادم الداخلي',
    });
  }

  // معالجة استثناءات HTTP المخصصة
  if (err instanceof HttpException) {
    return res.status(err.statusCode).json({
      success: false,
      statusCode: err.statusCode,
      message: err.message,
      error: err.error,
    });
  }

  // معالجة الأخطاء الأخرى
  return res.status(500).json({
    success: false,
    statusCode: 500,
    message: err.message || 'حدث خطأ ما',
    error: 'خطأ في الخادم الداخلي',
  });
};
