{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --report-unused-disable-directives --fix", "preview": "vite preview", "test": "vitest run", "test:unit": "vitest run --config vite.config.ts", "test:integration": "vitest run --config vitest.integration.config.ts", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^5.15.11", "@mui/material": "^5.15.11", "@mui/x-data-grid": "^6.19.5", "@mui/x-date-pickers": "^8.4.0", "@reduxjs/toolkit": "^2.2.1", "@tanstack/react-query": "^5.24.1", "@tanstack/react-query-devtools": "^5.24.1", "@types/uuid": "^10.0.0", "axios": "^1.6.7", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "i18next": "^23.10.0", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.3", "notistack": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.50.1", "react-i18next": "^14.0.5", "react-redux": "^9.1.0", "react-router-dom": "^6.22.2", "recharts": "^2.12.2", "shared-types": "workspace:*", "tailwindcss": "^3.4.1", "ui-library": "workspace:*", "uuid": "^11.1.0", "zod": "^3.22.4"}, "devDependencies": {"@emotion/cache": "^11.14.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "eslint": "^9.27.0", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^24.0.0", "msw": "^2.8.4", "stylis": "^4.3.6", "stylis-plugin-rtl": "^2.1.1", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "vite": "^6.3.5", "vitest": "^3.1.4"}}