import request from 'supertest';
import { app } from '../../../app.js';
import {
  setupIntegrationDatabase,
  cleanupIntegrationDatabase,
  createIntegrationTestUser,
  getIntegrationAuthToken,
  getIntegrationPrismaClient
} from '../../../core/utils/test/integration-test-setup.js';

/**
 * اختبارات تكامل وحدة حركة الأصناف
 */
describe('Item Movements Integration Tests', () => {
  let authToken: string;
  let userId: string;
  let clientId: string;
  let declarationId: string;
  let prisma: any;
  let testUser: any;

  // قبل جميع الاختبارات
  beforeAll(async () => {
    // إعداد قاعدة البيانات
    await setupIntegrationDatabase();
    await cleanupIntegrationDatabase();

    // الحصول على عميل Prisma
    prisma = getIntegrationPrismaClient();
  });

  // قبل كل اختبار
  beforeEach(async () => {
    // إنشاء مستخدم للاختبار (أو استخدام الموجود)
    testUser = await createIntegrationTestUser();
    userId = testUser.id;
    authToken = await getIntegrationAuthToken(testUser);

    // إنشاء عميل للاختبار مع قيم فريدة
    const timestamp = Date.now();
    const client = await prisma.client.create({
      data: {
        name: `عميل اختبار ${timestamp}`,
        taxNumber: `*********${timestamp}`,
        address: 'عنوان اختبار',
        phone: `0*********${timestamp}`,
        email: `test${timestamp}@example.com`
      }
    });
    clientId = client.id;

    // إنشاء بيان للاختبار مع التأكد من صحة البيانات
    const declaration = await prisma.declaration.create({
      data: {
        declarationNumber: `3001-${timestamp}`,
        taxNumber: `*********${timestamp}`,
        clientName: `عميل اختبار ${timestamp}`,
        companyName: 'شركة اختبار',
        policyNumber: '7001',
        invoiceNumber: '12001',
        gatewayEntryNumber: '4001',
        declarationType: 'IMPORT',
        declarationDate: new Date(),
        count: 100,
        weight: 500.0,
        goodsType: 'MEDICAL_SUPPLIES',
        itemsCount: 5,
        entryDate: new Date(),
        exitDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        clientId: clientId,
        userId: userId, // إضافة userId للبيان
      },
    });

    declarationId = declaration.id;

    // التحقق من أن declarationId هو UUID صالح
    console.log('🔍 Declaration ID created:', declarationId);
    expect(declarationId).toBeDefined();
    expect(typeof declarationId).toBe('string');
    expect(declarationId.length).toBeGreaterThan(0);
  }, 180000); // 3 دقائق timeout

  // بعد جميع الاختبارات
  afterAll(async () => {
    // تنظيف قاعدة البيانات
    await cleanupIntegrationDatabase();
  }, 60000); // دقيقة واحدة timeout

  /**
   * اختبار إنشاء حركة صنف جديدة
   */
  describe('POST /api/item-movements', () => {
    it('يجب أن ينشئ حركة صنف جديدة بنجاح', async () => {
      const response = await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          quantity: 10,
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId,
          notes: 'اختبار حركة صنف'
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.itemName).toBe('هاتف ذكي');
      expect(response.body.data.quantity).toBe(10);
      expect(response.body.data.declarationId).toBe(declarationId);
    });

    it('يجب أن يرفض إنشاء حركة صنف بدون بيانات مطلوبة', async () => {
      const response = await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          // عدم إرسال quantity (مطلوبة)
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  /**
   * اختبار الحصول على حركات الأصناف
   */
  describe('GET /api/item-movements', () => {

    it('يجب أن يعيد قائمة حركات الأصناف بنجاح', async () => {
      const response = await request(app)
        .get('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      // التحقق من أن البيانات إما array أو object مع pagination
      if (response.body.data && typeof response.body.data === 'object') {
        // إذا كانت البيانات في format pagination
        if (response.body.data.data) {
          expect(Array.isArray(response.body.data.data)).toBe(true);
          expect(response.body.data.pagination).toBeDefined();
        } else {
          // إذا كانت البيانات array مباشرة
          expect(Array.isArray(response.body.data)).toBe(true);
        }
      }
      expect(response.body.pagination).toBeDefined();
      expect(response.body.pagination.page).toBeDefined();
      expect(response.body.pagination.limit).toBeDefined();
      expect(response.body.pagination.total).toBeDefined();
      expect(response.body.pagination.pages).toBeDefined();
    });

    it('يجب أن يعيد حركات الأصناف مع تصفية حسب البيان', async () => {
      const response = await request(app)
        .get('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ declarationId: declarationId })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      // التحقق من أن البيانات إما array أو object مع pagination
      if (response.body.data && typeof response.body.data === 'object') {
        // إذا كانت البيانات في format pagination
        if (response.body.data.data) {
          expect(Array.isArray(response.body.data.data)).toBe(true);
          if (response.body.data.data.length > 0) {
            expect(response.body.data.data[0].declarationId).toBe(declarationId);
          }
        } else {
          // إذا كانت البيانات array مباشرة
          expect(Array.isArray(response.body.data)).toBe(true);
          if (response.body.data.length > 0) {
            expect(response.body.data[0].declarationId).toBe(declarationId);
          }
        }
      }
      expect(response.body.pagination).toBeDefined();
    });
  });

  /**
   * اختبار الحصول على حركة صنف محددة
   */
  describe('GET /api/item-movements/:id', () => {

    it('يجب أن يعيد حركة صنف محددة بنجاح', async () => {
      // إنشاء حركة صنف للاختبار أولاً
      const createResponse = await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          quantity: 10,
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId
        })
        .expect(201);

      const itemMovementId = createResponse.body.data.id;

      // الآن جلب حركة الصنف
      const response = await request(app)
        .get(`/api/item-movements/${itemMovementId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(itemMovementId);
      expect(response.body.data.itemName).toBe('هاتف ذكي');
      expect(response.body.data.quantity).toBe(10);
    });

    it('يجب أن يعيد خطأ عند طلب حركة صنف غير موجودة', async () => {
      const response = await request(app)
        .get('/api/item-movements/non-existent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });
  });

  /**
   * اختبار تحديث حركة صنف
   */
  describe('PUT /api/item-movements/:id', () => {
    it('يجب أن يحدث حركة صنف بنجاح', async () => {
      // إنشاء حركة صنف للاختبار أولاً
      const createResponse = await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          quantity: 10,
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId
        })
        .expect(201);

      const itemMovementId = createResponse.body.data.id;

      // الآن تحديث حركة الصنف
      const response = await request(app)
        .put(`/api/item-movements/${itemMovementId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي محدث',
          quantity: 15,
          unit: 'قطعة',
          movementType: 'OUT',
          movementDate: new Date().toISOString()
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBeDefined();
      expect(response.body.data.itemName).toBe('هاتف ذكي محدث');
      expect(response.body.data.quantity).toBe(15);
    });
  });

  /**
   * اختبار حذف حركة صنف
   */
  describe('DELETE /api/item-movements/:id', () => {
    it('يجب أن يحذف حركة صنف بنجاح', async () => {
      // إنشاء حركة صنف للاختبار أولاً
      const createResponse = await request(app)
        .post('/api/item-movements')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          itemName: 'هاتف ذكي',
          quantity: 10,
          unit: 'قطعة',
          movementType: 'IN',
          movementDate: new Date().toISOString(),
          declarationId: declarationId
        })
        .expect(201);

      const itemMovementId = createResponse.body.data.id;

      // الآن حذف حركة الصنف
      const response = await request(app)
        .delete(`/api/item-movements/${itemMovementId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBeDefined();
      expect(response.body.message).toBe('تم حذف حركة الصنف بنجاح');
    });
  });
});
