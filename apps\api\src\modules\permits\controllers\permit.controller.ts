import { Request, Response, NextFunction } from 'express';
import { permitService } from '../services/permit.service.js';
import { successResponse, paginatedResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const permitController = {
  /**
   * إنشاء تصريح جديد
   */
  createPermit: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على بيانات التصريح من الطلب
      const permitData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // إنشاء التصريح
      const permit = await permitService.createPermit(
        permitData,
        file
      );

      return res.status(201).json(successResponse(permit, 'تم إنشاء التصريح بنجاح', 201));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث تصريح
   */
  updatePermit: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف التصريح من المعلمات
      const { id } = req.params;

      // الحصول على بيانات التصريح من الطلب
      const permitData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // تحديث التصريح
      const permit = await permitService.updatePermit(
        id,
        permitData,
        file
      );

      return res.status(200).json(successResponse(permit, 'تم تحديث التصريح بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على تصريح محدد
   */
  getPermit: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معرف التصريح من المعلمات
      const { id } = req.params;

      // الحصول على التصريح
      const permit = await permitService.getPermit(id);

      return res.status(200).json(successResponse(permit));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف تصريح
   */
  deletePermit: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف التصريح من المعلمات
      const { id } = req.params;

      // حذف التصريح
      await permitService.deletePermit(id);

      return res.status(200).json(successResponse(null, 'تم حذف التصريح بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة التصاريح
   */
  listPermits: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معلمات البحث
      const { page, limit, sort, order, search, declarationId, fromDate, toDate } =
        req.query as any;

      // الحصول على قائمة التصاريح
      const result = await permitService.listPermits({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        search,
        declarationId,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined,
      });

      return res.status(200).json(paginatedResponse(
        result.data,
        result.pagination.page,
        result.pagination.limit,
        result.pagination.total,
        'تم الحصول على قائمة التصاريح بنجاح'
      ));
    } catch (error) {
      next(error);
    }
  },
};
