import { useState, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  InputAdornment,
  MenuItem,
  Paper,
  TextField,
  Typography,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useAuthorizations, useDeleteAuthorization } from '../hooks/useAuthorizations';
import { AuthorizationType, Authorization } from '../types/authorization.types';
import { AuthorizationSearchParams } from '../api/authorizations.api';
import { useToast } from '@/lib/hooks/useToast';
import { useConfirm } from '@/core/hooks/useConfirm';

const AuthorizationsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const toast = useToast();
  const confirm = useConfirm();

  // حالة البحث
  const [searchParams, setSearchParams] = useState<AuthorizationSearchParams>({
    page: 1,
    limit: 10,
    sort: 'authorizationNumber',
    order: 'desc',
  });

  const [searchText, setSearchText] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [authorizationType, setAuthorizationType] = useState<AuthorizationType | ''>('');
  const [fromDate, setFromDate] = useState<Dayjs | null>(null);
  const [toDate, setToDate] = useState<Dayjs | null>(null);

  // التحقق من صحة نطاق التواريخ
  const isDateRangeValid = useMemo(() => {
    if (!fromDate || !toDate) return true;
    return fromDate.isBefore(toDate) || fromDate.isSame(toDate, 'day');
  }, [fromDate, toDate]);

  // استخدام خطافات البيانات مع معالجة أفضل للأخطاء
  const { data, isLoading, isError, error } = useAuthorizations(searchParams);
  const deleteMutation = useDeleteAuthorization();

  // التعامل مع تغيير الصفحة
  const handlePageChange = useCallback((page: number) => {
    setSearchParams((prev) => ({ ...prev, page }));
  }, []);

  // التعامل مع تغيير عدد العناصر في الصفحة
  const handleLimitChange = useCallback((limit: number) => {
    setSearchParams((prev) => ({ ...prev, limit, page: 1 }));
  }, []);

  // التعامل مع تغيير الترتيب
  const handleSortChange = useCallback((sort: string) => {
    setSearchParams((prev) => ({
      ...prev,
      sort,
      order: prev.sort === sort && prev.order === 'asc' ? 'desc' : 'asc',
    }));
  }, []);

  // التعامل مع البحث مع التحقق من صحة التواريخ
  const handleSearch = useCallback(() => {
    // التحقق من صحة نطاق التواريخ قبل البحث
    if (!isDateRangeValid) {
      toast.showError(t('common.invalidDateRange'));
      return;
    }

    const params: AuthorizationSearchParams = {
      ...searchParams,
      page: 1,
      search: searchText || undefined,
      authorizationType: authorizationType || undefined,
      fromDate: fromDate ? fromDate.toDate() : undefined,
      toDate: toDate ? toDate.toDate() : undefined,
    };

    setSearchParams(params);
  }, [searchParams, searchText, authorizationType, fromDate, toDate, isDateRangeValid, toast, t]);

  // التعامل مع إعادة تعيين البحث
  const handleResetSearch = useCallback(() => {
    setSearchText('');
    setAuthorizationType('');
    setFromDate(null);
    setToDate(null);

    setSearchParams({
      page: 1,
      limit: 10,
      sort: 'authorizationNumber',
      order: 'desc',
    });
  }, []);

  // التعامل مع حذف التفويض مع تأكيد محسن
  const handleDeleteAuthorization = useCallback(async (id: string) => {
    const confirmed = await confirm.confirm({
      title: t('authorizations.confirmDelete'),
      message: t('authorizations.confirmDeleteMessage'),
      confirmText: t('common.delete'),
      cancelText: t('common.cancel'),
      confirmColor: 'error',
    });

    if (confirmed) {
      try {
        await deleteMutation.mutateAsync(id);
        toast.showSuccess(t('authorizations.deleteSuccess'));
      } catch (error: any) {
        console.error('Error deleting authorization:', error);
        toast.showError(error?.message || t('authorizations.deleteError'));
      }
    }
  }, [confirm, deleteMutation, toast, t]);

  // التعامل مع عرض التفويض
  const handleViewAuthorization = (id: string) => {
    navigate(`/authorizations/${id}`);
  };

  // التعامل مع تعديل التفويض
  const handleEditAuthorization = (id: string) => {
    navigate(`/authorizations/${id}`);
  };

  // التعامل مع إنشاء تفويض جديد
  const handleCreateAuthorization = () => {
    navigate('/authorizations/new');
  };

  // عرض حالة التحميل
  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  // عرض حالة الخطأ مع تفاصيل أكثر
  if (isError) {
    return (
      <Container maxWidth="xl">
        <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="60vh">
          <Alert severity="error" sx={{ mb: 2, maxWidth: 600 }}>
            <Typography variant="h6" gutterBottom>
              {t('common.errorLoading')}
            </Typography>
            <Typography variant="body2">
              {error?.message || t('common.errorLoadingDetails')}
            </Typography>
          </Alert>
          <Button
            variant="contained"
            onClick={() => window.location.reload()}
            sx={{ mt: 2 }}
          >
            {t('common.retry')}
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box mb={4}>
        <Typography variant="h4" gutterBottom>
          {t('navigation.authorizations')}
        </Typography>

        <Grid container spacing={2} alignItems="center" mb={3}>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              placeholder={t('authorizations.searchPlaceholder')}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={() => setShowFilters(!showFilters)}
            >
              {t('common.filters')}
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleCreateAuthorization}
            >
              {t('authorizations.addNew')}
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              onClick={handleResetSearch}
            >
              {t('common.resetFilters')}
            </Button>
          </Grid>
        </Grid>

        {showFilters && (
          <Paper sx={{ p: 2, mb: 3 }}>
            {/* تنبيه عند وجود خطأ في نطاق التواريخ */}
            {!isDateRangeValid && fromDate && toDate && (
              <Alert severity="error" sx={{ mb: 2 }}>
                <Box display="flex" alignItems="center">
                  <WarningIcon sx={{ mr: 1 }} />
                  {t('common.invalidDateRange')}
                </Box>
              </Alert>
            )}
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  select
                  fullWidth
                  label={t('authorizations.type')}
                  value={authorizationType}
                  onChange={(e) => setAuthorizationType(e.target.value as AuthorizationType | '')}
                >
                  <MenuItem value="">{t('common.all')}</MenuItem>
                  <MenuItem value={AuthorizationType.FOLLOW_UP}>{t('authorizations.types.FOLLOW_UP')}</MenuItem>
                  <MenuItem value={AuthorizationType.CLEARANCE}>{t('authorizations.types.CLEARANCE')}</MenuItem>
                  <MenuItem value={AuthorizationType.RECEIPT}>{t('authorizations.types.RECEIPT')}</MenuItem>
                  <MenuItem value={AuthorizationType.FULL}>{t('authorizations.types.FULL')}</MenuItem>
                </TextField>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('common.fromDate')}
                  value={fromDate}
                  onChange={(date) => {
                    // معالجة آمنة للتاريخ - تحويل Date إلى Dayjs إذا لزم الأمر
                    if (date === null) {
                      setFromDate(null);
                    } else if (dayjs.isDayjs(date)) {
                      setFromDate(date as Dayjs);
                    } else if (date instanceof Date) {
                      // تحويل Date إلى Dayjs
                      setFromDate(dayjs(date));
                    } else {
                      setFromDate(date as Dayjs);
                    }
                  }}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: !isDateRangeValid && !!fromDate && !!toDate,
                      helperText: !isDateRangeValid && !!fromDate && !!toDate ? t('common.invalidDateRange') : undefined
                    }
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('common.toDate')}
                  value={toDate}
                  onChange={(date) => {
                    // معالجة آمنة للتاريخ - تحويل Date إلى Dayjs إذا لزم الأمر
                    if (date === null) {
                      setToDate(null);
                    } else if (dayjs.isDayjs(date)) {
                      setToDate(date as Dayjs);
                    } else if (date instanceof Date) {
                      // تحويل Date إلى Dayjs
                      setToDate(dayjs(date));
                    } else {
                      setToDate(date as Dayjs);
                    }
                  }}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: !isDateRangeValid && !!fromDate && !!toDate,
                      helperText: !isDateRangeValid && !!fromDate && !!toDate ? t('common.invalidDateRange') : undefined
                    }
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Button
                  fullWidth
                  variant="contained"
                  onClick={handleSearch}
                  disabled={isLoading || (!isDateRangeValid && !!fromDate && !!toDate)}
                  sx={{ height: '56px' }}
                  startIcon={isLoading ? <CircularProgress size={20} /> : undefined}
                >
                  {isLoading ? t('common.searching') : t('common.search')}
                </Button>
              </Grid>
            </Grid>
          </Paper>
        )}

        <Card>
          <CardContent>
            <Box sx={{ overflowX: 'auto' }}>
              <table style={{
                width: '100%',
                borderCollapse: 'collapse',
                direction: 'rtl',
                textAlign: 'right'
              }}>
                <thead>
                  <tr style={{
                    backgroundColor: '#f5f5f5',
                    borderBottom: '2px solid #e0e0e0'
                  }}>
                    <th style={{ padding: '12px 16px' }}>{t('authorizations.number')}</th>
                    <th style={{ padding: '12px 16px' }}>{t('authorizations.clientName')}</th>
                    <th style={{ padding: '12px 16px' }}>{t('authorizations.taxNumber')}</th>
                    <th style={{ padding: '12px 16px' }}>{t('authorizations.type')}</th>
                    <th style={{ padding: '12px 16px' }}>{t('authorizations.startDate')}</th>
                    <th style={{ padding: '12px 16px' }}>{t('authorizations.endDate')}</th>
                    <th style={{ padding: '12px 16px' }}>{t('common.actions')}</th>
                  </tr>
                </thead>
                <tbody>
                  {data?.data.map((authorization: Authorization) => (
                    <tr
                      key={authorization.id}
                      style={{
                        borderBottom: '1px solid #e0e0e0',
                      }}
                    >
                      <td style={{ padding: '12px 16px' }}>{authorization.authorizationNumber}</td>
                      <td style={{ padding: '12px 16px' }}>{authorization.clientName || (authorization.client?.name || '')}</td>
                      <td style={{ padding: '12px 16px' }}>{authorization.taxNumber}</td>
                      <td style={{ padding: '12px 16px' }}>
                        <Chip
                          label={t(`authorizations.types.${authorization.authorizationType}`)}
                          color={
                            authorization.authorizationType === AuthorizationType.FULL
                              ? 'primary'
                              : authorization.authorizationType === AuthorizationType.CLEARANCE
                              ? 'secondary'
                              : 'default'
                          }
                          size="small"
                        />
                      </td>
                      <td style={{ padding: '12px 16px' }}>
                        {new Date(authorization.startDate).toLocaleDateString('ar-SA')}
                      </td>
                      <td style={{ padding: '12px 16px' }}>
                        {new Date(authorization.endDate).toLocaleDateString('ar-SA')}
                      </td>
                      <td style={{ padding: '12px 16px' }}>
                        <IconButton
                          color="info"
                          onClick={() => handleViewAuthorization(authorization.id)}
                          size="small"
                          title={t('common.view')}
                        >
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton
                          color="primary"
                          onClick={() => handleEditAuthorization(authorization.id)}
                          size="small"
                          title={t('common.edit')}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          color="error"
                          onClick={() => handleDeleteAuthorization(authorization.id)}
                          size="small"
                          title={t('common.delete')}
                          disabled={deleteMutation.isPending}
                        >
                          {deleteMutation.isPending ? (
                            <CircularProgress size={20} />
                          ) : (
                            <DeleteIcon />
                          )}
                        </IconButton>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </Box>

            {data?.data.length === 0 && (
              <Box textAlign="center" py={3}>
                <Typography variant="body1">{t('common.noData')}</Typography>
              </Box>
            )}

            {/* Pagination */}
            {data?.pagination && data.pagination.totalPages > 1 && (
              <Box display="flex" justifyContent="center" mt={3}>
                <Box display="flex" alignItems="center" gap={2}>
                  <Button
                    disabled={data.pagination.page === 1}
                    onClick={() => handlePageChange(data.pagination.page - 1)}
                  >
                    {t('common.previous')}
                  </Button>

                  <Typography>
                    {t('common.pageOf', {
                      page: data.pagination.page,
                      totalPages: data.pagination.totalPages,
                    })}
                  </Typography>

                  <Button
                    disabled={data.pagination.page === data.pagination.totalPages}
                    onClick={() => handlePageChange(data.pagination.page + 1)}
                  >
                    {t('common.next')}
                  </Button>
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>
      </Box>

      {/* مكون حوار التأكيد */}
      <Dialog
        open={confirm.confirmState.open}
        onClose={confirm.hideConfirm}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
      >
        <DialogTitle id="confirm-dialog-title">
          {confirm.confirmState.title}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="confirm-dialog-description">
            {confirm.confirmState.message}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={confirm.confirmState.onCancel} color="inherit">
            {confirm.confirmState.cancelText}
          </Button>
          <Button
            onClick={confirm.confirmState.onConfirm}
            color={confirm.confirmState.confirmColor}
            variant="contained"
            autoFocus
          >
            {confirm.confirmState.confirmText}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AuthorizationsPage;
