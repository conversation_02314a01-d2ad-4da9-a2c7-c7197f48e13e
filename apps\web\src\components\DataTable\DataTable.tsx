import React from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  CircularProgress,
  Typography,
} from '@mui/material';

export interface Column<T> {
  /**
   * معرف العمود
   */
  id: string;
  
  /**
   * عنوان العمود
   */
  label: string;
  
  /**
   * محاذاة العمود
   * @default 'right'
   */
  align?: 'left' | 'center' | 'right';
  
  /**
   * عرض العمود
   */
  width?: number | string;
  
  /**
   * دالة تحويل قيمة الخلية
   */
  format?: (value: any, row: T) => React.ReactNode;
  
  /**
   * هل العمود قابل للفرز
   * @default false
   */
  sortable?: boolean;
}

interface DataTableProps<T> {
  /**
   * أعمدة الجدول
   */
  columns: Column<T>[];
  
  /**
   * صفوف الجدول
   */
  rows: T[];
  
  /**
   * عدد الصفوف الكلي
   */
  rowCount: number;
  
  /**
   * رقم الصفحة الحالية (يبدأ من 0)
   */
  page: number;
  
  /**
   * عدد الصفوف في الصفحة
   */
  pageSize: number;
  
  /**
   * دالة تنفذ عند تغيير الصفحة
   */
  onPageChange: (page: number) => void;
  
  /**
   * دالة تنفذ عند تغيير عدد الصفوف في الصفحة
   */
  onPageSizeChange: (pageSize: number) => void;
  
  /**
   * هل الجدول في حالة تحميل
   * @default false
   */
  loading?: boolean;
  
  /**
   * محتوى يعرض عند عدم وجود بيانات
   */
  emptyContent?: React.ReactNode;
  
  /**
   * دالة تنفذ عند النقر على صف
   */
  onRowClick?: (row: T) => void;
  
  /**
   * هل الصفوف قابلة للنقر
   * @default false
   */
  rowsClickable?: boolean;
}

/**
 * مكون جدول البيانات
 * يستخدم لعرض البيانات في شكل جدول مع دعم الصفحات والتحميل
 */
export function DataTable<T extends { id: string }>({
  columns,
  rows,
  rowCount,
  page,
  pageSize,
  onPageChange,
  onPageSizeChange,
  loading = false,
  emptyContent,
  onRowClick,
  rowsClickable = false,
}: DataTableProps<T>) {
  const handleChangePage = (_: unknown, newPage: number) => {
    onPageChange(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    onPageSizeChange(parseInt(event.target.value, 10));
    onPageChange(0);
  };

  const handleRowClick = (row: T) => {
    if (onRowClick && rowsClickable) {
      onRowClick(row);
    }
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer sx={{ maxHeight: 440 }}>
        <Table stickyHeader aria-label="جدول البيانات">
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || 'right'}
                  style={{ width: column.width }}
                >
                  {column.label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} align="center" sx={{ py: 5 }}>
                  <CircularProgress size={40} />
                  <Typography variant="body2" sx={{ mt: 2 }}>
                    جاري تحميل البيانات...
                  </Typography>
                </TableCell>
              </TableRow>
            ) : rows.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length} align="center" sx={{ py: 5 }}>
                  {emptyContent || (
                    <Typography variant="body2" color="text.secondary">
                      لا توجد بيانات
                    </Typography>
                  )}
                </TableCell>
              </TableRow>
            ) : (
              rows.map((row) => (
                <TableRow
                  hover
                  key={row.id}
                  onClick={() => handleRowClick(row)}
                  sx={{ cursor: rowsClickable ? 'pointer' : 'default' }}
                >
                  {columns.map((column) => {
                    const value = (row as any)[column.id];
                    return (
                      <TableCell key={column.id} align={column.align || 'right'}>
                        {column.format ? column.format(value, row) : value}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50, 100]}
        component="div"
        count={rowCount}
        rowsPerPage={pageSize}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="صفوف في الصفحة:"
        labelDisplayedRows={({ from, to, count }) => `${from}-${to} من ${count}`}
      />
    </Paper>
  );
}
