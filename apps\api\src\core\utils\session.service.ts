import { prisma } from './prisma.js';
import { logger } from './logger.js';
import { Request } from 'express';
import * as UAParser from 'ua-parser-js';

/**
 * خدمة إدارة جلسات المستخدمين
 */
export const sessionService = {
  /**
   * إنشاء جلسة جديدة
   * @param userId معرف المستخدم
   * @param req طلب Express
   * @returns معلومات الجلسة
   */
  createSession: async (userId: string, req: Request) => {
    try {
      // استخراج معلومات الجهاز والمتصفح
      const ipAddress = req.ip || req.socket.remoteAddress || 'unknown';
      const userAgent = req.headers['user-agent'] || 'unknown';

      // تحليل معلومات المتصفح والجهاز
      const deviceInfo = getDeviceInfo(userAgent);

      // حساب تاريخ انتهاء الصلاحية (7 أيام افتراضيًا)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // يمكن استخدام قيمة من التكوين

      // إنشاء الجلسة في قاعدة البيانات
      const session = await prisma.session.create({
        data: {
          userId,
          ipAddress,
          userAgent,
          deviceInfo,
          expiresAt,
        },
      });

      return session;
    } catch (error) {
      logger.error('خطأ في إنشاء جلسة:', error);
      throw new Error('فشل في إنشاء جلسة');
    }
  },

  /**
   * تحديث نشاط الجلسة
   * @param sessionId معرف الجلسة
   * @returns معلومات الجلسة المحدثة
   */
  updateSessionActivity: async (sessionId: string) => {
    try {
      return await prisma.session.update({
        where: { id: sessionId },
        data: { lastActivity: new Date() },
      });
    } catch (error) {
      logger.error('خطأ في تحديث نشاط الجلسة:', error);
      // لا نريد إيقاف العملية إذا فشل تحديث النشاط
      return null;
    }
  },

  /**
   * إنهاء جلسة
   * @param sessionId معرف الجلسة
   * @returns نتيجة العملية
   */
  endSession: async (sessionId: string) => {
    try {
      await prisma.session.update({
        where: { id: sessionId },
        data: { isActive: false },
      });

      return { success: true };
    } catch (error) {
      logger.error('خطأ في إنهاء الجلسة:', error);
      throw new Error('فشل في إنهاء الجلسة');
    }
  },

  /**
   * إنهاء جميع جلسات المستخدم
   * @param userId معرف المستخدم
   * @param currentSessionId معرف الجلسة الحالية (اختياري، للاستثناء)
   * @returns عدد الجلسات التي تم إنهاؤها
   */
  endAllUserSessions: async (userId: string, currentSessionId?: string) => {
    try {
      const result = await prisma.session.updateMany({
        where: {
          userId,
          isActive: true,
          ...(currentSessionId ? { id: { not: currentSessionId } } : {}),
        },
        data: { isActive: false },
      });

      return { count: result.count };
    } catch (error) {
      logger.error('خطأ في إنهاء جلسات المستخدم:', error);
      throw new Error('فشل في إنهاء جلسات المستخدم');
    }
  },

  /**
   * الحصول على جلسات المستخدم النشطة
   * @param userId معرف المستخدم
   * @returns قائمة الجلسات النشطة
   */
  getUserActiveSessions: async (userId: string) => {
    try {
      return await prisma.session.findMany({
        where: {
          userId,
          isActive: true,
          expiresAt: { gt: new Date() },
        },
        orderBy: { lastActivity: 'desc' },
      });
    } catch (error) {
      logger.error('خطأ في الحصول على جلسات المستخدم:', error);
      throw new Error('فشل في الحصول على جلسات المستخدم');
    }
  },

  /**
   * تنظيف الجلسات منتهية الصلاحية
   * @returns عدد الجلسات التي تم تنظيفها
   */
  cleanupExpiredSessions: async () => {
    try {
      const result = await prisma.session.updateMany({
        where: {
          expiresAt: { lt: new Date() },
          isActive: true,
        },
        data: { isActive: false },
      });

      logger.info(`تم تحديث ${result.count} جلسة منتهية الصلاحية`);
      return { count: result.count };
    } catch (error) {
      logger.error('خطأ في تنظيف الجلسات منتهية الصلاحية:', error);
      throw new Error('فشل في تنظيف الجلسات منتهية الصلاحية');
    }
  },
};

/**
 * استخراج معلومات الجهاز من سلسلة User-Agent
 * @param userAgent سلسلة User-Agent
 * @returns معلومات الجهاز كسلسلة JSON
 */
function getDeviceInfo(userAgent: string): string {
  try {
    const parser = new UAParser.UAParser(userAgent);
    const browser = parser.getBrowser();
    const os = parser.getOS();
    const device = parser.getDevice();

    return JSON.stringify({
      browser: {
        name: browser.name || 'unknown',
        version: browser.version || 'unknown',
      },
      os: {
        name: os.name || 'unknown',
        version: os.version || 'unknown',
      },
      device: {
        type: device.type || 'unknown',
        vendor: device.vendor || 'unknown',
        model: device.model || 'unknown',
      },
    });
  } catch (error) {
    logger.warn('خطأ في تحليل معلومات الجهاز:', error);
    return JSON.stringify({ error: 'فشل في تحليل معلومات الجهاز' });
  }
}
