import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import {
  TextField,
  Checkbox,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  TextFieldProps,
  SelectProps,
} from '@mui/material';

export type FieldType = 'text' | 'number' | 'email' | 'password' | 'date' | 'select' | 'checkbox' | 'textarea';

export interface SelectOption {
  value: string | number;
  label: string;
}

export interface FormFieldProps {
  /**
   * اسم الحقل
   */
  name: string;
  
  /**
   * نوع الحقل
   */
  type: FieldType;
  
  /**
   * تسمية الحقل
   */
  label: string;
  
  /**
   * هل الحقل مطلوب
   */
  required?: boolean;
  
  /**
   * رسالة الخطأ عند عدم تعبئة الحقل
   */
  requiredMessage?: string;
  
  /**
   * خيارات القائمة المنسدلة
   */
  options?: SelectOption[];
  
  /**
   * خصائص حقل النص
   */
  textFieldProps?: Omit<TextFieldProps, 'name' | 'type' | 'label' | 'required'>;
  
  /**
   * خصائص القائمة المنسدلة
   */
  selectProps?: Omit<SelectProps, 'name' | 'label' | 'required'>;
  
  /**
   * هل الحقل معطل
   */
  disabled?: boolean;
}

/**
 * مكون حقل النموذج
 */
export const FormField: React.FC<FormFieldProps> = ({
  name,
  type,
  label,
  required = false,
  requiredMessage = 'هذا الحقل مطلوب',
  options = [],
  textFieldProps = {},
  selectProps = {},
  disabled = false,
}) => {
  const { control, formState: { errors } } = useFormContext();
  
  const renderField = () => {
    switch (type) {
      case 'select':
        return (
          <Controller
            name={name}
            control={control}
            rules={{ required: required ? requiredMessage : false }}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors[name]} disabled={disabled}>
                <InputLabel>{label}</InputLabel>
                <Select
                  {...field}
                  label={label}
                  {...selectProps}
                >
                  {options.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
                {errors[name] && (
                  <FormHelperText>{errors[name]?.message as string}</FormHelperText>
                )}
              </FormControl>
            )}
          />
        );
        
      case 'checkbox':
        return (
          <Controller
            name={name}
            control={control}
            rules={{ required: required ? requiredMessage : false }}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Checkbox
                    {...field}
                    checked={field.value}
                    disabled={disabled}
                  />
                }
                label={label}
              />
            )}
          />
        );
        
      case 'textarea':
        return (
          <Controller
            name={name}
            control={control}
            rules={{ required: required ? requiredMessage : false }}
            render={({ field }) => (
              <TextField
                {...field}
                label={label}
                multiline
                rows={4}
                fullWidth
                error={!!errors[name]}
                helperText={errors[name]?.message as string}
                required={required}
                disabled={disabled}
                {...textFieldProps}
              />
            )}
          />
        );
        
      default:
        return (
          <Controller
            name={name}
            control={control}
            rules={{ required: required ? requiredMessage : false }}
            render={({ field }) => (
              <TextField
                {...field}
                type={type}
                label={label}
                fullWidth
                error={!!errors[name]}
                helperText={errors[name]?.message as string}
                required={required}
                disabled={disabled}
                {...textFieldProps}
              />
            )}
          />
        );
    }
  };
  
  return renderField();
};
