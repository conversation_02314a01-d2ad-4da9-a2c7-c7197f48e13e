import { z } from 'zod';

// مخطط إنشاء حركة الصنف
export const createItemMovementSchema = z.object({
  body: z.object({
    declarationId: z.string({
      required_error: 'معرف البيان مطلوب',
    }).uuid(),
    itemName: z.string({
      required_error: 'اسم الصنف مطلوب',
    }),
    quantity: z.union([
      z.number(),
      z.string().transform((val) => {
        const num = parseFloat(val);
        if (isNaN(num)) {
          throw new Error('الكمية يجب أن تكون رقم صالح');
        }
        return num;
      })
    ], {
      required_error: 'الكمية مطلوبة',
    }),
    unit: z.string({
      required_error: 'الوحدة مطلوبة',
    }),
    movementDate: z.string({
      required_error: 'تاريخ الحركة مطلوب',
    }).transform((val) => new Date(val)),
    movementType: z.string({
      required_error: 'نوع الحركة مطلوب',
    }),
    notes: z.string().optional(),
  }),
});

// مخطط تحديث حركة الصنف
export const updateItemMovementSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف حركة الصنف مطلوب',
    }),
  }),
  body: z.object({
    itemName: z.string().optional(),
    quantity: z.union([
      z.number(),
      z.string().transform((val) => {
        const num = parseFloat(val);
        if (isNaN(num)) {
          throw new Error('الكمية يجب أن تكون رقم صالح');
        }
        return num;
      })
    ]).optional(),
    unit: z.string().optional(),
    movementDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    movementType: z.string().optional(),
    notes: z.string().optional(),
  }),
});

// مخطط الحصول على حركة الصنف
export const getItemMovementSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف حركة الصنف مطلوب',
    }),
  }),
});

// مخطط حذف حركة الصنف
export const deleteItemMovementSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف حركة الصنف مطلوب',
    }),
  }),
});

// مخطط قائمة حركات الأصناف
export const listItemMovementsSchema = z.object({
  query: z.object({
    page: z.string().optional().transform((val) => (val ? parseInt(val) : 1)),
    limit: z.string().optional().transform((val) => (val ? parseInt(val) : 10)),
    sort: z.string().optional().default('createdAt'),
    order: z.enum(['asc', 'desc']).optional().default('desc'),
    search: z.string().optional(),
    declarationId: z.string().uuid().optional(),
    fromDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    toDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
  }),
});
