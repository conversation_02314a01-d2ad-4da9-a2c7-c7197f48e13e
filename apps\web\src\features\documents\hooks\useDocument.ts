import { useQuery } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';

export interface Document {
  id: string;
  documentNumber: string;
  documentType: string;
  title: string;
  description?: string;
  documentDate: string;
  expiryDate?: string;
  issuingAuthority?: string;
  referenceNumber?: string;
  filePath?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export const useDocument = (id: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['document', id],
    queryFn: async (): Promise<Document> => {
      const response = await api.get<Document>(`/api/documents/${id}`);
      return response;
    },
    enabled: options?.enabled ?? !!id,
  });
};
