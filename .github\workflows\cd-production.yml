name: CD Production

on:
  push:
    tags:
      - 'v*'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: alnoor_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Setup test database
        run: |
          cd apps/api
          npx prisma migrate deploy
          npx prisma db seed
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/alnoor_test?schema=public
      
      - name: Run tests
        run: npm run test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/alnoor_test?schema=public
          JWT_SECRET: test_secret
          NODE_ENV: test

  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}
      
      - name: Extract metadata (tags, labels) for API
        id: meta-api
        uses: docker/metadata-action@v4
        with:
          images: ${{ secrets.DOCKER_HUB_USERNAME }}/alnoor-api
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            latest
      
      - name: Extract metadata (tags, labels) for Web
        id: meta-web
        uses: docker/metadata-action@v4
        with:
          images: ${{ secrets.DOCKER_HUB_USERNAME }}/alnoor-web
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            latest
      
      - name: Build and push API image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./apps/api/Dockerfile
          push: true
          tags: ${{ steps.meta-api.outputs.tags }}
          labels: ${{ steps.meta-api.outputs.labels }}
          cache-from: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/alnoor-api:buildcache
          cache-to: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/alnoor-api:buildcache,mode=max
      
      - name: Build and push Web image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./apps/web/Dockerfile
          push: true
          tags: ${{ steps.meta-web.outputs.tags }}
          labels: ${{ steps.meta-web.outputs.labels }}
          cache-from: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/alnoor-web:buildcache
          cache-to: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/alnoor-web:buildcache,mode=max

  deploy:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-and-push
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      
      - name: Add host key
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H ${{ secrets.DEPLOY_HOST }} >> ~/.ssh/known_hosts
      
      - name: Copy deployment files to server
        run: |
          scp docker-compose.yml ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:/opt/alnoorarch/
          scp .env.example ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:/opt/alnoorarch/.env.example
          scp -r nginx ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:/opt/alnoorarch/
      
      - name: Deploy to server
        run: |
          ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "cd /opt/alnoorarch && \
          [ -f .env ] || cp .env.example .env && \
          docker-compose pull && \
          docker-compose up -d && \
          docker-compose exec -T api npx prisma migrate deploy"
      
      - name: Verify deployment
        run: |
          ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "cd /opt/alnoorarch && \
          docker-compose ps"
      
      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          generate_release_notes: true
          files: |
            README.md
            CHANGELOG.md
