// Prisma Client للاختبارات - يستخدم SQLite
import { PrismaClient } from '../__mocks__/prisma-client-test/index.js';

// إنشاء instance من Prisma Client للاختبارات
export const testPrisma = new PrismaClient({
  log: process.env.NODE_ENV === 'test' ? [] : ['query', 'info', 'warn', 'error']
});

// دالة لإعداد قاعدة البيانات للاختبارات
export const setupTestDatabase = async () => {
  try {
    console.log('🔌 الاتصال بقاعدة بيانات الاختبار...');
    await testPrisma.$connect();
    console.log('✅ تم الاتصال بقاعدة بيانات الاختبار بنجاح');
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة بيانات الاختبار:', error);
    throw error;
  }
};

// دالة لتنظيف قاعدة البيانات
export const cleanupTestDatabase = async () => {
  try {
    console.log('🧹 تنظيف قاعدة بيانات الاختبار...');

    // حذف البيانات بترتيب يحترم القيود الخارجية
    await testPrisma.itemMovement.deleteMany({});
    await testPrisma.receipt.deleteMany({});
    await testPrisma.returnableGuarantee.deleteMany({});
    await testPrisma.nonReturnableGuarantee.deleteMany({});
    await testPrisma.permit.deleteMany({});
    await testPrisma.release.deleteMany({});
    await testPrisma.authorization.deleteMany({});
    await testPrisma.driver.deleteMany({});
    await testPrisma.declaration.deleteMany({});
    await testPrisma.client.deleteMany({});
    await testPrisma.customForm.deleteMany({});
    await testPrisma.reportTemplate.deleteMany({});
    await testPrisma.officeDocument.deleteMany({});
    await testPrisma.session.deleteMany({});
    await testPrisma.loginAttempt.deleteMany({});
    await testPrisma.invalidatedToken.deleteMany({});
    await testPrisma.auditLog.deleteMany({});
    await testPrisma.user.deleteMany({});

    console.log('✅ تم تنظيف قاعدة بيانات الاختبار');
  } catch (error) {
    console.warn('⚠️ تحذير: خطأ في تنظيف قاعدة البيانات:', error);
  }
};

// دالة لقطع الاتصال
export const disconnectTestDatabase = async () => {
  try {
    await testPrisma.$disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة بيانات الاختبار');
  } catch (error) {
    console.warn('⚠️ تحذير: خطأ في قطع الاتصال:', error);
  }
};

// دالة لإنشاء مستخدم تجريبي
export const createTestUser = async (overrides: any = {}) => {
  const timestamp = Date.now();
  const username = overrides.username || `test_admin_${timestamp}`;

  console.log(`👤 إنشاء مستخدم تجريبي: ${username}`);

  try {
    const user = await testPrisma.user.create({
      data: {
        id: `test-user-${timestamp}`,
        username,
        email: overrides.email || `${username}@test.com`,
        password: overrides.password || '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', // Test@123
        name: overrides.name || 'مستخدم تجريبي',
        role: overrides.role || 'ADMIN',
        isActive: true,
        ...overrides
      }
    });

    console.log(`✅ تم إنشاء المستخدم التجريبي: ${user.username} (${user.id})`);
    return user;
  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم التجريبي:', error);
    throw error;
  }
};

export default testPrisma;
