/**
 * أنواع الاستلامات
 */
export enum ReceiptType {
  TYPE_A = 'TYPE_A',
  TYPE_B = 'TYPE_B',
  TYPE_C = 'TYPE_C',
}

/**
 * واجهة الاستلام
 */
export interface Receipt {
  id: string;
  receiptNumber: string;
  receiptDate: string;
  receiverName: string;
  receiverPhone?: string;
  receiverIdNumber?: string;
  itemsDescription?: string;
  notes?: string;
  pdfFile?: string;
  declarationId: string;
  declarationNumber?: number;
  clientName?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * واجهة إنشاء استلام جديد
 */
export interface CreateReceiptDto {
  declarationId: string;
  receiptDate: string;
  receiptNumber: string;
  receiverName: string;
  receiverPhone?: string;
  receiverIdNumber?: string;
  itemsDescription?: string;
  notes?: string;
}

/**
 * واجهة تحديث استلام
 */
export interface UpdateReceiptDto {
  declarationId?: string;
  receiptDate?: string;
  receiptNumber?: string;
  receiverName?: string;
  receiverPhone?: string;
  receiverIdNumber?: string;
  itemsDescription?: string;
  notes?: string;
}

/**
 * واجهة إنشاء استلام مع ملف
 */
export interface CreateReceiptWithFileDto {
  data: CreateReceiptDto;
  file?: File;
}

/**
 * واجهة تحديث استلام مع ملف
 */
export interface UpdateReceiptWithFileDto {
  id: string;
  data: UpdateReceiptDto;
  file?: File;
}

/**
 * واجهة معلمات البحث عن الاستلامات
 */
export interface ReceiptSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationId?: string;
  fromDate?: string;
  toDate?: string;
}

/**
 * واجهة قيم نموذج الاستلام
 */
export interface ReceiptFormValues {
  declarationId: string;
  receiptDate: Date | null;
  receiptNumber: string;
  receiverName: string;
  receiverPhone: string;
  receiverIdNumber: string;
  itemsDescription: string;
  notes: string;
  file: File | null;
}
