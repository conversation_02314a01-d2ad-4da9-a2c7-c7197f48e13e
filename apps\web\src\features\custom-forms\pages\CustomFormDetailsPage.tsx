import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  Typography,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  FileCopy as CopyIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  TextFields as TextFieldsIcon,
  Numbers as NumbersIcon,
  CalendarMonth as CalendarIcon,
  List as ListIcon,
  CheckBox as CheckBoxIcon,
  Notes as NotesIcon,
  AttachFile as FileIcon,
} from '@mui/icons-material';
import { useCustomForm, useDeleteCustomForm } from '../hooks/useCustomForms';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { FormField, FieldType } from '../types/custom-form.types';

const CustomFormDetailsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // حالة حوار التأكيد
  const [confirmOpen, setConfirmOpen] = useState(false);

  // استخدام خطافات النماذج المخصصة
  const { data: customForm, isLoading } = useCustomForm(id || '');
  const deleteMutation = useDeleteCustomForm();

  // معالجة العودة إلى صفحة القائمة
  const handleBack = () => {
    navigate('/custom-forms');
  };

  // معالجة الانتقال إلى صفحة التعديل
  const handleEdit = () => {
    navigate(`/custom-forms/edit/${id}`);
  };

  // معالجة الانتقال إلى صفحة النسخ
  const handleCopy = () => {
    navigate(`/custom-forms/copy/${id}`);
  };

  // معالجة فتح حوار التأكيد
  const handleOpenConfirmDialog = () => {
    setConfirmOpen(true);
  };

  // معالجة إغلاق حوار التأكيد
  const handleCloseConfirmDialog = () => {
    setConfirmOpen(false);
  };

  // معالجة حذف النموذج المخصص
  const handleDelete = async () => {
    if (id) {
      await deleteMutation.mutateAsync(id);
      handleCloseConfirmDialog();
      navigate('/custom-forms');
    }
  };

  // تحويل نوع النموذج إلى نص مقروء
  const getFormTypeText = (type: string) => {
    switch (type) {
      case 'declarations':
        return t('customForms.formTypes.declarations');
      case 'item_movements':
        return t('customForms.formTypes.itemMovements');
      case 'authorizations':
        return t('customForms.formTypes.authorizations');
      case 'releases':
        return t('customForms.formTypes.releases');
      case 'permits':
        return t('customForms.formTypes.permits');
      case 'guarantees':
        return t('customForms.formTypes.guarantees');
      case 'receipts':
        return t('customForms.formTypes.receipts');
      case 'clients':
        return t('customForms.formTypes.clients');
      case 'documents':
        return t('customForms.formTypes.documents');
      default:
        return type;
    }
  };

  // الحصول على أيقونة نوع الحقل
  const getFieldTypeIcon = (type: FieldType) => {
    switch (type) {
      case 'text':
        return <TextFieldsIcon />;
      case 'number':
        return <NumbersIcon />;
      case 'date':
        return <CalendarIcon />;
      case 'select':
        return <ListIcon />;
      case 'checkbox':
        return <CheckBoxIcon />;
      case 'textarea':
        return <NotesIcon />;
      case 'file':
        return <FileIcon />;
      default:
        return <TextFieldsIcon />;
    }
  };

  // تحويل نوع الحقل إلى نص مقروء
  const getFieldTypeText = (type: FieldType) => {
    switch (type) {
      case 'text':
        return t('customForms.fieldTypes.text');
      case 'number':
        return t('customForms.fieldTypes.number');
      case 'date':
        return t('customForms.fieldTypes.date');
      case 'select':
        return t('customForms.fieldTypes.select');
      case 'checkbox':
        return t('customForms.fieldTypes.checkbox');
      case 'textarea':
        return t('customForms.fieldTypes.textarea');
      case 'file':
        return t('customForms.fieldTypes.file');
      default:
        return type;
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!customForm) {
    return (
      <Container maxWidth="xl">
        <Box sx={{ mb: 4 }}>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleBack}
            startIcon={<ArrowBackIcon />}
            sx={{ mb: 2 }}
          >
            {t('common.back')}
          </Button>

          <Typography variant="h4" color="error">
            {t('common.notFound')}
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box sx={{ mb: 4 }}>
        <Button
          variant="outlined"
          color="primary"
          onClick={handleBack}
          startIcon={<ArrowBackIcon />}
          sx={{ mb: 2 }}
        >
          {t('common.back')}
        </Button>

        <Typography variant="h4" gutterBottom>
          {customForm.name}
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Chip
            label={getFormTypeText(customForm.formType)}
            color="primary"
            sx={{ mr: 1 }}
          />

          <Chip
            icon={customForm.isActive ? <CheckCircleIcon /> : <CancelIcon />}
            label={customForm.isActive ? t('common.active') : t('common.inactive')}
            color={customForm.isActive ? 'success' : 'error'}
          />
        </Box>

        <Typography variant="body1" color="text.secondary">
          {customForm.description || t('customForms.noDescription')}
        </Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('customForms.formDetails')}
              </Typography>

              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    {t('customForms.id')}
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {customForm.id}
                  </Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    {t('customForms.name')}
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {customForm.name}
                  </Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    {t('customForms.formType')}
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {getFormTypeText(customForm.formType)}
                  </Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    {t('customForms.status')}
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {customForm.isActive ? t('common.active') : t('common.inactive')}
                  </Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    {t('customForms.createdAt')}
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {format(new Date(customForm.createdAt), 'yyyy/MM/dd HH:mm', { locale: arSA })}
                  </Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    {t('customForms.updatedAt')}
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {format(new Date(customForm.updatedAt), 'yyyy/MM/dd HH:mm', { locale: arSA })}
                  </Typography>
                </Grid>

                <Grid item xs={4}>
                  <Typography variant="subtitle2" color="text.secondary">
                    {t('customForms.createdBy')}
                  </Typography>
                </Grid>
                <Grid item xs={8}>
                  <Typography variant="body2">
                    {(customForm as any).createdBy?.name || customForm.userId}
                  </Typography>
                </Grid>
              </Grid>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<EditIcon />}
                  onClick={handleEdit}
                >
                  {t('common.edit')}
                </Button>

                <Button
                  variant="outlined"
                  color="secondary"
                  startIcon={<CopyIcon />}
                  onClick={handleCopy}
                >
                  {t('common.copy')}
                </Button>

                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={handleOpenConfirmDialog}
                >
                  {t('common.delete')}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('customForms.fields')} ({customForm.fields.length})
              </Typography>

              <Divider sx={{ mb: 2 }} />

              {customForm.fields.length === 0 ? (
                <Typography align="center" color="text.secondary" sx={{ py: 4 }}>
                  {t('customForms.noFields')}
                </Typography>
              ) : (
                <List>
                  {customForm.fields.map((field: FormField) => (
                    <Paper key={field.id} sx={{ mb: 2 }}>
                      <ListItem>
                        <ListItemIcon>
                          {getFieldTypeIcon(field.type)}
                        </ListItemIcon>

                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Typography variant="subtitle1">
                                {field.label}
                              </Typography>

                              {field.required && (
                                <Chip
                                  label={t('customForms.required')}
                                  color="error"
                                  size="small"
                                  sx={{ ml: 1 }}
                                />
                              )}
                            </Box>
                          }
                          secondary={
                            <>
                              <Typography variant="body2" color="text.secondary">
                                {t('customForms.fieldName')}: {field.name}
                              </Typography>

                              <Typography variant="body2" color="text.secondary">
                                {t('customForms.fieldType')}: {getFieldTypeText(field.type)}
                              </Typography>

                              {field.type === 'select' && field.options && field.options.length > 0 && (
                                <Box sx={{ mt: 1 }}>
                                  <Typography variant="body2" color="text.secondary">
                                    {t('customForms.options')}:
                                  </Typography>

                                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                                    {field.options.map((option, index) => (
                                      <Chip
                                        key={index}
                                        label={`${option.label} (${option.value})`}
                                        size="small"
                                        variant="outlined"
                                      />
                                    ))}
                                  </Box>
                                </Box>
                              )}
                            </>
                          }
                        />
                      </ListItem>
                    </Paper>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Dialog
        open={confirmOpen}
        onClose={handleCloseConfirmDialog}
      >
        <DialogTitle>{t('customForms.deleteConfirmTitle')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('customForms.deleteConfirmMessage')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseConfirmDialog} color="primary">
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleDelete}
            color="error"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? (
              <CircularProgress size={24} />
            ) : (
              t('common.delete')
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default CustomFormDetailsPage;
