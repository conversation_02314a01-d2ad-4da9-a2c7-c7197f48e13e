import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  Typography,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Chip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useDocument } from '../hooks/useDocument';
import { useDeleteDocument } from '../hooks/useDeleteDocument';
import { useDownloadDocument } from '../hooks/useDownloadDocument';
import { formatDate, formatDateTime } from '@lib/utils/date-utils';
import { PageHeader } from '@components/PageHeader';
import { LoadingScreen } from '@components/LoadingScreen';
import { ErrorScreen } from '@components/ErrorScreen';
import { DocumentType } from '../types/document.types';

/**
 * صفحة تفاصيل المستند
 */
const DocumentDetailsPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // استخدام خطافات البيانات
  const { data: document, isLoading, error } = useDocument(id || '');
  const deleteDocumentMutation = useDeleteDocument();
  const downloadDocumentMutation = useDownloadDocument();

  // التعامل مع حذف المستند
  const handleDelete = async () => {
    if (!id) return;

    try {
      await deleteDocumentMutation.mutateAsync(id);
      navigate('/documents');
    } catch (error) {
      console.error('Error deleting document:', error);
    }
  };

  // التعامل مع تحميل المستند
  const handleDownload = async () => {
    if (!id) return;

    try {
      await downloadDocumentMutation.mutateAsync(id);
    } catch (error) {
      console.error('Error downloading document:', error);
    }
  };

  // عرض شاشة التحميل
  if (isLoading) {
    return <LoadingScreen />;
  }

  // عرض شاشة الخطأ
  if (error || !document) {
    return (
      <ErrorScreen
        message={t('documents.details.errorLoading')}
        onRetry={() => window.location.reload()}
      />
    );
  }

  // تحديد لون نوع المستند
  const getDocumentTypeColor = (type: DocumentType) => {
    switch (type) {
      case DocumentType.INVOICE:
        return 'primary';
      case DocumentType.CONTRACT:
        return 'secondary';
      case DocumentType.CERTIFICATE:
        return 'success';
      case DocumentType.REPORT:
        return 'info';
      case DocumentType.OTHER:
        return 'default';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <PageHeader
        title={t('documents.details.title')}
        subtitle={document.title}
        backButton={
          <Button
            component={Link}
            to="/documents"
            startIcon={<ArrowBackIcon />}
            variant="outlined"
          >
            {t('common.back')}
          </Button>
        }
        actions={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              startIcon={<EditIcon />}
              variant="outlined"
              component={Link}
              to={`/documents/edit/${id}`}
            >
              {t('common.edit')}
            </Button>
            <Button
              startIcon={<PrintIcon />}
              variant="outlined"
              onClick={() => window.print()}
            >
              {t('common.print')}
            </Button>
            <Button
              startIcon={<DownloadIcon />}
              variant="outlined"
              onClick={handleDownload}
              disabled={downloadDocumentMutation.isPending}
            >
              {downloadDocumentMutation.isPending ? (
                <CircularProgress size={24} />
              ) : (
                t('common.download')
              )}
            </Button>
            <Button
              startIcon={<DeleteIcon />}
              variant="outlined"
              color="error"
              onClick={() => setDeleteDialogOpen(true)}
            >
              {t('common.delete')}
            </Button>
          </Box>
        }
      />

      <Paper sx={{ mb: 3 }}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                {t('documents.details.basicInfo')}
              </Typography>
              <Chip
                label={t(`documents.types.${document.documentType}`)}
                color={getDocumentTypeColor(document.documentType as DocumentType)}
              />
            </Box>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('documents.documentNumber')}
                </Typography>
                <Typography variant="body1">{document.documentNumber}</Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('documents.title')}
                </Typography>
                <Typography variant="body1">{document.title}</Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('documents.documentDate')}
                </Typography>
                <Typography variant="body1">
                  {formatDate(document.documentDate)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('documents.expiryDate')}
                </Typography>
                <Typography variant="body1">
                  {document.expiryDate ? formatDate(document.expiryDate) : t('common.notSpecified')}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('documents.issuingAuthority')}
                </Typography>
                <Typography variant="body1">
                  {document.issuingAuthority || t('common.notSpecified')}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('documents.referenceNumber')}
                </Typography>
                <Typography variant="body1">
                  {document.referenceNumber || t('common.notSpecified')}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('documents.createdAt')}
                </Typography>
                <Typography variant="body1">
                  {formatDateTime(document.createdAt)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('documents.updatedAt')}
                </Typography>
                <Typography variant="body1">
                  {formatDateTime(document.updatedAt)}
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('documents.description')}
                </Typography>
                <Typography variant="body1">
                  {document.description || t('common.notSpecified')}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Paper>

      {document.filePath && (
        <Paper sx={{ mb: 3 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('documents.details.attachedFile')}
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  startIcon={<DownloadIcon />}
                  onClick={handleDownload}
                  disabled={downloadDocumentMutation.isPending}
                >
                  {downloadDocumentMutation.isPending ? (
                    <CircularProgress size={24} />
                  ) : (
                    t('common.download')
                  )}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Paper>
      )}

      {/* حوار تأكيد الحذف */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>{t('documents.delete.confirmTitle')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('documents.delete.confirmMessage')}
          </DialogContentText>
          <Alert severity="warning" sx={{ mt: 2 }}>
            {t('documents.delete.warning')}
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleDelete}
            color="error"
            disabled={deleteDocumentMutation.isPending}
          >
            {deleteDocumentMutation.isPending ? (
              <CircularProgress size={24} />
            ) : (
              t('common.delete')
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DocumentDetailsPage;
