import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getItemMovements,
  getItemMovement,
  createItemMovement,
  updateItemMovement,
  deleteItemMovement,
  ItemMovementSearchParams,
} from '../api/items-movement.api';
import { CreateItemMovementRequest, UpdateItemMovementRequest } from '../api/items-movement.api';

// خطاف للحصول على قائمة حركات الأصناف
export const useItemMovements = (params: ItemMovementSearchParams = {}) => {
  return useQuery({
    queryKey: ['itemMovements', params],
    queryFn: () => getItemMovements(params),
  });
};

// خطاف للحصول على حركة صنف محددة
export const useItemMovement = (id: string) => {
  return useQuery({
    queryKey: ['itemMovement', id],
    queryFn: () => getItemMovement(id),
    enabled: !!id,
  });
};

// خطاف لإنشاء حركة صنف جديدة
export const useCreateItemMovement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ data, file }: { data: CreateItemMovementRequest; file?: File }) =>
      createItemMovement(data, file),
    onSuccess: () => {
      // إعادة تحميل قائمة حركات الأصناف بعد الإنشاء
      queryClient.invalidateQueries({ queryKey: ['itemMovements'] });
    },
  });
};

// خطاف لتحديث حركة صنف
export const useUpdateItemMovement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
      file,
    }: {
      id: string;
      data: UpdateItemMovementRequest;
      file?: File;
    }) => updateItemMovement(id, data, file),
    onSuccess: (_, variables) => {
      // إعادة تحميل حركة الصنف وقائمة حركات الأصناف بعد التحديث
      queryClient.invalidateQueries({ queryKey: ['itemMovement', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['itemMovements'] });
    },
  });
};

// خطاف لحذف حركة صنف
export const useDeleteItemMovement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteItemMovement(id),
    onSuccess: () => {
      // إعادة تحميل قائمة حركات الأصناف بعد الحذف
      queryClient.invalidateQueries({ queryKey: ['itemMovements'] });
    },
  });
};
