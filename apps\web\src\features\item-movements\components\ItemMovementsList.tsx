import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Grid,
} from '@mui/material';

interface ItemMovement {
  id: string;
  itemName: string;
  movementType: string;
  quantity: number;
  date: string;
  notes?: string;
}

interface ItemMovementsListProps {
  movements: ItemMovement[];
  loading?: boolean;
}

export const ItemMovementsList: React.FC<ItemMovementsListProps> = ({
  movements,
  loading = false,
}) => {
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography>جاري التحميل...</Typography>
      </Box>
    );
  }

  if (!movements.length) {
    return (
      <Box display="flex" justifyContent="center" p={4}>
        <Typography color="text.secondary">
          لا توجد حركات أصناف
        </Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={2}>
      {movements.map((movement) => (
        <Grid item xs={12} md={6} key={movement.id}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                <Typography variant="h6" component="h3">
                  {movement.itemName}
                </Typography>
                <Chip
                  label={movement.movementType}
                  size="small"
                  color={movement.movementType === 'IN' ? 'success' : 'error'}
                />
              </Box>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                الكمية: {movement.quantity}
              </Typography>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                التاريخ: {new Date(movement.date).toLocaleDateString('ar-SA')}
              </Typography>
              
              {movement.notes && (
                <Typography variant="body2" color="text.secondary">
                  ملاحظات: {movement.notes}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};
