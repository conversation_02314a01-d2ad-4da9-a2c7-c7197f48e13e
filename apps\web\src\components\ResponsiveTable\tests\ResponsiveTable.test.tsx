import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ResponsiveTable } from '../ResponsiveTable';
import { useMediaQuery } from '@mui/material';

// Mock useMediaQuery hook
jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  useMediaQuery: jest.fn(),
}));

// بيانات اختبار
interface TestData {
  id: string;
  title: string;
  description: string;
  date: string;
}

const testData: TestData[] = [
  { id: '1', title: 'عنوان 1', description: 'وصف 1', date: '2023-01-01' },
  { id: '2', title: 'عنوان 2', description: 'وصف 2', date: '2023-01-02' },
];

const testColumns = [
  { id: 'title', label: 'العنوان', showInCard: true },
  { id: 'description', label: 'الوصف', showInCard: true },
  { id: 'date', label: 'التاريخ', showInCard: false },
];

describe('ResponsiveTable Component', () => {
  const defaultProps = {
    columns: testColumns,
    data: testData,
    totalCount: testData.length,
    page: 0,
    rowsPerPage: 10,
    onPageChange: jest.fn(),
    onRowsPerPageChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders as table on desktop', () => {
    (useMediaQuery as any).mockReturnValue(false); // desktop view

    render(<ResponsiveTable {...defaultProps} />);

    // التحقق من وجود عناصر الجدول
    expect(screen.getByRole('table')).toBeInTheDocument();
    expect(screen.getByText('العنوان')).toBeInTheDocument();
    expect(screen.getByText('الوصف')).toBeInTheDocument();
    expect(screen.getByText('التاريخ')).toBeInTheDocument();
    expect(screen.getByText('عنوان 1')).toBeInTheDocument();
    expect(screen.getByText('وصف 1')).toBeInTheDocument();
  });

  test('renders as cards on mobile', () => {
    (useMediaQuery as any).mockReturnValue(true); // mobile view

    render(<ResponsiveTable {...defaultProps} primaryColumn="title" secondaryColumn="date" />);

    // التحقق من عدم وجود جدول
    expect(screen.queryByRole('table')).not.toBeInTheDocument();

    // التحقق من وجود البطاقات
    expect(screen.getByText('عنوان 1')).toBeInTheDocument();
    expect(screen.getByText('2023-01-01')).toBeInTheDocument();
    expect(screen.getByText('العنوان:')).toBeInTheDocument();
    expect(screen.getByText('الوصف:')).toBeInTheDocument();
  });

  test('shows empty message when no data', () => {
    (useMediaQuery as any).mockReturnValue(false); // desktop view

    render(<ResponsiveTable {...defaultProps} data={[]} emptyMessage="لا توجد بيانات" />);

    expect(screen.getByText('لا توجد بيانات')).toBeInTheDocument();
  });

  test('calls onView when view button is clicked', () => {
    (useMediaQuery as any).mockReturnValue(false); // desktop view

    const handleView = jest.fn();
    render(<ResponsiveTable {...defaultProps} onView={handleView} />);

    const viewButton = screen.getAllByRole('button')[0]; // أول زر عرض
    fireEvent.click(viewButton);

    expect(handleView).toHaveBeenCalledWith(testData[0]);
  });

  test('calls onEdit when edit button is clicked', () => {
    (useMediaQuery as any).mockReturnValue(false); // desktop view

    const handleEdit = jest.fn();
    render(<ResponsiveTable {...defaultProps} onEdit={handleEdit} />);

    const editButton = screen.getAllByRole('button')[1]; // أول زر تعديل
    fireEvent.click(editButton);

    expect(handleEdit).toHaveBeenCalledWith(testData[0]);
  });

  test('calls onDelete when delete button is clicked', () => {
    (useMediaQuery as any).mockReturnValue(false); // desktop view

    const handleDelete = jest.fn();
    render(<ResponsiveTable {...defaultProps} onDelete={handleDelete} />);

    const deleteButton = screen.getAllByRole('button')[2]; // أول زر حذف
    fireEvent.click(deleteButton);

    expect(handleDelete).toHaveBeenCalledWith(testData[0]);
  });

  test('renders title when provided', () => {
    (useMediaQuery as any).mockReturnValue(false); // desktop view

    render(<ResponsiveTable {...defaultProps} title="قائمة العناصر" />);

    expect(screen.getByText('قائمة العناصر')).toBeInTheDocument();
  });

  test('does not show actions when showActions is false', () => {
    (useMediaQuery as any).mockReturnValue(false); // desktop view

    render(
      <ResponsiveTable
        {...defaultProps}
        showActions={false}
        onView={jest.fn()}
        onEdit={jest.fn()}
        onDelete={jest.fn()}
      />
    );

    // لا يجب أن يكون هناك أزرار إجراءات
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  test('calls onPageChange when page is changed', () => {
    (useMediaQuery as any).mockReturnValue(false); // desktop view

    render(<ResponsiveTable {...defaultProps} />);

    // النقر على زر الصفحة التالية
    const nextPageButton = screen.getByRole('button', { name: /التالي/i });
    fireEvent.click(nextPageButton);

    expect(defaultProps.onPageChange).toHaveBeenCalledWith(1);
  });
});
