import { useState } from 'react';
import apiService from '../services/api.service';
import { AxiosError, AxiosRequestConfig } from 'axios';

/**
 * واجهة حالة الطلب
 */
interface RequestState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
}

/**
 * خطاف لاستخدام خدمة API
 * يوفر واجهة سهلة للتعامل مع طلبات API مع إدارة حالة الطلب
 */
export const useApi = <T = any>() => {
  // حالة الطلب
  const [state, setState] = useState<RequestState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  /**
   * إرسال طلب GET
   * @param url مسار الطلب
   * @param config إعدادات الطلب
   * @returns وعد بالاستجابة
   */
  const get = async (url: string, config?: AxiosRequestConfig): Promise<T | null> => {
    setState({ ...state, loading: true, error: null });
    
    try {
      const response = await apiService.get<T>(url, config);
      setState({ data: response, loading: false, error: null });
      return response;
    } catch (error) {
      const axiosError = error as AxiosError;
      setState({ data: null, loading: false, error: axiosError as Error });
      return null;
    }
  };

  /**
   * إرسال طلب POST
   * @param url مسار الطلب
   * @param data البيانات المرسلة
   * @param config إعدادات الطلب
   * @returns وعد بالاستجابة
   */
  const post = async (url: string, data?: any, config?: AxiosRequestConfig): Promise<T | null> => {
    setState({ ...state, loading: true, error: null });
    
    try {
      const response = await apiService.post<T>(url, data, config);
      setState({ data: response, loading: false, error: null });
      return response;
    } catch (error) {
      const axiosError = error as AxiosError;
      setState({ data: null, loading: false, error: axiosError as Error });
      return null;
    }
  };

  /**
   * إرسال طلب PUT
   * @param url مسار الطلب
   * @param data البيانات المرسلة
   * @param config إعدادات الطلب
   * @returns وعد بالاستجابة
   */
  const put = async (url: string, data?: any, config?: AxiosRequestConfig): Promise<T | null> => {
    setState({ ...state, loading: true, error: null });
    
    try {
      const response = await apiService.put<T>(url, data, config);
      setState({ data: response, loading: false, error: null });
      return response;
    } catch (error) {
      const axiosError = error as AxiosError;
      setState({ data: null, loading: false, error: axiosError as Error });
      return null;
    }
  };

  /**
   * إرسال طلب DELETE
   * @param url مسار الطلب
   * @param config إعدادات الطلب
   * @returns وعد بالاستجابة
   */
  const del = async (url: string, config?: AxiosRequestConfig): Promise<T | null> => {
    setState({ ...state, loading: true, error: null });
    
    try {
      const response = await apiService.delete<T>(url, config);
      setState({ data: response, loading: false, error: null });
      return response;
    } catch (error) {
      const axiosError = error as AxiosError;
      setState({ data: null, loading: false, error: axiosError as Error });
      return null;
    }
  };

  return {
    ...state,
    get,
    post,
    put,
    delete: del,
  };
};
