import React from 'react';
import { render, screen, fireEvent, within } from '@testing-library/react';
import { ResponsiveTable } from '../ResponsiveTable';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';

// Mock useMediaQuery hook
vi.mock('@mui/material/useMediaQuery', () => ({
  __esModule: true,
  default: vi.fn(),
}));

// Create a theme for testing
const theme = createTheme({
  direction: 'rtl',
});

// Test data
const columns = [
  {
    id: 'id',
    label: 'المعرف',
    align: 'right' as const,
  },
  {
    id: 'name',
    label: 'الاسم',
    align: 'right' as const,
    showInCard: true,
  },
  {
    id: 'email',
    label: 'البريد الإلكتروني',
    align: 'right' as const,
    showInCard: true,
  },
  {
    id: 'phone',
    label: 'رقم الهاتف',
    align: 'right' as const,
  },
];

const data = [
  { id: '1', name: 'أحمد', email: '<EMAIL>', phone: '**********' },
  { id: '2', name: 'محمد', email: '<EMAIL>', phone: '**********' },
  { id: '3', name: 'علي', email: '<EMAIL>', phone: '**********' },
];

// Mock handlers
const mockHandlePageChange = vi.fn();
const mockHandleRowsPerPageChange = vi.fn();
const mockHandleView = vi.fn();
const mockHandleEdit = vi.fn();
const mockHandleDelete = vi.fn();

// Wrapper component for testing
const TestComponent = ({ isMobile = false }) => {
  // Mock the useMediaQuery hook
  (useMediaQuery as any).mockReturnValue(isMobile);

  return (
    <ThemeProvider theme={theme}>
      <ResponsiveTable
        columns={columns}
        data={data}
        totalCount={data.length}
        page={0}
        rowsPerPage={10}
        onPageChange={mockHandlePageChange}
        onRowsPerPageChange={mockHandleRowsPerPageChange}
        onView={mockHandleView}
        onEdit={mockHandleEdit}
        onDelete={mockHandleDelete}
        title="جدول الاختبار"
        emptyMessage="لا توجد بيانات"
        showActions={true}
        primaryColumn="name"
        secondaryColumn="email"
      />
    </ThemeProvider>
  );
};

describe('ResponsiveTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Desktop View (RT-01)', () => {
    it('should render table view on desktop screens', () => {
      render(<TestComponent isMobile={false} />);
      
      // Check if the title is rendered
      expect(screen.getByText('جدول الاختبار')).toBeInTheDocument();
      
      // Check if all columns are rendered
      expect(screen.getByText('المعرف')).toBeInTheDocument();
      expect(screen.getByText('الاسم')).toBeInTheDocument();
      expect(screen.getByText('البريد الإلكتروني')).toBeInTheDocument();
      expect(screen.getByText('رقم الهاتف')).toBeInTheDocument();
      
      // Check if all data rows are rendered
      expect(screen.getByText('أحمد')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('**********')).toBeInTheDocument();
      
      // Check if actions column is rendered
      expect(screen.getByText('الإجراءات')).toBeInTheDocument();
    });

    it('should call onView when view button is clicked (RT-09)', () => {
      render(<TestComponent isMobile={false} />);
      
      // Find the view button for the first row and click it
      const viewButtons = screen.getAllByLabelText('عرض');
      fireEvent.click(viewButtons[0]);
      
      // Check if the onView handler was called with the correct row
      expect(mockHandleView).toHaveBeenCalledWith(data[0]);
    });

    it('should call onEdit when edit button is clicked (RT-09)', () => {
      render(<TestComponent isMobile={false} />);
      
      // Find the edit button for the first row and click it
      const editButtons = screen.getAllByLabelText('تعديل');
      fireEvent.click(editButtons[0]);
      
      // Check if the onEdit handler was called with the correct row
      expect(mockHandleEdit).toHaveBeenCalledWith(data[0]);
    });

    it('should call onDelete when delete button is clicked (RT-09)', () => {
      render(<TestComponent isMobile={false} />);
      
      // Find the delete button for the first row and click it
      const deleteButtons = screen.getAllByLabelText('حذف');
      fireEvent.click(deleteButtons[0]);
      
      // Check if the onDelete handler was called with the correct row
      expect(mockHandleDelete).toHaveBeenCalledWith(data[0]);
    });

    it('should call onPageChange when page is changed (RT-05)', () => {
      render(<TestComponent isMobile={false} />);
      
      // Find the next page button and click it
      const nextPageButton = screen.getByLabelText('الصفحة التالية');
      fireEvent.click(nextPageButton);
      
      // Check if the onPageChange handler was called with the correct page
      expect(mockHandlePageChange).toHaveBeenCalledWith(1);
    });

    it('should call onRowsPerPageChange when rows per page is changed (RT-06)', () => {
      render(<TestComponent isMobile={false} />);
      
      // Find the rows per page select and change it
      const rowsPerPageSelect = screen.getByLabelText('عدد العناصر في الصفحة:');
      fireEvent.mouseDown(rowsPerPageSelect);
      
      // Select 25 rows per page
      const option = screen.getByText('25');
      fireEvent.click(option);
      
      // Check if the onRowsPerPageChange handler was called with the correct value
      expect(mockHandleRowsPerPageChange).toHaveBeenCalledWith(25);
    });
  });

  describe('Mobile View (RT-03)', () => {
    it('should render card view on mobile screens', () => {
      render(<TestComponent isMobile={true} />);
      
      // Check if the title is rendered
      expect(screen.getByText('جدول الاختبار')).toBeInTheDocument();
      
      // Check if cards are rendered instead of table
      const cards = screen.getAllByRole('article');
      expect(cards.length).toBe(data.length);
      
      // Check if primary column is rendered as title in the card
      expect(screen.getByText('أحمد')).toBeInTheDocument();
      
      // Check if secondary column is rendered as subtitle in the card
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      
      // Check if only columns with showInCard=true are rendered in the card
      const firstCard = cards[0];
      expect(within(firstCard).getByText('الاسم:')).toBeInTheDocument();
      expect(within(firstCard).getByText('البريد الإلكتروني:')).toBeInTheDocument();
      expect(within(firstCard).queryByText('رقم الهاتف:')).not.toBeInTheDocument();
    });

    it('should call onView when view button is clicked in card (RT-13)', () => {
      render(<TestComponent isMobile={true} />);
      
      // Find the view button for the first card and click it
      const viewButtons = screen.getAllByLabelText('عرض');
      fireEvent.click(viewButtons[0]);
      
      // Check if the onView handler was called with the correct row
      expect(mockHandleView).toHaveBeenCalledWith(data[0]);
    });

    it('should call onEdit when edit button is clicked in card (RT-13)', () => {
      render(<TestComponent isMobile={true} />);
      
      // Find the edit button for the first card and click it
      const editButtons = screen.getAllByLabelText('تعديل');
      fireEvent.click(editButtons[0]);
      
      // Check if the onEdit handler was called with the correct row
      expect(mockHandleEdit).toHaveBeenCalledWith(data[0]);
    });

    it('should call onDelete when delete button is clicked in card (RT-13)', () => {
      render(<TestComponent isMobile={true} />);
      
      // Find the delete button for the first card and click it
      const deleteButtons = screen.getAllByLabelText('حذف');
      fireEvent.click(deleteButtons[0]);
      
      // Check if the onDelete handler was called with the correct row
      expect(mockHandleDelete).toHaveBeenCalledWith(data[0]);
    });

    it('should call onPageChange when page is changed in mobile view (RT-05)', () => {
      render(<TestComponent isMobile={true} />);
      
      // Find the next page button and click it
      const nextPageButton = screen.getByLabelText('الصفحة التالية');
      fireEvent.click(nextPageButton);
      
      // Check if the onPageChange handler was called with the correct page
      expect(mockHandlePageChange).toHaveBeenCalledWith(1);
    });
  });

  describe('Empty State', () => {
    it('should display empty message when no data is provided', () => {
      // Override the TestComponent to provide empty data
      const EmptyTestComponent = () => (
        <ThemeProvider theme={theme}>
          <ResponsiveTable
            columns={columns}
            data={[]}
            totalCount={0}
            page={0}
            rowsPerPage={10}
            onPageChange={mockHandlePageChange}
            onRowsPerPageChange={mockHandleRowsPerPageChange}
            title="جدول الاختبار"
            emptyMessage="لا توجد بيانات"
          />
        </ThemeProvider>
      );
      
      render(<EmptyTestComponent />);
      
      // Check if the empty message is displayed
      expect(screen.getByText('لا توجد بيانات')).toBeInTheDocument();
    });
  });

  describe('Loading State', () => {
    it('should display loading indicator when isLoading is true', () => {
      // Override the TestComponent to set isLoading to true
      const LoadingTestComponent = () => (
        <ThemeProvider theme={theme}>
          <ResponsiveTable
            columns={columns}
            data={data}
            totalCount={data.length}
            page={0}
            rowsPerPage={10}
            onPageChange={mockHandlePageChange}
            onRowsPerPageChange={mockHandleRowsPerPageChange}
            title="جدول الاختبار"
            isLoading={true}
          />
        </ThemeProvider>
      );
      
      render(<LoadingTestComponent />);
      
      // Check if the loading indicator is displayed
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });
});
