import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api/api';
import { CustomForm } from '@/types/global';

export const useCustomForm = (id: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: ['customForm', id],
    queryFn: async (): Promise<CustomForm> => {
      const response = await api.get(`/api/custom-forms/${id}`);
      return response as CustomForm;
    },
    enabled: options?.enabled ?? !!id,
  });
};
