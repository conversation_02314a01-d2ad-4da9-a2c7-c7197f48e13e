import { useState, useEffect, useCallback } from 'react';
import { Notification } from '@components/NotificationCenter/NotificationCenter';

/**
 * Hook لإدارة الإشعارات في التطبيق
 */
export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // تحميل الإشعارات من localStorage عند بدء التطبيق
  useEffect(() => {
    const savedNotifications = localStorage.getItem('app_notifications');
    if (savedNotifications) {
      try {
        const parsed = JSON.parse(savedNotifications);
        setNotifications(parsed.map((n: any) => ({
          ...n,
          timestamp: new Date(n.timestamp),
        })));
      } catch (error) {
        console.error('Error loading notifications:', error);
      }
    }
  }, []);

  // حفظ الإشعارات في localStorage عند تغييرها
  useEffect(() => {
    localStorage.setItem('app_notifications', JSON.stringify(notifications));
  }, [notifications]);

  /**
   * إضافة إشعار جديد
   */
  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      read: false,
    };

    setNotifications(prev => [newNotification, ...prev]);
    return newNotification.id;
  }, []);

  /**
   * وضع علامة مقروء على إشعار
   */
  const markAsRead = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    );
  }, []);

  /**
   * وضع علامة مقروء على جميع الإشعارات
   */
  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  }, []);

  /**
   * حذف إشعار
   */
  const deleteNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  /**
   * حذف جميع الإشعارات
   */
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  /**
   * إضافة إشعار نجاح
   */
  const addSuccessNotification = useCallback((title: string, message: string) => {
    return addNotification({ title, message, type: 'success' });
  }, [addNotification]);

  /**
   * إضافة إشعار خطأ
   */
  const addErrorNotification = useCallback((title: string, message: string) => {
    return addNotification({ title, message, type: 'error' });
  }, [addNotification]);

  /**
   * إضافة إشعار تحذير
   */
  const addWarningNotification = useCallback((title: string, message: string) => {
    return addNotification({ title, message, type: 'warning' });
  }, [addNotification]);

  /**
   * إضافة إشعار معلومات
   */
  const addInfoNotification = useCallback((title: string, message: string) => {
    return addNotification({ title, message, type: 'info' });
  }, [addNotification]);

  // إحصائيات الإشعارات
  const unreadCount = notifications.filter(n => !n.read).length;
  const totalCount = notifications.length;

  return {
    notifications,
    unreadCount,
    totalCount,
    addNotification,
    addSuccessNotification,
    addErrorNotification,
    addWarningNotification,
    addInfoNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
  };
};

/**
 * إشعارات تجريبية للاختبار
 */
export const createSampleNotifications = (): Omit<Notification, 'id' | 'timestamp' | 'read'>[] => [
  {
    title: 'تم إنشاء بيان جديد',
    message: 'تم إنشاء بيان رقم 1001 بنجاح',
    type: 'success',
  },
  {
    title: 'تحذير: انتهاء صلاحية ترخيص',
    message: 'سينتهي ترخيص العميل "شركة الأمل" خلال 7 أيام',
    type: 'warning',
  },
  {
    title: 'خطأ في معالجة البيان',
    message: 'فشل في معالجة البيان رقم 1002. يرجى المراجعة',
    type: 'error',
  },
  {
    title: 'معلومات: تحديث النظام',
    message: 'سيتم تحديث النظام غداً من الساعة 2:00 إلى 4:00 صباحاً',
    type: 'info',
  },
  {
    title: 'تم اعتماد الإفراج',
    message: 'تم اعتماد إفراج البضائع للبيان رقم 999',
    type: 'success',
  },
];
