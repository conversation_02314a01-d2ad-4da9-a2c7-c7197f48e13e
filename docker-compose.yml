version: '3.8'

services:
  postgres:
    image: postgres:16
    container_name: alnoor-postgres
    restart: always
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: alnoor
      POSTGRES_PASSWORD: alnoor_password
      POSTGRES_DB: alnoor_db
    volumes:
      - postgres_data:/var/lib/postgresql/data

  api:
    build:
      context: .
      dockerfile: ./apps/api/Dockerfile
    container_name: alnoor-api
    restart: always
    ports:
      - "3001:3001"
    depends_on:
      - postgres
    environment:
      - DATABASE_URL=*************************************************/alnoor_db
      - NODE_ENV=development
      - PORT=3001

  web:
    build:
      context: .
      dockerfile: ./apps/web/Dockerfile
    container_name: alnoor-web
    restart: always
    ports:
      - "3000:3000"
    depends_on:
      - api
    environment:
      - VITE_API_URL=http://api:3001

volumes:
  postgres_data:
