import { Request, Response, NextFunction } from 'express';
import { reportService } from '../services/report.service.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import path from 'path';
import { config } from '../../../core/config/app.config.js';

export const reportController = {
  /**
   * إنشاء تقرير
   * يقوم بإنشاء تقرير بالصيغة المطلوبة حسب نوع التقرير والمعايير المحددة
   */
  generateReport: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معلمات التقرير
      const {
        reportType,
        format,
        fromDate,
        toDate,
        clientId,
        declarationType,
        goodsType,
        includeDetails,
      } = req.query as any;

      // إنشاء التقرير
      const result = await reportService.generateReport({
        reportType,
        format,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined,
        clientId,
        declarationType,
        goodsType,
        includeDetails: includeDetails === 'true',
      }, req.user.id);

      // إرجاع رابط التحميل
      return res.status(200).json({
        success: true,
        data: {
          downloadUrl: `/api/reports/download/${result.fileName}`,
          fileName: result.fileName,
        },
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحميل تقرير
   * يقوم بتحميل ملف التقرير المطلوب
   */
  downloadReport: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على اسم الملف
      const { fileName } = req.params;

      // التحقق من وجود الملف
      const filePath = path.join(config.upload.dir, 'reports', fileName);
      if (!fileName) {
        throw new HttpException(400, 'اسم الملف مطلوب', 'Bad Request');
      }

      // إرسال الملف
      return res.download(filePath, fileName, (err) => {
        if (err) {
          next(new HttpException(404, 'الملف غير موجود', 'Not Found'));
        }
      });
    } catch (error) {
      next(error);
    }
  },
};
