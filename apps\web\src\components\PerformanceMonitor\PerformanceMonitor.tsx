import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Grid,
  IconButton,
  Collapse,
  Alert,
} from '@mui/material';
import {
  Memory as MemoryIcon,
  Speed as SpeedIcon,
  NetworkCheck as NetworkIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

interface PerformanceMetrics {
  memoryUsage: number;
  loadTime: number;
  networkLatency: number;
  renderTime: number;
  bundleSize: number;
}

interface PerformanceMonitorProps {
  showDetails?: boolean;
  onPerformanceIssue?: (issue: string) => void;
}

/**
 * مكون مراقبة الأداء
 * يعرض معلومات الأداء والذاكرة في الوقت الفعلي
 */
export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  showDetails = false,
  onPerformanceIssue,
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    memoryUsage: 0,
    loadTime: 0,
    networkLatency: 0,
    renderTime: 0,
    bundleSize: 0,
  });
  const [expanded, setExpanded] = useState(showDetails);
  const [performanceIssues, setPerformanceIssues] = useState<string[]>([]);

  /**
   * قياس استخدام الذاكرة
   */
  const measureMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit,
        percentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
      };
    }
    return { used: 0, total: 0, limit: 0, percentage: 0 };
  }, []);

  /**
   * قياس زمن التحميل
   */
  const measureLoadTime = useCallback(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      return navigation.loadEventEnd - navigation.fetchStart;
    }
    return 0;
  }, []);

  /**
   * قياس زمن الاستجابة للشبكة
   */
  const measureNetworkLatency = useCallback(async () => {
    try {
      const start = performance.now();
      await fetch('/api/health', { method: 'HEAD' });
      const end = performance.now();
      return end - start;
    } catch (error) {
      return -1; // خطأ في الشبكة
    }
  }, []);

  /**
   * قياس زمن الرسم
   */
  const measureRenderTime = useCallback(() => {
    const paintEntries = performance.getEntriesByType('paint');
    const firstContentfulPaint = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return firstContentfulPaint ? firstContentfulPaint.startTime : 0;
  }, []);

  /**
   * تقدير حجم الحزمة
   */
  const estimateBundleSize = useCallback(() => {
    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    let totalSize = 0;
    
    resources.forEach(resource => {
      if (resource.name.includes('.js') || resource.name.includes('.css')) {
        totalSize += resource.transferSize || 0;
      }
    });
    
    return totalSize;
  }, []);

  /**
   * فحص مشاكل الأداء
   */
  const checkPerformanceIssues = useCallback((currentMetrics: PerformanceMetrics) => {
    const issues: string[] = [];

    // فحص استخدام الذاكرة
    if (currentMetrics.memoryUsage > 80) {
      issues.push('استخدام الذاكرة مرتفع (أكثر من 80%)');
    }

    // فحص زمن التحميل
    if (currentMetrics.loadTime > 3000) {
      issues.push('زمن التحميل بطيء (أكثر من 3 ثوانٍ)');
    }

    // فحص زمن الاستجابة للشبكة
    if (currentMetrics.networkLatency > 1000) {
      issues.push('زمن الاستجابة للشبكة بطيء (أكثر من ثانية واحدة)');
    } else if (currentMetrics.networkLatency === -1) {
      issues.push('خطأ في الاتصال بالشبكة');
    }

    // فحص حجم الحزمة
    if (currentMetrics.bundleSize > 5 * 1024 * 1024) { // 5MB
      issues.push('حجم الحزمة كبير (أكثر من 5 ميجابايت)');
    }

    setPerformanceIssues(issues);

    // إشعار المكون الأب بالمشاكل
    if (onPerformanceIssue && issues.length > 0) {
      issues.forEach(issue => onPerformanceIssue(issue));
    }
  }, [onPerformanceIssue]);

  /**
   * تحديث المقاييس
   */
  const updateMetrics = useCallback(async () => {
    const memory = measureMemoryUsage();
    const loadTime = measureLoadTime();
    const networkLatency = await measureNetworkLatency();
    const renderTime = measureRenderTime();
    const bundleSize = estimateBundleSize();

    const newMetrics: PerformanceMetrics = {
      memoryUsage: memory.percentage,
      loadTime,
      networkLatency,
      renderTime,
      bundleSize,
    };

    setMetrics(newMetrics);
    checkPerformanceIssues(newMetrics);
  }, [measureMemoryUsage, measureLoadTime, measureNetworkLatency, measureRenderTime, estimateBundleSize, checkPerformanceIssues]);

  // تحديث المقاييس كل 5 ثوانٍ
  useEffect(() => {
    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);
    return () => clearInterval(interval);
  }, [updateMetrics]);

  /**
   * تنسيق الأحجام
   */
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  /**
   * تنسيق الوقت
   */
  const formatTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  /**
   * الحصول على لون المؤشر حسب القيمة
   */
  const getProgressColor = (value: number, thresholds: { warning: number; error: number }) => {
    if (value >= thresholds.error) return 'error';
    if (value >= thresholds.warning) return 'warning';
    return 'success';
  };

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SpeedIcon />
            مراقب الأداء
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {performanceIssues.length > 0 && (
              <Chip
                icon={<WarningIcon />}
                label={`${performanceIssues.length} مشكلة`}
                color="warning"
                size="small"
              />
            )}
            <IconButton onClick={() => setExpanded(!expanded)} size="small">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        {/* المؤشرات الأساسية */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <MemoryIcon color="primary" />
              <Typography variant="caption" display="block">
                الذاكرة
              </Typography>
              <Typography variant="h6">
                {metrics.memoryUsage.toFixed(1)}%
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <SpeedIcon color="primary" />
              <Typography variant="caption" display="block">
                التحميل
              </Typography>
              <Typography variant="h6">
                {formatTime(metrics.loadTime)}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <NetworkIcon color="primary" />
              <Typography variant="caption" display="block">
                الشبكة
              </Typography>
              <Typography variant="h6">
                {metrics.networkLatency === -1 ? 'خطأ' : formatTime(metrics.networkLatency)}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="caption" display="block">
                الحزمة
              </Typography>
              <Typography variant="h6">
                {formatBytes(metrics.bundleSize)}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* التفاصيل المتقدمة */}
        <Collapse in={expanded}>
          <Box sx={{ mt: 2 }}>
            {/* مؤشرات التقدم */}
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" gutterBottom>
                  استخدام الذاكرة
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={Math.min(metrics.memoryUsage, 100)}
                  color={getProgressColor(metrics.memoryUsage, { warning: 60, error: 80 })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" gutterBottom>
                  أداء الشبكة
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={metrics.networkLatency === -1 ? 100 : Math.min((metrics.networkLatency / 2000) * 100, 100)}
                  color={getProgressColor(metrics.networkLatency, { warning: 500, error: 1000 })}
                />
              </Grid>
            </Grid>

            {/* تحذيرات الأداء */}
            {performanceIssues.length > 0 && (
              <Box sx={{ mt: 2 }}>
                {performanceIssues.map((issue, index) => (
                  <Alert key={index} severity="warning" sx={{ mb: 1 }}>
                    {issue}
                  </Alert>
                ))}
              </Box>
            )}
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default PerformanceMonitor;
