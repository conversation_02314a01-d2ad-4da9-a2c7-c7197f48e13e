
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.8.2
 * Query Engine version: 2060c79ba17c6bb9f5823312b6f6b7f4a845738e
 */
Prisma.prismaVersion = {
  client: "6.8.2",
  engine: "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  name: 'name',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  isActive: 'isActive'
};

exports.Prisma.ClientScalarFieldEnum = {
  id: 'id',
  clientNumber: 'clientNumber',
  taxNumber: 'taxNumber',
  clientName: 'clientName',
  companyName: 'companyName',
  addedDate: 'addedDate',
  phone: 'phone',
  email: 'email',
  address: 'address',
  pdfFile: 'pdfFile',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DeclarationScalarFieldEnum = {
  id: 'id',
  declarationNumber: 'declarationNumber',
  taxNumber: 'taxNumber',
  clientName: 'clientName',
  companyName: 'companyName',
  policyNumber: 'policyNumber',
  invoiceNumber: 'invoiceNumber',
  gatewayEntryNumber: 'gatewayEntryNumber',
  declarationType: 'declarationType',
  declarationDate: 'declarationDate',
  count: 'count',
  weight: 'weight',
  goodsType: 'goodsType',
  itemsCount: 'itemsCount',
  entryDate: 'entryDate',
  exitDate: 'exitDate',
  pdfFile: 'pdfFile',
  clientId: 'clientId',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DriverScalarFieldEnum = {
  id: 'id',
  declarationId: 'declarationId',
  driverName: 'driverName',
  truckNumber: 'truckNumber',
  trailerNumber: 'trailerNumber',
  driverPhone: 'driverPhone',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ItemMovementScalarFieldEnum = {
  id: 'id',
  movementNumber: 'movementNumber',
  movementDate: 'movementDate',
  declarationNumber: 'declarationNumber',
  itemNumber: 'itemNumber',
  invoiceNumber: 'invoiceNumber',
  packingListNumber: 'packingListNumber',
  tariffCode: 'tariffCode',
  itemName: 'itemName',
  quantity: 'quantity',
  packageType: 'packageType',
  goodsType: 'goodsType',
  countryOfOrigin: 'countryOfOrigin',
  itemValue: 'itemValue',
  currency: 'currency',
  totalValue: 'totalValue',
  pdfFile: 'pdfFile',
  declarationId: 'declarationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AuthorizationScalarFieldEnum = {
  id: 'id',
  authorizationNumber: 'authorizationNumber',
  clientName: 'clientName',
  taxNumber: 'taxNumber',
  authorizationType: 'authorizationType',
  startDate: 'startDate',
  endDate: 'endDate',
  pdfFile: 'pdfFile',
  declarationId: 'declarationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReleaseScalarFieldEnum = {
  id: 'id',
  releaseNumber: 'releaseNumber',
  issuingAuthority: 'issuingAuthority',
  invoiceNumber: 'invoiceNumber',
  invoiceDate: 'invoiceDate',
  invoiceValue: 'invoiceValue',
  approvalDate: 'approvalDate',
  releaseStartDate: 'releaseStartDate',
  releaseEndDate: 'releaseEndDate',
  driverPermit: 'driverPermit',
  pdfFile: 'pdfFile',
  declarationId: 'declarationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PermitScalarFieldEnum = {
  id: 'id',
  permitNumber: 'permitNumber',
  declarationNumber: 'declarationNumber',
  issuingAuthority: 'issuingAuthority',
  permitDate: 'permitDate',
  pdfFile: 'pdfFile',
  declarationId: 'declarationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReturnableGuaranteeScalarFieldEnum = {
  id: 'id',
  guaranteeSlipNumber: 'guaranteeSlipNumber',
  declarationNumber: 'declarationNumber',
  guaranteeType: 'guaranteeType',
  guaranteeStartDate: 'guaranteeStartDate',
  guaranteeEndDate: 'guaranteeEndDate',
  clientName: 'clientName',
  invoiceValue: 'invoiceValue',
  invoiceNumber: 'invoiceNumber',
  originCertNumber: 'originCertNumber',
  packingListNumber: 'packingListNumber',
  countryOfOrigin: 'countryOfOrigin',
  declarationValue: 'declarationValue',
  guaranteeAmount: 'guaranteeAmount',
  guaranteeDate: 'guaranteeDate',
  invoiceDate: 'invoiceDate',
  pdfFile: 'pdfFile',
  declarationId: 'declarationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NonReturnableGuaranteeScalarFieldEnum = {
  id: 'id',
  bankSlipNumber: 'bankSlipNumber',
  declarationNumber: 'declarationNumber',
  clientName: 'clientName',
  confiscationDate: 'confiscationDate',
  confiscatedAmount: 'confiscatedAmount',
  confiscationReason: 'confiscationReason',
  notes: 'notes',
  pdfFile: 'pdfFile',
  declarationId: 'declarationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReceiptScalarFieldEnum = {
  id: 'id',
  receiptNumber: 'receiptNumber',
  declarationNumber: 'declarationNumber',
  receiptType: 'receiptType',
  invoiceDate: 'invoiceDate',
  invoiceValue: 'invoiceValue',
  pdfFile: 'pdfFile',
  declarationId: 'declarationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OfficeDocumentScalarFieldEnum = {
  id: 'id',
  documentNumber: 'documentNumber',
  documentType: 'documentType',
  documentDate: 'documentDate',
  documentValue: 'documentValue',
  pdfFile: 'pdfFile',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InvalidatedTokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  tokenType: 'tokenType',
  userId: 'userId',
  expiresAt: 'expiresAt',
  invalidatedAt: 'invalidatedAt'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  deviceInfo: 'deviceInfo',
  isActive: 'isActive',
  lastActivity: 'lastActivity',
  createdAt: 'createdAt',
  expiresAt: 'expiresAt'
};

exports.Prisma.LoginAttemptScalarFieldEnum = {
  id: 'id',
  username: 'username',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  status: 'status',
  attemptTime: 'attemptTime',
  failureReason: 'failureReason'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  tableName: 'tableName',
  operation: 'operation',
  action: 'action',
  recordId: 'recordId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  userId: 'userId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  timestamp: 'timestamp'
};

exports.Prisma.CustomFormScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  formData: 'formData',
  formType: 'formType',
  isActive: 'isActive',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReportTemplateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  template: 'template',
  reportType: 'reportType',
  isDefault: 'isDefault',
  isActive: 'isActive',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SystemSettingsScalarFieldEnum = {
  id: 'id',
  companyName: 'companyName',
  companyLogo: 'companyLogo',
  companyAddress: 'companyAddress',
  companyPhone: 'companyPhone',
  companyEmail: 'companyEmail',
  companyWebsite: 'companyWebsite',
  primaryColor: 'primaryColor',
  secondaryColor: 'secondaryColor',
  defaultFont: 'defaultFont',
  defaultLanguage: 'defaultLanguage',
  maxFileSize: 'maxFileSize',
  enablePrinting: 'enablePrinting',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  USER: 'USER',
  MANAGER: 'MANAGER'
};

exports.DeclarationType = exports.$Enums.DeclarationType = {
  IMPORT: 'IMPORT',
  EXPORT: 'EXPORT'
};

exports.GoodsType = exports.$Enums.GoodsType = {
  HUMAN_MEDICINE: 'HUMAN_MEDICINE',
  LABORATORY_SOLUTIONS: 'LABORATORY_SOLUTIONS',
  MEDICAL_SUPPLIES: 'MEDICAL_SUPPLIES',
  SUGAR_STRIPS: 'SUGAR_STRIPS',
  MEDICAL_DEVICES: 'MEDICAL_DEVICES',
  MISCELLANEOUS: 'MISCELLANEOUS'
};

exports.PackageType = exports.$Enums.PackageType = {
  DRUM: 'DRUM',
  CARTON: 'CARTON',
  BARREL: 'BARREL'
};

exports.Currency = exports.$Enums.Currency = {
  USD: 'USD',
  EUR: 'EUR',
  GBP: 'GBP',
  SAR: 'SAR'
};

exports.AuthorizationType = exports.$Enums.AuthorizationType = {
  FOLLOW_UP: 'FOLLOW_UP',
  CLEARANCE: 'CLEARANCE',
  RECEIPT: 'RECEIPT',
  FULL: 'FULL'
};

exports.GuaranteeType = exports.$Enums.GuaranteeType = {
  DOCUMENTS: 'DOCUMENTS',
  FINANCIAL: 'FINANCIAL'
};

exports.ReceiptType = exports.$Enums.ReceiptType = {
  FOLLOW_UP: 'FOLLOW_UP',
  CLEARANCE: 'CLEARANCE',
  RECEIPT: 'RECEIPT',
  DELIVERY: 'DELIVERY'
};

exports.DocumentType = exports.$Enums.DocumentType = {
  INVOICE: 'INVOICE',
  CERTIFICATE: 'CERTIFICATE',
  PERMIT: 'PERMIT',
  AUTHORIZATION: 'AUTHORIZATION',
  GUARANTEE: 'GUARANTEE',
  RECEIPT: 'RECEIPT',
  RELEASE: 'RELEASE',
  OTHER: 'OTHER'
};

exports.TokenType = exports.$Enums.TokenType = {
  ACCESS: 'ACCESS',
  REFRESH: 'REFRESH'
};

exports.LoginStatus = exports.$Enums.LoginStatus = {
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  LOCKED: 'LOCKED',
  SUSPICIOUS: 'SUSPICIOUS'
};

exports.Prisma.ModelName = {
  User: 'User',
  Client: 'Client',
  Declaration: 'Declaration',
  Driver: 'Driver',
  ItemMovement: 'ItemMovement',
  Authorization: 'Authorization',
  Release: 'Release',
  Permit: 'Permit',
  ReturnableGuarantee: 'ReturnableGuarantee',
  NonReturnableGuarantee: 'NonReturnableGuarantee',
  Receipt: 'Receipt',
  OfficeDocument: 'OfficeDocument',
  InvalidatedToken: 'InvalidatedToken',
  Session: 'Session',
  LoginAttempt: 'LoginAttempt',
  AuditLog: 'AuditLog',
  CustomForm: 'CustomForm',
  ReportTemplate: 'ReportTemplate',
  SystemSettings: 'SystemSettings'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
