// 🔗 أنواع البيانات المشتركة - مشروع AlnoorArch
// ملف مؤقت لحل مشاكل imports حتى يتم ربط shared-types package

export enum ReportType {
  DECLARATION = 'DECLARATION',
  PERMIT = 'PERMIT',
  GUARANTEE = 'GUARANTEE',
  AUTHORIZATION = 'AUTHORIZATION',
  RECEIPT = 'RECEIPT',
  RELEASE = 'RELEASE',
  ITEM_MOVEMENT = 'ITEM_MOVEMENT',
  CLIENT = 'CLIENT',
  DOCUMENT = 'DOCUMENT',
  CUSTOM = 'CUSTOM'
}

export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  MANAGER = 'MANAGER'
}

export enum DeclarationType {
  IMPORT = 'IMPORT',
  EXPORT = 'EXPORT'
}

export enum GoodsType {
  HUMAN_MEDICINE = 'HUMAN_MEDICINE',
  LABORATORY_SOLUTIONS = 'LABORATORY_SOLUTIONS',
  MEDICAL_SUPPLIES = 'MEDICAL_SUPPLIES',
  SUGAR_STRIPS = 'SUGAR_STRIPS',
  MEDICAL_DEVICES = 'MEDICAL_DEVICES',
  MISCELLANEOUS = 'MISCELLANEOUS'
}

export enum AuthorizationType {
  FOLLOW_UP = 'FOLLOW_UP',
  CLEARANCE = 'CLEARANCE',
  RECEIPT = 'RECEIPT',
  FULL = 'FULL'
}

export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  SAR = 'SAR'
}

export enum GuaranteeStatus {
  ACTIVE = 'ACTIVE',
  RETURNED = 'RETURNED',
  EXPIRED = 'EXPIRED'
}

export enum TokenType {
  ACCESS = 'ACCESS',
  REFRESH = 'REFRESH'
}

export enum LoginStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  LOCKED = 'LOCKED',
  SUSPICIOUS = 'SUSPICIOUS'
}

// أنواع البيانات الأساسية
export interface User {
  id: string;
  username: string;
  email: string;
  name: string;
  role: UserRole;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Client {
  id: string;
  name: string;
  taxNumber: string;
  phone?: string;
  email?: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Declaration {
  id: string;
  declarationNumber: string;
  taxNumber: string;
  clientName: string;
  companyName?: string;
  policyNumber?: string;
  invoiceNumber?: string;
  gatewayEntryNumber?: string;
  declarationType: DeclarationType;
  declarationDate: string;
  count?: number;
  weight?: number;
  goodsType?: GoodsType;
  itemsCount?: number;
  entryDate?: string;
  exitDate?: string;
  pdfFile?: string;
  clientId?: string;
  userId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Permit {
  id: string;
  declarationId: string;
  permitNumber: string;
  permitType: string;
  permitDate: string;
  expiryDate?: string;
  status: string;
  notes?: string;
  pdfFile?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Guarantee {
  id: string;
  declarationId: string;
  guaranteeNumber: string;
  guaranteeType: string;
  amount: number;
  currency: Currency;
  issueDate: string;
  expiryDate?: string;
  status: GuaranteeStatus;
  notes?: string;
  pdfFile?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Authorization {
  id: string;
  declarationId: string;
  authorizationType: AuthorizationType;
  authorizedPerson: string;
  idNumber: string;
  startDate: string;
  endDate?: string;
  notes?: string;
  pdfFile?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Receipt {
  id: string;
  declarationId: string;
  receiptNumber: string;
  receiptDate: string;
  receiverName: string;
  receiverPhone?: string;
  receiverIdNumber?: string;
  declarationNumber?: number;
  clientName?: string;
  itemsDescription?: string;
  notes?: string;
  pdfFile?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Release {
  id: string;
  declarationId: string;
  releaseNumber: string;
  releaseDate: string;
  notes?: string;
  pdfFile?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ItemMovement {
  id: string;
  declarationId: string;
  itemName: string;
  quantity: number;
  unit: string;
  movementDate: string;
  movementType: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Document {
  id: string;
  title: string;
  description?: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  filePath: string;
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}

// أنواع التقارير
export interface ReportTemplate {
  id: string;
  name: string;
  description?: string;
  template: any;
  reportType: string;
  isDefault: boolean;
  isActive: boolean;
  createdBy: string;
  userId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CustomForm {
  id: string;
  name: string;
  description?: string;
  formData: any;
  formType?: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface SystemSettings {
  id: string;
  key: string;
  value: any;
  description?: string;
  category?: string;
  isSystem: boolean;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
}

// أنواع API Response
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SearchParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}
