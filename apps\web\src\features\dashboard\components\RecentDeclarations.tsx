import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Typography,
} from '@mui/material';
import { useRecentDeclarations } from '../hooks/useDeclarations';

const RecentDeclarations = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { data: declarations = [], isLoading, isError } = useRecentDeclarations();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'PENDING':
        return 'warning';
      case 'IN_PROGRESS':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return t('declarations.statusCompleted');
      case 'PENDING':
        return t('declarations.statusPending');
      case 'IN_PROGRESS':
        return t('declarations.statusInProgress');
      default:
        return status;
    }
  };

  const handleViewDeclaration = (id: string) => {
    navigate(`/declarations/${id}`);
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
        <CircularProgress size={40} />
        <Typography variant="body2" sx={{ ml: 2 }}>
          جاري تحميل البيانات...
        </Typography>
      </Box>
    );
  }

  if (isError) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
        <Typography variant="body2" color="error">
          حدث خطأ في تحميل البيانات
        </Typography>
      </Box>
    );
  }

  return (
    <TableContainer>
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>{t('declarations.declarationNumber')}</TableCell>
            <TableCell>{t('declarations.clientName')}</TableCell>
            <TableCell>{t('declarations.declarationType')}</TableCell>
            <TableCell>{t('declarations.declarationDate')}</TableCell>
            <TableCell>{t('common.status')}</TableCell>
            <TableCell align="center">{t('common.actions')}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {declarations.map((declaration) => (
            <TableRow key={declaration.id}>
              <TableCell>{declaration.declarationNumber}</TableCell>
              <TableCell>{declaration.clientName}</TableCell>
              <TableCell>
                {declaration.declarationType === 'IMPORT'
                  ? t('declarations.import')
                  : t('declarations.export')}
              </TableCell>
              <TableCell>{declaration.declarationDate}</TableCell>
              <TableCell>
                <Chip
                  label={getStatusLabel(declaration.status)}
                  color={getStatusColor(declaration.status) as any}
                  size="small"
                />
              </TableCell>
              <TableCell align="center">
                <Button
                  size="small"
                  onClick={() => handleViewDeclaration(declaration.id)}
                >
                  {t('common.view')}
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="outlined"
          size="small"
          onClick={() => navigate('/declarations')}
        >
          {t('common.viewAll')}
        </Button>
      </Box>
    </TableContainer>
  );
};

export default RecentDeclarations;
