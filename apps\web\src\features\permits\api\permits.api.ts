import axios from 'axios';
import { Permit, PermitType } from '../types/permit.types';

// واجهة طلب إنشاء التصريح
export interface CreatePermitRequest {
  permitType: string;
  permitDate: string;
  issueDate?: string;
  expiryDate: string;
  notes?: string;
  declarationId: string;
}

// واجهة طلب تحديث التصريح
export interface UpdatePermitRequest {
  permitType?: string;
  permitDate?: string;
  issueDate?: string;
  expiryDate?: string;
  notes?: string;
  declarationId?: string;
}

// واجهة معلمات البحث عن التصاريح
export interface PermitSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  permitType?: string;
  fromDate?: Date;
  toDate?: Date;
  declarationId?: string;
  isActive?: boolean;
}

// الحصول على قائمة التصاريح
export const getPermits = async (params: PermitSearchParams = {}) => {
  const { data } = await axios.get('/api/permits', { params });
  return data;
};

// الحصول على تصريح محدد
export const getPermit = async (id: string) => {
  const { data } = await axios.get(`/api/permits/${id}`);
  return data.data;
};

// إنشاء تصريح جديد
export const createPermit = async (
  permitData: CreatePermitRequest,
  file?: File
) => {
  const formData = new FormData();
  formData.append('data', JSON.stringify(permitData));

  if (file) {
    formData.append('file', file);
  }

  const { data } = await axios.post('/api/permits', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return data.data;
};

// تحديث تصريح
export const updatePermit = async (
  id: string,
  permitData: UpdatePermitRequest,
  file?: File
) => {
  const formData = new FormData();
  formData.append('data', JSON.stringify(permitData));

  if (file) {
    formData.append('file', file);
  }

  const { data } = await axios.put(`/api/permits/${id}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return data.data;
};

// حذف تصريح
export const deletePermit = async (id: string) => {
  const { data } = await axios.delete(`/api/permits/${id}`);
  return data;
};
