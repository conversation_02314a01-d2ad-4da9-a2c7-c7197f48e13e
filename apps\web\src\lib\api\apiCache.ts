/**
 * وحدة التخزين المؤقت لطلبات API
 * تستخدم لتحسين أداء التطبيق وتقليل عدد الطلبات إلى الخادم
 * تدعم التخزين المؤقت المحلي والتخزين المؤقت في الجلسة
 */

// نوع بيانات التخزين المؤقت
interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

// نوع استراتيجية التخزين المؤقت
type CacheStrategy = 'memory' | 'localStorage' | 'sessionStorage';

// خيارات التخزين المؤقت
interface CacheOptions {
  // مدة صلاحية التخزين المؤقت بالمللي ثانية
  ttl?: number;
  // هل يتم تحديث التخزين المؤقت في الخلفية
  backgroundRefresh?: boolean;
  // استراتيجية التخزين المؤقت
  strategy?: CacheStrategy;
  // هل يتم تخزين البيانات حتى بعد الأخطاء
  cacheOnError?: boolean;
  // الأولوية (الأعلى يعني أنه سيتم الاحتفاظ بها لفترة أطول عند تنظيف الذاكرة)
  priority?: number;
}

// الخيارات الافتراضية
const DEFAULT_OPTIONS: CacheOptions = {
  ttl: 5 * 60 * 1000, // 5 دقائق
  backgroundRefresh: true,
  strategy: 'memory',
  cacheOnError: false,
  priority: 1,
};

// التخزين المؤقت في الذاكرة
const memoryCache = new Map<string, CacheItem<any>>();

// الحد الأقصى لحجم التخزين المؤقت في الذاكرة
const MAX_MEMORY_CACHE_SIZE = 100;

// قائمة الأولويات للتخزين المؤقت
const cachePriorities = new Map<string, number>();

/**
 * الحصول على بيانات من التخزين المؤقت
 * @param key مفتاح التخزين المؤقت
 * @param options خيارات التخزين المؤقت
 * @returns البيانات المخزنة مؤقتًا أو undefined إذا لم تكن موجودة
 */
export const getCachedData = <T>(key: string, options: CacheOptions = {}): T | undefined => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const cacheKey = `api_cache_${key}`;
  let item: CacheItem<T> | undefined;

  // الحصول على البيانات حسب استراتيجية التخزين
  switch (opts.strategy) {
    case 'localStorage':
      try {
        const storedItem = localStorage.getItem(cacheKey);
        if (storedItem) {
          item = JSON.parse(storedItem);
        }
      } catch (error) {
        console.error('خطأ في قراءة التخزين المحلي:', error);
      }
      break;

    case 'sessionStorage':
      try {
        const storedItem = sessionStorage.getItem(cacheKey);
        if (storedItem) {
          item = JSON.parse(storedItem);
        }
      } catch (error) {
        console.error('خطأ في قراءة تخزين الجلسة:', error);
      }
      break;

    case 'memory':
    default:
      item = memoryCache.get(cacheKey);
      break;
  }

  if (!item) {
    return undefined;
  }

  const now = Date.now();

  // التحقق من صلاحية التخزين المؤقت
  if (now > item.expiresAt) {
    removeCachedData(key, opts);
    return undefined;
  }

  // تحديث أولوية التخزين المؤقت عند الاستخدام
  cachePriorities.set(cacheKey, (cachePriorities.get(cacheKey) || 0) + 1);

  return item.data;
};

/**
 * تخزين البيانات مؤقتًا
 * @param key مفتاح التخزين المؤقت
 * @param data البيانات المراد تخزينها
 * @param options خيارات التخزين المؤقت
 */
export const setCachedData = <T>(
  key: string,
  data: T,
  options: CacheOptions = {}
): void => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const now = Date.now();
  const cacheKey = `api_cache_${key}`;

  const cacheItem: CacheItem<T> = {
    data,
    timestamp: now,
    expiresAt: now + (opts.ttl || 0),
  };

  // تخزين البيانات حسب استراتيجية التخزين
  switch (opts.strategy) {
    case 'localStorage':
      try {
        localStorage.setItem(cacheKey, JSON.stringify(cacheItem));
      } catch (error) {
        console.error('خطأ في كتابة التخزين المحلي:', error);
        // الرجوع إلى التخزين في الذاكرة في حالة الخطأ
        memoryCache.set(cacheKey, cacheItem);
      }
      break;

    case 'sessionStorage':
      try {
        sessionStorage.setItem(cacheKey, JSON.stringify(cacheItem));
      } catch (error) {
        console.error('خطأ في كتابة تخزين الجلسة:', error);
        // الرجوع إلى التخزين في الذاكرة في حالة الخطأ
        memoryCache.set(cacheKey, cacheItem);
      }
      break;

    case 'memory':
    default:
      // تنظيف الذاكرة إذا تجاوزت الحد الأقصى
      if (memoryCache.size >= MAX_MEMORY_CACHE_SIZE) {
        cleanMemoryCache();
      }

      memoryCache.set(cacheKey, cacheItem);
      break;
  }

  // تعيين أولوية التخزين المؤقت
  cachePriorities.set(cacheKey, opts.priority || 1);
};

/**
 * حذف بيانات من التخزين المؤقت
 * @param key مفتاح التخزين المؤقت
 * @param options خيارات التخزين المؤقت
 */
export const removeCachedData = (key: string, options: CacheOptions = {}): void => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const cacheKey = `api_cache_${key}`;

  // حذف البيانات حسب استراتيجية التخزين
  switch (opts.strategy) {
    case 'localStorage':
      try {
        localStorage.removeItem(cacheKey);
      } catch (error) {
        console.error('خطأ في حذف التخزين المحلي:', error);
      }
      break;

    case 'sessionStorage':
      try {
        sessionStorage.removeItem(cacheKey);
      } catch (error) {
        console.error('خطأ في حذف تخزين الجلسة:', error);
      }
      break;

    case 'memory':
    default:
      memoryCache.delete(cacheKey);
      break;
  }

  // حذف الأولوية
  cachePriorities.delete(cacheKey);
};

/**
 * حذف جميع البيانات من التخزين المؤقت
 */
export const clearCache = (): void => {
  // حذف التخزين المؤقت في الذاكرة
  memoryCache.clear();

  // حذف التخزين المؤقت في التخزين المحلي
  try {
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('api_cache_')) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));
  } catch (error) {
    console.error('خطأ في حذف التخزين المحلي:', error);
  }

  // حذف التخزين المؤقت في تخزين الجلسة
  try {
    const keysToRemove: string[] = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && key.startsWith('api_cache_')) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => sessionStorage.removeItem(key));
  } catch (error) {
    console.error('خطأ في حذف تخزين الجلسة:', error);
  }

  // حذف قائمة الأولويات
  cachePriorities.clear();
};

/**
 * تنظيف الذاكرة عند تجاوز الحد الأقصى
 * يحذف العناصر ذات الأولوية الأقل أولاً
 */
const cleanMemoryCache = (): void => {
  // تحويل الأولويات إلى مصفوفة وترتيبها
  const priorities = Array.from(cachePriorities.entries())
    .filter(([key]) => memoryCache.has(key))
    .sort((a, b) => a[1] - b[1]);

  // حذف 20% من العناصر ذات الأولوية الأقل
  const itemsToRemove = Math.ceil(memoryCache.size * 0.2);
  priorities.slice(0, itemsToRemove).forEach(([key]) => {
    memoryCache.delete(key);
    cachePriorities.delete(key);
  });
};

/**
 * حذف البيانات المنتهية الصلاحية من التخزين المؤقت
 */
export const cleanExpiredCache = (): void => {
  const now = Date.now();

  // تنظيف التخزين المؤقت في الذاكرة
  for (const [key, item] of memoryCache.entries()) {
    if (now > item.expiresAt) {
      memoryCache.delete(key);
      cachePriorities.delete(key);
    }
  }

  // تنظيف التخزين المؤقت في التخزين المحلي
  try {
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('api_cache_')) {
        const item = JSON.parse(localStorage.getItem(key) || '{}');
        if (item.expiresAt && now > item.expiresAt) {
          keysToRemove.push(key);
        }
      }
    }
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      cachePriorities.delete(key);
    });
  } catch (error) {
    console.error('خطأ في تنظيف التخزين المحلي:', error);
  }

  // تنظيف التخزين المؤقت في تخزين الجلسة
  try {
    const keysToRemove: string[] = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && key.startsWith('api_cache_')) {
        const item = JSON.parse(sessionStorage.getItem(key) || '{}');
        if (item.expiresAt && now > item.expiresAt) {
          keysToRemove.push(key);
        }
      }
    }
    keysToRemove.forEach(key => {
      sessionStorage.removeItem(key);
      cachePriorities.delete(key);
    });
  } catch (error) {
    console.error('خطأ في تنظيف تخزين الجلسة:', error);
  }
};

/**
 * تنفيذ طلب API مع التخزين المؤقت
 * @param key مفتاح التخزين المؤقت
 * @param fetchFn دالة الطلب
 * @param options خيارات التخزين المؤقت
 * @returns البيانات المخزنة مؤقتًا أو البيانات الجديدة
 */
export const cachedFetch = async <T>(
  key: string,
  fetchFn: () => Promise<T>,
  options: CacheOptions = {}
): Promise<T> => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const cachedData = getCachedData<T>(key, opts);
  const cacheKey = `api_cache_${key}`;

  // إذا كانت البيانات موجودة في التخزين المؤقت، استخدمها
  if (cachedData !== undefined) {
    // إذا كان التحديث في الخلفية مفعلًا، قم بتحديث البيانات في الخلفية
    if (opts.backgroundRefresh) {
      let cacheItem: CacheItem<T> | undefined;

      // الحصول على عنصر التخزين المؤقت حسب استراتيجية التخزين
      switch (opts.strategy) {
        case 'localStorage':
          try {
            const storedItem = localStorage.getItem(cacheKey);
            if (storedItem) {
              cacheItem = JSON.parse(storedItem);
            }
          } catch (error) {
            console.error('خطأ في قراءة التخزين المحلي:', error);
          }
          break;

        case 'sessionStorage':
          try {
            const storedItem = sessionStorage.getItem(cacheKey);
            if (storedItem) {
              cacheItem = JSON.parse(storedItem);
            }
          } catch (error) {
            console.error('خطأ في قراءة تخزين الجلسة:', error);
          }
          break;

        case 'memory':
        default:
          cacheItem = memoryCache.get(cacheKey);
          break;
      }

      const now = Date.now();

      // تحديث البيانات إذا مر نصف مدة الصلاحية
      if (cacheItem && now - cacheItem.timestamp > (opts.ttl || 0) / 2) {
        fetchFn()
          .then((newData) => {
            setCachedData(key, newData, opts);
          })
          .catch((error) => {
            console.error(`فشل في تحديث التخزين المؤقت لـ ${key}:`, error);
          });
      }
    }

    return cachedData;
  }

  // إذا لم تكن البيانات موجودة في التخزين المؤقت، قم بتنفيذ الطلب
  try {
    const data = await fetchFn();
    setCachedData(key, data, opts);
    return data;
  } catch (error) {
    // تخزين البيانات في حالة الخطأ إذا كان مسموحًا
    if (opts.cacheOnError) {
      console.warn(`تم حدوث خطأ ولكن تم تفعيل cacheOnError لـ ${key}`);
      // يمكن تخزين بيانات فارغة أو قيمة افتراضية
      setCachedData(key, {} as T, {
        ...opts,
        ttl: 60 * 1000, // تخزين مؤقت لمدة دقيقة واحدة فقط في حالة الخطأ
      });
    }
    throw error;
  }
};

// تنظيف التخزين المؤقت المنتهي الصلاحية كل 5 دقائق
setInterval(cleanExpiredCache, 5 * 60 * 1000);

/**
 * تخزين استجابة API مؤقتًا
 * @param apiCall دالة طلب API
 * @param url مسار الطلب
 * @param config إعدادات الطلب
 * @returns البيانات المخزنة مؤقتًا أو البيانات الجديدة
 */
export const cacheApiResponse = async <T>(
  apiCall: () => Promise<{ data: T }>,
  url: string,
  config?: any
): Promise<T> => {
  // إنشاء مفتاح فريد للتخزين المؤقت
  const cacheKey = `${url}${config ? JSON.stringify(config) : ''}`;

  return cachedFetch<T>(
    cacheKey,
    async () => {
      const response = await apiCall();
      return response.data;
    },
    {
      ttl: 5 * 60 * 1000, // 5 دقائق
      backgroundRefresh: true,
      strategy: 'memory',
    }
  );
};
