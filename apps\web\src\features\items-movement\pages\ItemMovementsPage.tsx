import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  InputAdornment,
  MenuItem,
  Paper,
  TextField,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useItemMovements, useDeleteItemMovement } from '../hooks/useItemMovements';
import { GoodsType } from '../types/item-movement.types';
import { ItemMovementSearchParams } from '../api/items-movement.api';

const ItemMovementsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // حالة البحث والتصفية
  const [searchParams, setSearchParams] = useState<ItemMovementSearchParams>({
    page: 1,
    limit: 10,
    sort: 'movementNumber',
    order: 'desc',
  });
  const [searchText, setSearchText] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [goodsType, setGoodsType] = useState<GoodsType | ''>('');
  const [fromDate, setFromDate] = useState<Date | null>(null);
  const [toDate, setToDate] = useState<Date | null>(null);

  // استخدام خطافات البيانات
  const { data, isLoading, isError } = useItemMovements(searchParams);
  const deleteMutation = useDeleteItemMovement();

  // التعامل مع تغيير الصفحة
  const handlePageChange = (page: number) => {
    setSearchParams((prev) => ({ ...prev, page }));
  };

  // التعامل مع البحث
  const handleSearch = () => {
    const params: ItemMovementSearchParams = {
      ...searchParams,
      page: 1,
      search: searchText || undefined,
      goodsType: goodsType || undefined,
      fromDate: fromDate ? fromDate.toISOString() : undefined,
      toDate: toDate ? toDate.toISOString() : undefined,
    };
    setSearchParams(params);
  };

  // التعامل مع إعادة تعيين التصفية
  const handleResetFilters = () => {
    setSearchText('');
    setGoodsType('');
    setFromDate(null);
    setToDate(null);
    setSearchParams({
      page: 1,
      limit: 10,
      sort: 'movementNumber',
      order: 'desc',
    });
  };

  // التعامل مع حذف حركة الصنف
  const handleDeleteItemMovement = async (id: string) => {
    if (window.confirm(t('itemMovements.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id);
      } catch (error) {
        console.error('Error deleting item movement:', error);
      }
    }
  };

  // التعامل مع عرض حركة الصنف
  const handleViewItemMovement = (id: string) => {
    navigate(`/items-movement/${id}`);
  };

  // التعامل مع تعديل حركة الصنف
  const handleEditItemMovement = (id: string) => {
    navigate(`/items-movement/${id}`);
  };

  // التعامل مع إنشاء حركة صنف جديدة
  const handleCreateItemMovement = () => {
    navigate('/items-movement/new');
  };

  // عرض حالة التحميل
  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  // عرض حالة الخطأ
  if (isError) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <Typography color="error">{t('common.errorLoading')}</Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('itemMovements.title')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('itemMovements.description')}
        </Typography>
      </Box>

      <Paper sx={{ p: 2, mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <TextField
            label={t('common.search')}
            variant="outlined"
            size="small"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={handleSearch} edge="end">
                    <SearchIcon />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{ width: 300 }}
          />
          <Box>
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={() => setShowFilters(!showFilters)}
              sx={{ mr: 1 }}
            >
              {t('common.filters')}
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateItemMovement}
            >
              {t('itemMovements.create')}
            </Button>
          </Box>
        </Box>

        {showFilters && (
          <Box mb={3}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  select
                  fullWidth
                  label={t('itemMovements.goodsType')}
                  value={goodsType}
                  onChange={(e) => setGoodsType(e.target.value as GoodsType | '')}
                  size="small"
                >
                  <MenuItem value="">{t('common.all')}</MenuItem>
                  <MenuItem value={GoodsType.HUMAN_MEDICINE}>{t('itemMovements.humanMedicine')}</MenuItem>
                  <MenuItem value={GoodsType.LABORATORY_SOLUTIONS}>{t('itemMovements.laboratorySolutions')}</MenuItem>
                  <MenuItem value={GoodsType.MEDICAL_SUPPLIES}>{t('itemMovements.medicalSupplies')}</MenuItem>
                  <MenuItem value={GoodsType.SUGAR_STRIPS}>{t('itemMovements.sugarStrips')}</MenuItem>
                  <MenuItem value={GoodsType.MEDICAL_DEVICES}>{t('itemMovements.medicalDevices')}</MenuItem>
                  <MenuItem value={GoodsType.MISCELLANEOUS}>{t('itemMovements.miscellaneous')}</MenuItem>
                </TextField>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('common.fromDate')}
                  value={fromDate}
                  onChange={(date) => {
                    if (date && 'toDate' in date && typeof date.toDate === 'function') {
                      setFromDate(date.toDate());
                    } else {
                      setFromDate(date as Date | null);
                    }
                  }}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label={t('common.toDate')}
                  value={toDate}
                  onChange={(date) => {
                    if (date && 'toDate' in date && typeof date.toDate === 'function') {
                      setToDate(date.toDate());
                    } else {
                      setToDate(date as Date | null);
                    }
                  }}
                  slotProps={{ textField: { size: 'small', fullWidth: true } }}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box display="flex" gap={1}>
                  <Button variant="contained" onClick={handleSearch} fullWidth>
                    {t('common.apply')}
                  </Button>
                  <Button variant="outlined" onClick={handleResetFilters} fullWidth>
                    {t('common.reset')}
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </Box>
        )}

        <Box>
          {data?.data.length === 0 ? (
            <Box textAlign="center" py={4}>
              <Typography variant="h6">{t('itemMovements.noItemMovements')}</Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateItemMovement}
                sx={{ mt: 2 }}
              >
                {t('itemMovements.create')}
              </Button>
            </Box>
          ) : (
            <Grid container spacing={2}>
              {data?.data.map((itemMovement) => (
                <Grid item xs={12} sm={6} md={4} key={itemMovement.id}>
                  <Card>
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="h6">
                          {t('itemMovements.number')}: {itemMovement.movementNumber}
                        </Typography>
                        {itemMovement.goodsType && (
                          <Chip
                            label={t(`itemMovements.${itemMovement.goodsType.toLowerCase()}`)}
                            color="primary"
                            size="small"
                          />
                        )}
                      </Box>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {t('itemMovements.declarationNumber')}: {itemMovement.declarationNumber}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {t('itemMovements.itemName')}: {itemMovement.itemName || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {t('itemMovements.count')}: {itemMovement.count || '-'}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {t('itemMovements.date')}:{' '}
                        {itemMovement.movementDate
                          ? new Date(itemMovement.movementDate).toLocaleDateString()
                          : '-'}
                      </Typography>
                      <Box display="flex" justifyContent="flex-end" mt={2}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewItemMovement(itemMovement.id)}
                          title={t('common.view')}
                        >
                          <VisibilityIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleEditItemMovement(itemMovement.id)}
                          title={t('common.edit')}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteItemMovement(itemMovement.id)}
                          title={t('common.delete')}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>

        {data && data.pagination.total > 0 && (
          <Box display="flex" justifyContent="center" mt={4}>
            <Box display="flex" gap={1}>
              <Button
                disabled={data.pagination.page === 1}
                onClick={() => handlePageChange(data.pagination.page - 1)}
              >
                {t('common.previous')}
              </Button>
              <Box display="flex" alignItems="center" mx={2}>
                {t('common.page')} {data.pagination.page} {t('common.of')}{' '}
                {data.pagination.pages}
              </Box>
              <Button
                disabled={data.pagination.page >= data.pagination.pages}
                onClick={() => handlePageChange(data.pagination.page + 1)}
              >
                {t('common.next')}
              </Button>
            </Box>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default ItemMovementsPage;
