import { Router } from 'express';
import multer from 'multer';
import { authorizationController } from '../controllers/authorization.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import {
  createAuthorizationSchema,
  updateAuthorizationSchema,
  getAuthorizationSchema,
  deleteAuthorizationSchema,
  listAuthorizationsSchema,
} from '../schemas/authorization.schema.js';
import { config } from '../../../core/config/app.config.js';

export const authorizationRoutes = Router();

// إعداد Multer لرفع الملفات
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: config.upload.maxFileSize,
  },
  fileFilter: (req, file, cb) => {
    // قبول ملفات PDF فقط
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم. يرجى رفع ملف PDF فقط.'));
    }
  },
});

// مسارات التفويضات
authorizationRoutes.get(
  '/',
  authMiddleware,
  validateRequest(listAuthorizationsSchema),
  authorizationController.listAuthorizations
);

authorizationRoutes.post(
  '/',
  authMiddleware,
  upload.single('file'),
  validateRequest(createAuthorizationSchema),
  authorizationController.createAuthorization
);

authorizationRoutes.get(
  '/:id',
  authMiddleware,
  validateRequest(getAuthorizationSchema),
  authorizationController.getAuthorization
);

authorizationRoutes.put(
  '/:id',
  authMiddleware,
  upload.single('file'),
  validateRequest(updateAuthorizationSchema),
  authorizationController.updateAuthorization
);

authorizationRoutes.delete(
  '/:id',
  authMiddleware,
  validateRequest(deleteAuthorizationSchema),
  authorizationController.deleteAuthorization
);
