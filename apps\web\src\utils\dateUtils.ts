import dayjs, { Dayjs } from 'dayjs';

/**
 * تحويل قيمة DatePicker إلى Date أو null
 */
export const convertPickerValueToDate = (value: Dayjs | null): Date | null => {
  if (!value) return null;
  return value.toDate();
};

/**
 * معالج تغيير التاريخ للـ DatePicker
 */
export const createDateChangeHandler = (
  setter: (date: Date | null) => void
) => {
  return (value: Dayjs | null) => {
    setter(convertPickerValueToDate(value));
  };
};

/**
 * تحويل Date إلى Dayjs للـ DatePicker
 */
export const convertDateToPickerValue = (date: Date | null): Dayjs | null => {
  if (!date) return null;
  return dayjs(date);
};
