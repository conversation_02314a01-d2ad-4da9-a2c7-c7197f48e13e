const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('بدء إنشاء المستخدمين...');

  // إنشاء مستخدمين
  const adminPassword = await bcrypt.hash('admin123', 10);
  const userPassword = await bcrypt.hash('user123', 10);

  try {
    const admin = await prisma.user.upsert({
      where: { username: 'admin' },
      update: {
        password: adminPassword
      },
      create: {
        username: 'admin',
        password: adminPassword,
        name: 'مدير النظام',
        email: '<EMAIL>',
        role: 'ADMIN',
      },
    });

    console.log('تم إنشاء المستخدم الأول:', admin);

    const user = await prisma.user.upsert({
      where: { username: 'user' },
      update: {
        password: userPassword
      },
      create: {
        username: 'user',
        password: userPassword,
        name: 'مستخدم النظام',
        email: '<EMAIL>',
        role: 'USER',
      },
    });

    console.log('تم إنشاء المستخدم الثاني:', user);

    console.log('تم إنشاء المستخدمين بنجاح');
  } catch (error) {
    console.error('حدث خطأ أثناء إنشاء المستخدمين:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
