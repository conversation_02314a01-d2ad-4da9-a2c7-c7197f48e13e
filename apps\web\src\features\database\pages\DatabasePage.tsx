import React from 'react';
import { Box, Divider, Typography, Alert } from '@mui/material';
import { useTranslation } from 'react-i18next';
import BackupsList from '../components/BackupsList';
import CreateBackupForm from '../components/CreateBackupForm';
import ExportDatabaseForm from '../components/ExportDatabaseForm';
import InitializeDatabaseForm from '../components/InitializeDatabaseForm';

/**
 * صفحة إدارة قاعدة البيانات
 */
const DatabasePage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {t('database.title')}
      </Typography>

      <Alert severity="info" sx={{ mb: 4 }}>
        {t('database.adminOnlyFeature')}
      </Alert>

      <Typography variant="h5" gutterBottom>
        {t('database.backups')}
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        {t('database.backupsDescription')}
      </Typography>

      <CreateBackupForm />
      <BackupsList />

      <Divider sx={{ my: 4 }} />

      <Typography variant="h5" gutterBottom>
        {t('database.export')}
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        {t('database.exportDescription')}
      </Typography>

      <ExportDatabaseForm />

      <Divider sx={{ my: 4 }} />

      <Typography variant="h5" gutterBottom>
        {t('database.dangerZone')}
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        {t('database.dangerZoneDescription')}
      </Typography>

      <InitializeDatabaseForm />
    </Box>
  );
};

export default DatabasePage;
