/**
 * سكريبت لإنشاء تقرير اختبارات الأداء
 * 
 * يستخدم هذا السكريبت لتحليل نتائج اختبارات الأداء وإنشاء تقرير شامل.
 * 
 * الاستخدام:
 * node performance-test-report.js
 */

const fs = require('fs');
const path = require('path');

// الإعدادات الافتراضية
const REPORTS_DIR = path.join(__dirname, 'reports');
const OUTPUT_DIR = path.join(__dirname, 'output');

// دالة لقراءة جميع ملفات التقارير
function readReportFiles() {
  // التحقق من وجود مجلد التقارير
  if (!fs.existsSync(REPORTS_DIR)) {
    console.error(`مجلد التقارير غير موجود: ${REPORTS_DIR}`);
    return [];
  }
  
  // قراءة جميع ملفات التقارير
  const files = fs.readdirSync(REPORTS_DIR);
  const reportFiles = files.filter(file => file.endsWith('.json'));
  
  // قراءة محتوى كل ملف
  const reports = [];
  for (const file of reportFiles) {
    try {
      const content = fs.readFileSync(path.join(REPORTS_DIR, file), 'utf8');
      const report = JSON.parse(content);
      reports.push({
        file,
        report,
      });
    } catch (error) {
      console.error(`خطأ في قراءة الملف ${file}:`, error);
    }
  }
  
  return reports;
}

// دالة لتحليل تقارير API
function analyzeApiReports(reports) {
  const apiReports = reports.filter(r => r.file.includes('api-') || r.file.includes('-api'));
  
  if (apiReports.length === 0) {
    return null;
  }
  
  // تجميع البيانات
  const summary = {
    endpoints: {},
    overall: {
      requestsPerSecond: 0,
      latency: {
        average: 0,
        min: Infinity,
        max: 0,
      },
      throughput: {
        average: 0,
      },
      errors: 0,
      totalRequests: 0,
    },
  };
  
  // تحليل كل تقرير
  for (const { file, report } of apiReports) {
    // استخراج اسم نقطة النهاية من اسم الملف
    const match = file.match(/api-([^-]+)/);
    const endpoint = match ? match[1] : 'unknown';
    
    // إضافة البيانات إلى الملخص
    if (!summary.endpoints[endpoint]) {
      summary.endpoints[endpoint] = {
        requestsPerSecond: [],
        latency: {
          average: [],
          min: [],
          max: [],
        },
        throughput: {
          average: [],
        },
        errors: [],
        totalRequests: [],
      };
    }
    
    summary.endpoints[endpoint].requestsPerSecond.push(report.requests.average);
    summary.endpoints[endpoint].latency.average.push(report.latency.average);
    summary.endpoints[endpoint].latency.min.push(report.latency.min);
    summary.endpoints[endpoint].latency.max.push(report.latency.max);
    summary.endpoints[endpoint].throughput.average.push(report.throughput.average);
    summary.endpoints[endpoint].errors.push(report.errors);
    summary.endpoints[endpoint].totalRequests.push(report.requests.total);
    
    // إضافة البيانات إلى الإجمالي
    summary.overall.requestsPerSecond += report.requests.average;
    summary.overall.latency.average += report.latency.average;
    summary.overall.latency.min = Math.min(summary.overall.latency.min, report.latency.min);
    summary.overall.latency.max = Math.max(summary.overall.latency.max, report.latency.max);
    summary.overall.throughput.average += report.throughput.average;
    summary.overall.errors += report.errors;
    summary.overall.totalRequests += report.requests.total;
  }
  
  // حساب المتوسطات
  for (const endpoint in summary.endpoints) {
    const data = summary.endpoints[endpoint];
    summary.endpoints[endpoint] = {
      requestsPerSecond: calculateAverage(data.requestsPerSecond),
      latency: {
        average: calculateAverage(data.latency.average),
        min: calculateAverage(data.latency.min),
        max: calculateAverage(data.latency.max),
      },
      throughput: {
        average: calculateAverage(data.throughput.average),
      },
      errors: calculateSum(data.errors),
      totalRequests: calculateSum(data.totalRequests),
    };
  }
  
  // حساب المتوسطات الإجمالية
  summary.overall.requestsPerSecond /= apiReports.length;
  summary.overall.latency.average /= apiReports.length;
  summary.overall.throughput.average /= apiReports.length;
  
  return summary;
}

// دالة لتحليل تقارير UI
function analyzeUiReports(reports) {
  const uiReports = reports.filter(r => r.file.includes('ui-') || r.file.includes('-ui'));
  
  if (uiReports.length === 0) {
    return null;
  }
  
  // تجميع البيانات
  const summary = {
    pages: {},
    overall: {
      loadTime: 0,
      domContentLoaded: 0,
      transferSize: 0,
      jsHeapSize: 0,
    },
  };
  
  // تحليل كل تقرير
  for (const { file, report } of uiReports) {
    // استخراج اسم الصفحة من اسم الملف
    const match = file.match(/ui-([^-]+)/);
    const page = match ? match[1] : 'unknown';
    
    // إضافة البيانات إلى الملخص
    if (!summary.pages[page]) {
      summary.pages[page] = {
        loadTime: [],
        domContentLoaded: [],
        transferSize: [],
        jsHeapSize: [],
      };
    }
    
    const loadTime = report.performanceMetrics.duration || 0;
    const domContentLoaded = (report.performanceMetrics.domContentLoadedEventEnd - report.performanceMetrics.domContentLoadedEventStart) || 0;
    const transferSize = report.performanceMetrics.transferSize || 0;
    const jsHeapSize = report.memoryInfo.usedJSHeapSize || 0;
    
    summary.pages[page].loadTime.push(loadTime);
    summary.pages[page].domContentLoaded.push(domContentLoaded);
    summary.pages[page].transferSize.push(transferSize);
    summary.pages[page].jsHeapSize.push(jsHeapSize);
    
    // إضافة البيانات إلى الإجمالي
    summary.overall.loadTime += loadTime;
    summary.overall.domContentLoaded += domContentLoaded;
    summary.overall.transferSize += transferSize;
    summary.overall.jsHeapSize += jsHeapSize;
  }
  
  // حساب المتوسطات
  for (const page in summary.pages) {
    const data = summary.pages[page];
    summary.pages[page] = {
      loadTime: calculateAverage(data.loadTime),
      domContentLoaded: calculateAverage(data.domContentLoaded),
      transferSize: calculateAverage(data.transferSize),
      jsHeapSize: calculateAverage(data.jsHeapSize),
    };
  }
  
  // حساب المتوسطات الإجمالية
  summary.overall.loadTime /= uiReports.length;
  summary.overall.domContentLoaded /= uiReports.length;
  summary.overall.transferSize /= uiReports.length;
  summary.overall.jsHeapSize /= uiReports.length;
  
  return summary;
}

// دالة لتحليل تقارير UX
function analyzeUxReports(reports) {
  const uxReports = reports.filter(r => r.file.includes('ux-'));
  
  if (uxReports.length === 0) {
    return null;
  }
  
  // تجميع البيانات
  const summary = {
    tests: {},
    overall: {
      success: 0,
      failure: 0,
      successRate: 0,
    },
  };
  
  // تحليل كل تقرير
  for (const { file, report } of uxReports) {
    // استخراج اسم الاختبار من اسم الملف
    const match = file.match(/ux-test-([^-]+)/);
    const test = match ? match[1] : 'unknown';
    
    // إضافة البيانات إلى الملخص
    if (!summary.tests[test]) {
      summary.tests[test] = {
        success: 0,
        failure: 0,
        successRate: 0,
        runs: 0,
      };
    }
    
    summary.tests[test].runs++;
    if (report.result) {
      summary.tests[test].success++;
      summary.overall.success++;
    } else {
      summary.tests[test].failure++;
      summary.overall.failure++;
    }
  }
  
  // حساب معدلات النجاح
  for (const test in summary.tests) {
    const data = summary.tests[test];
    summary.tests[test].successRate = (data.success / data.runs) * 100;
  }
  
  // حساب المعدل الإجمالي للنجاح
  const totalRuns = summary.overall.success + summary.overall.failure;
  summary.overall.successRate = (summary.overall.success / totalRuns) * 100;
  
  return summary;
}

// دالة لإنشاء تقرير HTML
function generateHtmlReport(apiSummary, uiSummary, uxSummary) {
  // إنشاء مجلد الإخراج إذا لم يكن موجودًا
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }
  
  // إنشاء محتوى HTML
  const html = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تقرير اختبارات الأداء - نظام النور للأرشفة</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    h1 {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 10px;
      border-bottom: 2px solid #3498db;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 12px 15px;
      text-align: right;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #3498db;
      color: white;
    }
    tr:nth-child(even) {
      background-color: #f2f2f2;
    }
    .summary {
      background-color: #e8f4f8;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .success {
      color: #27ae60;
    }
    .failure {
      color: #e74c3c;
    }
    .chart {
      width: 100%;
      height: 300px;
      margin-bottom: 30px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>تقرير اختبارات الأداء - نظام النور للأرشفة</h1>
    <p>تاريخ التقرير: ${new Date().toLocaleString('ar-SA')}</p>
    
    ${apiSummary ? `
    <h2>اختبارات أداء واجهة برمجة التطبيقات (API)</h2>
    <div class="summary">
      <h3>ملخص</h3>
      <p>متوسط الطلبات في الثانية: <strong>${apiSummary.overall.requestsPerSecond.toFixed(2)}</strong></p>
      <p>متوسط زمن الاستجابة: <strong>${apiSummary.overall.latency.average.toFixed(2)} مللي ثانية</strong></p>
      <p>أقل زمن استجابة: <strong>${apiSummary.overall.latency.min.toFixed(2)} مللي ثانية</strong></p>
      <p>أقصى زمن استجابة: <strong>${apiSummary.overall.latency.max.toFixed(2)} مللي ثانية</strong></p>
      <p>متوسط معدل نقل البيانات: <strong>${formatBytes(apiSummary.overall.throughput.average)}/ثانية</strong></p>
      <p>إجمالي الأخطاء: <strong>${apiSummary.overall.errors}</strong></p>
      <p>إجمالي الطلبات: <strong>${apiSummary.overall.totalRequests}</strong></p>
    </div>
    
    <h3>تفاصيل نقاط النهاية</h3>
    <table>
      <tr>
        <th>نقطة النهاية</th>
        <th>الطلبات/ثانية</th>
        <th>متوسط زمن الاستجابة (مللي ثانية)</th>
        <th>معدل نقل البيانات</th>
        <th>الأخطاء</th>
        <th>إجمالي الطلبات</th>
      </tr>
      ${Object.entries(apiSummary.endpoints).map(([endpoint, data]) => `
      <tr>
        <td>${endpoint}</td>
        <td>${data.requestsPerSecond.toFixed(2)}</td>
        <td>${data.latency.average.toFixed(2)}</td>
        <td>${formatBytes(data.throughput.average)}/ثانية</td>
        <td>${data.errors}</td>
        <td>${data.totalRequests}</td>
      </tr>
      `).join('')}
    </table>
    ` : '<h2>لا توجد بيانات لاختبارات أداء واجهة برمجة التطبيقات (API)</h2>'}
    
    ${uiSummary ? `
    <h2>اختبارات أداء واجهة المستخدم (UI)</h2>
    <div class="summary">
      <h3>ملخص</h3>
      <p>متوسط وقت التحميل الكامل: <strong>${uiSummary.overall.loadTime.toFixed(2)} مللي ثانية</strong></p>
      <p>متوسط وقت DOM Content Loaded: <strong>${uiSummary.overall.domContentLoaded.toFixed(2)} مللي ثانية</strong></p>
      <p>متوسط حجم الصفحة: <strong>${formatBytes(uiSummary.overall.transferSize)}</strong></p>
      <p>متوسط استخدام ذاكرة JavaScript: <strong>${formatBytes(uiSummary.overall.jsHeapSize)}</strong></p>
    </div>
    
    <h3>تفاصيل الصفحات</h3>
    <table>
      <tr>
        <th>الصفحة</th>
        <th>وقت التحميل (مللي ثانية)</th>
        <th>DOM Content Loaded (مللي ثانية)</th>
        <th>حجم الصفحة</th>
        <th>استخدام ذاكرة JavaScript</th>
      </tr>
      ${Object.entries(uiSummary.pages).map(([page, data]) => `
      <tr>
        <td>${page}</td>
        <td>${data.loadTime.toFixed(2)}</td>
        <td>${data.domContentLoaded.toFixed(2)}</td>
        <td>${formatBytes(data.transferSize)}</td>
        <td>${formatBytes(data.jsHeapSize)}</td>
      </tr>
      `).join('')}
    </table>
    ` : '<h2>لا توجد بيانات لاختبارات أداء واجهة المستخدم (UI)</h2>'}
    
    ${uxSummary ? `
    <h2>اختبارات تجربة المستخدم (UX)</h2>
    <div class="summary">
      <h3>ملخص</h3>
      <p>معدل النجاح الإجمالي: <strong class="${uxSummary.overall.successRate >= 90 ? 'success' : 'failure'}">${uxSummary.overall.successRate.toFixed(2)}%</strong></p>
      <p>عدد الاختبارات الناجحة: <strong class="success">${uxSummary.overall.success}</strong></p>
      <p>عدد الاختبارات الفاشلة: <strong class="failure">${uxSummary.overall.failure}</strong></p>
    </div>
    
    <h3>تفاصيل الاختبارات</h3>
    <table>
      <tr>
        <th>الاختبار</th>
        <th>عدد مرات التشغيل</th>
        <th>النجاح</th>
        <th>الفشل</th>
        <th>معدل النجاح</th>
      </tr>
      ${Object.entries(uxSummary.tests).map(([test, data]) => `
      <tr>
        <td>${test}</td>
        <td>${data.runs}</td>
        <td class="success">${data.success}</td>
        <td class="failure">${data.failure}</td>
        <td class="${data.successRate >= 90 ? 'success' : 'failure'}">${data.successRate.toFixed(2)}%</td>
      </tr>
      `).join('')}
    </table>
    ` : '<h2>لا توجد بيانات لاختبارات تجربة المستخدم (UX)</h2>'}
    
    <h2>التوصيات</h2>
    <ul>
      ${apiSummary && apiSummary.overall.latency.average > 200 ? '<li>تحسين أداء واجهة برمجة التطبيقات لتقليل زمن الاستجابة.</li>' : ''}
      ${apiSummary && apiSummary.overall.errors > 0 ? '<li>معالجة الأخطاء في واجهة برمجة التطبيقات.</li>' : ''}
      ${uiSummary && uiSummary.overall.loadTime > 3000 ? '<li>تحسين وقت تحميل صفحات واجهة المستخدم.</li>' : ''}
      ${uiSummary && uiSummary.overall.jsHeapSize > 50000000 ? '<li>تحسين استخدام الذاكرة في واجهة المستخدم.</li>' : ''}
      ${uxSummary && uxSummary.overall.successRate < 90 ? '<li>تحسين تجربة المستخدم لزيادة معدل نجاح الاختبارات.</li>' : ''}
    </ul>
  </div>
</body>
</html>
  `;
  
  // حفظ التقرير
  const outputFile = path.join(OUTPUT_DIR, `performance-report-${new Date().toISOString().replace(/:/g, '-')}.html`);
  fs.writeFileSync(outputFile, html);
  
  return outputFile;
}

// دالة لحساب المتوسط
function calculateAverage(arr) {
  if (arr.length === 0) return 0;
  return arr.reduce((sum, val) => sum + val, 0) / arr.length;
}

// دالة لحساب المجموع
function calculateSum(arr) {
  if (arr.length === 0) return 0;
  return arr.reduce((sum, val) => sum + val, 0);
}

// دالة لتنسيق حجم البيانات
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 بايت';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت', 'تيرابايت'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// الدالة الرئيسية
function main() {
  console.log('بدء إنشاء تقرير اختبارات الأداء...');
  
  // قراءة ملفات التقارير
  const reports = readReportFiles();
  
  if (reports.length === 0) {
    console.error('لم يتم العثور على أي تقارير!');
    return;
  }
  
  console.log(`تم العثور على ${reports.length} تقرير.`);
  
  // تحليل التقارير
  const apiSummary = analyzeApiReports(reports);
  const uiSummary = analyzeUiReports(reports);
  const uxSummary = analyzeUxReports(reports);
  
  // إنشاء تقرير HTML
  const outputFile = generateHtmlReport(apiSummary, uiSummary, uxSummary);
  
  console.log(`تم إنشاء التقرير بنجاح: ${outputFile}`);
}

// تنفيذ الدالة الرئيسية
main();
