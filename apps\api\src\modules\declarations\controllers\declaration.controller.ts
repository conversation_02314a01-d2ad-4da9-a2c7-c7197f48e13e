import { Request, Response, NextFunction } from 'express';
import { declarationService } from '../services/declaration.service.js';
import { successResponse, paginatedResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const declarationController = {
  /**
   * إنشاء بيان جديد
   */
  createDeclaration: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على بيانات البيان من الطلب
      const declarationData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // إنشاء البيان
      const declaration = await declarationService.createDeclaration(
        declarationData,
        req.user.id,
        file
      );

      return res.status(201).json(successResponse(declaration, 'تم إنشاء البيان بنجاح', 201));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث بيان
   */
  updateDeclaration: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف البيان من المعلمات
      const { id } = req.params;

      // الحصول على بيانات البيان من الطلب
      const declarationData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // تحديث البيان
      const declaration = await declarationService.updateDeclaration(
        id,
        declarationData,
        req.user.id,
        file
      );

      return res.status(200).json(successResponse(declaration, 'تم تحديث البيان بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على بيان محدد
   */
  getDeclaration: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معرف البيان من المعلمات
      const { id } = req.params;

      // الحصول على البيان
      const declaration = await declarationService.getDeclaration(id);

      return res.status(200).json(successResponse(declaration, 'تم الحصول على البيان بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف بيان
   */
  deleteDeclaration: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف البيان من المعلمات
      const { id } = req.params;

      // حذف البيان
      await declarationService.deleteDeclaration(id, req.user.id);

      return res.status(200).json(successResponse(null, 'تم حذف البيان بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة البيانات
   */
  listDeclarations: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معلمات البحث
      const { page, limit, sort, order, search, declarationType, fromDate, toDate, clientId } =
        req.query as any;

      // الحصول على قائمة البيانات
      const result = await declarationService.listDeclarations({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        search,
        declarationType,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined,
        clientId,
      });

      return res
        .status(200)
        .json(
          paginatedResponse(
            result.data,
            result.meta.page,
            result.meta.limit,
            result.meta.total,
            'تم الحصول على قائمة البيانات بنجاح'
          )
        );
    } catch (error) {
      next(error);
    }
  },
};
