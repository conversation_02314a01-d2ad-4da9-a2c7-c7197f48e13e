import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';
import { ItemMovement } from './useItemMovement';

export interface UpdateItemMovementRequest {
  itemName?: string;
  movementType?: string;
  quantity?: number;
  unit?: string;
  date?: string;
  movementDate?: string;
  status?: string;
  notes?: string;
  declarationId?: string;
  file?: File;
}

export const useUpdateItemMovement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data, file }: { id: string; data: UpdateItemMovementRequest; file?: File | null }): Promise<ItemMovement> => {
      const formData = new FormData();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      if (file) {
        formData.append('file', file);
      }

      const response = await api.put<ItemMovement>(`/api/item-movements/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['itemMovements'] });
      queryClient.invalidateQueries({ queryKey: ['itemMovement', id] });
    },
  });
};
