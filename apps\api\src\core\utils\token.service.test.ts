import { tokenService } from './token.service.js';
import { prisma } from './prisma.js';
import { verifyToken } from './jwt.js';
import { TokenType } from '@prisma/client';
import * as jwt from 'jsonwebtoken';

// Mock dependencies
jest.mock('./prisma', () => ({
  prisma: {
    invalidatedToken: {
      create: jest.fn(),
      findUnique: jest.fn(),
      deleteMany: jest.fn(),
    },
  },
}));

jest.mock('./jwt', () => ({
  verifyToken: jest.fn(),
}));

jest.mock('./logger', () => ({
  logger: {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
  },
}));

describe('Token Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('invalidateToken', () => {
    it('should invalidate an access token successfully', async () => {
      // Arrange
      const token = 'valid-access-token';
      const userId = 'user-123';
      const mockExpiry = new Date();
      mockExpiry.setDate(mockExpiry.getDate() + 1);

      (verifyToken as jest.Mock).mockReturnValue({
        exp: Math.floor(mockExpiry.getTime() / 1000),
      });

      (prisma.invalidatedToken.create as jest.Mock).mockResolvedValue({
        id: 'token-1',
        token,
        tokenType: TokenType.ACCESS,
        userId,
        expiresAt: mockExpiry,
        invalidatedAt: new Date(),
      });

      // Act
      const result = await tokenService.invalidateToken(token, 'access', userId);

      // Assert
      expect(verifyToken).toHaveBeenCalledWith(token, 'access');
      expect(prisma.invalidatedToken.create).toHaveBeenCalledWith({
        data: {
          token,
          tokenType: TokenType.ACCESS,
          userId,
          expiresAt: expect.any(Date),
        },
      });
      expect(result).toEqual({ success: true });
    });

    it('should invalidate a refresh token successfully', async () => {
      // Arrange
      const token = 'valid-refresh-token';
      const userId = 'user-123';
      const mockExpiry = new Date();
      mockExpiry.setDate(mockExpiry.getDate() + 7);

      (verifyToken as jest.Mock).mockReturnValue({
        exp: Math.floor(mockExpiry.getTime() / 1000),
      });

      (prisma.invalidatedToken.create as jest.Mock).mockResolvedValue({
        id: 'token-2',
        token,
        tokenType: TokenType.REFRESH,
        userId,
        expiresAt: mockExpiry,
        invalidatedAt: new Date(),
      });

      // Act
      const result = await tokenService.invalidateToken(token, 'refresh', userId);

      // Assert
      expect(verifyToken).toHaveBeenCalledWith(token, 'refresh');
      expect(prisma.invalidatedToken.create).toHaveBeenCalledWith({
        data: {
          token,
          tokenType: TokenType.REFRESH,
          userId,
          expiresAt: expect.any(Date),
        },
      });
      expect(result).toEqual({ success: true });
    });

    it('should handle expired tokens gracefully', async () => {
      // Arrange
      const token = 'expired-token';
      const userId = 'user-123';

      (verifyToken as jest.Mock).mockImplementation(() => {
        throw new jwt.TokenExpiredError('Token expired', new Date());
      });

      (prisma.invalidatedToken.create as jest.Mock).mockResolvedValue({
        id: 'token-3',
        token,
        tokenType: TokenType.ACCESS,
        userId,
        expiresAt: expect.any(Date),
        invalidatedAt: new Date(),
      });

      // Act
      const result = await tokenService.invalidateToken(token, 'access', userId);

      // Assert
      expect(verifyToken).toHaveBeenCalledWith(token, 'access');
      expect(prisma.invalidatedToken.create).toHaveBeenCalledWith({
        data: {
          token,
          tokenType: TokenType.ACCESS,
          userId,
          expiresAt: expect.any(Date),
        },
      });
      expect(result).toEqual({ success: true });
    });
  });

  describe('isTokenInvalidated', () => {
    it('should return true if token is invalidated', async () => {
      // Arrange
      const token = 'invalidated-token';

      (prisma.invalidatedToken.findUnique as jest.Mock).mockResolvedValue({
        id: 'token-4',
        token,
        tokenType: TokenType.ACCESS,
        userId: 'user-123',
        expiresAt: new Date(),
        invalidatedAt: new Date(),
      });

      // Act
      const result = await tokenService.isTokenInvalidated(token);

      // Assert
      expect(prisma.invalidatedToken.findUnique).toHaveBeenCalledWith({
        where: { token },
      });
      expect(result).toBe(true);
    });

    it('should return false if token is not invalidated', async () => {
      // Arrange
      const token = 'valid-token';

      (prisma.invalidatedToken.findUnique as jest.Mock).mockResolvedValue(null);

      // Act
      const result = await tokenService.isTokenInvalidated(token);

      // Assert
      expect(prisma.invalidatedToken.findUnique).toHaveBeenCalledWith({
        where: { token },
      });
      expect(result).toBe(false);
    });
  });

  describe('cleanupExpiredTokens', () => {
    it('should delete expired tokens', async () => {
      // Arrange
      (prisma.invalidatedToken.deleteMany as jest.Mock).mockResolvedValue({
        count: 5,
      });

      // Act
      const result = await tokenService.cleanupExpiredTokens();

      // Assert
      expect(prisma.invalidatedToken.deleteMany).toHaveBeenCalledWith({
        where: {
          expiresAt: {
            lt: expect.any(Date),
          },
        },
      });
      expect(result).toEqual({ count: 5 });
    });
  });
});
