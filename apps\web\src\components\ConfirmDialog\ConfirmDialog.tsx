import React from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface ConfirmDialogProps {
  /**
   * هل الحوار مفتوح
   */
  open: boolean;

  /**
   * دالة تنفذ عند إغلاق الحوار
   */
  onClose: () => void;

  /**
   * دالة تنفذ عند تأكيد الحوار
   */
  onConfirm: () => void;

  /**
   * عنوان الحوار
   */
  title: string;

  /**
   * محتوى الحوار
   */
  content?: React.ReactNode;

  /**
   * رسالة الحوار (بديل للمحتوى)
   */
  message?: string;

  /**
   * نص زر التأكيد
   * @default 'تأكيد'
   */
  confirmText?: string;

  /**
   * نص زر الإلغاء
   * @default 'إلغاء'
   */
  cancelText?: string;

  /**
   * لون زر التأكيد
   * @default 'primary'
   */
  confirmColor?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';

  /**
   * هل زر التأكيد في حالة تحميل
   * @default false
   */
  confirmLoading?: boolean;

  /**
   * هل زر التأكيد معطل
   * @default false
   */
  confirmDisabled?: boolean;

  /**
   * هل يتم عرض زر الإغلاق في العنوان
   * @default true
   */
  showCloseButton?: boolean;
}

/**
 * مكون حوار التأكيد
 * يستخدم لعرض حوار تأكيد للمستخدم قبل تنفيذ إجراء مهم
 */
export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  open,
  onClose,
  onConfirm,
  title,
  content,
  message,
  confirmText = 'تأكيد',
  cancelText = 'إلغاء',
  confirmColor = 'primary',
  confirmLoading = false,
  confirmDisabled = false,
  showCloseButton = true,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="confirm-dialog-title"
      aria-describedby="confirm-dialog-description"
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle id="confirm-dialog-title">
        {title}
        {showCloseButton && (
          <IconButton
            aria-label="close"
            onClick={onClose}
            sx={{
              position: 'absolute',
              left: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        )}
      </DialogTitle>
      <DialogContent>
        {content ? (
          typeof content === 'string' ? (
            <DialogContentText id="confirm-dialog-description">
              {content}
            </DialogContentText>
          ) : (
            content
          )
        ) : message ? (
          <DialogContentText id="confirm-dialog-description">
            {message}
          </DialogContentText>
        ) : null}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="inherit">
          {cancelText}
        </Button>
        <Button
          onClick={onConfirm}
          color={confirmColor}
          variant="contained"
          disabled={confirmDisabled}
          autoFocus
        >
          {confirmLoading ? 'جاري التنفيذ...' : confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
