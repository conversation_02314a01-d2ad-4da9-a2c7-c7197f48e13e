openapi: 3.0.0
info:
  title: نظام النور للأرشفة API
  description: واجهة برمجة التطبيقات لنظام النور للأرشفة
  version: 1.0.0
servers:
  - url: http://localhost:3001/api
    description: خادم التطوير المحلي
  - url: https://api.alnoor-archive.com/api
    description: خادم الإنتاج

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        username:
          type: string
        name:
          type: string
        email:
          type: string
          format: email
        role:
          type: string
          enum: [ADMIN, USER]
        isActive:
          type: boolean
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    
    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
        password:
          type: string
    
    LoginResponse:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        token:
          type: string
        refreshToken:
          type: string
    
    RefreshTokenRequest:
      type: object
      required:
        - refreshToken
      properties:
        refreshToken:
          type: string
    
    Declaration:
      type: object
      properties:
        id:
          type: string
          format: uuid
        declarationNumber:
          type: integer
        taxNumber:
          type: string
        clientName:
          type: string
        companyName:
          type: string
        policyNumber:
          type: integer
        invoiceNumber:
          type: integer
        gatewayEntryNumber:
          type: integer
        declarationType:
          type: string
          enum: [IMPORT, EXPORT]
        declarationDate:
          type: string
          format: date
        count:
          type: integer
        weight:
          type: string
        goodsType:
          type: string
        itemsCount:
          type: integer
        entryDate:
          type: string
          format: date
        exitDate:
          type: string
          format: date
        pdfFile:
          type: string
        clientId:
          type: string
          format: uuid
        drivers:
          type: array
          items:
            $ref: '#/components/schemas/Driver'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    
    Driver:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        truckNumber:
          type: string
        trailerNumber:
          type: string
        phoneNumber:
          type: string
        declarationId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    
    ItemMovement:
      type: object
      properties:
        id:
          type: string
          format: uuid
        movementNumber:
          type: integer
        movementDate:
          type: string
          format: date
        declarationNumber:
          type: integer
        itemNumber:
          type: integer
        invoiceNumber:
          type: integer
        packingListNumber:
          type: integer
        identificationClause:
          type: integer
        itemName:
          type: string
        count:
          type: integer
        packageType:
          type: string
          enum: [PALLET, CARTON, BARREL]
        goodsType:
          type: string
          enum: [HUMAN_MEDICINE, LABORATORY_SOLUTIONS, MEDICAL_SUPPLIES, SUGAR_STRIPS, MEDICAL_DEVICES, MISCELLANEOUS]
        countryOfOrigin:
          type: string
        itemValue:
          type: number
          format: float
        currency:
          type: string
          enum: [USD, EUR, GBP]
        totalValue:
          type: number
          format: float
        pdfFile:
          type: string
        declarationId:
          type: string
          format: uuid
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
    
    ErrorResponse:
      type: object
      properties:
        message:
          type: string
        statusCode:
          type: integer
        error:
          type: string

security:
  - bearerAuth: []

paths:
  /auth/login:
    post:
      tags:
        - Authentication
      summary: تسجيل الدخول
      description: تسجيل الدخول إلى النظام
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: تم تسجيل الدخول بنجاح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: اسم المستخدم أو كلمة المرور غير صحيحة
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  
  /auth/refresh-token:
    post:
      tags:
        - Authentication
      summary: تجديد الرمز المميز
      description: تجديد الرمز المميز باستخدام رمز التحديث
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: تم تجديد الرمز المميز بنجاح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: رمز التحديث غير صالح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  
  /declarations:
    get:
      tags:
        - Declarations
      summary: الحصول على قائمة البيانات
      description: الحصول على قائمة البيانات مع إمكانية التصفية والترتيب والتقسيم
      parameters:
        - name: page
          in: query
          description: رقم الصفحة
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: عدد العناصر في الصفحة
          schema:
            type: integer
            default: 10
        - name: sort
          in: query
          description: حقل الترتيب
          schema:
            type: string
            default: declarationNumber
        - name: order
          in: query
          description: اتجاه الترتيب
          schema:
            type: string
            enum: [asc, desc]
            default: desc
        - name: search
          in: query
          description: نص البحث
          schema:
            type: string
        - name: declarationType
          in: query
          description: نوع البيان
          schema:
            type: string
            enum: [IMPORT, EXPORT]
        - name: fromDate
          in: query
          description: من تاريخ
          schema:
            type: string
            format: date
        - name: toDate
          in: query
          description: إلى تاريخ
          schema:
            type: string
            format: date
      responses:
        '200':
          description: تم الحصول على قائمة البيانات بنجاح
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Declaration'
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                      limit:
                        type: integer
                      total:
                        type: integer
                      pages:
                        type: integer
        '401':
          description: غير مصرح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    
    post:
      tags:
        - Declarations
      summary: إنشاء بيان جديد
      description: إنشاء بيان جديد
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                data:
                  type: string
                  description: بيانات البيان بتنسيق JSON
                file:
                  type: string
                  format: binary
                  description: ملف PDF
      responses:
        '201':
          description: تم إنشاء البيان بنجاح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Declaration'
        '400':
          description: بيانات غير صالحة
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: غير مصرح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  
  /declarations/{id}:
    get:
      tags:
        - Declarations
      summary: الحصول على بيان محدد
      description: الحصول على بيان محدد بواسطة المعرف
      parameters:
        - name: id
          in: path
          required: true
          description: معرف البيان
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: تم الحصول على البيان بنجاح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Declaration'
        '404':
          description: البيان غير موجود
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: غير مصرح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    
    put:
      tags:
        - Declarations
      summary: تحديث بيان
      description: تحديث بيان محدد بواسطة المعرف
      parameters:
        - name: id
          in: path
          required: true
          description: معرف البيان
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                data:
                  type: string
                  description: بيانات البيان بتنسيق JSON
                file:
                  type: string
                  format: binary
                  description: ملف PDF
      responses:
        '200':
          description: تم تحديث البيان بنجاح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Declaration'
        '400':
          description: بيانات غير صالحة
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: البيان غير موجود
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: غير مصرح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    
    delete:
      tags:
        - Declarations
      summary: حذف بيان
      description: حذف بيان محدد بواسطة المعرف
      parameters:
        - name: id
          in: path
          required: true
          description: معرف البيان
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: تم حذف البيان بنجاح
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
        '404':
          description: البيان غير موجود
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: غير مصرح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  
  /item-movements:
    get:
      tags:
        - Item Movements
      summary: الحصول على قائمة حركات الأصناف
      description: الحصول على قائمة حركات الأصناف مع إمكانية التصفية والترتيب والتقسيم
      parameters:
        - name: page
          in: query
          description: رقم الصفحة
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: عدد العناصر في الصفحة
          schema:
            type: integer
            default: 10
        - name: sort
          in: query
          description: حقل الترتيب
          schema:
            type: string
            default: movementNumber
        - name: order
          in: query
          description: اتجاه الترتيب
          schema:
            type: string
            enum: [asc, desc]
            default: desc
        - name: search
          in: query
          description: نص البحث
          schema:
            type: string
        - name: declarationNumber
          in: query
          description: رقم البيان
          schema:
            type: integer
        - name: fromDate
          in: query
          description: من تاريخ
          schema:
            type: string
            format: date
        - name: toDate
          in: query
          description: إلى تاريخ
          schema:
            type: string
            format: date
      responses:
        '200':
          description: تم الحصول على قائمة حركات الأصناف بنجاح
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/ItemMovement'
                  pagination:
                    type: object
                    properties:
                      page:
                        type: integer
                      limit:
                        type: integer
                      total:
                        type: integer
                      pages:
                        type: integer
        '401':
          description: غير مصرح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    
    post:
      tags:
        - Item Movements
      summary: إنشاء حركة صنف جديدة
      description: إنشاء حركة صنف جديدة
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                data:
                  type: string
                  description: بيانات حركة الصنف بتنسيق JSON
                file:
                  type: string
                  format: binary
                  description: ملف PDF
      responses:
        '201':
          description: تم إنشاء حركة الصنف بنجاح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ItemMovement'
        '400':
          description: بيانات غير صالحة
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: غير مصرح
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
