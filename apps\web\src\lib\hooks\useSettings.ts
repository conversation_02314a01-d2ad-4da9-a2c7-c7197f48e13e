import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import settingsService, { Settings } from '../services/settings.service';
import { useToast } from './useToast';

/**
 * خطاف لاستخدام خدمة الإعدادات
 * يوفر واجهة سهلة للتعامل مع إعدادات النظام
 */
export const useSettings = () => {
  const queryClient = useQueryClient();
  const toast = useToast();

  // استعلام للحصول على قائمة الإعدادات
  const useGetSettings = (group?: string) => {
    return useQuery({
      queryKey: ['settings', group],
      queryFn: () => settingsService.getSettings(group),
    });
  };

  // استعلام للحصول على إعداد واحد
  const useGetSetting = (key: string) => {
    return useQuery({
      queryKey: ['settings', key],
      queryFn: () => settingsService.getSetting(key),
      enabled: !!key,
    });
  };

  // طلب لتحديث إعداد
  const useUpdateSetting = () => {
    return useMutation({
      mutationFn: ({ key, value }: { key: string; value: any }) => settingsService.updateSetting(key, value),
      onSuccess: (_, variables) => {
        // تحديث ذاكرة التخزين المؤقت
        queryClient.invalidateQueries({ queryKey: ['settings'] });
        queryClient.invalidateQueries({ queryKey: ['settings', variables.key] });
        
        // عرض رسالة نجاح
        toast.showSuccess('تم تحديث الإعداد بنجاح');
      },
      onError: (error) => {
        // عرض رسالة خطأ
        if (error instanceof Error) {
          toast.showError(error.message);
        } else {
          toast.showError('حدث خطأ أثناء تحديث الإعداد');
        }
      },
    });
  };

  // طلب لتحديث عدة إعدادات
  const useUpdateSettings = () => {
    return useMutation({
      mutationFn: (settings: Record<string, any>) => settingsService.updateSettings(settings),
      onSuccess: () => {
        // تحديث ذاكرة التخزين المؤقت
        queryClient.invalidateQueries({ queryKey: ['settings'] });
        
        // عرض رسالة نجاح
        toast.showSuccess('تم تحديث الإعدادات بنجاح');
      },
      onError: (error) => {
        // عرض رسالة خطأ
        if (error instanceof Error) {
          toast.showError(error.message);
        } else {
          toast.showError('حدث خطأ أثناء تحديث الإعدادات');
        }
      },
    });
  };

  // طلب لإنشاء إعداد جديد
  const useCreateSetting = () => {
    return useMutation({
      mutationFn: (setting: Omit<Settings, 'id' | 'createdAt' | 'updatedAt'>) => settingsService.createSetting(setting),
      onSuccess: () => {
        // تحديث ذاكرة التخزين المؤقت
        queryClient.invalidateQueries({ queryKey: ['settings'] });
        
        // عرض رسالة نجاح
        toast.showSuccess('تم إنشاء الإعداد بنجاح');
      },
      onError: (error) => {
        // عرض رسالة خطأ
        if (error instanceof Error) {
          toast.showError(error.message);
        } else {
          toast.showError('حدث خطأ أثناء إنشاء الإعداد');
        }
      },
    });
  };

  // طلب لحذف إعداد
  const useDeleteSetting = () => {
    return useMutation({
      mutationFn: (key: string) => settingsService.deleteSetting(key),
      onSuccess: (_, key) => {
        // تحديث ذاكرة التخزين المؤقت
        queryClient.invalidateQueries({ queryKey: ['settings'] });
        queryClient.invalidateQueries({ queryKey: ['settings', key] });
        
        // عرض رسالة نجاح
        toast.showSuccess('تم حذف الإعداد بنجاح');
      },
      onError: (error) => {
        // عرض رسالة خطأ
        if (error instanceof Error) {
          toast.showError(error.message);
        } else {
          toast.showError('حدث خطأ أثناء حذف الإعداد');
        }
      },
    });
  };

  return {
    useGetSettings,
    useGetSetting,
    useUpdateSetting,
    useUpdateSettings,
    useCreateSetting,
    useDeleteSetting,
  };
};
