import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  TextField,
  Tooltip,
  Typography,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PictureAsPdf as PdfIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { useGuarantees, useDeleteGuarantee, useDownloadGuaranteePdf } from '../hooks/useGuarantees';
import { GuaranteeType, GuaranteeNature, GuaranteeSearchParams } from '../types/guarantee.types';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const GuaranteesPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  // حالة البحث
  const [searchParams, setSearchParams] = useState<GuaranteeSearchParams>({
    page: 1,
    limit: 10,
    sort: 'createdAt',
    order: 'desc',
  });
  
  // استخدام خطافات الضمانات
  const { data, isLoading, isError, refetch } = useGuarantees(searchParams);
  const deleteMutation = useDeleteGuarantee();
  const downloadPdfMutation = useDownloadGuaranteePdf();
  
  // التعامل مع تغيير معلمات البحث
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchParams((prev) => ({ ...prev, search: e.target.value }));
  };
  
  const handleGuaranteeTypeChange = (e: any) => {
    setSearchParams((prev) => ({ ...prev, guaranteeType: e.target.value || undefined }));
  };
  
  const handleGuaranteeNatureChange = (e: any) => {
    setSearchParams((prev) => ({ ...prev, guaranteeNature: e.target.value || undefined }));
  };
  
  const handleIsReturnedChange = (e: any) => {
    setSearchParams((prev) => ({ ...prev, isReturned: e.target.value === '' ? undefined : e.target.value === 'true' }));
  };
  
  const handleSearch = () => {
    refetch();
  };
  
  // التعامل مع إنشاء ضمان جديد
  const handleCreateGuarantee = () => {
    navigate('/guarantees/new');
  };
  
  // التعامل مع عرض تفاصيل الضمان
  const handleViewGuarantee = (id: string) => {
    navigate(`/guarantees/${id}`);
  };
  
  // التعامل مع تعديل الضمان
  const handleEditGuarantee = (id: string) => {
    navigate(`/guarantees/${id}/edit`);
  };
  
  // التعامل مع حذف الضمان
  const handleDeleteGuarantee = async (id: string) => {
    if (window.confirm(t('guarantees.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id);
      } catch (error) {
        console.error('Error deleting guarantee:', error);
      }
    }
  };
  
  // التعامل مع تحميل ملف PDF
  const handleDownloadPdf = async (id: string) => {
    try {
      const blob = await downloadPdfMutation.mutateAsync(id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `guarantee-${id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading PDF:', error);
    }
  };
  
  // عرض رسالة التحميل
  if (isLoading) {
    return (
      <Container maxWidth="lg">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <CircularProgress />
        </Box>
      </Container>
    );
  }
  
  // عرض رسالة الخطأ
  if (isError) {
    return (
      <Container maxWidth="lg">
        <Box textAlign="center" py={4}>
          <Typography variant="h6" color="error">
            {t('common.errorOccurred')}
          </Typography>
          <Button variant="contained" onClick={() => refetch()} sx={{ mt: 2 }}>
            {t('common.retry')}
          </Button>
        </Box>
      </Container>
    );
  }
  
  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('guarantees.title')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('guarantees.description')}
        </Typography>
      </Box>
      
      {/* نموذج البحث */}
      <Box mb={4} component="form" noValidate autoComplete="off">
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              label={t('common.search')}
              variant="outlined"
              value={searchParams.search || ''}
              onChange={handleSearchChange}
              InputProps={{
                endAdornment: (
                  <IconButton size="small" onClick={handleSearch}>
                    <SearchIcon />
                  </IconButton>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>{t('guarantees.guaranteeType')}</InputLabel>
              <Select
                value={searchParams.guaranteeType || ''}
                onChange={handleGuaranteeTypeChange}
                label={t('guarantees.guaranteeType')}
              >
                <MenuItem value="">{t('common.all')}</MenuItem>
                <MenuItem value={GuaranteeType.RETURNABLE}>{t('guarantees.returnable')}</MenuItem>
                <MenuItem value={GuaranteeType.NON_RETURNABLE}>{t('guarantees.nonReturnable')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>{t('guarantees.guaranteeNature')}</InputLabel>
              <Select
                value={searchParams.guaranteeNature || ''}
                onChange={handleGuaranteeNatureChange}
                label={t('guarantees.guaranteeNature')}
              >
                <MenuItem value="">{t('common.all')}</MenuItem>
                <MenuItem value={GuaranteeNature.DOCUMENTS}>{t('guarantees.documents')}</MenuItem>
                <MenuItem value={GuaranteeNature.FINANCIAL}>{t('guarantees.financial')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>{t('guarantees.isReturned')}</InputLabel>
              <Select
                value={searchParams.isReturned === undefined ? '' : searchParams.isReturned.toString()}
                onChange={handleIsReturnedChange}
                label={t('guarantees.isReturned')}
              >
                <MenuItem value="">{t('common.all')}</MenuItem>
                <MenuItem value="true">{t('common.yes')}</MenuItem>
                <MenuItem value="false">{t('common.no')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>
      
      {/* زر إنشاء ضمان جديد */}
      <Box mb={3} display="flex" justifyContent="flex-end">
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleCreateGuarantee}
        >
          {t('guarantees.create')}
        </Button>
      </Box>
      
      {/* قائمة الضمانات */}
      <Box>
        {data?.data.length === 0 ? (
          <Box textAlign="center" py={4}>
            <Typography variant="h6">{t('guarantees.noGuarantees')}</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateGuarantee}
              sx={{ mt: 2 }}
            >
              {t('guarantees.create')}
            </Button>
          </Box>
        ) : (
          <Grid container spacing={2}>
            {data?.data.map((guarantee) => (
              <Grid item xs={12} sm={6} md={4} key={guarantee.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="h6">
                        {t('guarantees.number')}: {guarantee.guaranteeNumber}
                      </Typography>
                      <Chip
                        label={
                          guarantee.guaranteeType === GuaranteeType.RETURNABLE
                            ? t('guarantees.returnable')
                            : t('guarantees.nonReturnable')
                        }
                        color={guarantee.guaranteeType === GuaranteeType.RETURNABLE ? 'primary' : 'secondary'}
                        size="small"
                      />
                    </Box>
                    
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      {t('guarantees.nature')}: {t(`guarantees.${guarantee.guaranteeNature.toLowerCase()}`)}
                    </Typography>
                    
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      {t('guarantees.amount')}: {guarantee.amount} {guarantee.currency}
                    </Typography>
                    
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                      {t('guarantees.issueDate')}: {format(new Date(guarantee.issueDate), 'PPP', { locale: arSA })}
                    </Typography>
                    
                    {guarantee.expiryDate && (
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {t('guarantees.expiryDate')}: {format(new Date(guarantee.expiryDate), 'PPP', { locale: arSA })}
                      </Typography>
                    )}
                    
                    <Box display="flex" alignItems="center" mt={1}>
                      <Typography variant="body2" color="textSecondary" sx={{ mr: 1 }}>
                        {t('guarantees.returnStatus')}:
                      </Typography>
                      {guarantee.isReturned ? (
                        <Chip
                          icon={<CheckCircleIcon />}
                          label={t('guarantees.returned')}
                          color="success"
                          size="small"
                        />
                      ) : (
                        <Chip
                          icon={<CancelIcon />}
                          label={t('guarantees.notReturned')}
                          color="error"
                          size="small"
                        />
                      )}
                    </Box>
                    
                    <Box display="flex" justifyContent="flex-end" mt={2}>
                      {guarantee.pdfFile && (
                        <Tooltip title={t('common.downloadPdf')}>
                          <IconButton
                            size="small"
                            onClick={() => handleDownloadPdf(guarantee.id)}
                            sx={{ mr: 1 }}
                          >
                            <PdfIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      <Tooltip title={t('common.view')}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewGuarantee(guarantee.id)}
                          sx={{ mr: 1 }}
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('common.edit')}>
                        <IconButton
                          size="small"
                          onClick={() => handleEditGuarantee(guarantee.id)}
                          sx={{ mr: 1 }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={t('common.delete')}>
                        <IconButton
                          size="small"
                          onClick={() => handleDeleteGuarantee(guarantee.id)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>
    </Container>
  );
};

export default GuaranteesPage;
