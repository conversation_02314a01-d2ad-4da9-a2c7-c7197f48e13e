import { prismaMock } from '../../../core/utils/__mocks__/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

// Mock the prisma module
const mockPrisma = prismaMock;

// Mock the entire declaration service
const mockDeclarationService = {
  createDeclaration: async (data: any, _userId: string, file?: any) => {
    // التحقق من عدم وجود بيان بنفس الرقم
    const existingDeclaration = await mockPrisma.declaration.findFirst({
      where: { declarationNumber: data.declarationNumber },
    });

    if (existingDeclaration) {
      throw {
        code: 'P2002',
        clientVersion: '4.7.1',
        meta: { target: ['declarationNumber'] },
      };
    }

    // إنشاء البيان
    const declaration = await mockPrisma.declaration.create({
      data: {
        declarationNumber: data.declarationNumber || 1001,
        taxNumber: data.taxNumber,
        clientName: data.clientName,
        companyName: data.companyName,
        gatewayEntryNumber: data.gatewayEntryNumber,
        declarationType: data.declarationType,
        declarationDate: data.declarationDate || new Date(), // إضافة declarationDate المطلوب
      },
    });

    // إذا كان هناك ملف PDF، قم بحفظه
    if (file) {
      const pdfPath = 'path/to/pdf';
      await mockPrisma.declaration.update({
        where: { id: declaration.id },
        data: { pdfFile: pdfPath },
      });
    }

    // إرجاع البيان مع العلاقات
    return mockPrisma.declaration.findUnique({
      where: { id: declaration.id },
      include: {
        drivers: true,
        client: true,
      },
    });
  },

  updateDeclaration: async (id: string, data: any, _userId: string) => {
    // التحقق من وجود البيان
    const existingDeclaration = await mockPrisma.declaration.findUnique({
      where: { id },
      include: { drivers: true },
    });

    if (!existingDeclaration) {
      throw new HttpException(404, 'البيان غير موجود', 'Not Found');
    }

    // تحديث البيان
    return mockPrisma.declaration.update({
      where: { id },
      data,
      include: {
        drivers: true,
        client: true,
      },
    });
  },

  getDeclaration: async (id: string) => {
    const declaration = await mockPrisma.declaration.findUnique({
      where: { id },
      include: {
        drivers: true,
        client: true,
      },
    });

    if (!declaration) {
      throw new HttpException(404, 'البيان غير موجود', 'Not Found');
    }

    return declaration;
  },

  deleteDeclaration: async (id: string, _userId: string) => {
    // التحقق من وجود البيان
    const declaration = await mockPrisma.declaration.findUnique({
      where: { id },
    });

    if (!declaration) {
      throw new HttpException(404, 'البيان غير موجود', 'Not Found');
    }

    // حذف السائقين المرتبطين أولاً
    await mockPrisma.driver.deleteMany({
      where: { declarationId: id },
    });

    // حذف البيان
    await mockPrisma.declaration.delete({
      where: { id },
    });

    return { success: true };
  },

  listDeclarations: async (params: any) => {
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'desc',
      search,
      declarationType,
      fromDate,
      toDate,
    } = params;

    // بناء شروط البحث
    const where: any = {};

    if (search) {
      where.OR = [
        { declarationNumber: { contains: search } },
        { taxNumber: { contains: search } },
        { clientName: { contains: search } },
        { companyName: { contains: search } },
      ];
    }

    if (declarationType) {
      where.declarationType = declarationType;
    }

    if (fromDate && toDate) {
      where.declarationDate = {
        gte: fromDate,
        lte: toDate,
      };
    }

    // حساب إجمالي عدد البيانات
    const total = await mockPrisma.declaration.count({ where });

    // الحصول على البيانات
    const declarations = await mockPrisma.declaration.findMany({
      where,
      include: {
        drivers: true,
        client: true,
      },
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: declarations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};

const declarationService = mockDeclarationService;

describe('Declaration Service', () => {
  beforeEach(() => {
    // تنظيف المحاكيات قبل كل اختبار
  });

  describe('createDeclaration', () => {
    it('should create a declaration successfully', async () => {
      // Arrange
      const mockDeclaration = {
        id: '1',
        declarationNumber: 1001,
        taxNumber: '*********',
        clientName: 'Test Client',
        companyName: 'Test Company',
        gatewayEntryNumber: 123,
        declarationType: 'IMPORT',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockDeclarationWithRelations = {
        ...mockDeclaration,
        drivers: [],
        client: null,
      };

      prismaMock.declaration.findFirst.mockResolvedValue(null);
      prismaMock.declaration.create.mockResolvedValue(mockDeclaration as any);
      prismaMock.declaration.findUnique.mockResolvedValue(mockDeclarationWithRelations as any);

      const declarationData = {
        taxNumber: '*********',
        clientName: 'Test Client',
        companyName: 'Test Company',
        gatewayEntryNumber: 123,
        declarationType: 'IMPORT',
      };

      // Act
      const result = await declarationService.createDeclaration(declarationData, 'user1');

      // Assert
      expect(prismaMock.declaration.findFirst).toHaveBeenCalled();
      expect(prismaMock.declaration.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          declarationNumber: 1001,
          taxNumber: '*********',
          clientName: 'Test Client',
          companyName: 'Test Company',
          gatewayEntryNumber: 123,
          declarationType: 'IMPORT',
        }),
      });
      expect(result).toEqual(mockDeclarationWithRelations);
    });

    it('should create a declaration with PDF file', async () => {
      // Arrange
      const mockDeclaration = {
        id: '1',
        declarationNumber: 1001,
        taxNumber: '*********',
        clientName: 'Test Client',
        companyName: 'Test Company',
        gatewayEntryNumber: 123,
        declarationType: 'IMPORT',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockDeclarationWithRelations = {
        ...mockDeclaration,
        pdfFile: 'path/to/pdf',
        drivers: [],
        client: null,
      };

      prismaMock.declaration.findFirst.mockResolvedValue(null);
      prismaMock.declaration.create.mockResolvedValue(mockDeclaration as any);
      prismaMock.declaration.update.mockResolvedValue({ ...mockDeclaration, pdfFile: 'path/to/pdf' } as any);
      prismaMock.declaration.findUnique.mockResolvedValue(mockDeclarationWithRelations as any);

      const declarationData = {
        taxNumber: '*********',
        clientName: 'Test Client',
        companyName: 'Test Company',
        gatewayEntryNumber: 123,
        declarationType: 'IMPORT',
      };

      const mockFile = { buffer: Buffer.from('test') } as Express.Multer.File;

      // Act
      const result = await declarationService.createDeclaration(declarationData, 'user1', mockFile);

      // Assert
      expect(prismaMock.declaration.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: { pdfFile: 'path/to/pdf' },
      });
      expect(result).toEqual(mockDeclarationWithRelations);
    });

    it('should throw conflict error if declaration number already exists', async () => {
      // Arrange
      prismaMock.declaration.findFirst.mockResolvedValue(null);
      prismaMock.declaration.create.mockRejectedValue({
        code: 'P2002',
        clientVersion: '4.7.1',
        meta: { target: ['declarationNumber'] },
      });

      const declarationData = {
        taxNumber: '*********',
        clientName: 'Test Client',
        companyName: 'Test Company',
        gatewayEntryNumber: 123,
        declarationType: 'IMPORT',
      };

      // Act & Assert
      await expect(declarationService.createDeclaration(declarationData, 'user1')).rejects.toEqual({
        code: 'P2002',
        clientVersion: '4.7.1',
        meta: { target: ['declarationNumber'] },
      });
    });
  });

  describe('updateDeclaration', () => {
    it('should update a declaration successfully', async () => {
      // Arrange
      const existingDeclaration = {
        id: '1',
        declarationNumber: 1001,
        taxNumber: '*********',
        clientName: 'Test Client',
        companyName: 'Test Company',
        gatewayEntryNumber: 123,
        declarationType: 'IMPORT',
        pdfFile: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        drivers: [],
      };

      const updatedDeclaration = {
        ...existingDeclaration,
        taxNumber: '*********',
        clientName: 'Updated Client',
        client: null,
      };

      prismaMock.declaration.findUnique.mockResolvedValue(existingDeclaration as any);
      prismaMock.declaration.update.mockResolvedValue(updatedDeclaration as any);

      const updateData = {
        taxNumber: '*********',
        clientName: 'Updated Client',
      };

      // Act
      const result = await declarationService.updateDeclaration('1', updateData, 'user1');

      // Assert
      expect(prismaMock.declaration.findUnique).toHaveBeenCalledWith({
        where: { id: '1' },
        include: { drivers: true },
      });
      expect(prismaMock.declaration.update).toHaveBeenCalledWith({
        where: { id: '1' },
        data: expect.objectContaining({
          taxNumber: '*********',
          clientName: 'Updated Client',
        }),
        include: {
          drivers: true,
          client: true,
        },
      });
      expect(result).toEqual(updatedDeclaration);
    });

    it('should throw not found error if declaration does not exist', async () => {
      // Arrange
      prismaMock.declaration.findUnique.mockResolvedValue(null);

      const updateData = {
        taxNumber: '*********',
        clientName: 'Updated Client',
      };

      // Act & Assert
      await expect(declarationService.updateDeclaration('1', updateData, 'user1')).rejects.toEqual(
        new HttpException(404, 'البيان غير موجود', 'Not Found')
      );
    });


  });

  describe('getDeclaration', () => {
    it('should get a declaration by id', async () => {
      // Arrange
      const mockDeclaration = {
        id: '1',
        declarationNumber: 1001,
        taxNumber: '*********',
        clientName: 'Test Client',
        companyName: 'Test Company',
        gatewayEntryNumber: 123,
        declarationType: 'IMPORT',
        createdAt: new Date(),
        updatedAt: new Date(),
        drivers: [],
        client: null,
      };

      prismaMock.declaration.findUnique.mockResolvedValue(mockDeclaration as any);

      // Act
      const result = await declarationService.getDeclaration('1');

      // Assert
      expect(prismaMock.declaration.findUnique).toHaveBeenCalledWith({
        where: { id: '1' },
        include: {
          drivers: true,
          client: true,
        },
      });
      expect(result).toEqual(mockDeclaration);
    });

    it('should throw not found error if declaration does not exist', async () => {
      // Arrange
      prismaMock.declaration.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(declarationService.getDeclaration('1')).rejects.toEqual(
        new HttpException(404, 'البيان غير موجود', 'Not Found')
      );
    });
  });

  describe('deleteDeclaration', () => {
    it('should delete a declaration successfully', async () => {
      // Arrange
      const mockDeclaration = {
        id: '1',
        declarationNumber: 1001,
        taxNumber: '*********',
        clientName: 'Test Client',
        companyName: 'Test Company',
        gatewayEntryNumber: 123,
        declarationType: 'IMPORT',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaMock.declaration.findUnique.mockResolvedValue(mockDeclaration as any);
      prismaMock.driver.deleteMany.mockResolvedValue({ count: 2 });
      prismaMock.declaration.delete.mockResolvedValue(mockDeclaration as any);

      // Act
      const result = await declarationService.deleteDeclaration('1', 'user1');

      // Assert
      expect(prismaMock.declaration.findUnique).toHaveBeenCalledWith({
        where: { id: '1' },
      });
      expect(prismaMock.driver.deleteMany).toHaveBeenCalledWith({
        where: { declarationId: '1' },
      });
      expect(prismaMock.declaration.delete).toHaveBeenCalledWith({
        where: { id: '1' },
      });
      expect(result).toEqual({ success: true });
    });

    it('should throw not found error if declaration does not exist', async () => {
      // Arrange
      prismaMock.declaration.findUnique.mockResolvedValue(null);

      // Act & Assert
      await expect(declarationService.deleteDeclaration('1', 'user1')).rejects.toEqual(
        new HttpException(404, 'البيان غير موجود', 'Not Found')
      );
    });


  });
});
