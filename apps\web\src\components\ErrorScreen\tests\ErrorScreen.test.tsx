import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ErrorScreen } from '../ErrorScreen';

describe('ErrorScreen Component', () => {
  test('renders with default message and title', () => {
    render(<ErrorScreen />);
    expect(screen.getByText('حدث خطأ')).toBeInTheDocument();
    expect(screen.getByText('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.')).toBeInTheDocument();
  });

  test('renders with custom message and title', () => {
    render(<ErrorScreen title="خطأ في التحميل" message="فشل في تحميل البيانات" />);
    expect(screen.getByText('خطأ في التحميل')).toBeInTheDocument();
    expect(screen.getByText('فشل في تحميل البيانات')).toBeInTheDocument();
  });

  test('renders error code when provided', () => {
    render(<ErrorScreen errorCode="404" />);
    expect(screen.getByText('404')).toBeInTheDocument();
  });

  test('calls onRetry when retry button is clicked', () => {
    const handleRetry = jest.fn();
    render(<ErrorScreen onRetry={handleRetry} />);
    
    const retryButton = screen.getByText('إعادة المحاولة');
    fireEvent.click(retryButton);
    
    expect(handleRetry).toHaveBeenCalledTimes(1);
  });

  test('does not show retry button when showRetryButton is false', () => {
    render(<ErrorScreen onRetry={() => {}} showRetryButton={false} />);
    expect(screen.queryByText('إعادة المحاولة')).not.toBeInTheDocument();
  });

  test('renders without background when withBackground is false', () => {
    const { container } = render(<ErrorScreen withBackground={false} />);
    // في حالة عدم وجود خلفية، لن يكون هناك عنصر Paper
    expect(container.querySelector('.MuiPaper-root')).not.toBeInTheDocument();
  });
});
