import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';
import { ItemMovement } from './useItemMovement';

export interface CreateItemMovementRequest {
  itemName: string;
  movementType: string;
  quantity: number;
  unit: string;
  date: string;
  movementDate: string;
  status: string;
  notes?: string;
  declarationId?: string;
  data?: any;
}

export const useCreateItemMovement = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ data, file }: { data: CreateItemMovementRequest; file?: File | null }): Promise<ItemMovement> => {
      const formData = new FormData();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      if (file) {
        formData.append('file', file);
      }

      const response = await api.post<ItemMovement>('/api/item-movements', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['itemMovements'] });
    },
  });
};
