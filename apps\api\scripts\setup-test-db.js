#!/usr/bin/env node

/**
 * سكريبت إعداد قاعدة البيانات للاختبارات
 * Setup Test Database Script
 */

import { execSync } from 'child_process';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// تحميل متغيرات البيئة للاختبار
dotenv.config({ path: path.join(__dirname, '..', '.env.test') });

console.log('🔧 إعداد قاعدة البيانات للاختبارات...');
console.log('Setting up test database...');

try {
  // إنشاء قاعدة البيانات إذا لم تكن موجودة
  console.log('📋 إنشاء قاعدة البيانات...');
  try {
    execSync('createdb alnoorarch_test', { stdio: 'inherit' });
    console.log('✅ تم إنشاء قاعدة البيانات بنجاح');
  } catch (error) {
    console.log('ℹ️ قاعدة البيانات موجودة مسبقاً أو تم إنشاؤها');
  }

  // تطبيق المخططات
  console.log('🔄 تطبيق مخططات قاعدة البيانات...');
  execSync('npx prisma db push --force-reset', { 
    stdio: 'inherit',
    env: { ...process.env, DATABASE_URL: process.env.DATABASE_URL }
  });

  // إنشاء البيانات الأولية للاختبار
  console.log('📊 إنشاء البيانات الأولية...');
  execSync('npx prisma db seed', { 
    stdio: 'inherit',
    env: { ...process.env, DATABASE_URL: process.env.DATABASE_URL }
  });

  console.log('✅ تم إعداد قاعدة البيانات للاختبارات بنجاح!');
  
} catch (error) {
  console.error('❌ خطأ في إعداد قاعدة البيانات:', error.message);
  process.exit(1);
}
