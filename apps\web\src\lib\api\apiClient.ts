/**
 * عميل API
 * يستخدم لإجراء طلبات HTTP إلى الخادم
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { cachedFetch, removeCachedData, clearCache } from './apiCache';

// تكوين Axios
const axiosConfig: AxiosRequestConfig = {
  baseURL: '/api',
  timeout: 30000, // 30 ثانية
  headers: {
    'Content-Type': 'application/json',
  },
};

// إنشاء مثيل Axios
const axiosInstance: AxiosInstance = axios.create(axiosConfig);

// إضافة معترض الطلبات
axiosInstance.interceptors.request.use(
  (config) => {
    // إضافة رمز المصادقة إلى الطلب إذا كان موجودًا
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// إضافة معترض الاستجابات
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // التعامل مع أخطاء المصادقة
    if (error.response && error.response.status === 401) {
      // حذف رمز المصادقة
      localStorage.removeItem('token');
      // إعادة توجيه المستخدم إلى صفحة تسجيل الدخول
      window.location.href = '/login';
      // حذف التخزين المؤقت
      clearCache();
    }
    return Promise.reject(error);
  }
);

/**
 * واجهة خيارات الطلب
 */
interface RequestOptions extends AxiosRequestConfig {
  // هل يتم استخدام التخزين المؤقت
  useCache?: boolean;
  // مدة صلاحية التخزين المؤقت بالمللي ثانية
  cacheTTL?: number;
  // هل يتم تحديث التخزين المؤقت في الخلفية
  backgroundRefresh?: boolean;
}

/**
 * عميل API
 */
const apiClient = {
  /**
   * إجراء طلب GET
   * @param url عنوان URL
   * @param options خيارات الطلب
   * @returns وعد بالاستجابة
   */
  get: async <T>(url: string, options: RequestOptions = {}): Promise<T> => {
    const { useCache = false, cacheTTL, backgroundRefresh, ...axiosOptions } = options;
    
    // إذا كان التخزين المؤقت مفعلًا، استخدمه
    if (useCache) {
      return cachedFetch<T>(
        `GET:${url}`,
        () => axiosInstance.get<T>(url, axiosOptions).then((res) => res.data),
        { ttl: cacheTTL, backgroundRefresh }
      );
    }
    
    // إذا لم يكن التخزين المؤقت مفعلًا، قم بتنفيذ الطلب مباشرة
    const response: AxiosResponse<T> = await axiosInstance.get(url, axiosOptions);
    return response.data;
  },
  
  /**
   * إجراء طلب POST
   * @param url عنوان URL
   * @param data البيانات المرسلة
   * @param options خيارات الطلب
   * @returns وعد بالاستجابة
   */
  post: async <T>(url: string, data?: any, options: RequestOptions = {}): Promise<T> => {
    // حذف التخزين المؤقت المرتبط بهذا العنوان
    removeCachedData(`GET:${url}`);
    
    const response: AxiosResponse<T> = await axiosInstance.post(url, data, options);
    return response.data;
  },
  
  /**
   * إجراء طلب PUT
   * @param url عنوان URL
   * @param data البيانات المرسلة
   * @param options خيارات الطلب
   * @returns وعد بالاستجابة
   */
  put: async <T>(url: string, data?: any, options: RequestOptions = {}): Promise<T> => {
    // حذف التخزين المؤقت المرتبط بهذا العنوان
    removeCachedData(`GET:${url}`);
    
    const response: AxiosResponse<T> = await axiosInstance.put(url, data, options);
    return response.data;
  },
  
  /**
   * إجراء طلب PATCH
   * @param url عنوان URL
   * @param data البيانات المرسلة
   * @param options خيارات الطلب
   * @returns وعد بالاستجابة
   */
  patch: async <T>(url: string, data?: any, options: RequestOptions = {}): Promise<T> => {
    // حذف التخزين المؤقت المرتبط بهذا العنوان
    removeCachedData(`GET:${url}`);
    
    const response: AxiosResponse<T> = await axiosInstance.patch(url, data, options);
    return response.data;
  },
  
  /**
   * إجراء طلب DELETE
   * @param url عنوان URL
   * @param options خيارات الطلب
   * @returns وعد بالاستجابة
   */
  delete: async <T>(url: string, options: RequestOptions = {}): Promise<T> => {
    // حذف التخزين المؤقت المرتبط بهذا العنوان
    removeCachedData(`GET:${url}`);
    
    const response: AxiosResponse<T> = await axiosInstance.delete(url, options);
    return response.data;
  },
  
  /**
   * رفع ملف
   * @param url عنوان URL
   * @param file الملف المراد رفعه
   * @param options خيارات الطلب
   * @returns وعد بالاستجابة
   */
  uploadFile: async <T>(url: string, file: File, options: RequestOptions = {}): Promise<T> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const config: AxiosRequestConfig = {
      ...options,
      headers: {
        ...options.headers,
        'Content-Type': 'multipart/form-data',
      },
    };
    
    const response: AxiosResponse<T> = await axiosInstance.post(url, formData, config);
    return response.data;
  },
};

export default apiClient;
