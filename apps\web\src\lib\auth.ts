/**
 * الحصول على رمز المصادقة من التخزين المحلي
 */
export const getToken = (): string | null => {
  return localStorage.getItem('accessToken');
};

/**
 * حفظ رمز المصادقة في التخزين المحلي
 */
export const setToken = (token: string): void => {
  localStorage.setItem('accessToken', token);
};

/**
 * الحصول على رمز التحديث من التخزين المحلي
 */
export const getRefreshToken = (): string | null => {
  return localStorage.getItem('refreshToken');
};

/**
 * حفظ رمز التحديث في التخزين المحلي
 */
export const setRefreshToken = (token: string): void => {
  localStorage.setItem('refreshToken', token);
};

/**
 * حفظ رموز المصادقة في التخزين المحلي
 */
export const setTokens = (accessToken: string, refreshToken: string): void => {
  setToken(accessToken);
  setRefreshToken(refreshToken);
};

/**
 * مسح رموز المصادقة من التخزين المحلي
 */
export const clearTokens = (): void => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
};

/**
 * التحقق من وجود رمز المصادقة
 */
export const isAuthenticated = (): boolean => {
  return !!getToken();
};
