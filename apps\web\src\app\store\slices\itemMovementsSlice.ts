import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ItemMovement } from '@features/items-movement/types/item-movement.types';

export interface ItemMovementsState {
  selectedItemMovement: ItemMovement | null;
  filters: {
    movementNumber?: string;
    declarationNumber?: string;
    itemName?: string;
    startDate?: string;
    endDate?: string;
  };
  pagination: {
    page: number;
    limit: number;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: ItemMovementsState = {
  selectedItemMovement: null,
  filters: {},
  pagination: {
    page: 0,
    limit: 10,
  },
  isLoading: false,
  error: null,
};

export const itemMovementsSlice = createSlice({
  name: 'itemMovements',
  initialState,
  reducers: {
    setSelectedItemMovement: (state, action: PayloadAction<ItemMovement | null>) => {
      state.selectedItemMovement = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<ItemMovementsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = {};
    },
    setPagination: (state, action: PayloadAction<Partial<ItemMovementsState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setSelectedItemMovement,
  setFilters,
  resetFilters,
  setPagination,
  setLoading,
  setError,
} = itemMovementsSlice.actions;

export default itemMovementsSlice.reducer;
