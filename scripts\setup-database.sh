#!/bin/bash

# سكريبت إعداد قاعدة البيانات
# يقوم بإنشاء قواعد البيانات المطلوبة للتطوير والاختبار

echo "🗄️  إعداد قواعد البيانات..."

# قراءة كلمة المرور من ملف البيئة
source .env
DB_PASSWORD="$POSTGRES_PASSWORD"

# التحقق من وجود PostgreSQL
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL غير مثبت. يرجى تثبيته أولاً."
    exit 1
fi

# التحقق من الاتصال بقاعدة البيانات
echo "🔍 التحقق من الاتصال بقاعدة البيانات..."
if ! PGPASSWORD="$DB_PASSWORD" psql -h localhost -U postgres -c '\q' 2>/dev/null; then
    echo "❌ فشل الاتصال بقاعدة البيانات. تحقق من:"
    echo "   - تشغيل خدمة PostgreSQL"
    echo "   - صحة كلمة المرور في ملف .env"
    echo "   - صلاحيات المستخدم postgres"
    exit 1
fi

# إنشاء قاعدة بيانات التطوير
echo "📊 إنشاء قاعدة بيانات التطوير (alnoor_db)..."
PGPASSWORD="$DB_PASSWORD" createdb -h localhost -U postgres alnoor_db 2>/dev/null || {
    echo "⚠️  قاعدة بيانات التطوير موجودة مسبقاً"
}

# إنشاء قاعدة بيانات الاختبار
echo "🧪 إنشاء قاعدة بيانات الاختبار (alnoor_test)..."
PGPASSWORD="$DB_PASSWORD" createdb -h localhost -U postgres alnoor_test 2>/dev/null || {
    echo "⚠️  قاعدة بيانات الاختبار موجودة مسبقاً"
}

# تشغيل migrations على قاعدة بيانات التطوير
echo "🔄 تشغيل migrations على قاعدة بيانات التطوير..."
cd apps/api
DATABASE_URL="postgresql://postgres:$DB_PASSWORD@localhost:5432/alnoor_db" npx prisma migrate deploy

# تشغيل migrations على قاعدة بيانات الاختبار
echo "🔄 تشغيل migrations على قاعدة بيانات الاختبار..."
DATABASE_URL="postgresql://postgres:$DB_PASSWORD@localhost:5432/alnoor_test" npx prisma migrate deploy

# إنشاء البيانات الأولية
echo "🌱 إنشاء البيانات الأولية..."
DATABASE_URL="postgresql://postgres:$DB_PASSWORD@localhost:5432/alnoor_db" npx prisma db seed

echo "✅ تم إعداد قواعد البيانات بنجاح!"
echo ""
echo "📋 ملخص:"
echo "   - قاعدة بيانات التطوير: alnoor_db"
echo "   - قاعدة بيانات الاختبار: alnoor_test"
echo "   - تم تشغيل جميع migrations"
echo "   - تم إنشاء البيانات الأولية"
echo ""
echo "🚀 يمكنك الآن تشغيل المشروع باستخدام:"
echo "   npm run dev"
