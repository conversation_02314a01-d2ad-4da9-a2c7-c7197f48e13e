import { Request, Response, NextFunction } from 'express';
import { authMiddleware, adminMiddleware } from './auth.middleware.js';
import { prisma } from '../utils/prisma.js';
import { verifyToken } from '../utils/jwt.js';
import { tokenService } from '../utils/token.service.js';
import { HttpException } from './error.middleware.js';
import * as jwt from 'jsonwebtoken';

// Mock dependencies
jest.mock('../utils/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
  },
}));

jest.mock('../utils/jwt', () => ({
  verifyToken: jest.fn(),
}));

jest.mock('../utils/token.service', () => ({
  tokenService: {
    isTokenInvalidated: jest.fn(),
  },
}));

describe('Auth Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: jest.Mock;

  beforeEach(() => {
    mockRequest = {
      headers: {},
      user: undefined,
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    nextFunction = jest.fn();
    jest.clearAllMocks();
  });

  describe('authMiddleware', () => {
    it('should add user to request when token is valid', async () => {
      // Arrange
      const token = 'valid-token';
      const userId = 'user-123';
      const username = 'testuser';
      const role = 'USER';

      mockRequest.headers = {
        authorization: `Bearer ${token}`,
      };

      (tokenService.isTokenInvalidated as jest.Mock).mockResolvedValue(false);
      (verifyToken as jest.Mock).mockReturnValue({
        id: userId,
        username,
        role,
      });
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: userId,
        username,
        role,
        isActive: true,
      });

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(tokenService.isTokenInvalidated).toHaveBeenCalledWith(token);
      expect(verifyToken).toHaveBeenCalledWith(token, 'access');
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        select: { id: true, username: true, role: true, isActive: true },
      });
      expect(mockRequest.user).toEqual({
        id: userId,
        username,
        role,
      });
      expect(nextFunction).toHaveBeenCalled();
      expect(nextFunction).not.toHaveBeenCalledWith(expect.any(HttpException));
    });

    it('should call next with error when no authorization header', async () => {
      // Arrange
      mockRequest.headers = {};

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(nextFunction).toHaveBeenCalledWith(expect.any(HttpException));
      expect(tokenService.isTokenInvalidated).not.toHaveBeenCalled();
      expect(verifyToken).not.toHaveBeenCalled();
      expect(prisma.user.findUnique).not.toHaveBeenCalled();
    });

    it('should call next with error when token is invalidated', async () => {
      // Arrange
      const token = 'invalidated-token';
      mockRequest.headers = {
        authorization: `Bearer ${token}`,
      };

      (tokenService.isTokenInvalidated as jest.Mock).mockResolvedValue(true);

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(tokenService.isTokenInvalidated).toHaveBeenCalledWith(token);
      expect(nextFunction).toHaveBeenCalledWith(expect.any(HttpException));
      expect(verifyToken).not.toHaveBeenCalled();
      expect(prisma.user.findUnique).not.toHaveBeenCalled();
    });

    it('should call next with error when token verification fails', async () => {
      // Arrange
      const token = 'invalid-token';
      mockRequest.headers = {
        authorization: `Bearer ${token}`,
      };

      (tokenService.isTokenInvalidated as jest.Mock).mockResolvedValue(false);
      (verifyToken as jest.Mock).mockImplementation(() => {
        throw new jwt.JsonWebTokenError('Invalid token');
      });

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(tokenService.isTokenInvalidated).toHaveBeenCalledWith(token);
      expect(verifyToken).toHaveBeenCalledWith(token, 'access');
      expect(nextFunction).toHaveBeenCalledWith(expect.any(HttpException));
      expect(prisma.user.findUnique).not.toHaveBeenCalled();
    });

    it('should call next with error when user is not found', async () => {
      // Arrange
      const token = 'valid-token';
      const userId = 'nonexistent-user';
      mockRequest.headers = {
        authorization: `Bearer ${token}`,
      };

      (tokenService.isTokenInvalidated as jest.Mock).mockResolvedValue(false);
      (verifyToken as jest.Mock).mockReturnValue({
        id: userId,
        username: 'testuser',
        role: 'USER',
      });
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(tokenService.isTokenInvalidated).toHaveBeenCalledWith(token);
      expect(verifyToken).toHaveBeenCalledWith(token, 'access');
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        select: { id: true, username: true, role: true, isActive: true },
      });
      expect(nextFunction).toHaveBeenCalledWith(expect.any(HttpException));
    });

    it('should call next with error when user is not active', async () => {
      // Arrange
      const token = 'valid-token';
      const userId = 'inactive-user';
      mockRequest.headers = {
        authorization: `Bearer ${token}`,
      };

      (tokenService.isTokenInvalidated as jest.Mock).mockResolvedValue(false);
      (verifyToken as jest.Mock).mockReturnValue({
        id: userId,
        username: 'testuser',
        role: 'USER',
      });
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: userId,
        username: 'testuser',
        role: 'USER',
        isActive: false,
      });

      // Act
      await authMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(tokenService.isTokenInvalidated).toHaveBeenCalledWith(token);
      expect(verifyToken).toHaveBeenCalledWith(token, 'access');
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: userId },
        select: { id: true, username: true, role: true, isActive: true },
      });
      expect(nextFunction).toHaveBeenCalledWith(expect.any(HttpException));
    });
  });

  describe('adminMiddleware', () => {
    it('should call next when user is admin', () => {
      // Arrange
      mockRequest.user = {
        id: 'admin-123',
        username: 'admin',
        role: 'ADMIN',
      };

      // Act
      adminMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(nextFunction).toHaveBeenCalled();
      expect(nextFunction).not.toHaveBeenCalledWith(expect.any(HttpException));
    });

    it('should call next with error when user is not admin', () => {
      // Arrange
      mockRequest.user = {
        id: 'user-123',
        username: 'user',
        role: 'USER',
      };

      // Act
      adminMiddleware(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );

      // Assert
      expect(nextFunction).toHaveBeenCalledWith(expect.any(HttpException));
    });
  });
});
