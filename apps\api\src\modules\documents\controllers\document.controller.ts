import { Request, Response, NextFunction } from 'express';
import { documentService } from '../services/document.service.js';
import { successResponse, paginatedResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const documentController = {
  /**
   * إنشاء مستند جديد
   */
  createDocument: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على بيانات المستند من الطلب
      const documentData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // إنشاء المستند
      const document = await documentService.createDocument(
        documentData,
        req.user.id,
        file
      );

      return res.status(201).json(successResponse(document, 'تم إنشاء المستند بنجاح', 201));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث مستند
   */
  updateDocument: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف المستند من المعلمات
      const { id } = req.params;

      // الحصول على بيانات المستند من الطلب
      const documentData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // تحديث المستند
      const document = await documentService.updateDocument(
        id,
        documentData,
        req.user.id,
        file
      );

      return res.status(200).json(successResponse(document, 'تم تحديث المستند بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على مستند محدد
   */
  getDocument: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معرف المستند من المعلمات
      const { id } = req.params;

      // الحصول على المستند
      const document = await documentService.getDocument(id);

      return res.status(200).json(successResponse(document));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف مستند
   */
  deleteDocument: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف المستند من المعلمات
      const { id } = req.params;

      // حذف المستند
      await documentService.deleteDocument(id, req.user.id);

      return res.status(200).json(successResponse(null, 'تم حذف المستند بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة المستندات
   */
  listDocuments: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معلمات البحث
      const { page, limit, sort, order, search, documentType, fromDate, toDate, isExpired } =
        req.query as any;

      // الحصول على قائمة المستندات
      const result = await documentService.listDocuments({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        search,
        documentType,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined,
        isExpired,
      });

      return res.status(200).json(paginatedResponse(
        result.data,
        result.pagination.page,
        result.pagination.limit,
        result.pagination.total,
        'تم الحصول على قائمة المستندات بنجاح'
      ));
    } catch (error) {
      next(error);
    }
  },
};
