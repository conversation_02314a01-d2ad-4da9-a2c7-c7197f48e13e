// Prisma Client مخصص للاختبارات - يستخدم SQLite مع schema.test.prisma
import { PrismaClient as TestPrismaClient } from './__mocks__/prisma-client-test/index.js';
import { logger } from './logger.js';

// إنشاء instance من Prisma Client للاختبارات
export const testPrismaClient = new TestPrismaClient({
  log: ['error']
});

// Log errors
// Event listeners are disabled for SQLite compatibility
// testPrismaClient.$on('error', function(e: { message: string }) {
//   logger.error(`Test Prisma Error: ${e.message}`);
// });

// Handle Prisma connection for tests
export const connectTestPrismaClient = async () => {
  try {
    await testPrismaClient.$connect();
    console.log('✅ متصل بقاعدة بيانات الاختبار (SQLite)');
  } catch (error) {
    console.error('❌ فشل الاتصال بقاعدة بيانات الاختبار:', error);
    throw error;
  }
};

// Handle Prisma disconnection for tests
export const disconnectTestPrismaClient = async () => {
  try {
    await testPrismaClient.$disconnect();
    console.log('🔌 تم قطع الاتصال بقاعدة بيانات الاختبار');
  } catch (error) {
    console.error('❌ فشل قطع الاتصال بقاعدة بيانات الاختبار:', error);
  }
};

export default testPrismaClient;
