import { prismaMock } from '../../../core/utils/__mocks__/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

// Mock the permit service
const mockPermitService = {
  createPermit: async (data: any, userId: string) => {
    // Mock implementation
    const permitNumber = '1001';
    const permit = {
      id: 'permit-1',
      declarationId: data.declarationId || 'declaration-1',
      permitNumber,
      permitType: data.permitType || 'IMPORT', // إضافة permitType
      permitDate: data.permitDate || new Date(),
      expiryDate: data.expiryDate,
      status: 'ACTIVE', // إضافة status
      notes: data.notes || null,
      pdfFile: data.pdfFile || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    prismaMock.permit.create.mockResolvedValue(permit);
    return permit;
  },

  updatePermit: async (id: string, data: any, userId: string) => {
    const existingPermit = {
      id,
      declarationId: 'declaration-1',
      permitNumber: '1001',
      permitType: 'IMPORT',
      permitDate: new Date(),
      expiryDate: new Date(),
      status: 'ACTIVE',
      notes: null,
      pdfFile: null,
      createdAt: new Date(),
      userId: 'user-123', // Fixed user ID
    };

    if (existingPermit.userId !== userId) {
      throw new HttpException(403, 'غير مصرح لك بتحديث هذا التصريح', 'Forbidden');
    }

    const updatedPermit = {
      ...existingPermit,
      ...data,
      updatedAt: new Date(),
    };

    prismaMock.permit.update.mockResolvedValue(updatedPermit);
    return updatedPermit;
  },

  getPermit: async (id: string) => {
    const permit = {
      id,
      declarationId: 'declaration-1',
      permitNumber: '1001',
      permitType: 'IMPORT', // إضافة permitType المطلوب
      permitDate: new Date(),
      expiryDate: new Date(),
      status: 'ACTIVE', // إضافة status المطلوب
      notes: null,
      pdfFile: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    prismaMock.permit.findUnique.mockResolvedValue(permit);
    return permit;
  },

  deletePermit: async (id: string, userId: string) => {
    const permit = {
      id,
      declarationId: 'declaration-1',
      permitNumber: '1001',
      permitType: 'IMPORT', // إضافة permitType
      permitDate: new Date(),
      expiryDate: new Date(),
      status: 'ACTIVE', // إضافة status
      notes: null,
      pdfFile: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Mock authorization check
    if (userId !== 'user-123') {
      throw new HttpException(403, 'غير مصرح لك بحذف هذا التصريح', 'Forbidden');
    }

    prismaMock.permit.delete.mockResolvedValue(permit);
    return { success: true };
  },

  listPermits: async (params: any = {}) => {
    const permits = [
      {
        id: 'permit-1',
        declarationId: 'declaration-1',
        permitNumber: '1001',
        permitType: 'IMPORT', // إضافة permitType
        permitDate: new Date(),
        expiryDate: new Date(),
        status: 'ACTIVE', // إضافة status
        notes: null,
        pdfFile: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: 'permit-2',
        declarationId: 'declaration-2',
        permitNumber: '1002',
        permitType: 'EXPORT', // إضافة permitType
        permitDate: new Date(),
        expiryDate: new Date(),
        status: 'ACTIVE', // إضافة status
        notes: null,
        pdfFile: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    prismaMock.permit.findMany.mockResolvedValue(permits);
    prismaMock.permit.count.mockResolvedValue(permits.length);

    return {
      data: permits,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 10,
        total: permits.length,
        pages: Math.ceil(permits.length / (params.limit || 10)),
      },
    };
  },
};

describe('Permit Service', () => {
  describe('createPermit', () => {
    it('should create a new permit successfully', async () => {
      // Arrange
      const permitData = {
        permitType: 'IMPORT',
        issuingAuthority: 'الجمارك السعودية',
        issueDate: new Date(),
        expiryDate: new Date(),
      };
      const userId = 'user-123';

      // Act
      const result = await mockPermitService.createPermit(permitData, userId);

      // Assert
      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('permitNumber');
      expect(result).toHaveProperty('permitDate');
      expect(result).toHaveProperty('declarationId');
    });
  });

  describe('updatePermit', () => {
    it('should update permit successfully', async () => {
      // Arrange
      const permitId = 'permit-1';
      const updateData = {
        permitType: 'EXPORT',
        issuingAuthority: 'الجمارك السعودية المحدثة',
      };
      const userId = 'user-123';

      // Act
      const result = await mockPermitService.updatePermit(permitId, updateData, userId);

      // Assert
      expect(result.permitType).toBe(updateData.permitType);
      expect(result.issuingAuthority).toBe(updateData.issuingAuthority);
      expect(result).toHaveProperty('updatedAt');
    });

    it('should throw error when user is not authorized', async () => {
      // Arrange
      const permitId = 'permit-1';
      const updateData = { permitType: 'EXPORT' };
      const userId = 'different-user';

      // Act & Assert
      await expect(
        mockPermitService.updatePermit(permitId, updateData, userId)
      ).rejects.toThrow(HttpException);
    });
  });

  describe('getPermit', () => {
    it('should get permit by id successfully', async () => {
      // Arrange
      const permitId = 'permit-1';

      // Act
      const result = await mockPermitService.getPermit(permitId);

      // Assert
      expect(result).toHaveProperty('id', permitId);
      expect(result).toHaveProperty('permitNumber');
      expect(result).toHaveProperty('permitType');
      expect(result).toHaveProperty('status', 'ACTIVE');
    });
  });

  describe('deletePermit', () => {
    it('should delete permit successfully', async () => {
      // Arrange
      const permitId = 'permit-1';
      const userId = 'user-123';

      // Act
      const result = await mockPermitService.deletePermit(permitId, userId);

      // Assert
      expect(result).toEqual({ success: true });
    });

    it('should throw error when user is not authorized to delete', async () => {
      // Arrange
      const permitId = 'permit-1';
      const userId = 'different-user';

      // Act & Assert
      await expect(
        mockPermitService.deletePermit(permitId, userId)
      ).rejects.toThrow(HttpException);
    });
  });

  describe('listPermits', () => {
    it('should list permits with pagination', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };

      // Act
      const result = await mockPermitService.listPermits(params);

      // Assert
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('pagination');
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination.page).toBe(params.page);
      expect(result.pagination.limit).toBe(params.limit);
      expect(result.data.length).toBeGreaterThan(0);
    });

    it('should list permits with default parameters', async () => {
      // Act
      const result = await mockPermitService.listPermits();

      // Assert
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('pagination');
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
    });
  });
});
