import request from 'supertest';
import { app } from '../../../app.js';
import { prisma } from '../../../core/utils/prisma.js';
import { createTestUser, getAuthToken } from '../../../core/utils/test/auth.js';

/**
 * اختبارات وحدة البحث المتقدم
 */
describe('Advanced Search Module', () => {
  let authToken: string;
  let userId: string;

  // قبل جميع الاختبارات
  beforeAll(async () => {
    // إنشاء مستخدم اختبار والحصول على رمز المصادقة
    const user = await createTestUser();
    userId = user.id;
    authToken = await getAuthToken(user);
  });

  // بعد جميع الاختبارات
  afterAll(async () => {
    // حذف مستخدم الاختبار
    await prisma.user.delete({
      where: {
        id: userId,
      },
    });
  });

  /**
   * اختبار البحث المتقدم
   */
  describe('GET /api/advanced-search', () => {
    it('يجب أن يرفض الطلب إذا لم يكن المستخدم مصرحًا', async () => {
      const response = await request(app)
        .get('/api/advanced-search')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    it('يجب أن يعيد نتائج البحث بنجاح', async () => {
      const response = await request(app)
        .get('/api/advanced-search')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          searchType: 'ALL',
          page: '1',
          limit: '10',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.pagination).toBeDefined();
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(10);
    });

    it('يجب أن يعيد نتائج البحث في البيانات فقط', async () => {
      const response = await request(app)
        .get('/api/advanced-search')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          searchType: 'DECLARATIONS',
          page: '1',
          limit: '10',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.declarations).toBeDefined();
    });

    it('يجب أن يعيد نتائج البحث في حركة الأصناف فقط', async () => {
      const response = await request(app)
        .get('/api/advanced-search')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          searchType: 'ITEM_MOVEMENTS',
          page: '1',
          limit: '10',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.itemMovements).toBeDefined();
    });

    it('يجب أن يعيد نتائج البحث بالكلمة المفتاحية', async () => {
      const response = await request(app)
        .get('/api/advanced-search')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          searchType: 'ALL',
          keyword: 'test',
          page: '1',
          limit: '10',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
    });

    it('يجب أن يعيد نتائج البحث بالتاريخ', async () => {
      const fromDate = new Date();
      fromDate.setMonth(fromDate.getMonth() - 1);

      const toDate = new Date();

      const response = await request(app)
        .get('/api/advanced-search')
        .set('Authorization', `Bearer ${authToken}`)
        .query({
          searchType: 'ALL',
          fromDate: fromDate.toISOString(),
          toDate: toDate.toISOString(),
          page: '1',
          limit: '10',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
    });
  });
});
