import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import util from 'util';
import { prisma } from '../../../core/utils/prisma.js';
import { config } from '../../../core/config/app.config.js';
import { logger } from '../../../core/utils/logger.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

// تحويل exec إلى وعد
const execPromise = util.promisify(exec);

// واجهة معلومات النسخة الاحتياطية
interface BackupInfo {
  fileName: string;
  size: number;
  createdAt: Date;
}

/**
 * خدمة قاعدة البيانات
 * توفر وظائف للتعامل مع قاعدة البيانات مثل النسخ الاحتياطي والاستعادة والتصدير والتهيئة
 */
export const databaseService = {
  /**
   * الحصول على مسار مجلد النسخ الاحتياطية
   */
  getBackupsDir: (): string => {
    const backupsDir = path.join(process.cwd(), 'backups');

    // إنشاء المجلد إذا لم يكن موجودًا
    if (!fs.existsSync(backupsDir)) {
      fs.mkdirSync(backupsDir, { recursive: true });
    }

    return backupsDir;
  },

  /**
   * الحصول على قائمة النسخ الاحتياطية
   */
  getBackups: async (): Promise<BackupInfo[]> => {
    const backupsDir = databaseService.getBackupsDir();

    try {
      // قراءة محتويات المجلد
      const files = fs.readdirSync(backupsDir);

      // تصفية الملفات للحصول على ملفات SQL فقط
      const sqlFiles = files.filter(file => file.endsWith('.sql'));

      // الحصول على معلومات الملفات
      const backups: BackupInfo[] = sqlFiles.map(fileName => {
        const filePath = path.join(backupsDir, fileName);
        const stats = fs.statSync(filePath);

        return {
          fileName,
          size: stats.size,
          createdAt: stats.birthtime,
        };
      });

      // ترتيب النسخ الاحتياطية حسب تاريخ الإنشاء (الأحدث أولاً)
      return backups.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    } catch (error) {
      logger.error('Error getting backups:', error);
      throw new HttpException(500, 'حدث خطأ أثناء الحصول على قائمة النسخ الاحتياطية', 'Internal Server Error');
    }
  },

  /**
   * إنشاء نسخة احتياطية جديدة
   */
  createBackup: async (userId: string): Promise<BackupInfo> => {
    const backupsDir = databaseService.getBackupsDir();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `backup_${timestamp}.sql`;
    const filePath = path.join(backupsDir, fileName);

    try {
      // استخراج معلومات الاتصال بقاعدة البيانات من DATABASE_URL
      const dbUrl = process.env.DATABASE_URL;
      if (!dbUrl) {
        throw new HttpException(500, 'لم يتم تكوين DATABASE_URL', 'Internal Server Error');
      }

      // تحليل DATABASE_URL
      const dbUrlRegex = /^postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)$/;
      const matches = dbUrl.match(dbUrlRegex);

      if (!matches) {
        throw new HttpException(500, 'تنسيق DATABASE_URL غير صالح', 'Internal Server Error');
      }

      const [, user, password, host, port, dbName] = matches;

      // إنشاء أمر pg_dump
      const command = `PGPASSWORD=${password} pg_dump -h ${host} -p ${port} -U ${user} -d ${dbName} -f ${filePath}`;

      // تنفيذ الأمر
      await execPromise(command);

      // التحقق من وجود الملف
      if (!fs.existsSync(filePath)) {
        throw new HttpException(500, 'فشل إنشاء النسخة الاحتياطية', 'Internal Server Error');
      }

      // الحصول على معلومات الملف
      const stats = fs.statSync(filePath);

      // تسجيل عملية النسخ الاحتياطي
      await prisma.auditLog.create({
        data: {
          action: 'CREATE_BACKUP',
          userId,
          details: {
            tableName: 'database',
            operation: 'BACKUP',
            recordId: fileName,
            fileName,
            size: stats.size,
          },
        },
      });

      return {
        fileName,
        size: stats.size,
        createdAt: stats.birthtime,
      };
    } catch (error) {
      logger.error('Error creating backup:', error);
      throw new HttpException(500, 'حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'Internal Server Error');
    }
  },

  /**
   * الحصول على مسار ملف النسخة الاحتياطية
   */
  getBackupPath: (fileName: string): string => {
    const backupsDir = databaseService.getBackupsDir();
    const filePath = path.join(backupsDir, fileName);

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      throw new HttpException(404, 'النسخة الاحتياطية غير موجودة', 'Not Found');
    }

    return filePath;
  },

  /**
   * استعادة قاعدة البيانات من نسخة احتياطية
   */
  restoreBackup: async (fileName: string, userId: string): Promise<void> => {
    const filePath = databaseService.getBackupPath(fileName);

    try {
      // استخراج معلومات الاتصال بقاعدة البيانات من DATABASE_URL
      const dbUrl = process.env.DATABASE_URL;
      if (!dbUrl) {
        throw new HttpException(500, 'لم يتم تكوين DATABASE_URL', 'Internal Server Error');
      }

      // تحليل DATABASE_URL
      const dbUrlRegex = /^postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)$/;
      const matches = dbUrl.match(dbUrlRegex);

      if (!matches) {
        throw new HttpException(500, 'تنسيق DATABASE_URL غير صالح', 'Internal Server Error');
      }

      const [, user, password, host, port, dbName] = matches;

      // إنشاء أمر psql
      const command = `PGPASSWORD=${password} psql -h ${host} -p ${port} -U ${user} -d ${dbName} -f ${filePath}`;

      // تنفيذ الأمر
      await execPromise(command);

      // تسجيل عملية الاستعادة
      await prisma.auditLog.create({
        data: {
          action: 'RESTORE_BACKUP',
          userId,
          details: {
            tableName: 'database',
            operation: 'RESTORE',
            recordId: fileName,
            fileName,
          },
        },
      });
    } catch (error) {
      logger.error('Error restoring backup:', error);
      throw new HttpException(500, 'حدث خطأ أثناء استعادة قاعدة البيانات', 'Internal Server Error');
    }
  },

  /**
   * حذف نسخة احتياطية
   */
  deleteBackup: async (fileName: string): Promise<void> => {
    const filePath = databaseService.getBackupPath(fileName);

    try {
      // حذف الملف
      fs.unlinkSync(filePath);
    } catch (error) {
      logger.error('Error deleting backup:', error);
      throw new HttpException(500, 'حدث خطأ أثناء حذف النسخة الاحتياطية', 'Internal Server Error');
    }
  },

  /**
   * تصدير قاعدة البيانات
   */
  exportDatabase: async (userId: string): Promise<string> => {
    const exportsDir = path.join(process.cwd(), 'exports');

    // إنشاء المجلد إذا لم يكن موجودًا
    if (!fs.existsSync(exportsDir)) {
      fs.mkdirSync(exportsDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `export_${timestamp}.sql`;
    const filePath = path.join(exportsDir, fileName);

    try {
      // استخراج معلومات الاتصال بقاعدة البيانات من DATABASE_URL
      const dbUrl = process.env.DATABASE_URL;
      if (!dbUrl) {
        throw new HttpException(500, 'لم يتم تكوين DATABASE_URL', 'Internal Server Error');
      }

      // تحليل DATABASE_URL
      const dbUrlRegex = /^postgresql:\/\/([^:]+):([^@]+)@([^:]+):(\d+)\/(.+)$/;
      const matches = dbUrl.match(dbUrlRegex);

      if (!matches) {
        throw new HttpException(500, 'تنسيق DATABASE_URL غير صالح', 'Internal Server Error');
      }

      const [, user, password, host, port, dbName] = matches;

      // إنشاء أمر pg_dump
      const command = `PGPASSWORD=${password} pg_dump -h ${host} -p ${port} -U ${user} -d ${dbName} -f ${filePath}`;

      // تنفيذ الأمر
      await execPromise(command);

      // التحقق من وجود الملف
      if (!fs.existsSync(filePath)) {
        throw new HttpException(500, 'فشل تصدير قاعدة البيانات', 'Internal Server Error');
      }

      // تسجيل عملية التصدير
      await prisma.auditLog.create({
        data: {
          action: 'EXPORT_DATABASE',
          userId,
          details: {
            tableName: 'database',
            operation: 'EXPORT',
            recordId: fileName,
            fileName,
          },
        },
      });

      return filePath;
    } catch (error) {
      logger.error('Error exporting database:', error);
      throw new HttpException(500, 'حدث خطأ أثناء تصدير قاعدة البيانات', 'Internal Server Error');
    }
  },

  /**
   * تهيئة قاعدة البيانات
   */
  initializeDatabase: async (userId: string): Promise<void> => {
    try {
      // إنشاء نسخة احتياطية قبل التهيئة
      await databaseService.createBackup(userId);

      // تنفيذ أمر prisma migrate reset
      const command = 'npx prisma migrate reset --force';

      // تنفيذ الأمر
      await execPromise(command);

      // تسجيل عملية التهيئة
      await prisma.auditLog.create({
        data: {
          action: 'INITIALIZE_DATABASE',
          userId,
          details: {
            tableName: 'database',
            operation: 'INITIALIZE',
            recordId: 'database_init',
          },
        },
      });
    } catch (error) {
      logger.error('Error initializing database:', error);
      throw new HttpException(500, 'حدث خطأ أثناء تهيئة قاعدة البيانات', 'Internal Server Error');
    }
  },
};
