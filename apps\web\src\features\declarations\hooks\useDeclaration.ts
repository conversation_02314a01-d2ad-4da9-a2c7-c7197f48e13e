import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api/api';

export interface Declaration {
  id: string;
  declarationNumber?: string;
  taxNumber: string;
  clientName?: string;
  companyName?: string;
  policyNumber?: number;
  invoiceNumber?: number;
  gatewayEntryNumber?: number;
  declarationType: string;
  declarationDate: string;
  status?: string;
  client?: {
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

export const useDeclaration = (id: string) => {
  return useQuery({
    queryKey: ['declaration', id],
    queryFn: async (): Promise<Declaration> => {
      const response = await api.get<Declaration>(`/api/declarations/${id}`);
      return response;
    },
    enabled: !!id,
  });
};
