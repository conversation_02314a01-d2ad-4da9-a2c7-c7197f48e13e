import { useMutation } from '@tanstack/react-query';
import authService, { LoginResponse } from '@lib/services/auth.service';
import { User } from '@lib/services/user.service';

interface LoginRequest {
  username: string;
  password: string;
}

const login = async (data: LoginRequest): Promise<LoginResponse> => {
  try {
    // استخدام خدمة المصادقة للتسجيل الدخول
    return await authService.login(data.username, data.password);
  } catch (error) {
    // معالجة الأخطاء
    if (error instanceof Error) {
      throw new Error(error.message || 'اسم المستخدم أو كلمة المرور غير صحيحة');
    }
    throw new Error('حدث خطأ أثناء تسجيل الدخول');
  }
};

export const useLogin = () => {
  return useMutation({
    mutationFn: login,
  });
};
