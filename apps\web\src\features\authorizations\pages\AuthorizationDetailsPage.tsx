import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  Typography,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Chip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Print as PrintIcon,
  Download as DownloadIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useAuthorization } from '../hooks/useAuthorization';
import { useDeleteAuthorization } from '../hooks/useDeleteAuthorization';
import { useDownloadAuthorizationPdf } from '../hooks/useDownloadAuthorizationPdf';
import { formatDate, formatDateTime } from '@lib/utils/date-utils';
import { PageHeader } from '@components/PageHeader';
import { LoadingScreen } from '@components/LoadingScreen';
import { ErrorScreen } from '@components/ErrorScreen';
import { AuthorizationStatus } from '@/types/global.d';

/**
 * صفحة تفاصيل التفويض
 */
const AuthorizationDetailsPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // استخدام خطافات البيانات
  const { data: authorization, isLoading, error } = useAuthorization(id || '');
  const deleteAuthorizationMutation = useDeleteAuthorization();
  const downloadPdfMutation = useDownloadAuthorizationPdf();

  // التعامل مع حذف التفويض
  const handleDelete = async () => {
    if (!id) return;

    try {
      await deleteAuthorizationMutation.mutateAsync(id);
      navigate('/authorizations');
    } catch (error) {
      console.error('Error deleting authorization:', error);
    }
  };

  // التعامل مع تحميل ملف PDF
  const handleDownloadPdf = async () => {
    if (!id) return;

    try {
      await downloadPdfMutation.mutateAsync(id);
    } catch (error) {
      console.error('Error downloading PDF:', error);
    }
  };

  // عرض شاشة التحميل
  if (isLoading) {
    return <LoadingScreen />;
  }

  // عرض شاشة الخطأ
  if (error || !authorization) {
    return (
      <ErrorScreen
        message={t('authorizations.details.errorLoading')}
        onRetry={() => window.location.reload()}
      />
    );
  }

  // تحديد لون حالة التفويض
  const getStatusColor = (status: AuthorizationStatus) => {
    switch (status) {
      case AuthorizationStatus.ACTIVE:
        return 'success';
      case AuthorizationStatus.EXPIRED:
        return 'error';
      case AuthorizationStatus.PENDING:
        return 'warning';
      case AuthorizationStatus.CANCELLED:
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <PageHeader
        title={t('authorizations.details.title')}
        subtitle={`${t('authorizations.authorizationNumber')}: ${authorization.authorizationNumber}`}
        backButton={
          <Button
            component={Link}
            to="/authorizations"
            startIcon={<ArrowBackIcon />}
            variant="outlined"
          >
            {t('common.back')}
          </Button>
        }
        actions={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              startIcon={<EditIcon />}
              variant="outlined"
              component={Link}
              to={`/authorizations/edit/${id}`}
            >
              {t('common.edit')}
            </Button>
            <Button
              startIcon={<PrintIcon />}
              variant="outlined"
              onClick={() => window.print()}
            >
              {t('common.print')}
            </Button>
            <Button
              startIcon={<DownloadIcon />}
              variant="outlined"
              onClick={handleDownloadPdf}
              disabled={downloadPdfMutation.isPending}
            >
              {downloadPdfMutation.isPending ? (
                <CircularProgress size={24} />
              ) : (
                t('common.downloadPdf')
              )}
            </Button>
            <Button
              startIcon={<DeleteIcon />}
              variant="outlined"
              color="error"
              onClick={() => setDeleteDialogOpen(true)}
            >
              {t('common.delete')}
            </Button>
          </Box>
        }
      />

      <Paper sx={{ mb: 3 }}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                {t('authorizations.details.basicInfo')}
              </Typography>
              <Chip
                label={t(`authorizations.statuses.${authorization.status}`)}
                color={getStatusColor(authorization.status as AuthorizationStatus)}
              />
            </Box>
            <Divider sx={{ mb: 2 }} />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('authorizations.authorizationNumber')}
                </Typography>
                <Typography variant="body1">{authorization.authorizationNumber}</Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('authorizations.declarationNumber')}
                </Typography>
                <Typography variant="body1">
                  <Link to={`/declarations/${authorization.declarationId}`}>
                    {authorization.declaration?.declarationNumber || t('common.notSpecified')}
                  </Link>
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('authorizations.authorizationDate')}
                </Typography>
                <Typography variant="body1">
                  {formatDate(authorization.authorizationDate)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('authorizations.startDate')}
                </Typography>
                <Typography variant="body1">
                  {formatDate(authorization.startDate)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('authorizations.endDate')}
                </Typography>
                <Typography variant="body1">
                  {formatDate(authorization.endDate)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('authorizations.authorizedPerson')}
                </Typography>
                <Typography variant="body1">
                  {authorization.authorizedPerson}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('authorizations.authorizedPersonId')}
                </Typography>
                <Typography variant="body1">
                  {authorization.authorizedPersonId}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('authorizations.createdAt')}
                </Typography>
                <Typography variant="body1">
                  {formatDateTime(authorization.createdAt)}
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('authorizations.updatedAt')}
                </Typography>
                <Typography variant="body1">
                  {formatDateTime(authorization.updatedAt)}
                </Typography>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  {t('authorizations.notes')}
                </Typography>
                <Typography variant="body1">
                  {authorization.notes || t('common.notSpecified')}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Paper>

      {authorization.pdfFile && (
        <Paper sx={{ mb: 3 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                {t('authorizations.details.attachedDocument')}
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  startIcon={<DownloadIcon />}
                  onClick={handleDownloadPdf}
                  disabled={downloadPdfMutation.isPending}
                >
                  {downloadPdfMutation.isPending ? (
                    <CircularProgress size={24} />
                  ) : (
                    t('common.downloadPdf')
                  )}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Paper>
      )}

      {/* حوار تأكيد الحذف */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>{t('authorizations.delete.confirmTitle')}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('authorizations.delete.confirmMessage')}
          </DialogContentText>
          <Alert severity="warning" sx={{ mt: 2 }}>
            {t('authorizations.delete.warning')}
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleDelete}
            color="error"
            disabled={deleteAuthorizationMutation.isPending}
          >
            {deleteAuthorizationMutation.isPending ? (
              <CircularProgress size={24} />
            ) : (
              t('common.delete')
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AuthorizationDetailsPage;
