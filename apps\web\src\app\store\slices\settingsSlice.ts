import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { SystemSettings } from '@features/settings/types/settings.types';

export interface SettingsState {
  settings: SystemSettings | null;
  isLoading: boolean;
  error: string | null;
  isDarkMode: boolean;
  language: string;
}

const initialState: SettingsState = {
  settings: null,
  isLoading: false,
  error: null,
  isDarkMode: localStorage.getItem('theme') === 'dark',
  language: localStorage.getItem('language') || 'ar',
};

export const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setSettings: (state, action: PayloadAction<SystemSettings>) => {
      state.settings = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    toggleDarkMode: (state) => {
      state.isDarkMode = !state.isDarkMode;
      localStorage.setItem('theme', state.isDarkMode ? 'dark' : 'light');
    },
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
      localStorage.setItem('language', action.payload);
    },
  },
});

export const {
  setSettings,
  setLoading,
  setError,
  toggleDarkMode,
  setLanguage,
} = settingsSlice.actions;

export default settingsSlice.reducer;
