import apiService from './api.service';

/**
 * واجهة الإعدادات
 */
export interface Settings {
  id: string;
  key: string;
  value: any;
  description?: string;
  group?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * خدمة الإعدادات
 * توفر واجهة موحدة للتعامل مع إعدادات النظام
 */
class SettingsService {
  /**
   * الحصول على قائمة الإعدادات
   * @param group مجموعة الإعدادات (اختياري)
   * @returns وعد بقائمة الإعدادات
   */
  public async getSettings(group?: string): Promise<Settings[]> {
    try {
      // إنشاء سلسلة الاستعلام
      const queryString = group ? `?group=${group}` : '';
      
      // الحصول على قائمة الإعدادات
      const response = await apiService.get<Settings[]>(`/api/settings${queryString}`);
      
      return response;
    } catch (error) {
      console.error('Error getting settings:', error);
      throw error;
    }
  }

  /**
   * الحصول على إعداد واحد
   * @param key مفتاح الإعداد
   * @returns وعد بالإعداد
   */
  public async getSetting(key: string): Promise<Settings> {
    try {
      // الحصول على الإعداد
      const response = await apiService.get<Settings>(`/api/settings/${key}`);
      
      return response;
    } catch (error) {
      console.error(`Error getting setting ${key}:`, error);
      throw error;
    }
  }

  /**
   * تحديث إعداد
   * @param key مفتاح الإعداد
   * @param value قيمة الإعداد
   * @returns وعد بالإعداد المحدث
   */
  public async updateSetting(key: string, value: any): Promise<Settings> {
    try {
      // تحديث الإعداد
      const response = await apiService.put<Settings>(`/api/settings/${key}`, { value });
      
      return response;
    } catch (error) {
      console.error(`Error updating setting ${key}:`, error);
      throw error;
    }
  }

  /**
   * تحديث عدة إعدادات
   * @param settings الإعدادات المراد تحديثها
   * @returns وعد بالإعدادات المحدثة
   */
  public async updateSettings(settings: Record<string, any>): Promise<Settings[]> {
    try {
      // تحديث الإعدادات
      const response = await apiService.put<Settings[]>('/api/settings', { settings });
      
      return response;
    } catch (error) {
      console.error('Error updating settings:', error);
      throw error;
    }
  }

  /**
   * إنشاء إعداد جديد
   * @param setting الإعداد المراد إنشاؤه
   * @returns وعد بالإعداد الجديد
   */
  public async createSetting(setting: Omit<Settings, 'id' | 'createdAt' | 'updatedAt'>): Promise<Settings> {
    try {
      // إنشاء الإعداد
      const response = await apiService.post<Settings>('/api/settings', setting);
      
      return response;
    } catch (error) {
      console.error('Error creating setting:', error);
      throw error;
    }
  }

  /**
   * حذف إعداد
   * @param key مفتاح الإعداد
   * @returns وعد بنتيجة الحذف
   */
  public async deleteSetting(key: string): Promise<void> {
    try {
      // حذف الإعداد
      await apiService.delete(`/api/settings/${key}`);
    } catch (error) {
      console.error(`Error deleting setting ${key}:`, error);
      throw error;
    }
  }
}

// إنشاء نسخة واحدة من الخدمة
const settingsService = new SettingsService();

export default settingsService;
