import { Router } from 'express';
import multer from 'multer';
import { permitController } from '../controllers/permit.controller.js';
import { validateRequest } from '../../../core/middleware/validation.middleware.js';
import { authMiddleware } from '../../../core/middleware/auth.middleware.js';
import {
  createPermitSchema,
  updatePermitSchema,
  getPermitSchema,
  deletePermitSchema,
  listPermitsSchema,
} from '../schemas/permit.schema.js';
import { config } from '../../../core/config/app.config.js';

export const permitRoutes = Router();

// إعداد Multer لرفع الملفات
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: config.upload.maxFileSize,
  },
  fileFilter: (req, file, cb) => {
    // قبول ملفات PDF فقط
    if (file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم. يرجى رفع ملف PDF فقط.'));
    }
  },
});

// مسارات التصاريح
permitRoutes.get(
  '/',
  authMiddleware,
  validateRequest(listPermitsSchema),
  permitController.listPermits
);

permitRoutes.post(
  '/',
  authMiddleware,
  upload.single('file'),
  validateRequest(createPermitSchema),
  permitController.createPermit
);

permitRoutes.get(
  '/:id',
  authMiddleware,
  validateRequest(getPermitSchema),
  permitController.getPermit
);

permitRoutes.put(
  '/:id',
  authMiddleware,
  upload.single('file'),
  validateRequest(updatePermitSchema),
  permitController.updatePermit
);

permitRoutes.delete(
  '/:id',
  authMiddleware,
  validateRequest(deletePermitSchema),
  permitController.deletePermit
);
