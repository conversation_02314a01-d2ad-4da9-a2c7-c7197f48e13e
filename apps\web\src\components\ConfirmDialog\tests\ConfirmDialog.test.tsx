import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ConfirmDialog } from '../ConfirmDialog';

describe('ConfirmDialog Component', () => {
  const defaultProps = {
    open: true,
    onClose: jest.fn(),
    onConfirm: jest.fn(),
    title: 'عنوان الحوار',
    content: 'محتوى الحوار',
  };

  test('renders title and content correctly', () => {
    render(<ConfirmDialog {...defaultProps} />);
    expect(screen.getByText('عنوان الحوار')).toBeInTheDocument();
    expect(screen.getByText('محتوى الحوار')).toBeInTheDocument();
  });

  test('renders default button text', () => {
    render(<ConfirmDialog {...defaultProps} />);
    expect(screen.getByText('تأكيد')).toBeInTheDocument();
    expect(screen.getByText('إلغاء')).toBeInTheDocument();
  });

  test('renders custom button text when provided', () => {
    render(
      <ConfirmDialog
        {...defaultProps}
        confirmText="موافق"
        cancelText="رجوع"
      />
    );
    expect(screen.getByText('موافق')).toBeInTheDocument();
    expect(screen.getByText('رجوع')).toBeInTheDocument();
  });

  test('calls onConfirm when confirm button is clicked', () => {
    render(<ConfirmDialog {...defaultProps} />);
    
    const confirmButton = screen.getByText('تأكيد');
    fireEvent.click(confirmButton);
    
    expect(defaultProps.onConfirm).toHaveBeenCalledTimes(1);
  });

  test('calls onClose when cancel button is clicked', () => {
    render(<ConfirmDialog {...defaultProps} />);
    
    const cancelButton = screen.getByText('إلغاء');
    fireEvent.click(cancelButton);
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  test('calls onClose when close icon is clicked', () => {
    render(<ConfirmDialog {...defaultProps} showCloseButton={true} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  test('does not render close icon when showCloseButton is false', () => {
    render(<ConfirmDialog {...defaultProps} showCloseButton={false} />);
    
    const closeButton = screen.queryByRole('button', { name: /close/i });
    expect(closeButton).not.toBeInTheDocument();
  });

  test('disables confirm button when confirmDisabled is true', () => {
    render(<ConfirmDialog {...defaultProps} confirmDisabled={true} />);
    
    const confirmButton = screen.getByText('تأكيد');
    expect(confirmButton).toBeDisabled();
  });

  test('shows loading text when confirmLoading is true', () => {
    render(<ConfirmDialog {...defaultProps} confirmLoading={true} />);
    
    expect(screen.getByText('جاري التنفيذ...')).toBeInTheDocument();
  });

  test('renders JSX content when content is not a string', () => {
    const jsxContent = <div data-testid="custom-content">محتوى مخصص</div>;
    render(<ConfirmDialog {...defaultProps} content={jsxContent} />);
    
    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
    expect(screen.getByText('محتوى مخصص')).toBeInTheDocument();
  });
});
