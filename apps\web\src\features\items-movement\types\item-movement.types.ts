import { Declaration } from '../../declarations/types/declaration.types';

// أنواع الحزم
export enum PackageType {
  PALLET = 'PALLET',
  CARTON = 'CARTON',
  BARREL = 'BARREL',
}

// أنواع البضائع
export enum GoodsType {
  HUMAN_MEDICINE = 'HUMAN_MEDICINE',
  LABORATORY_SOLUTIONS = 'LABORATORY_SOLUTIONS',
  MEDICAL_SUPPLIES = 'MEDICAL_SUPPLIES',
  SUGAR_STRIPS = 'SUGAR_STRIPS',
  MEDICAL_DEVICES = 'MEDICAL_DEVICES',
  MISCELLANEOUS = 'MISCELLANEOUS',
}

// أنواع العملات
export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
}

// واجهة المستخدم
export interface User {
  id: string;
  username: string;
  name: string;
}

// واجهة حركة الصنف
export interface ItemMovement {
  id: string;
  movementNumber: number;
  movementDate?: string;
  declarationNumber: number;
  itemNumber?: number;
  invoiceNumber: number;
  packingListNumber?: number;
  identificationClause?: number;
  itemName?: string;
  count?: number;
  packageType?: PackageType;
  goodsType?: GoodsType;
  countryOfOrigin?: string;
  itemValue?: string;
  currency?: Currency;
  totalValue?: string;
  pdfFile?: string;
  declarationId: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  declaration?: Declaration;
  createdBy?: User;
}

// واجهة نموذج حركة الصنف
export interface ItemMovementFormValues {
  declarationId: string;
  movementDate: Date | null;
  declarationNumber: number | '';
  itemNumber: number | '';
  invoiceNumber: number | '';
  packingListNumber: number | '';
  identificationClause: number | '';
  itemName: string;
  count: number | '';
  packageType: PackageType | '';
  goodsType: GoodsType | '';
  countryOfOrigin: string;
  itemValue: number | '';
  currency: Currency | '';
  totalValue: number | '';
  file: File | null;
}
