import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getGuarantees,
  getGuarantee,
  createGuarantee,
  updateGuarantee,
  deleteGuarantee,
  updateGuaranteeReturnStatus,
  downloadGuaranteePdf
} from '../api/guarantees.api';
import {
  GuaranteeSearchParams,
  CreateGuaranteeDto,
  UpdateGuaranteeDto,
  CreateGuaranteeWithFileDto,
  UpdateGuaranteeWithFileDto
} from '../types/guarantee.types';
import { useToast } from '@lib/hooks/useToast';
import { useTranslation } from 'react-i18next';

/**
 * خطاف للحصول على قائمة الضمانات
 */
export const useGuarantees = (params: GuaranteeSearchParams = {}) => {
  return useQuery({
    queryKey: ['guarantees', params],
    queryFn: () => getGuarantees(params),
  });
};

/**
 * خطاف للحصول على ضمان محدد
 */
export const useGuarantee = (id: string) => {
  return useQuery({
    queryKey: ['guarantees', id],
    queryFn: () => getGuarantee(id),
    enabled: !!id,
  });
};

/**
 * خطاف لإنشاء ضمان جديد
 */
export const useCreateGuarantee = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ data, file }: CreateGuaranteeWithFileDto) => createGuarantee(data, file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['guarantees'] });
      showSuccess(t('guarantees.createSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

/**
 * خطاف لتحديث ضمان
 */
export const useUpdateGuarantee = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, data, file }: UpdateGuaranteeWithFileDto) => updateGuarantee(id, data, file),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['guarantees'] });
      queryClient.invalidateQueries({ queryKey: ['guarantees', variables.id] });
      showSuccess(t('guarantees.updateSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

/**
 * خطاف لحذف ضمان
 */
export const useDeleteGuarantee = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => deleteGuarantee(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['guarantees'] });
      showSuccess(t('guarantees.deleteSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

/**
 * خطاف لتحديث حالة استرجاع الضمان
 */
export const useUpdateGuaranteeReturnStatus = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, isReturned, returnDate }: { id: string; isReturned: boolean; returnDate?: string }) =>
      updateGuaranteeReturnStatus(id, isReturned, returnDate),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['guarantees'] });
      queryClient.invalidateQueries({ queryKey: ['guarantees', variables.id] });
      showSuccess(t('guarantees.returnStatusUpdateSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

/**
 * خطاف لتحميل ملف PDF للضمان
 */
export const useDownloadGuaranteePdf = () => {
  const { showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => downloadGuaranteePdf(id),
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};
