
import { useTranslation } from 'react-i18next';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import {
  Box,
  Button,
  Card,
  CardContent,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { SearchType, DeclarationType, GoodsType } from '../api/advanced-search.api';
import { AdvancedSearchFormValues } from '../types/advanced-search.types';
import { useClients } from '../../clients/hooks/useClients';

interface AdvancedSearchFormProps {
  onSubmit: (data: AdvancedSearchFormValues) => void;
  isLoading?: boolean;
}

/**
 * مكون نموذج البحث المتقدم
 */
export const AdvancedSearchForm = ({ onSubmit, isLoading = false }: AdvancedSearchFormProps) => {
  const { t } = useTranslation();

  // استخدام React Hook Form للتحكم في النموذج
  const { control, handleSubmit, formState: { errors } } = useForm<AdvancedSearchFormValues>({
    defaultValues: {
      searchType: SearchType.ALL,
      keyword: '',
      taxNumber: '',
      clientId: '',
      declarationNumber: '',
      invoiceNumber: '',
      declarationType: undefined,
      goodsType: undefined,
      fromDate: null,
      toDate: null,
    },
  });

  // الحصول على قائمة العملاء
  const { data: clientsData } = useClients();

  // التعامل مع تغيير نوع البحث
  const handleSearchTypeChange = (value: SearchType) => {
    console.log('Search type changed:', value);
  };

  // تقديم النموذج
  const handleFormSubmit = (data: AdvancedSearchFormValues) => {
    onSubmit(data);
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {t('advancedSearch.formTitle')}
        </Typography>

        <Box component="form" noValidate onSubmit={handleSubmit(handleFormSubmit)}>
          <Grid container spacing={3}>
            {/* نوع البحث */}
            <Grid item xs={12} md={6}>
              <Controller
                name="searchType"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.searchType}>
                    <InputLabel>{t('advancedSearch.searchType')}</InputLabel>
                    <Select
                      {...field}
                      label={t('advancedSearch.searchType')}
                      onChange={(e) => {
                        field.onChange(e);
                        handleSearchTypeChange(e.target.value as SearchType);
                      }}
                    >
                      <MenuItem value={SearchType.ALL}>{t('advancedSearch.searchTypes.ALL')}</MenuItem>
                      <MenuItem value={SearchType.DECLARATIONS}>{t('advancedSearch.searchTypes.DECLARATIONS')}</MenuItem>
                      <MenuItem value={SearchType.ITEM_MOVEMENTS}>{t('advancedSearch.searchTypes.ITEM_MOVEMENTS')}</MenuItem>
                      <MenuItem value={SearchType.AUTHORIZATIONS}>{t('advancedSearch.searchTypes.AUTHORIZATIONS')}</MenuItem>
                      <MenuItem value={SearchType.RELEASES}>{t('advancedSearch.searchTypes.RELEASES')}</MenuItem>
                      <MenuItem value={SearchType.PERMITS}>{t('advancedSearch.searchTypes.PERMITS')}</MenuItem>
                      <MenuItem value={SearchType.GUARANTEES}>{t('advancedSearch.searchTypes.GUARANTEES')}</MenuItem>
                      <MenuItem value={SearchType.RECEIPTS}>{t('advancedSearch.searchTypes.RECEIPTS')}</MenuItem>
                      <MenuItem value={SearchType.CLIENTS}>{t('advancedSearch.searchTypes.CLIENTS')}</MenuItem>
                      <MenuItem value={SearchType.DOCUMENTS}>{t('advancedSearch.searchTypes.DOCUMENTS')}</MenuItem>
                    </Select>
                    {errors.searchType && (
                      <FormHelperText>{errors.searchType.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            {/* كلمة البحث */}
            <Grid item xs={12} md={6}>
              <Controller
                name="keyword"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label={t('advancedSearch.keyword')}
                    error={!!errors.keyword}
                    helperText={errors.keyword?.message}
                  />
                )}
              />
            </Grid>

            {/* الرقم الضريبي */}
            <Grid item xs={12} md={6}>
              <Controller
                name="taxNumber"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label={t('advancedSearch.taxNumber')}
                    error={!!errors.taxNumber}
                    helperText={errors.taxNumber?.message}
                  />
                )}
              />
            </Grid>

            {/* العميل */}
            <Grid item xs={12} md={6}>
              <Controller
                name="clientId"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.clientId}>
                    <InputLabel>{t('advancedSearch.client')}</InputLabel>
                    <Select
                      {...field}
                      label={t('advancedSearch.client')}
                    >
                      <MenuItem value="">{t('common.none')}</MenuItem>
                      {clientsData?.data.map((client: any) => (
                        <MenuItem key={client.id} value={client.id}>
                          {client.name} ({client.taxNumber})
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.clientId && (
                      <FormHelperText>{errors.clientId.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            {/* رقم البيان */}
            <Grid item xs={12} md={6}>
              <Controller
                name="declarationNumber"
                control={control}
                render={({ field: { value, onChange, ...field } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label={t('advancedSearch.declarationNumber')}
                    value={value === '' ? '' : value}
                    onChange={(e) => onChange(e.target.value === '' ? '' : Number(e.target.value))}
                    error={!!errors.declarationNumber}
                    helperText={errors.declarationNumber?.message}
                  />
                )}
              />
            </Grid>

            {/* رقم الفاتورة */}
            <Grid item xs={12} md={6}>
              <Controller
                name="invoiceNumber"
                control={control}
                render={({ field: { value, onChange, ...field } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    type="number"
                    label={t('advancedSearch.invoiceNumber')}
                    value={value === '' ? '' : value}
                    onChange={(e) => onChange(e.target.value === '' ? '' : Number(e.target.value))}
                    error={!!errors.invoiceNumber}
                    helperText={errors.invoiceNumber?.message}
                  />
                )}
              />
            </Grid>

            {/* نوع البيان */}
            <Grid item xs={12} md={6}>
              <Controller
                name="declarationType"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.declarationType}>
                    <InputLabel>{t('advancedSearch.declarationType')}</InputLabel>
                    <Select
                      {...field}
                      label={t('advancedSearch.declarationType')}
                    >
                      <MenuItem value="">{t('common.none')}</MenuItem>
                      <MenuItem value={DeclarationType.IMPORT}>{t('declarations.types.IMPORT')}</MenuItem>
                      <MenuItem value={DeclarationType.EXPORT}>{t('declarations.types.EXPORT')}</MenuItem>
                    </Select>
                    {errors.declarationType && (
                      <FormHelperText>{errors.declarationType.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            {/* نوع البضاعة */}
            <Grid item xs={12} md={6}>
              <Controller
                name="goodsType"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth error={!!errors.goodsType}>
                    <InputLabel>{t('advancedSearch.goodsType')}</InputLabel>
                    <Select
                      {...field}
                      label={t('advancedSearch.goodsType')}
                    >
                      <MenuItem value="">{t('common.none')}</MenuItem>
                      <MenuItem value={GoodsType.HUMAN_MEDICINE}>{t('itemMovements.goodsTypes.HUMAN_MEDICINE')}</MenuItem>
                      <MenuItem value={GoodsType.LABORATORY_SOLUTIONS}>{t('itemMovements.goodsTypes.LABORATORY_SOLUTIONS')}</MenuItem>
                      <MenuItem value={GoodsType.MEDICAL_SUPPLIES}>{t('itemMovements.goodsTypes.MEDICAL_SUPPLIES')}</MenuItem>
                      <MenuItem value={GoodsType.SUGAR_STRIPS}>{t('itemMovements.goodsTypes.SUGAR_STRIPS')}</MenuItem>
                      <MenuItem value={GoodsType.MEDICAL_DEVICES}>{t('itemMovements.goodsTypes.MEDICAL_DEVICES')}</MenuItem>
                      <MenuItem value={GoodsType.MISCELLANEOUS}>{t('itemMovements.goodsTypes.MISCELLANEOUS')}</MenuItem>
                    </Select>
                    {errors.goodsType && (
                      <FormHelperText>{errors.goodsType.message}</FormHelperText>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            {/* من تاريخ */}
            <Grid item xs={12} md={6}>
              <Controller
                name="fromDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label={t('advancedSearch.fromDate')}
                    value={field.value}
                    onChange={field.onChange}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.fromDate,
                        helperText: errors.fromDate?.message,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* إلى تاريخ */}
            <Grid item xs={12} md={6}>
              <Controller
                name="toDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label={t('advancedSearch.toDate')}
                    value={field.value}
                    onChange={field.onChange}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.toDate,
                        helperText: errors.toDate?.message,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* زر البحث */}
            <Grid item xs={12}>
              <Box display="flex" justifyContent="flex-end">
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={isLoading}
                >
                  {isLoading ? t('common.searching') : t('common.search')}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
};
