import { z } from 'zod';

/**
 * مخطط إنشاء قالب تقرير
 */
export const createReportTemplateSchema = z.object({
  body: z.object({
    name: z.string({
      required_error: 'اسم القالب مطلوب',
    }).min(3, 'اسم القالب يجب أن يكون على الأقل 3 أحرف'),
    description: z.string().optional(),
    reportType: z.string({
      required_error: 'نوع التقرير مطلوب',
    }),
    template: z.object({}).passthrough(),
    isDefault: z.boolean().optional(),
  }),
});

/**
 * مخطط تحديث قالب تقرير
 */
export const updateReportTemplateSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف القالب مطلوب',
    }),
  }),
  body: z.object({
    name: z.string().min(3, 'اسم القالب يجب أن يكون على الأقل 3 أحرف').optional(),
    description: z.string().optional(),
    reportType: z.string().optional(),
    template: z.object({}).passthrough().optional(),
    isDefault: z.boolean().optional(),
  }),
});

/**
 * مخطط الحصول على قالب تقرير
 */
export const getReportTemplateSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف القالب مطلوب',
    }),
  }),
});

/**
 * مخطط حذف قالب تقرير
 */
export const deleteReportTemplateSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف القالب مطلوب',
    }),
  }),
});

/**
 * مخطط قائمة قوالب التقارير
 */
export const listReportTemplatesSchema = z.object({
  query: z.object({
    reportType: z.string().optional(),
    page: z.string().optional(),
    limit: z.string().optional(),
    sort: z.string().optional(),
    order: z.string().optional(),
    search: z.string().optional(),
  }),
});

/**
 * مخطط الحصول على القالب الافتراضي
 */
export const getDefaultTemplateSchema = z.object({
  params: z.object({
    reportType: z.string({
      required_error: 'نوع التقرير مطلوب',
    }),
  }),
});
