// أنواع البيانات للاختبارات - تحاكي Prisma enums
export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  MANAGER = 'MANAGER'
}

export enum TokenType {
  ACCESS = 'ACCESS',
  REFRESH = 'REFRESH'
}

export enum DeclarationType {
  IMPORT = 'IMPORT',
  EXPORT = 'EXPORT'
}

export enum GoodsType {
  HUMAN_MEDICINE = 'HUMAN_MEDICINE',
  LABORATORY_SOLUTIONS = 'LABORATORY_SOLUTIONS',
  MEDICAL_SUPPLIES = 'MEDICAL_SUPPLIES',
  SUGAR_STRIPS = 'SUGAR_STRIPS',
  MEDICAL_DEVICES = 'MEDICAL_DEVICES',
  MISCELLANEOUS = 'MISCELLANEOUS'
}

export enum AuthorizationType {
  FOLLOW_UP = 'FOLLOW_UP',
  CLEARANCE = 'CLEARANCE',
  RECEIPT = 'RECEIPT',
  FULL = 'FULL'
}

export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  SAR = 'SAR'
}

export enum GuaranteeStatus {
  ACTIVE = 'ACTIVE',
  RETURNED = 'RETURNED',
  EXPIRED = 'EXPIRED'
}

export enum LoginStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  LOCKED = 'LOCKED',
  SUSPICIOUS = 'SUSPICIOUS'
}
