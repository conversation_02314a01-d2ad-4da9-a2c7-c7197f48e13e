import { Request, Response, NextFunction } from 'express';
import { databaseService } from '../services/database.service.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { logger } from '../../../core/utils/logger.js';

/**
 * وحدة التحكم في قاعدة البيانات
 * توفر وظائف للتعامل مع قاعدة البيانات مثل النسخ الاحتياطي والاستعادة والتصدير والتهيئة
 */
export const databaseController = {
  /**
   * الحصول على قائمة النسخ الاحتياطية
   */
  getBackups: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const backups = await databaseService.getBackups();
      res.status(200).json({ data: backups });
    } catch (error) {
      logger.error('Error getting backups:', error);
      next(error);
    }
  },

  /**
   * إنشاء نسخة احتياطية جديدة
   */
  createBackup: async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      const backup = await databaseService.createBackup(req.user.id);
      res.status(201).json({ data: backup, message: 'تم إنشاء النسخة الاحتياطية بنجاح' });
    } catch (error) {
      logger.error('Error creating backup:', error);
      next(error);
    }
  },

  /**
   * استعادة قاعدة البيانات من نسخة احتياطية
   */
  restoreBackup: async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      const { fileName } = req.params;

      if (!fileName) {
        throw new HttpException(400, 'اسم الملف مطلوب', 'Bad Request');
      }

      await databaseService.restoreBackup(fileName, req.user.id);
      res.status(200).json({ message: 'تم استعادة قاعدة البيانات بنجاح' });
    } catch (error) {
      logger.error('Error restoring backup:', error);
      next(error);
    }
  },

  /**
   * حذف نسخة احتياطية
   */
  deleteBackup: async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      const { fileName } = req.params;

      if (!fileName) {
        throw new HttpException(400, 'اسم الملف مطلوب', 'Bad Request');
      }

      await databaseService.deleteBackup(fileName);
      res.status(200).json({ message: 'تم حذف النسخة الاحتياطية بنجاح' });
    } catch (error) {
      logger.error('Error deleting backup:', error);
      next(error);
    }
  },

  /**
   * تنزيل نسخة احتياطية
   */
  downloadBackup: async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      const { fileName } = req.params;

      if (!fileName) {
        throw new HttpException(400, 'اسم الملف مطلوب', 'Bad Request');
      }

      const filePath = await databaseService.getBackupPath(fileName);
      res.download(filePath, fileName);
    } catch (error) {
      logger.error('Error downloading backup:', error);
      next(error);
    }
  },

  /**
   * تصدير قاعدة البيانات
   */
  exportDatabase: async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      const filePath = await databaseService.exportDatabase(req.user.id);
      res.download(filePath);
    } catch (error) {
      logger.error('Error exporting database:', error);
      next(error);
    }
  },

  /**
   * تهيئة قاعدة البيانات
   */
  initializeDatabase: async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // التحقق من أن المستخدم هو مدير النظام
      if (req.user.role !== 'ADMIN') {
        throw new HttpException(403, 'غير مسموح', 'Forbidden');
      }

      await databaseService.initializeDatabase(req.user.id);
      res.status(200).json({ message: 'تم تهيئة قاعدة البيانات بنجاح' });
    } catch (error) {
      logger.error('Error initializing database:', error);
      next(error);
    }
  }
};
