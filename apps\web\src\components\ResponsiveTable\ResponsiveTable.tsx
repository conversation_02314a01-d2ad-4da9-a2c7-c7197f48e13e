import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Divider,
  Grid,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Typography,
  useMediaQuery,
  useTheme,
  Tooltip,
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';

interface Column<T> {
  id: string;
  label: string;
  align?: 'left' | 'center' | 'right';
  render?: (row: T) => React.ReactNode;
  showInCard?: boolean;
}

interface ResponsiveTableProps<T> {
  /**
   * أعمدة الجدول
   */
  columns: Column<T>[];
  
  /**
   * بيانات الجدول
   */
  data: T[];
  
  /**
   * إجمالي عدد العناصر
   */
  totalCount: number;
  
  /**
   * رقم الصفحة الحالية (يبدأ من 0)
   */
  page: number;
  
  /**
   * عدد الصفوف في الصفحة
   */
  rowsPerPage: number;
  
  /**
   * دالة تنفذ عند تغيير الصفحة
   */
  onPageChange: (page: number) => void;
  
  /**
   * دالة تنفذ عند تغيير عدد الصفوف في الصفحة
   */
  onRowsPerPageChange: (rowsPerPage: number) => void;
  
  /**
   * دالة تنفذ عند النقر على زر العرض
   */
  onView?: (row: T) => void;
  
  /**
   * دالة تنفذ عند النقر على زر التعديل
   */
  onEdit?: (row: T) => void;
  
  /**
   * دالة تنفذ عند النقر على زر الحذف
   */
  onDelete?: (row: T) => void;
  
  /**
   * هل الجدول في حالة تحميل
   */
  isLoading?: boolean;
  
  /**
   * عنوان الجدول
   */
  title?: string;
  
  /**
   * رسالة عند عدم وجود بيانات
   */
  emptyMessage?: string;
  
  /**
   * هل يعرض أزرار الإجراءات
   */
  showActions?: boolean;
  
  /**
   * عمود البيانات الرئيسي (للعرض في البطاقات)
   */
  primaryColumn?: string;
  
  /**
   * عمود البيانات الثانوي (للعرض في البطاقات)
   */
  secondaryColumn?: string;
}

/**
 * مكون الجدول المتجاوب
 * يعرض البيانات كجدول على الشاشات الكبيرة وكبطاقات على الأجهزة المحمولة
 */
export function ResponsiveTable<T extends { id: string }>({
  columns,
  data,
  totalCount,
  page,
  rowsPerPage,
  onPageChange,
  onRowsPerPageChange,
  onView,
  onEdit,
  onDelete,
  isLoading = false,
  title,
  emptyMessage = 'لا توجد بيانات',
  showActions = true,
  primaryColumn,
  secondaryColumn,
}: ResponsiveTableProps<T>) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const handleChangePage = (_: unknown, newPage: number) => {
    onPageChange(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    onRowsPerPageChange(parseInt(event.target.value, 10));
    onPageChange(0);
  };

  // عرض البيانات كبطاقات على الأجهزة المحمولة
  if (isMobile) {
    return (
      <Box>
        {title && (
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
        )}
        
        {data.length === 0 ? (
          <Typography variant="body2" color="text.secondary" align="center" py={2}>
            {emptyMessage}
          </Typography>
        ) : (
          <Grid container spacing={2}>
            {data.map((row) => (
              <Grid item xs={12} key={row.id}>
                <Card variant="outlined">
                  <CardContent>
                    {primaryColumn && (
                      <Typography variant="h6" gutterBottom>
                        {(row as any)[primaryColumn]}
                      </Typography>
                    )}
                    
                    {secondaryColumn && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {(row as any)[secondaryColumn]}
                      </Typography>
                    )}
                    
                    <Divider sx={{ my: 1 }} />
                    
                    {columns
                      .filter((column) => column.showInCard)
                      .map((column) => (
                        <Box key={column.id} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            {column.label}:
                          </Typography>
                          <Typography variant="body2">
                            {column.render
                              ? column.render(row)
                              : (row as any)[column.id] || '-'}
                          </Typography>
                        </Box>
                      ))}
                    
                    {showActions && (
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                        {onView && (
                          <Tooltip title="عرض">
                            <IconButton size="small" onClick={() => onView(row)}>
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        {onEdit && (
                          <Tooltip title="تعديل">
                            <IconButton size="small" onClick={() => onEdit(row)}>
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        {onDelete && (
                          <Tooltip title="حذف">
                            <IconButton size="small" onClick={() => onDelete(row)}>
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
        
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="صفوف في الصفحة:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} من ${count}`}
        />
      </Box>
    );
  }

  // عرض البيانات كجدول على الشاشات الكبيرة
  return (
    <Paper>
      {title && (
        <Box p={2}>
          <Typography variant="h6">{title}</Typography>
        </Box>
      )}
      
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell key={column.id} align={column.align || 'right'}>
                  {column.label}
                </TableCell>
              ))}
              {showActions && (onView || onEdit || onDelete) && (
                <TableCell align="center">الإجراءات</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length + (showActions ? 1 : 0)}
                  align="center"
                >
                  {emptyMessage}
                </TableCell>
              </TableRow>
            ) : (
              data.map((row) => (
                <TableRow key={row.id}>
                  {columns.map((column) => (
                    <TableCell key={column.id} align={column.align || 'right'}>
                      {column.render
                        ? column.render(row)
                        : (row as any)[column.id] || '-'}
                    </TableCell>
                  ))}
                  {showActions && (onView || onEdit || onDelete) && (
                    <TableCell align="center">
                      {onView && (
                        <Tooltip title="عرض">
                          <IconButton size="small" onClick={() => onView(row)}>
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      {onEdit && (
                        <Tooltip title="تعديل">
                          <IconButton size="small" onClick={() => onEdit(row)}>
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      {onDelete && (
                        <Tooltip title="حذف">
                          <IconButton size="small" onClick={() => onDelete(row)}>
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="صفوف في الصفحة:"
        labelDisplayedRows={({ from, to, count }) => `${from}-${to} من ${count}`}
      />
    </Paper>
  );
}
