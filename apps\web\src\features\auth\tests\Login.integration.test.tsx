import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, waitFor, cleanup } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { createMockHandlers, server, http, HttpResponse } from '../../../test/integration-setup';
import { configureStore } from '@reduxjs/toolkit';
import { authReducer } from '../store/authSlice';
import Login from '../components/Login';

// إنشاء متجر وهمي
const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
    },
  });
};

// مكون الاختبار مع المزودين
const TestComponent = () => {
  const store = createTestStore();

  return (
    <Provider store={store}>
      <BrowserRouter>
        <Login onLogin={async () => {}} />
      </BrowserRouter>
    </Provider>
  );
};

describe('Login Integration Tests', () => {
  // إعداد المعالجات الوهمية
  const mockHandlers = createMockHandlers();

  beforeEach(() => {
    // إعادة تعيين المعالجات الوهمية قبل كل اختبار
    server.resetHandlers();
  });

  afterEach(() => {
    cleanup();
  });

  it('يجب أن يعرض نموذج تسجيل الدخول بشكل صحيح', async () => {
    render(<TestComponent />);

    // التحقق من وجود عناصر النموذج
    expect(screen.getByLabelText(/اسم المستخدم/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/كلمة المرور/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /تسجيل الدخول/i })).toBeInTheDocument();
  });

  it('يجب أن يعرض رسالة خطأ عند إدخال بيانات غير صالحة', async () => {
    // تعريف معالج وهمي لتسجيل الدخول مع خطأ
    server.use(
      http.post('http://localhost:3001/api/auth/login', () => {
        return HttpResponse.json({
          success: false,
          error: 'اسم المستخدم أو كلمة المرور غير صحيحة',
        }, { status: 401 });
      })
    );

    render(<TestComponent />);

    // إدخال بيانات غير صالحة
    const usernameInput = screen.getByLabelText(/اسم المستخدم/i);
    const passwordInput = screen.getByLabelText(/كلمة المرور/i);
    const loginButton = screen.getByRole('button', { name: /تسجيل الدخول/i });

    await userEvent.type(usernameInput, 'wrong_user');
    await userEvent.type(passwordInput, 'wrong_password');
    await userEvent.click(loginButton);

    // التحقق من ظهور رسالة الخطأ
    await waitFor(() => {
      expect(screen.getByText(/اسم المستخدم أو كلمة المرور غير صحيحة/i)).toBeInTheDocument();
    });
  });

  it('يجب أن يقوم بتسجيل الدخول بنجاح مع بيانات صحيحة', async () => {
    // تعريف معالج وهمي لتسجيل الدخول بنجاح
    server.use(
      http.post('http://localhost:3001/api/auth/login', () => {
        return HttpResponse.json({
          success: true,
          data: {
            token: 'mock-token',
            refreshToken: 'mock-refresh-token',
            user: {
              id: 'user-1',
              username: 'test_user',
              name: 'مستخدم اختبار',
              role: 'ADMIN',
            },
          },
        }, { status: 200 });
      })
    );

    // تعريف معالج وهمي للتوجيه بعد تسجيل الدخول
    const mockNavigate = vi.fn();
    vi.mock('react-router-dom', async () => {
      const actual = await vi.importActual('react-router-dom');
      return {
        ...actual,
        useNavigate: () => mockNavigate,
      };
    });

    render(<TestComponent />);

    // إدخال بيانات صحيحة
    const usernameInput = screen.getByLabelText(/اسم المستخدم/i);
    const passwordInput = screen.getByLabelText(/كلمة المرور/i);
    const loginButton = screen.getByRole('button', { name: /تسجيل الدخول/i });

    await userEvent.type(usernameInput, 'test_user');
    await userEvent.type(passwordInput, 'Test@123');
    await userEvent.click(loginButton);

    // التحقق من التوجيه بعد تسجيل الدخول
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
    });
  });

  it('يجب أن يتحقق من حقول النموذج المطلوبة', async () => {
    render(<TestComponent />);

    // محاولة تسجيل الدخول بدون إدخال بيانات
    const loginButton = screen.getByRole('button', { name: /تسجيل الدخول/i });
    await userEvent.click(loginButton);

    // التحقق من ظهور رسائل التحقق
    await waitFor(() => {
      expect(screen.getByText(/اسم المستخدم مطلوب/i)).toBeInTheDocument();
      expect(screen.getByText(/كلمة المرور مطلوبة/i)).toBeInTheDocument();
    });
  });
});
