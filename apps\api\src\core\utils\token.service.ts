import { prisma } from './prisma.js';
import { logger } from './logger.js';
import { TokenType } from '@prisma/client';
import { verifyToken } from './jwt.js';

/**
 * خدمة إدارة التوكنات
 * تستخدم للتحقق من صحة التوكنات وإبطالها
 */
export const tokenService = {
  /**
   * إبطال توكن
   * @param token التوكن المراد إبطاله
   * @param tokenType نوع التوكن (ACCESS أو REFRESH)
   * @param userId معرف المستخدم
   */
  invalidateToken: async (token: string, tokenType: 'access' | 'refresh', userId: string) => {
    try {
      // التحقق من صحة التوكن للحصول على تاريخ انتهاء الصلاحية
      let expiresAt: Date;

      try {
        // محاولة التحقق من التوكن للحصول على تاريخ انتهاء الصلاحية
        const decoded = verifyToken(token, tokenType);

        // استخراج تاريخ انتهاء الصلاحية من التوكن
        if (typeof decoded.exp === 'number') {
          expiresAt = new Date(decoded.exp * 1000);
        } else {
          // إذا لم يكن هناك تاريخ انتهاء صلاحية، استخدم تاريخ افتراضي (بعد 7 أيام)
          expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + 7);
        }
      } catch (error) {
        // إذا كان التوكن منتهي الصلاحية بالفعل، استخدم تاريخ افتراضي (بعد يوم واحد)
        expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 1);

        // تسجيل الخطأ ولكن استمر في إبطال التوكن
        logger.warn('تم محاولة إبطال توكن غير صالح أو منتهي الصلاحية', { error });
      }

      // تحويل نوع التوكن إلى النوع المناسب في Prisma
      const dbTokenType = tokenType === 'access' ? TokenType.ACCESS : TokenType.REFRESH;

      // إضافة التوكن إلى قائمة التوكنات المبطلة
      await prisma.invalidatedToken.create({
        data: {
          token,
          tokenType: dbTokenType,
          userId,
          expiresAt,
        },
      });

      return { success: true };
    } catch (error) {
      logger.error('خطأ في إبطال التوكن:', error);
      throw new Error('فشل في إبطال التوكن');
    }
  },

  /**
   * التحقق مما إذا كان التوكن مبطلاً
   * @param token التوكن المراد التحقق منه
   * @returns true إذا كان التوكن مبطلاً، false إذا كان صالحاً
   */
  isTokenInvalidated: async (token: string): Promise<boolean> => {
    try {
      // البحث عن التوكن في قائمة التوكنات المبطلة
      const invalidatedToken = await prisma.invalidatedToken.findUnique({
        where: { token },
      });

      // إذا وجد التوكن في القائمة، فهو مبطل
      return !!invalidatedToken;
    } catch (error) {
      logger.error('خطأ في التحقق من حالة التوكن:', error);
      // في حالة الخطأ، نفترض أن التوكن صالح
      return false;
    }
  },

  /**
   * تنظيف التوكنات المبطلة منتهية الصلاحية
   * يمكن تشغيل هذه الوظيفة بشكل دوري لتنظيف قاعدة البيانات
   */
  cleanupExpiredTokens: async () => {
    try {
      // حذف جميع التوكنات المبطلة التي انتهت صلاحيتها
      const result = await prisma.invalidatedToken.deleteMany({
        where: {
          expiresAt: {
            lt: new Date(),
          },
        },
      });

      logger.info(`تم حذف ${result.count} توكن مبطل منتهي الصلاحية`);
      return { count: result.count };
    } catch (error) {
      logger.error('خطأ في تنظيف التوكنات المبطلة:', error);
      throw new Error('فشل في تنظيف التوكنات المبطلة');
    }
  },
};
