// واجهة المستخدم
export interface User {
  id: string;
  username: string;
  name: string;
}

// واجهة العميل
export interface Client {
  id: string;
  clientNumber?: number;
  taxNumber: string;
  name: string;
  companyName?: string;
  phone?: string;
  email?: string;
  address?: string;
  contactPerson?: string;
  contactPhone?: string;
  notes?: string;
  createdBy: User;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

// واجهة قيم نموذج العميل
export interface ClientFormValues {
  taxNumber: string;
  name: string;
  companyName?: string;
  phone?: string;
  email?: string;
  address?: string;
  contactPerson?: string;
  contactPhone?: string;
  notes?: string;
}

// واجهة استجابة قائمة العملاء
export interface ClientsResponse {
  data: Client[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// واجهة استجابة العميل
export interface ClientResponse {
  data: Client;
}
