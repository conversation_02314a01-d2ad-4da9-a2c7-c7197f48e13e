// واجهة المستخدم
export interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'USER';
  createdAt: Date;
  updatedAt: Date;
}

// واجهة قيم نموذج الملف الشخصي
export interface ProfileFormValues {
  name: string;
  email: string;
  currentPassword?: string;
  newPassword?: string;
  confirmPassword?: string;
}

// واجهة استجابة الملف الشخصي
export interface ProfileResponse {
  data: User;
}
