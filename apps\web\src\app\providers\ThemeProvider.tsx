import { ReactNode, useMemo } from 'react';
import { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { useTranslation } from 'react-i18next';
import { prefixer } from 'stylis';
import rtlPlugin from 'stylis-plugin-rtl';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';

// Create rtl cache
const createRtlCache = () => {
  return createCache({
    key: 'muirtl',
    stylisPlugins: [prefixer, rtlPlugin],
  });
};

// Create ltr cache
const createLtrCache = () => {
  return createCache({
    key: 'muiltr',
  });
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider = ({ children }: ThemeProviderProps) => {
  const { i18n } = useTranslation();
  const isRtl = i18n.dir() === 'rtl';
  
  // Create appropriate cache based on direction
  const cache = isRtl ? createRtlCache() : createLtrCache();

  // Create theme with direction
  const theme = useMemo(
    () =>
      createTheme({
        direction: isRtl ? 'rtl' : 'ltr',
        typography: {
          fontFamily: isRtl 
            ? '"Tajawal", "Roboto", "Helvetica", "Arial", sans-serif'
            : '"Roboto", "Helvetica", "Arial", sans-serif',
        },
        palette: {
          primary: {
            main: '#1976d2',
            light: '#42a5f5',
            dark: '#1565c0',
            contrastText: '#fff',
          },
          secondary: {
            main: '#dc004e',
            light: '#ec407a',
            dark: '#c2185b',
            contrastText: '#fff',
          },
          error: {
            main: '#f44336',
          },
          warning: {
            main: '#ff9800',
          },
          info: {
            main: '#2196f3',
          },
          success: {
            main: '#4caf50',
          },
          background: {
            default: '#f5f5f5',
            paper: '#fff',
          },
        },
        shape: {
          borderRadius: 4,
        },
        components: {
          MuiButton: {
            styleOverrides: {
              root: {
                fontWeight: 500,
                textTransform: 'none',
              },
            },
          },
          MuiTextField: {
            defaultProps: {
              variant: 'outlined',
              size: 'small',
              fullWidth: true,
            },
          },
          MuiInputLabel: {
            styleOverrides: {
              root: {
                fontFamily: isRtl ? '"Tajawal", sans-serif' : '"Roboto", sans-serif',
              },
            },
          },
          MuiOutlinedInput: {
            styleOverrides: {
              root: {
                fontFamily: isRtl ? '"Tajawal", sans-serif' : '"Roboto", sans-serif',
              },
            },
          },
          MuiTableCell: {
            styleOverrides: {
              root: {
                fontFamily: isRtl ? '"Tajawal", sans-serif' : '"Roboto", sans-serif',
                padding: '8px 16px',
              },
              head: {
                fontWeight: 700,
                backgroundColor: '#f5f5f5',
              },
            },
          },
          MuiDialogTitle: {
            styleOverrides: {
              root: {
                fontFamily: isRtl ? '"Cairo", sans-serif' : '"Roboto", sans-serif',
                fontWeight: 600,
              },
            },
          },
        },
      }),
    [isRtl]
  );

  return (
    <CacheProvider value={cache}>
      <MuiThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </CacheProvider>
  );
};

export default ThemeProvider;
