import React from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { Box, Button, Grid } from '@mui/material';
import { FormField, FormFieldProps } from './FormField';

export interface DynamicFormProps {
  /**
   * حقول النموذج
   */
  fields: FormFieldProps[];
  
  /**
   * القيم الافتراضية للنموذج
   */
  defaultValues?: Record<string, any>;
  
  /**
   * معالج الإرسال
   */
  onSubmit: (data: any) => void;
  
  /**
   * نص زر الإرسال
   */
  submitText?: string;
  
  /**
   * نص زر الإلغاء
   */
  cancelText?: string;
  
  /**
   * معالج الإلغاء
   */
  onCancel?: () => void;
  
  /**
   * حالة التحميل
   */
  isLoading?: boolean;
  
  /**
   * عدد الأعمدة
   */
  columns?: 1 | 2 | 3 | 4;
}

/**
 * مكون النموذج الديناميكي
 */
export const DynamicForm: React.FC<DynamicFormProps> = ({
  fields,
  defaultValues = {},
  onSubmit,
  submitText = 'حفظ',
  cancelText = 'إلغاء',
  onCancel,
  isLoading = false,
  columns = 1,
}) => {
  const methods = useForm({
    defaultValues,
  });
  
  const handleSubmit = methods.handleSubmit(onSubmit);
  
  return (
    <FormProvider {...methods}>
      <Box component="form" onSubmit={handleSubmit} noValidate>
        <Grid container spacing={2}>
          {fields.map((field) => (
            <Grid item xs={12} sm={12 / columns} key={field.name}>
              <FormField {...field} />
            </Grid>
          ))}
        </Grid>
        
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          {onCancel && (
            <Button
              variant="outlined"
              onClick={onCancel}
              sx={{ mr: 1 }}
              disabled={isLoading}
            >
              {cancelText}
            </Button>
          )}
          <Button
            type="submit"
            variant="contained"
            disabled={isLoading}
          >
            {submitText}
          </Button>
        </Box>
      </Box>
    </FormProvider>
  );
};
