@tailwind base;
@tailwind components;
@tailwind utilities;

/* تعريف الخطوط */
@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('/assets/fonts/Amiri-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Amiri';
  src: url('/assets/fonts/Amiri-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cairo';
  src: url('/assets/fonts/Cairo-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cairo';
  src: url('/assets/fonts/Cairo-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('/assets/fonts/Tajawal-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Tajawal';
  src: url('/assets/fonts/Tajawal-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

/* إعدادات عامة */
:root {
  --primary-color: #1976d2;
  --secondary-color: #dc004e;
  --background-color: #f5f5f5;
  --text-color: #333333;
  --border-color: #e0e0e0;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
}

html {
  direction: rtl;
}

body {
  font-family: 'Tajawal', 'Arial', sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--background-color);
  color: var(--text-color);
}

/* تخصيص Material UI */
.MuiButton-root {
  font-family: 'Tajawal', 'Arial', sans-serif !important;
}

.MuiTypography-root {
  font-family: 'Tajawal', 'Arial', sans-serif !important;
}

.MuiInputBase-root {
  font-family: 'Tajawal', 'Arial', sans-serif !important;
}

/* تخصيص الطباعة */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background-color: white;
  }
}
