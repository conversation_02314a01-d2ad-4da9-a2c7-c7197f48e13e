// مساعد بسيط للمصادقة في الاختبارات
import { generateToken } from '../jwt.js';

/**
 * إنشاء مستخدم وهمي للاختبارات
 */
export const createMockUser = () => {
  const timestamp = Date.now();
  return {
    id: `test-user-${timestamp}`,
    username: `test_admin_${timestamp}`,
    name: 'مستخدم اختبار',
    email: `test_${timestamp}@example.com`,
    role: 'ADMIN',
    isActive: true,
    password: 'Test@123',
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

/**
 * إنشاء رمز مصادقة وهمي للاختبارات
 */
export const createMockAuthToken = (user?: any) => {
  const mockUser = user || createMockUser();
  
  const payload = {
    id: mockUser.id,
    userId: mockUser.id,
    username: mockUser.username,
    role: mockUser.role,
    isActive: true,
  };

  return generateToken(payload, 'access', '24h');
};

/**
 * إنشاء رمز تحديث وهمي للاختبارات
 */
export const createMockRefreshToken = (user?: any) => {
  const mockUser = user || createMockUser();
  
  const payload = {
    id: mockUser.id,
    userId: mockUser.id,
    username: mockUser.username,
    role: mockUser.role,
    isActive: true,
  };

  return generateToken(payload, 'refresh', '7d');
};

/**
 * إنشاء بيانات مصادقة كاملة وهمية
 */
export const createMockAuthData = () => {
  const user = createMockUser();
  const accessToken = createMockAuthToken(user);
  const refreshToken = createMockRefreshToken(user);

  return {
    user,
    accessToken,
    refreshToken,
  };
};

/**
 * إنشاء عميل وهمي للاختبارات
 */
export const createMockClient = () => {
  const timestamp = Date.now();
  return {
    id: `test-client-${timestamp}`,
    clientNumber: `C${timestamp}`,
    taxNumber: `TX${timestamp}`,
    clientName: 'شركة الاختبار',
    companyName: 'Test Company',
    phone: '+966501234567',
    email: `client_${timestamp}@test.com`,
    address: 'الرياض، المملكة العربية السعودية',
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

/**
 * إنشاء بيان وهمي للاختبارات
 */
export const createMockDeclaration = (clientId?: string, userId?: string) => {
  const timestamp = Date.now();
  return {
    id: `test-declaration-${timestamp}`,
    declarationNumber: `D${timestamp}`,
    taxNumber: `TX${timestamp}`,
    clientName: 'شركة الاختبار',
    companyName: 'Test Company',
    policyNumber: '12345',
    invoiceNumber: '67890',
    gatewayEntryNumber: '11111',
    declarationType: 'IMPORT' as const,
    declarationDate: new Date(),
    count: 10,
    weight: 100.5,
    goodsType: 'MEDICAL_SUPPLIES' as const,
    itemsCount: 5,
    clientId: clientId || `test-client-${timestamp}`,
    userId: userId || `test-user-${timestamp}`,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

/**
 * إنشاء حركة صنف وهمية للاختبارات
 */
export const createMockItemMovement = (declarationId?: string) => {
  const timestamp = Date.now();
  return {
    id: `test-movement-${timestamp}`,
    movementNumber: `M${timestamp}`,
    movementDate: new Date(),
    declarationNumber: `D${timestamp}`,
    itemNumber: 'I001',
    invoiceNumber: 'INV001',
    itemName: 'أدوية طبية',
    quantity: 100,
    packageType: 'CARTON' as const,
    goodsType: 'MEDICAL_SUPPLIES' as const,
    countryOfOrigin: 'USA',
    itemValue: 1000.0,
    currency: 'USD' as const,
    totalValue: 1000.0,
    declarationId: declarationId || `test-declaration-${timestamp}`,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

/**
 * إنشاء نموذج مخصص وهمي للاختبارات
 */
export const createMockCustomForm = (userId?: string) => {
  const timestamp = Date.now();
  return {
    id: `test-form-${timestamp}`,
    name: `نموذج اختبار ${timestamp}`,
    description: 'وصف نموذج الاختبار',
    formData: JSON.stringify({
      fields: [
        { name: 'field1', type: 'text', label: 'حقل نصي' },
        { name: 'field2', type: 'number', label: 'حقل رقمي' },
      ],
    }),
    formType: 'CUSTOM',
    isActive: true,
    userId: userId || `test-user-${timestamp}`,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
};

/**
 * تنظيف بيانات الاختبار الوهمية
 */
export const cleanupMockData = () => {
  // لا حاجة لتنظيف البيانات الوهمية
  console.log('✅ تم تنظيف البيانات الوهمية');
};
