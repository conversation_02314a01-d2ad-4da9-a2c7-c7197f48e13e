import React from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  useMediaQuery,
  useTheme,
  Card,
  CardContent,
  Grid,
  Divider,
  IconButton,
  Tooltip,
  TablePagination,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';

export interface Column<T> {
  /**
   * معرف العمود
   */
  id: string;
  
  /**
   * عنوان العمود
   */
  label: string;
  
  /**
   * دالة لعرض قيمة الخلية
   */
  render?: (row: T) => React.ReactNode;
  
  /**
   * هل العمود قابل للفرز
   */
  sortable?: boolean;
  
  /**
   * عرض العمود
   */
  width?: string | number;
  
  /**
   * محاذاة العمود
   */
  align?: 'left' | 'center' | 'right';
  
  /**
   * هل يظهر العمود في عرض الكارت (للأجهزة المحمولة)
   * إذا لم يتم تحديده، سيظهر العمود دائمًا
   */
  showInCard?: boolean;
}

export interface ResponsiveTableProps<T> {
  /**
   * أعمدة الجدول
   */
  columns: Column<T>[];
  
  /**
   * بيانات الجدول
   */
  data: T[];
  
  /**
   * إجمالي عدد العناصر
   */
  totalCount: number;
  
  /**
   * رقم الصفحة الحالية
   */
  page: number;
  
  /**
   * عدد العناصر في الصفحة
   */
  rowsPerPage: number;
  
  /**
   * معالج تغيير الصفحة
   */
  onPageChange: (page: number) => void;
  
  /**
   * معالج تغيير عدد العناصر في الصفحة
   */
  onRowsPerPageChange: (rowsPerPage: number) => void;
  
  /**
   * معالج النقر على زر التعديل
   */
  onEdit?: (row: T) => void;
  
  /**
   * معالج النقر على زر الحذف
   */
  onDelete?: (row: T) => void;
  
  /**
   * معالج النقر على زر العرض
   */
  onView?: (row: T) => void;
  
  /**
   * هل الجدول في حالة تحميل
   */
  isLoading?: boolean;
  
  /**
   * عنوان الجدول
   */
  title?: string;
  
  /**
   * رسالة عند عدم وجود بيانات
   */
  emptyMessage?: string;
  
  /**
   * هل يعرض أزرار الإجراءات
   */
  showActions?: boolean;
  
  /**
   * العمود الرئيسي الذي سيظهر كعنوان في عرض الكارت
   */
  primaryColumn?: string;
  
  /**
   * العمود الثانوي الذي سيظهر كوصف في عرض الكارت
   */
  secondaryColumn?: string;
}

/**
 * مكون الجدول المتجاوب
 * يعرض البيانات كجدول على الشاشات الكبيرة وكبطاقات على الأجهزة المحمولة
 */
export function ResponsiveTable<T extends { id: string }>({
  columns,
  data,
  totalCount,
  page,
  rowsPerPage,
  onPageChange,
  onRowsPerPageChange,
  onEdit,
  onDelete,
  onView,
  isLoading = false,
  title,
  emptyMessage = 'لا توجد بيانات',
  showActions = true,
  primaryColumn,
  secondaryColumn,
}: ResponsiveTableProps<T>) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const handleChangePage = (_: unknown, newPage: number) => {
    onPageChange(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    onRowsPerPageChange(parseInt(event.target.value, 10));
    onPageChange(0);
  };
  
  // عرض أزرار الإجراءات
  const renderActions = (row: T) => (
    <Box sx={{ display: 'flex', justifyContent: isMobile ? 'flex-start' : 'center', gap: 1 }}>
      {onView && (
        <Tooltip title="عرض">
          <IconButton
            size="small"
            color="primary"
            onClick={() => onView(row)}
          >
            <VisibilityIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
      {onEdit && (
        <Tooltip title="تعديل">
          <IconButton
            size="small"
            color="primary"
            onClick={() => onEdit(row)}
          >
            <EditIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
      {onDelete && (
        <Tooltip title="حذف">
          <IconButton
            size="small"
            color="error"
            onClick={() => onDelete(row)}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
    </Box>
  );
  
  // عرض حالة التحميل
  if (isLoading) {
    return (
      <Paper sx={{ width: '100%', p: 3, textAlign: 'center' }}>
        <CircularProgress size={40} />
      </Paper>
    );
  }
  
  // عرض رسالة عند عدم وجود بيانات
  if (data.length === 0) {
    return (
      <Paper sx={{ width: '100%', p: 3, textAlign: 'center' }}>
        <Typography variant="body1">{emptyMessage}</Typography>
      </Paper>
    );
  }
  
  // عرض البيانات كبطاقات على الأجهزة المحمولة
  if (isMobile) {
    return (
      <Box sx={{ width: '100%' }}>
        {title && (
          <Typography variant="h6" component="div" sx={{ mb: 2 }}>
            {title}
          </Typography>
        )}
        
        <Grid container spacing={2}>
          {data.map((row) => (
            <Grid item xs={12} key={row.id}>
              <Card variant="outlined">
                <CardContent>
                  {/* العنوان الرئيسي والثانوي */}
                  {primaryColumn && (
                    <Typography variant="h6" component="div" gutterBottom>
                      {columns.find(col => col.id === primaryColumn)?.render
                        ? columns.find(col => col.id === primaryColumn)?.render!(row)
                        : (row as any)[primaryColumn]}
                    </Typography>
                  )}
                  {secondaryColumn && (
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {columns.find(col => col.id === secondaryColumn)?.render
                        ? columns.find(col => col.id === secondaryColumn)?.render!(row)
                        : (row as any)[secondaryColumn]}
                    </Typography>
                  )}
                  
                  <Divider sx={{ my: 1.5 }} />
                  
                  {/* بقية الأعمدة */}
                  <Grid container spacing={1}>
                    {columns
                      .filter(col => col.showInCard !== false && col.id !== primaryColumn && col.id !== secondaryColumn)
                      .map((column) => (
                        <Grid item xs={12} key={column.id}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="body2" color="text.secondary">
                              {column.label}:
                            </Typography>
                            <Typography variant="body2" align={column.align || 'right'}>
                              {column.render ? column.render(row) : (row as any)[column.id] || '-'}
                            </Typography>
                          </Box>
                        </Grid>
                      ))}
                  </Grid>
                  
                  {/* أزرار الإجراءات */}
                  {showActions && (onEdit || onDelete || onView) && (
                    <>
                      <Divider sx={{ my: 1.5 }} />
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                        {renderActions(row)}
                      </Box>
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
        
        {/* ترقيم الصفحات */}
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="عدد العناصر في الصفحة:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} من ${count}`}
        />
      </Box>
    );
  }
  
  // عرض البيانات كجدول على الشاشات الكبيرة
  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      {title && (
        <Box sx={{ p: 2, borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
          <Typography variant="h6" component="div">
            {title}
          </Typography>
        </Box>
      )}
      
      <TableContainer sx={{ maxHeight: 440 }}>
        <Table stickyHeader aria-label="sticky table">
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || 'right'}
                  style={{ width: column.width }}
                >
                  {column.label}
                </TableCell>
              ))}
              {showActions && (onEdit || onDelete || onView) && (
                <TableCell align="center" style={{ width: 150 }}>
                  الإجراءات
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.map((row) => (
              <TableRow hover role="checkbox" tabIndex={-1} key={row.id}>
                {columns.map((column) => (
                  <TableCell key={column.id} align={column.align || 'right'}>
                    {column.render ? column.render(row) : (row as any)[column.id] || '-'}
                  </TableCell>
                ))}
                {showActions && (onEdit || onDelete || onView) && (
                  <TableCell align="center">
                    {renderActions(row)}
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="عدد العناصر في الصفحة:"
        labelDisplayedRows={({ from, to, count }) => `${from}-${to} من ${count}`}
      />
    </Paper>
  );
}
