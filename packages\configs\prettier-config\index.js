module.exports = {
  // تحديد عرض السطر الأقصى
  printWidth: 100,
  
  // استخدام مسافتين للمسافة البادئة
  tabWidth: 2,
  
  // استخدام المسافات بدلاً من علامات التبويب
  useTabs: false,
  
  // استخدام فواصل منقوطة في نهاية الجمل
  semi: true,
  
  // استخدام علامات اقتباس مفردة
  singleQuote: true,
  
  // وضع فواصل بعد آخر عنصر في المصفوفات والكائنات
  trailingComma: 'all',
  
  // إضافة مسافات بين الأقواس في الكائنات
  bracketSpacing: true,
  
  // وضع الأقواس الزاوية الختامية في السطر التالي في JSX
  bracketSameLine: false,
  
  // دائمًا وضع أقواس حول معلمات الدالة السهمية
  arrowParens: 'always',
  
  // تنسيق الملفات المضمنة
  embeddedLanguageFormatting: 'auto',
  
  // عدم إضافة مسافة بادئة للكود داخل علامات <script> و <style>
  vueIndentScriptAndStyle: false,
  
  // استخدام نهايات أسطر LF
  endOfLine: 'lf',
  
  // تنسيق الملفات المدرجة في .gitignore
  ignorePatterns: [
    'node_modules',
    'dist',
    'build',
    '.next',
    'coverage',
    '.turbo',
    'pnpm-lock.yaml'
  ],
  
  // تنسيق الملفات بناءً على نوع الملف
  overrides: [
    {
      files: '*.{js,jsx,ts,tsx}',
      options: {
        parser: 'typescript',
      },
    },
    {
      files: '*.{json,jsonc}',
      options: {
        parser: 'json',
      },
    },
    {
      files: '*.{yml,yaml}',
      options: {
        parser: 'yaml',
      },
    },
    {
      files: '*.md',
      options: {
        parser: 'markdown',
      },
    },
    {
      files: '*.css',
      options: {
        parser: 'css',
      },
    },
    {
      files: '*.html',
      options: {
        parser: 'html',
      },
    },
  ],
};
