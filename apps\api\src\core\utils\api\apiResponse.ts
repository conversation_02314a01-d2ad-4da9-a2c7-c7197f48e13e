/**
 * تنسيق استجابة API القياسي
 * يستخدم لتوحيد شكل الاستجابة في جميع أنحاء التطبيق
 */
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  statusCode: number;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

/**
 * إنشاء استجابة نجاح
 * @param data البيانات المراد إرجاعها
 * @param message رسالة النجاح
 * @param statusCode رمز الحالة HTTP
 * @returns استجابة API موحدة
 */
export const successResponse = <T>(
  data: T,
  message = 'تمت العملية بنجاح',
  statusCode = 200
): ApiResponse<T> => {
  return {
    success: true,
    data,
    message,
    statusCode,
  };
};

/**
 * إنشاء استجابة نجاح مع تقسيم الصفحات
 * @param data البيانات المراد إرجاعها
 * @param page رقم الصفحة الحالية
 * @param limit عدد العناصر في الصفحة
 * @param total إجمالي عدد العناصر
 * @param message رسالة النجاح
 * @param statusCode رمز الحالة HTTP
 * @returns استجابة API موحدة مع معلومات تقسيم الصفحات
 */
export const paginatedResponse = <T>(
  data: T,
  page: number,
  limit: number,
  total: number,
  message = 'تمت العملية بنجاح',
  statusCode = 200
): ApiResponse<T> => {
  return {
    success: true,
    data,
    message,
    statusCode,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
    },
  };
};

/**
 * إنشاء استجابة خطأ
 * @param message رسالة الخطأ
 * @param statusCode رمز الحالة HTTP
 * @param error نوع الخطأ
 * @returns استجابة API موحدة للخطأ
 */
export const errorResponse = (
  message: string,
  statusCode = 500,
  error = 'خطأ في الخادم الداخلي'
): ApiResponse<null> => {
  return {
    success: false,
    message,
    error,
    statusCode,
  };
};
