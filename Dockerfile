# استخدام صورة Node.js الرسمية كصورة أساسية
FROM node:18-alpine AS base

# تثبيت Turbo
FROM base AS builder
RUN apk add --no-cache libc6-compat
RUN npm install -g turbo
WORKDIR /app
COPY . .
RUN turbo prune --scope=api --scope=web --docker

# إنشاء صورة للتثبيت
FROM base AS installer
RUN apk add --no-cache libc6-compat
WORKDIR /app

# نسخ ملفات الإعتماديات المستخرجة من الخطوة السابقة
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=builder /app/out/pnpm-workspace.yaml ./pnpm-workspace.yaml

# تثبيت الإعتماديات
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# نسخ الكود المصدري
COPY --from=builder /app/out/full/ .

# بناء التطبيق
RUN pnpm turbo build --filter=api --filter=web

# إنشاء صورة للتشغيل
FROM base AS runner
WORKDIR /app

# تثبيت الإعتماديات الإنتاجية فقط
RUN npm install -g pnpm
COPY --from=installer /app/package.json .
COPY --from=installer /app/pnpm-lock.yaml .
COPY --from=installer /app/pnpm-workspace.yaml .
COPY --from=installer /app/apps/api/package.json ./apps/api/package.json
COPY --from=installer /app/apps/web/package.json ./apps/web/package.json
COPY --from=installer /app/packages ./packages
RUN pnpm install --prod --frozen-lockfile

# نسخ الملفات المبنية
COPY --from=installer /app/apps/api/dist ./apps/api/dist
COPY --from=installer /app/apps/web/dist ./apps/web/dist

# نسخ ملفات Prisma
COPY --from=installer /app/database ./database
COPY --from=installer /app/apps/api/prisma ./apps/api/prisma

# تعريف متغيرات البيئة
ENV NODE_ENV production
ENV PORT 3000

# تعريض المنفذ
EXPOSE 3000

# تشغيل التطبيق
CMD ["node", "apps/api/dist/server.js"]
