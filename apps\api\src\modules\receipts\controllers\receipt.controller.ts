import { Request, Response, NextFunction } from 'express';
import { receiptService } from '../services/receipt.service.js';
import { successResponse, paginatedResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const receiptController = {
  /**
   * إنشاء استلام جديد
   */
  createReceipt: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على بيانات الاستلام من الطلب
      const receiptData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // إنشاء الاستلام
      const receipt = await receiptService.createReceipt(
        receiptData,
        req.user.id, // تم الاحتفاظ بمعرف المستخدم للتوافق مع واجهة الخدمة
        file
      );

      return res.status(201).json(successResponse(receipt, 'تم إنشاء الاستلام بنجاح', 201));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث استلام
   */
  updateReceipt: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف الاستلام من المعلمات
      const { id } = req.params;

      // الحصول على بيانات الاستلام من الطلب
      const receiptData = req.body;

      // الحصول على ملف PDF إذا تم تقديمه
      const file = req.file;

      // تحديث الاستلام
      const receipt = await receiptService.updateReceipt(
        id,
        receiptData,
        req.user.id, // تم الاحتفاظ بمعرف المستخدم للتوافق مع واجهة الخدمة
        file
      );

      return res.status(200).json(successResponse(receipt, 'تم تحديث الاستلام بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على استلام محدد
   */
  getReceipt: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معرف الاستلام من المعلمات
      const { id } = req.params;

      // الحصول على الاستلام
      const receipt = await receiptService.getReceipt(id);

      return res.status(200).json(successResponse(receipt));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف استلام
   */
  deleteReceipt: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف الاستلام من المعلمات
      const { id } = req.params;

      // حذف الاستلام
      await receiptService.deleteReceipt(id, req.user.id); // تم الاحتفاظ بمعرف المستخدم للتوافق مع واجهة الخدمة

      return res.status(200).json(successResponse(null, 'تم حذف الاستلام بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة الاستلامات
   */
  listReceipts: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معلمات البحث
      const { page, limit, sort, order, search, declarationId, fromDate, toDate } =
        req.query as any;

      // الحصول على قائمة الاستلامات
      const result = await receiptService.listReceipts({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        search,
        declarationId,
        fromDate: fromDate ? new Date(fromDate) : undefined,
        toDate: toDate ? new Date(toDate) : undefined,
      });

      return res.status(200).json(paginatedResponse(
        result.data,
        result.pagination.page,
        result.pagination.limit,
        result.pagination.total,
        'تم الحصول على قائمة الاستلامات بنجاح'
      ));
    } catch (error) {
      next(error);
    }
  },
};
