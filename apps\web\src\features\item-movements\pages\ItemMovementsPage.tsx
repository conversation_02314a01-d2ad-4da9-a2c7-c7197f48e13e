import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  MenuItem,
  IconButton,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useItemMovements } from '../hooks/useItemMovements';
import { useDeleteItemMovement } from '../hooks/useDeleteItemMovement';
import { PageHeader } from '@components/PageHeader';
import { DataTable } from '@components/DataTable';
import { ConfirmDialog } from '@components/ConfirmDialog';
import { formatDate } from '@lib/utils/date-utils';
import { ItemMovementStatus } from '../types/item-movement.types';

/**
 * نموذج البحث
 */
interface SearchFormData {
  search: string;
  status: string;
  fromDate: Date | null;
  toDate: Date | null;
}

/**
 * صفحة حركات الأصناف
 */
const ItemMovementsPage: React.FC = () => {
  const { t } = useTranslation();
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState<Record<string, any>>({});
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemMovementToDelete, setItemMovementToDelete] = useState<string | null>(null);

  // استخدام خطافات البيانات
  const {
    data: itemMovementsData,
    isLoading,
    refetch,
  } = useItemMovements({
    page: page + 1,
    limit: pageSize,
    ...filters,
  });

  const deleteItemMovementMutation = useDeleteItemMovement();

  // إعداد نموذج البحث
  const { control, handleSubmit, reset } = useForm<SearchFormData>({
    defaultValues: {
      search: '',
      status: '',
      fromDate: null,
      toDate: null,
    },
  });

  // معالجة تقديم نموذج البحث
  const onSubmit = (data: SearchFormData) => {
    const newFilters: Record<string, any> = {};

    if (data.search) {
      newFilters.search = data.search;
    }

    if (data.status) {
      newFilters.status = data.status;
    }

    if (data.fromDate) {
      newFilters.fromDate = formatDate(data.fromDate);
    }

    if (data.toDate) {
      newFilters.toDate = formatDate(data.toDate);
    }

    setFilters(newFilters);
    setPage(0);
  };

  // إعادة تعيين البحث
  const handleReset = () => {
    reset();
    setFilters({});
    setPage(0);
  };

  // التعامل مع حذف حركة صنف
  const handleDelete = async () => {
    if (!itemMovementToDelete) return;

    try {
      await deleteItemMovementMutation.mutateAsync(itemMovementToDelete);
      setDeleteDialogOpen(false);
      refetch();
    } catch (error) {
      console.error('Error deleting item movement:', error);
    }
  };

  // تعريف أعمدة الجدول
  const columns = [
    {
      id: 'movementNumber',
      label: t('itemMovements.movementNumber'),
      width: 150,
    },
    {
      id: 'declarationNumber',
      label: t('itemMovements.declarationNumber'),
      width: 150,
      format: (_: any, row: any) => row.declaration?.declarationNumber || '-',
    },
    {
      id: 'itemName',
      label: t('itemMovements.itemName'),
      width: 200,
    },
    {
      id: 'movementDate',
      label: t('itemMovements.movementDate'),
      width: 150,
      format: (_: any, row: any) => formatDate(row.movementDate),
    },
    {
      id: 'status',
      label: t('itemMovements.status'),
      width: 120,
      format: (_: any, row: any) => t(`itemMovements.statuses.${row.status}`),
    },
    {
      id: 'actions',
      label: t('common.actions'),
      width: 150,
      sortable: false,
      format: (_: any, row: any) => (
        <Box sx={{ display: 'flex' }}>
          <Tooltip title={t('common.view')}>
            <IconButton
              component={Link}
              to={`/item-movements/${row.id}`}
              size="small"
            >
              <VisibilityIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('common.edit')}>
            <IconButton
              component={Link}
              to={`/item-movements/edit/${row.id}`}
              size="small"
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('common.delete')}>
            <IconButton
              size="small"
              onClick={() => {
                setItemMovementToDelete(row.id);
                setDeleteDialogOpen(true);
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  return (
    <Box>
      <PageHeader
        title={t('itemMovements.title')}
        subtitle={t('itemMovements.subtitle')}
        actions={
          <Button
            component={Link}
            to="/item-movements/new"
            startIcon={<AddIcon />}
            variant="contained"
          >
            {t('itemMovements.addNew')}
          </Button>
        }
      />

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box component="form" onSubmit={handleSubmit(onSubmit)}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} sm={6} md={3}>
                <Controller
                  name="search"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('itemMovements.search.placeholder')}
                      fullWidth
                      variant="outlined"
                      size="small"
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      select
                      label={t('itemMovements.status')}
                      fullWidth
                      variant="outlined"
                      size="small"
                    >
                      <MenuItem value="">
                        {t('common.all')}
                      </MenuItem>
                      <MenuItem value={ItemMovementStatus.PENDING}>
                        {t('itemMovements.statuses.PENDING')}
                      </MenuItem>
                      <MenuItem value={ItemMovementStatus.IN_PROGRESS}>
                        {t('itemMovements.statuses.IN_PROGRESS')}
                      </MenuItem>
                      <MenuItem value={ItemMovementStatus.COMPLETED}>
                        {t('itemMovements.statuses.COMPLETED')}
                      </MenuItem>
                      <MenuItem value={ItemMovementStatus.CANCELLED}>
                        {t('itemMovements.statuses.CANCELLED')}
                      </MenuItem>
                    </TextField>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={2}>
                <Controller
                  name="fromDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label={t('common.fromDate')}
                      value={field.value}
                      onChange={field.onChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          variant: 'outlined',
                          size: 'small',
                        },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={2}>
                <Controller
                  name="toDate"
                  control={control}
                  render={({ field }) => (
                    <DatePicker
                      label={t('common.toDate')}
                      value={field.value}
                      onChange={field.onChange}
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          variant: 'outlined',
                          size: 'small',
                        },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={2} sx={{ display: 'flex', gap: 1 }}>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<SearchIcon />}
                  fullWidth
                >
                  {t('common.search')}
                </Button>
                <Button
                  type="button"
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={handleReset}
                >
                  {t('common.reset')}
                </Button>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>

      <DataTable
        rows={itemMovementsData?.data || []}
        columns={columns}
        rowCount={itemMovementsData?.pagination?.total || 0}
        page={page}
        pageSize={pageSize}
        loading={isLoading}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        emptyContent={
          <Typography align="center" color="text.secondary" sx={{ py: 3 }}>
            {t('itemMovements.noItemMovements')}
          </Typography>
        }
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        title={t('itemMovements.delete.confirmTitle')}
        message={t('itemMovements.delete.confirmMessage')}
        onConfirm={handleDelete}
        onClose={() => setDeleteDialogOpen(false)}
        confirmLoading={deleteItemMovementMutation.isPending}
      />
    </Box>
  );
};

export default ItemMovementsPage;
