import { useState } from 'react';

interface ConfirmDialogOptions {
  title: string;
  message: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  confirmButtonColor?: 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
}

interface ConfirmDialogState extends ConfirmDialogOptions {
  isOpen: boolean;
  resolve: (value: boolean) => void;
}

/**
 * خطاف لعرض مربع حوار التأكيد
 */
export const useConfirm = () => {
  const [dialogState, setDialogState] = useState<ConfirmDialogState>({
    isOpen: false,
    title: '',
    message: '',
    confirmButtonText: 'تأكيد',
    cancelButtonText: 'إلغاء',
    confirmButtonColor: 'primary',
    resolve: () => {},
  });

  const showConfirm = (options: ConfirmDialogOptions): Promise<boolean> => {
    return new Promise<boolean>((resolve) => {
      setDialogState({
        isOpen: true,
        title: options.title,
        message: options.message,
        confirmButtonText: options.confirmButtonText || 'تأكيد',
        cancelButtonText: options.cancelButtonText || 'إلغاء',
        confirmButtonColor: options.confirmButtonColor || 'primary',
        resolve,
      });
    });
  };

  const handleConfirm = () => {
    dialogState.resolve(true);
    setDialogState({ ...dialogState, isOpen: false });
  };

  const handleCancel = () => {
    dialogState.resolve(false);
    setDialogState({ ...dialogState, isOpen: false });
  };

  return {
    showConfirm,
    dialogState,
    handleConfirm,
    handleCancel,
  };
};
