import { useMutation } from '@tanstack/react-query';
import authService from '@lib/services/auth.service';

/**
 * واجهة بيانات طلب نسيت كلمة المرور
 */
interface ForgotPasswordRequest {
  email: string;
}

/**
 * خطاف لإدارة طلب نسيت كلمة المرور
 * يستخدم لإرسال طلب إعادة تعيين كلمة المرور
 */
export const useForgotPassword = () => {
  return useMutation({
    mutationFn: async (data: ForgotPasswordRequest) => {
      return await authService.forgotPassword(data.email);
    },
  });
};
