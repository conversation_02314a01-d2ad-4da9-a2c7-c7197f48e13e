
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تقرير اختبارات الأداء - نظام النور للأرشفة</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    h1 {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 10px;
      border-bottom: 2px solid #3498db;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 12px 15px;
      text-align: right;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #3498db;
      color: white;
    }
    tr:nth-child(even) {
      background-color: #f2f2f2;
    }
    .summary {
      background-color: #e8f4f8;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .success {
      color: #27ae60;
    }
    .failure {
      color: #e74c3c;
    }
    .chart {
      width: 100%;
      height: 300px;
      margin-bottom: 30px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>تقرير اختبارات الأداء - نظام النور للأرشفة</h1>
    <p>تاريخ التقرير: ٢١‏/٥‏/٢٠٢٥، ٥:٢٢:١٦ ص</p>
    
    
    <h2>اختبارات أداء واجهة برمجة التطبيقات (API)</h2>
    <div class="summary">
      <h3>ملخص</h3>
      <p>متوسط الطلبات في الثانية: <strong>101.47</strong></p>
      <p>متوسط زمن الاستجابة: <strong>74.43 مللي ثانية</strong></p>
      <p>أقل زمن استجابة: <strong>35.50 مللي ثانية</strong></p>
      <p>أقصى زمن استجابة: <strong>320.50 مللي ثانية</strong></p>
      <p>متوسط معدل نقل البيانات: <strong>1002.6 كيلوبايت/ثانية</strong></p>
      <p>إجمالي الأخطاء: <strong>20</strong></p>
      <p>إجمالي الطلبات: <strong>10500</strong></p>
    </div>
    
    <h3>تفاصيل نقاط النهاية</h3>
    <table>
      <tr>
        <th>نقطة النهاية</th>
        <th>الطلبات/ثانية</th>
        <th>متوسط زمن الاستجابة (مللي ثانية)</th>
        <th>معدل نقل البيانات</th>
        <th>الأخطاء</th>
        <th>إجمالي الطلبات</th>
      </tr>
      
      <tr>
        <td>declarations.json</td>
        <td>120.50</td>
        <td>85.20</td>
        <td>1.19 ميجابايت/ثانية</td>
        <td>12</td>
        <td>5000</td>
      </tr>
      
      <tr>
        <td>guarantees.json</td>
        <td>95.20</td>
        <td>65.80</td>
        <td>957.03 كيلوبايت/ثانية</td>
        <td>5</td>
        <td>3000</td>
      </tr>
      
      <tr>
        <td>receipts.json</td>
        <td>88.70</td>
        <td>72.30</td>
        <td>830.08 كيلوبايت/ثانية</td>
        <td>3</td>
        <td>2500</td>
      </tr>
      
    </table>
    
    
    
    <h2>اختبارات أداء واجهة المستخدم (UI)</h2>
    <div class="summary">
      <h3>ملخص</h3>
      <p>متوسط وقت التحميل الكامل: <strong>1750.35 مللي ثانية</strong></p>
      <p>متوسط وقت DOM Content Loaded: <strong>85.20 مللي ثانية</strong></p>
      <p>متوسط حجم الصفحة: <strong>1.06 ميجابايت</strong></p>
      <p>متوسط استخدام ذاكرة JavaScript: <strong>25.27 ميجابايت</strong></p>
    </div>
    
    <h3>تفاصيل الصفحات</h3>
    <table>
      <tr>
        <th>الصفحة</th>
        <th>وقت التحميل (مللي ثانية)</th>
        <th>DOM Content Loaded (مللي ثانية)</th>
        <th>حجم الصفحة</th>
        <th>استخدام ذاكرة JavaScript</th>
      </tr>
      
      <tr>
        <td>declarations.json</td>
        <td>1850.50</td>
        <td>100.60</td>
        <td>1.19 ميجابايت</td>
        <td>26.7 ميجابايت</td>
      </tr>
      
      <tr>
        <td>guarantees.json</td>
        <td>1650.20</td>
        <td>69.80</td>
        <td>957.03 كيلوبايت</td>
        <td>23.84 ميجابايت</td>
      </tr>
      
    </table>
    
    
    
    <h2>اختبارات تجربة المستخدم (UX)</h2>
    <div class="summary">
      <h3>ملخص</h3>
      <p>معدل النجاح الإجمالي: <strong class="failure">66.67%</strong></p>
      <p>عدد الاختبارات الناجحة: <strong class="success">2</strong></p>
      <p>عدد الاختبارات الفاشلة: <strong class="failure">1</strong></p>
    </div>
    
    <h3>تفاصيل الاختبارات</h3>
    <table>
      <tr>
        <th>الاختبار</th>
        <th>عدد مرات التشغيل</th>
        <th>النجاح</th>
        <th>الفشل</th>
        <th>معدل النجاح</th>
      </tr>
      
      <tr>
        <td>declaration.json</td>
        <td>1</td>
        <td class="success">1</td>
        <td class="failure">0</td>
        <td class="success">100.00%</td>
      </tr>
      
      <tr>
        <td>login.json</td>
        <td>1</td>
        <td class="success">1</td>
        <td class="failure">0</td>
        <td class="success">100.00%</td>
      </tr>
      
      <tr>
        <td>mobile.json</td>
        <td>1</td>
        <td class="success">0</td>
        <td class="failure">1</td>
        <td class="failure">0.00%</td>
      </tr>
      
    </table>
    
    
    <h2>التوصيات</h2>
    <ul>
      
      <li>معالجة الأخطاء في واجهة برمجة التطبيقات.</li>
      
      
      <li>تحسين تجربة المستخدم لزيادة معدل نجاح الاختبارات.</li>
    </ul>
  </div>
</body>
</html>
  