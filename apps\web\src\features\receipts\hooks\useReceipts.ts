import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getReceipts,
  getReceipt,
  createReceipt,
  updateReceipt,
  deleteReceipt,
  downloadReceiptPdf
} from '../api/receipts.api';
import {
  ReceiptSearchParams,
  CreateReceiptDto,
  UpdateReceiptDto,
  CreateReceiptWithFileDto,
  UpdateReceiptWithFileDto
} from '../types/receipt.types';
import { useToast } from '@lib/hooks/useToast';
import { useTranslation } from 'react-i18next';

/**
 * خطاف للحصول على قائمة الاستلامات
 */
export const useReceipts = (params: ReceiptSearchParams = {}) => {
  return useQuery({
    queryKey: ['receipts', params],
    queryFn: () => getReceipts(params),
  });
};

/**
 * خطاف للحصول على استلام محدد
 */
export const useReceipt = (id: string) => {
  return useQuery({
    queryKey: ['receipts', id],
    queryFn: () => getReceipt(id),
    enabled: !!id,
  });
};

/**
 * خطاف لإنشاء استلام جديد
 */
export const useCreateReceipt = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ data, file }: CreateReceiptWithFileDto) => createReceipt(data, file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['receipts'] });
      showSuccess(t('receipts.createSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

/**
 * خطاف لتحديث استلام
 */
export const useUpdateReceipt = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, data, file }: UpdateReceiptWithFileDto) => updateReceipt(id, data, file),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['receipts'] });
      queryClient.invalidateQueries({ queryKey: ['receipts', variables.id] });
      showSuccess(t('receipts.updateSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

/**
 * خطاف لحذف استلام
 */
export const useDeleteReceipt = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => deleteReceipt(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['receipts'] });
      showSuccess(t('receipts.deleteSuccess'));
    },
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};

/**
 * خطاف لتحميل ملف PDF للاستلام
 */
export const useDownloadReceiptPdf = () => {
  const { showError } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (id: string) => downloadReceiptPdf(id),
    onError: (error: any) => {
      showError(error?.response?.data?.message || t('common.errorOccurred'));
    },
  });
};
