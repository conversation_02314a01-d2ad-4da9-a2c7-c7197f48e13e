import { useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
} from '@mui/material';
import { LazyImage } from '../../../components';
import {
  ChevronRight as ChevronRightIcon,
  Dashboard as DashboardIcon,
  Description as DescriptionIcon,
  SwapHoriz as SwapHorizIcon,
  Assignment as AssignmentIcon,
  Gavel as GavelIcon,
  FactCheck as FactCheckIcon,
  Security as SecurityIcon,
  Receipt as ReceiptIcon,
  People as PeopleIcon,
  Folder as FolderIcon,
  Search as SearchIcon,
  BarChart as BarChartIcon,
  Settings as SettingsIcon,
  Api as ApiIcon,
  DynamicForm as DynamicFormIcon,
  Storage as StorageIcon,
} from '@mui/icons-material';

interface SidebarProps {
  open: boolean;
  mobileOpen: boolean;
  drawerWidth: number;
  handleDrawerClose: () => void;
  handleDrawerToggle: () => void;
}

interface MenuItem {
  key: string;
  path: string;
  icon: JSX.Element;
  translationKey: string;
}

const Sidebar = ({
  open,
  mobileOpen,
  drawerWidth,
  handleDrawerClose,
  handleDrawerToggle,
}: SidebarProps) => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      path: '/dashboard',
      icon: <DashboardIcon />,
      translationKey: 'navigation.dashboard',
    },
    {
      key: 'declarations',
      path: '/declarations',
      icon: <DescriptionIcon />,
      translationKey: 'navigation.declarations',
    },
    {
      key: 'items-movement',
      path: '/items-movement',
      icon: <SwapHorizIcon />,
      translationKey: 'navigation.itemsMovement',
    },
    {
      key: 'authorizations',
      path: '/authorizations',
      icon: <AssignmentIcon />,
      translationKey: 'navigation.authorizations',
    },
    {
      key: 'releases',
      path: '/releases',
      icon: <GavelIcon />,
      translationKey: 'navigation.releases',
    },
    {
      key: 'permits',
      path: '/permits',
      icon: <FactCheckIcon />,
      translationKey: 'navigation.permits',
    },
    {
      key: 'guarantees',
      path: '/guarantees',
      icon: <SecurityIcon />,
      translationKey: 'navigation.guarantees',
    },
    {
      key: 'receipts',
      path: '/receipts',
      icon: <ReceiptIcon />,
      translationKey: 'navigation.receipts',
    },
    {
      key: 'clients',
      path: '/clients',
      icon: <PeopleIcon />,
      translationKey: 'navigation.clients',
    },
    {
      key: 'documents',
      path: '/documents',
      icon: <FolderIcon />,
      translationKey: 'navigation.documents',
    },
    {
      key: 'advanced-search',
      path: '/advanced-search',
      icon: <SearchIcon />,
      translationKey: 'navigation.advancedSearch',
    },
    {
      key: 'custom-forms',
      path: '/custom-forms',
      icon: <DynamicFormIcon />,
      translationKey: 'navigation.customForms',
    },
    {
      key: 'reports',
      path: '/reports',
      icon: <BarChartIcon />,
      translationKey: 'navigation.reports',
    },
    {
      key: 'database',
      path: '/database',
      icon: <StorageIcon />,
      translationKey: 'navigation.database',
    },
    {
      key: 'api-test',
      path: '/api-test',
      icon: <ApiIcon />,
      translationKey: 'navigation.apiTest',
    },
    {
      key: 'settings',
      path: '/settings',
      icon: <SettingsIcon />,
      translationKey: 'navigation.settings',
    },
  ];

  const drawer = (
    <div>
      <Toolbar
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          px: [1],
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LazyImage
            src="/assets/images/logos/logo.png"
            alt={t('app.name')}
            width={40}
            height={40}
            lazy={false} // لا نريد تأخير تحميل الشعار
          />
          <Typography variant="h6" noWrap component="div">
            {t('app.name')}
          </Typography>
        </Box>
        <IconButton onClick={handleDrawerClose}>
          <ChevronRightIcon />
        </IconButton>
      </Toolbar>
      <Divider />
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.key} disablePadding>
            <ListItemButton
              selected={location.pathname.startsWith(item.path)}
              onClick={() => navigate(item.path)}
            >
              <ListItemIcon>{item.icon}</ListItemIcon>
              <ListItemText primary={t(item.translationKey)} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </div>
  );

  return (
    <Box
      component="nav"
      sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      aria-label="mailbox folders"
    >
      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Desktop drawer */}
      <Drawer
        variant="persistent"
        sx={{
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
          },
        }}
        open={open}
      >
        {drawer}
      </Drawer>
    </Box>
  );
};

export default Sidebar;
