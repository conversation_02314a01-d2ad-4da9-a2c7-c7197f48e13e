import { describe, it, expect, beforeAll, afterEach, afterAll } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { createStore } from '../../../app/store/store';
import ReceiptsPage from '../pages/ReceiptsPage';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { arSA } from 'date-fns/locale';

// Mock API Server
const server = setupServer(
  http.get('http://localhost:3001/api/receipts', () => {
    return HttpResponse.json({
        success: true,
        data: [
          {
            id: '123',
            receiptNumber: 'R-001',
            receiptDate: '2023-01-01T00:00:00.000Z',
            receiverName: 'Test Receiver',
            receiverPhone: '123456789',
            receiverIdNumber: 'ID123456',
            itemsDescription: 'Test items',
            notes: 'Test notes',
            pdfFile: 'test.pdf',
            declarationId: '456',
            declarationNumber: 123,
            clientName: 'Test Client',
            createdAt: '2023-01-01T00:00:00.000Z',
            updatedAt: '2023-01-01T00:00:00.000Z',
          },
          {
            id: '124',
            receiptNumber: 'R-002',
            receiptDate: '2023-02-01T00:00:00.000Z',
            receiverName: 'Test Receiver 2',
            receiverPhone: '987654321',
            receiverIdNumber: 'ID654321',
            itemsDescription: 'Test items 2',
            notes: 'Test notes 2',
            pdfFile: 'test2.pdf',
            declarationId: '457',
            declarationNumber: 124,
            clientName: 'Test Client 2',
            createdAt: '2023-02-01T00:00:00.000Z',
            updatedAt: '2023-02-01T00:00:00.000Z',
          },
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      });
  })
);

// Setup
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock Redux Store
const store = createStore();

// Mock React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

// Test Component
const TestComponent = () => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={arSA}>
        <BrowserRouter>
          <ReceiptsPage />
        </BrowserRouter>
      </LocalizationProvider>
    </QueryClientProvider>
  </Provider>
);

describe('Receipts Integration Tests', () => {
  it('should fetch and display receipts', async () => {
    render(<TestComponent />);

    // التحقق من عرض رسالة التحميل
    expect(screen.getByText('receipts.title')).toBeInTheDocument();

    // التحقق من عرض الاستلامات بعد التحميل
    await waitFor(() => {
      expect(screen.getByText('R-001')).toBeInTheDocument();
      expect(screen.getByText('R-002')).toBeInTheDocument();
    });

    // التحقق من عرض تفاصيل الاستلامات
    expect(screen.getByText('Test Receiver')).toBeInTheDocument();
    expect(screen.getByText('Test Receiver 2')).toBeInTheDocument();
  });

  it('should display empty state when no receipts are available', async () => {
    // تعريف معالج وهمي لجلب قائمة استلامات فارغة
    server.use(
      http.get('http://localhost:3001/api/receipts', () => {
        return HttpResponse.json({
            success: true,
            data: [],
            pagination: {
              total: 0,
              page: 1,
              limit: 10,
              totalPages: 0,
            },
        });
      })
    );

    render(<TestComponent />);

    // التحقق من عرض رسالة عدم وجود استلامات
    await waitFor(() => {
      expect(screen.getByText('receipts.noReceipts')).toBeInTheDocument();
    });
  });

  it('should display error state when API request fails', async () => {
    // تعريف معالج وهمي لمحاكاة فشل طلب API
    server.use(
      http.get('http://localhost:3001/api/receipts', () => {
        return new HttpResponse(null, { status: 500 });
      })
    );

    render(<TestComponent />);

    // التحقق من عرض رسالة الخطأ
    await waitFor(() => {
      expect(screen.getByText('common.errorOccurred')).toBeInTheDocument();
    });
  });
});
