import axios from 'axios';
import { ItemMovement, PackageType, GoodsType, Currency } from '../types/item-movement.types';

// واجهة طلب إنشاء حركة الصنف
export interface CreateItemMovementRequest {
  declarationId: string;
  movementDate?: string;
  declarationNumber: number;
  itemNumber?: number;
  invoiceNumber: number;
  packingListNumber?: number;
  identificationClause?: number;
  itemName?: string;
  count?: number;
  packageType?: PackageType;
  goodsType?: GoodsType;
  countryOfOrigin?: string;
  itemValue?: number;
  currency?: Currency;
  totalValue?: number;
}

// واجهة طلب تحديث حركة الصنف
export interface UpdateItemMovementRequest {
  movementDate?: string;
  declarationNumber?: number;
  itemNumber?: number;
  invoiceNumber?: number;
  packingListNumber?: number;
  identificationClause?: number;
  itemName?: string;
  count?: number;
  packageType?: PackageType;
  goodsType?: GoodsType;
  countryOfOrigin?: string;
  itemValue?: number;
  currency?: Currency;
  totalValue?: number;
}

// واجهة معلمات البحث
export interface ItemMovementSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationId?: string;
  fromDate?: string;
  toDate?: string;
  goodsType?: GoodsType;
}

// واجهة استجابة قائمة حركات الأصناف
export interface ItemMovementsResponse {
  data: ItemMovement[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// واجهة استجابة حركة الصنف
export interface ItemMovementResponse {
  data: ItemMovement;
}

// دالة إنشاء حركة صنف جديدة
export const createItemMovement = async (
  data: CreateItemMovementRequest,
  file?: File
): Promise<ItemMovement> => {
  try {
    // إنشاء FormData لإرسال البيانات والملف
    const formData = new FormData();
    formData.append('data', JSON.stringify(data));
    
    if (file) {
      formData.append('file', file);
    }

    const response = await axios.post<ItemMovementResponse>('/api/items-movement', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء إنشاء حركة الصنف');
    }
    throw new Error('حدث خطأ أثناء إنشاء حركة الصنف');
  }
};

// دالة تحديث حركة صنف
export const updateItemMovement = async (
  id: string,
  data: UpdateItemMovementRequest,
  file?: File
): Promise<ItemMovement> => {
  try {
    // إنشاء FormData لإرسال البيانات والملف
    const formData = new FormData();
    formData.append('data', JSON.stringify(data));
    
    if (file) {
      formData.append('file', file);
    }

    const response = await axios.put<ItemMovementResponse>(`/api/items-movement/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء تحديث حركة الصنف');
    }
    throw new Error('حدث خطأ أثناء تحديث حركة الصنف');
  }
};

// دالة الحصول على حركة صنف محددة
export const getItemMovement = async (id: string): Promise<ItemMovement> => {
  try {
    const response = await axios.get<ItemMovementResponse>(`/api/items-movement/${id}`);
    return response.data.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء الحصول على حركة الصنف');
    }
    throw new Error('حدث خطأ أثناء الحصول على حركة الصنف');
  }
};

// دالة حذف حركة صنف
export const deleteItemMovement = async (id: string): Promise<void> => {
  try {
    await axios.delete(`/api/items-movement/${id}`);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء حذف حركة الصنف');
    }
    throw new Error('حدث خطأ أثناء حذف حركة الصنف');
  }
};

// دالة الحصول على قائمة حركات الأصناف
export const getItemMovements = async (
  params: ItemMovementSearchParams = {}
): Promise<ItemMovementsResponse> => {
  try {
    const response = await axios.get<ItemMovementsResponse>('/api/items-movement', {
      params,
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      throw new Error(error.response?.data?.message || 'حدث خطأ أثناء الحصول على قائمة حركات الأصناف');
    }
    throw new Error('حدث خطأ أثناء الحصول على قائمة حركات الأصناف');
  }
};
