import { describe, it, expect, beforeAll, afterEach, afterAll } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { createStore } from '../../../app/store/store';
import GuaranteesPage from '../pages/GuaranteesPage';
import { setupServer } from 'msw/node';
import { rest, HttpResponse } from '../../../test/integration-setup';
import { GuaranteeType, GuaranteeNature, GuaranteeCurrency } from '../types/guarantee.types';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { arSA } from 'date-fns/locale';

// Mock API Server
const server = setupServer(
  rest.get('http://localhost:3001/api/guarantees', () => {
    return HttpResponse.json({
        success: true,
        data: [
          {
            id: '123',
            guaranteeType: GuaranteeType.RETURNABLE,
            guaranteeNature: GuaranteeNature.DOCUMENTS,
            guaranteeNumber: 'G-001',
            issueDate: '2023-01-01T00:00:00.000Z',
            expiryDate: '2023-12-31T00:00:00.000Z',
            amount: 1000,
            currency: GuaranteeCurrency.SAR,
            notes: 'Test notes',
            pdfFile: 'test.pdf',
            declarationId: '456',
            declarationNumber: 123,
            clientName: 'Test Client',
            isReturned: false,
            createdAt: '2023-01-01T00:00:00.000Z',
            updatedAt: '2023-01-01T00:00:00.000Z',
          },
          {
            id: '124',
            guaranteeType: GuaranteeType.NON_RETURNABLE,
            guaranteeNature: GuaranteeNature.FINANCIAL,
            guaranteeNumber: 'G-002',
            issueDate: '2023-02-01T00:00:00.000Z',
            expiryDate: '2023-12-31T00:00:00.000Z',
            amount: 2000,
            currency: GuaranteeCurrency.USD,
            notes: 'Test notes 2',
            pdfFile: 'test2.pdf',
            declarationId: '457',
            declarationNumber: 124,
            clientName: 'Test Client 2',
            isReturned: false,
            createdAt: '2023-02-01T00:00:00.000Z',
            updatedAt: '2023-02-01T00:00:00.000Z',
          },
        ],
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      });
  })
);

// Setup
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock Redux Store
const store = createStore();

// Mock React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

// Test Component
const TestComponent = () => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={arSA}>
        <BrowserRouter>
          <GuaranteesPage />
        </BrowserRouter>
      </LocalizationProvider>
    </QueryClientProvider>
  </Provider>
);

describe('Guarantees Integration Tests', () => {
  it('should fetch and display guarantees', async () => {
    render(<TestComponent />);

    // التحقق من عرض رسالة التحميل
    expect(screen.getByText('guarantees.title')).toBeInTheDocument();

    // التحقق من عرض الضمانات بعد التحميل
    await waitFor(() => {
      expect(screen.getByText('G-001')).toBeInTheDocument();
      expect(screen.getByText('G-002')).toBeInTheDocument();
    });

    // التحقق من عرض تفاصيل الضمانات
    expect(screen.getByText('1000 SAR')).toBeInTheDocument();
    expect(screen.getByText('2000 USD')).toBeInTheDocument();
  });

  it('should display empty state when no guarantees are available', async () => {
    // تعريف معالج وهمي لجلب قائمة ضمانات فارغة
    server.use(
      rest.get('http://localhost:3001/api/guarantees', () => {
        return HttpResponse.json({
            success: true,
            data: [],
            pagination: {
              total: 0,
              page: 1,
              limit: 10,
              totalPages: 0,
            },
          });
      })
    );

    render(<TestComponent />);

    // التحقق من عرض رسالة عدم وجود ضمانات
    await waitFor(() => {
      expect(screen.getByText('guarantees.noGuarantees')).toBeInTheDocument();
    });
  });

  it('should display error state when API request fails', async () => {
    // تعريف معالج وهمي لمحاكاة فشل طلب API
    server.use(
      rest.get('http://localhost:3001/api/guarantees', () => {
        return new HttpResponse(null, { status: 500 });
      })
    );

    render(<TestComponent />);

    // التحقق من عرض رسالة الخطأ
    await waitFor(() => {
      expect(screen.getByText('common.errorOccurred')).toBeInTheDocument();
    });
  });
});
