import { prisma } from '../../../core/utils/prisma.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';
import { saveUploadedPdf } from '../../../core/utils/pdf/pdfService.js';
import { Prisma } from '@prisma/client';
import { GuaranteeStatus, Currency } from '@prisma/client';

interface CreateGuaranteeInput {
  declarationId: string;
  guaranteeNumber: string;
  amount: number;
  currency: Currency;
  issueDate: Date;
  expiryDate?: Date;
  status?: GuaranteeStatus;
  notes?: string;
  guaranteeAmount?: number;
  guaranteeDate?: Date;
  endDate?: Date;
}

interface UpdateGuaranteeInput {
  declarationId?: string;
  guaranteeNumber?: string;
  amount?: number;
  currency?: Currency;
  issueDate?: Date;
  expiryDate?: Date;
  status?: GuaranteeStatus;
  notes?: string;
}

interface ListGuaranteesParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  declarationId?: string;
  fromDate?: Date;
  toDate?: Date;
  isActive?: boolean;
}

export const guaranteeService = {
  /**
   * إنشاء ضمان جديد
   */
  createGuarantee: async (
    data: CreateGuaranteeInput,
    _userId: string, // تم تغيير الاسم لتجنب تحذير عدم الاستخدام
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود البيان
      const declaration = await prisma.declaration.findUnique({
        where: { id: data.declarationId },
      });

      if (!declaration) {
        throw new HttpException(404, 'البيان غير موجود', 'Not Found');
      }

      // حفظ ملف PDF إذا تم تقديمه
      let pdfFile: string | undefined;
      if (file) {
        pdfFile = saveUploadedPdf(file, 'guarantees', `guarantee_${data.guaranteeNumber}`);
      }

      // إنشاء الضمان
      const guarantee = await prisma.guarantee.create({
        data: {
          guaranteeNumber: data.guaranteeNumber,
          amount: data.guaranteeAmount || 0,
          currency: data.currency || Currency.SAR,
          issueDate: data.guaranteeDate || new Date(),
          expiryDate: data.endDate,
          status: GuaranteeStatus.ACTIVE,
          notes: data.notes,
          pdfFile,
          declarationId: data.declarationId,
        },
      });

      return guarantee;
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new HttpException(400, 'رقم الضمان موجود بالفعل', 'Bad Request');
        }
      }
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء إنشاء الضمان', 'Internal Server Error');
    }
  },

  /**
   * تحديث ضمان
   */
  updateGuarantee: async (
    id: string,
    data: UpdateGuaranteeInput,
    _userId: string, // تم تغيير الاسم لتجنب تحذير عدم الاستخدام
    file?: Express.Multer.File
  ) => {
    try {
      // التحقق من وجود الضمان
      const existingGuarantee = await prisma.guarantee.findUnique({
        where: { id },
      });

      if (!existingGuarantee) {
        throw new HttpException(404, 'الضمان غير موجود', 'Not Found');
      }

      // تم إزالة التحقق من صلاحية المستخدم لأن نموذج الضمان لا يحتوي على حقل userId

      // التحقق من وجود البيان إذا تم تغييره
      if (data.declarationId) {
        const declaration = await prisma.declaration.findUnique({
          where: { id: data.declarationId },
        });

        if (!declaration) {
          throw new HttpException(404, 'البيان غير موجود', 'Not Found');
        }
      }

      // حفظ ملف PDF إذا تم تقديمه
      let pdfFile = existingGuarantee.pdfFile;
      if (file) {
        pdfFile = saveUploadedPdf(file, 'guarantees', id);
      }

      // تحديث الضمان
      const guarantee = await prisma.guarantee.update({
        where: { id },
        data: {
          guaranteeNumber: data.guaranteeNumber,
          amount: data.amount,
          currency: data.currency,
          issueDate: data.issueDate,
          expiryDate: data.expiryDate,
          status: data.status,
          notes: data.notes,
          pdfFile,
          declarationId: data.declarationId,
        },
      });

      return guarantee;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(500, 'حدث خطأ أثناء تحديث الضمان', 'Internal Server Error');
    }
  },

  /**
   * الحصول على ضمان محدد
   */
  getGuarantee: async (id: string) => {
    const guarantee = await prisma.guarantee.findUnique({
      where: { id },
      include: {
        declaration: true,
      },
    });

    if (!guarantee) {
      throw new HttpException(404, 'الضمان غير موجود', 'Not Found');
    }

    return guarantee;
  },

  /**
   * حذف ضمان
   */
  deleteGuarantee: async (id: string, _userId: string) => {
    // التحقق من وجود الضمان
    const guarantee = await prisma.guarantee.findUnique({
      where: { id },
    });

    if (!guarantee) {
      throw new HttpException(404, 'الضمان غير موجود', 'Not Found');
    }

    // تم إزالة التحقق من صلاحية المستخدم لأن نموذج الضمان لا يحتوي على حقل userId

    // حذف الضمان
    await prisma.guarantee.delete({
      where: { id },
    });

    return { success: true };
  },

  /**
   * الحصول على قائمة الضمانات
   */
  listGuarantees: async (params: ListGuaranteesParams = {}) => {
    const {
      page = 1,
      limit = 10,
      sort = 'createdAt',
      order = 'desc',
      search,
      declarationId,
      fromDate,
      toDate,
      isActive,
    } = params;

    // بناء شروط البحث
    const where: Prisma.GuaranteeWhereInput = {};

    if (search) {
      where.OR = [
        { notes: { contains: search } },
        { guaranteeNumber: { contains: search } },
      ];
    }

    if (declarationId) {
      where.declarationId = declarationId;
    }

    if (fromDate && toDate) {
      where.issueDate = {
        gte: fromDate,
        lte: toDate,
      };
    } else if (fromDate) {
      where.issueDate = {
        gte: fromDate,
      };
    } else if (toDate) {
      where.issueDate = {
        lte: toDate,
      };
    }

    // التحقق من حالة الضمان (نشط أو منتهي)
    if (isActive !== undefined && isActive !== null) {
      const now = new Date();
      if (isActive) {
        where.expiryDate = {
          gte: now,
        };
      } else {
        where.expiryDate = {
          lt: now,
        };
      }
    }

    // حساب إجمالي عدد الضمانات
    const total = await prisma.guarantee.count({ where });

    // الحصول على الضمانات
    const guarantees = await prisma.guarantee.findMany({
      where,
      include: {
        declaration: true,
      },
      orderBy: {
        [sort]: order,
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      data: guarantees,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  },
};
