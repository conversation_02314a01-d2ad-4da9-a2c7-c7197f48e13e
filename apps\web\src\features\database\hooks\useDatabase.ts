import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getBackups,
  createBackup,
  restoreBackup,
  deleteBackup,
  exportDatabase,
  initializeDatabase,
  BackupInfo,
} from '../api/database.api';
import { useToast } from '@lib/hooks/useToast';
import { useTranslation } from 'react-i18next';

/**
 * خطاف للحصول على قائمة النسخ الاحتياطية
 */
export const useBackups = () => {
  return useQuery({
    queryKey: ['backups'],
    queryFn: getBackups,
  });
};

/**
 * خطاف لإنشاء نسخة احتياطية جديدة
 */
export const useCreateBackup = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: createBackup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backups'] });
      toast.showSuccess(t('database.backupCreated'));
    },
    onError: (error) => {
      console.error('Error creating backup:', error);
      toast.showError(t('database.backupCreateError'));
    },
  });
};

/**
 * خطاف لاستعادة قاعدة البيانات من نسخة احتياطية
 */
export const useRestoreBackup = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (fileName: string) => restoreBackup(fileName),
    onSuccess: () => {
      queryClient.invalidateQueries();
      toast.showSuccess(t('database.backupRestored'));
    },
    onError: (error) => {
      console.error('Error restoring backup:', error);
      toast.showError(t('database.backupRestoreError'));
    },
  });
};

/**
 * خطاف لحذف نسخة احتياطية
 */
export const useDeleteBackup = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: (fileName: string) => deleteBackup(fileName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backups'] });
      toast.showSuccess(t('database.backupDeleted'));
    },
    onError: (error) => {
      console.error('Error deleting backup:', error);
      toast.showError(t('database.backupDeleteError'));
    },
  });
};

/**
 * خطاف لتهيئة قاعدة البيانات
 */
export const useInitializeDatabase = () => {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: initializeDatabase,
    onSuccess: () => {
      queryClient.invalidateQueries();
      toast.showSuccess(t('database.databaseInitialized'));
    },
    onError: (error) => {
      console.error('Error initializing database:', error);
      toast.showError(t('database.databaseInitializeError'));
    },
  });
};
