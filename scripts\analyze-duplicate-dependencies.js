#!/usr/bin/env node

/**
 * سكريبت تحليل المكتبات المكررة والتحسينات المحتملة
 * يبحث عن المكتبات المكررة عبر المشاريع والبدائل الأفضل
 */

const fs = require('fs');
const path = require('path');

/**
 * قراءة package.json
 */
function readPackageJson(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`خطأ في قراءة ${filePath}:`, error.message);
    return null;
  }
}

/**
 * تحليل المكتبات المكررة
 */
function analyzeDuplicates() {
  const packages = {
    root: readPackageJson('package.json'),
    api: readPackageJson('apps/api/package.json'),
    web: readPackageJson('apps/web/package.json')
  };

  const allDeps = {};
  const duplicates = {};
  const suggestions = [];

  // جمع جميع التبعيات
  Object.keys(packages).forEach(project => {
    const pkg = packages[project];
    if (!pkg) return;

    const deps = { ...pkg.dependencies || {}, ...pkg.devDependencies || {} };
    Object.keys(deps).forEach(dep => {
      if (!allDeps[dep]) {
        allDeps[dep] = [];
      }
      allDeps[dep].push({
        project,
        version: deps[dep],
        type: pkg.dependencies?.[dep] ? 'dependency' : 'devDependency'
      });
    });
  });

  // البحث عن المكررات
  Object.keys(allDeps).forEach(dep => {
    if (allDeps[dep].length > 1) {
      duplicates[dep] = allDeps[dep];
    }
  });

  // اقتراحات التحسين
  const improvements = analyzeImprovements(packages);

  return { duplicates, improvements, packages };
}

/**
 * تحليل التحسينات المحتملة
 */
function analyzeImprovements(packages) {
  const improvements = [];

  // 1. مكتبات يمكن نقلها للجذر
  const commonDevDeps = [
    '@typescript-eslint/eslint-plugin',
    '@typescript-eslint/parser',
    'eslint',
    'typescript',
    '@types/node'
  ];

  commonDevDeps.forEach(dep => {
    const projects = [];
    Object.keys(packages).forEach(project => {
      if (project === 'root') return;
      const pkg = packages[project];
      if (pkg?.devDependencies?.[dep]) {
        projects.push(project);
      }
    });

    if (projects.length > 1) {
      improvements.push({
        type: 'move-to-root',
        dependency: dep,
        projects,
        reason: 'مكتبة تطوير مشتركة يمكن نقلها للجذر'
      });
    }
  });

  // 2. مكتبات متشابهة يمكن توحيدها
  const similarLibs = [
    {
      libs: ['date-fns', 'dayjs'],
      suggestion: 'استخدم مكتبة واحدة فقط للتواريخ',
      recommended: 'date-fns'
    },
    {
      libs: ['@dnd-kit/core', 'react-beautiful-dnd'],
      suggestion: 'استخدم مكتبة واحدة فقط للسحب والإفلات',
      recommended: '@dnd-kit/core'
    }
  ];

  similarLibs.forEach(group => {
    const found = [];
    Object.keys(packages).forEach(project => {
      const pkg = packages[project];
      if (!pkg) return;
      
      group.libs.forEach(lib => {
        if (pkg.dependencies?.[lib] || pkg.devDependencies?.[lib]) {
          found.push({ project, lib });
        }
      });
    });

    if (found.length > 1) {
      improvements.push({
        type: 'consolidate',
        libraries: found,
        suggestion: group.suggestion,
        recommended: group.recommended
      });
    }
  });

  // 3. مكتبات قديمة تحتاج تحديث
  const outdatedLibs = [
    {
      name: 'react',
      currentVersion: '18.3.1',
      latestVersion: '19.1.0',
      breaking: true
    },
    {
      name: 'eslint',
      currentVersion: '8.57.1',
      latestVersion: '9.x',
      breaking: true
    }
  ];

  outdatedLibs.forEach(lib => {
    Object.keys(packages).forEach(project => {
      const pkg = packages[project];
      if (!pkg) return;
      
      const currentVer = pkg.dependencies?.[lib.name] || pkg.devDependencies?.[lib.name];
      if (currentVer && currentVer.includes(lib.currentVersion)) {
        improvements.push({
          type: 'update',
          dependency: lib.name,
          project,
          currentVersion: lib.currentVersion,
          latestVersion: lib.latestVersion,
          breaking: lib.breaking
        });
      }
    });
  });

  return improvements;
}

/**
 * إنشاء تقرير التحليل
 */
function generateReport(analysis) {
  const { duplicates, improvements } = analysis;
  
  let content = `# 🔄 تحليل المكتبات المكررة والتحسينات - مشروع AlnoorArch

**تاريخ التحليل**: ${new Date().toLocaleString('ar-EG')}  
**المكتبات المكررة**: ${Object.keys(duplicates).length}  
**اقتراحات التحسين**: ${improvements.length}

---

## 📊 المكتبات المكررة

`;

  if (Object.keys(duplicates).length === 0) {
    content += `✅ لا توجد مكتبات مكررة بإصدارات مختلفة

`;
  } else {
    Object.keys(duplicates).forEach(dep => {
      content += `### **${dep}**
`;
      duplicates[dep].forEach(usage => {
        content += `- **${usage.project}**: ${usage.version} (${usage.type})
`;
      });
      content += `
`;
    });
  }

  content += `---

## 💡 اقتراحات التحسين

`;

  if (improvements.length === 0) {
    content += `✅ لا توجد تحسينات مقترحة حالياً

`;
  } else {
    improvements.forEach((improvement, index) => {
      content += `### ${index + 1}. `;
      
      switch (improvement.type) {
        case 'move-to-root':
          content += `نقل ${improvement.dependency} للجذر
**السبب**: ${improvement.reason}
**المشاريع المتأثرة**: ${improvement.projects.join(', ')}

`;
          break;
          
        case 'consolidate':
          content += `توحيد مكتبات متشابهة
**المكتبات**: ${improvement.libraries.map(l => `${l.lib} (${l.project})`).join(', ')}
**الاقتراح**: ${improvement.suggestion}
**المكتبة المُوصى بها**: ${improvement.recommended}

`;
          break;
          
        case 'update':
          content += `تحديث ${improvement.dependency}
**المشروع**: ${improvement.project}
**الإصدار الحالي**: ${improvement.currentVersion}
**أحدث إصدار**: ${improvement.latestVersion}
**تغييرات جذرية**: ${improvement.breaking ? 'نعم ⚠️' : 'لا ✅'}

`;
          break;
      }
    });
  }

  content += `---

## 🛠️ خطة التنفيذ المقترحة

### **المرحلة الأولى: تنظيف المكررات**
`;

  const moveToRootSuggestions = improvements.filter(i => i.type === 'move-to-root');
  if (moveToRootSuggestions.length > 0) {
    content += `
1. **نقل التبعيات المشتركة للجذر**:
\`\`\`bash
# إضافة للجذر
`;
    moveToRootSuggestions.forEach(suggestion => {
      content += `pnpm add -D ${suggestion.dependency}
`;
    });
    
    content += `
# حذف من المشاريع الفرعية
`;
    moveToRootSuggestions.forEach(suggestion => {
      suggestion.projects.forEach(project => {
        content += `cd apps/${project} && pnpm remove ${suggestion.dependency}
`;
      });
    });
    content += `\`\`\`

`;
  }

  const consolidateSuggestions = improvements.filter(i => i.type === 'consolidate');
  if (consolidateSuggestions.length > 0) {
    content += `
2. **توحيد المكتبات المتشابهة**:
`;
    consolidateSuggestions.forEach(suggestion => {
      content += `   - ${suggestion.suggestion}
   - احتفظ بـ ${suggestion.recommended} واحذف البقية
`;
    });
  }

  content += `
### **المرحلة الثانية: التحديثات (اختيارية)**
`;

  const updateSuggestions = improvements.filter(i => i.type === 'update');
  if (updateSuggestions.length > 0) {
    content += `
⚠️ **تحذير**: هذه التحديثات قد تحتوي على تغييرات جذرية

`;
    updateSuggestions.forEach(suggestion => {
      content += `- **${suggestion.dependency}**: ${suggestion.currentVersion} → ${suggestion.latestVersion}
`;
    });
  }

  content += `

---

## 📈 الفوائد المتوقعة

1. **تقليل حجم node_modules** بنسبة 10-15%
2. **تحسين أوقات التثبيت** بنسبة 20%
3. **تبسيط إدارة التبعيات**
4. **تقليل التعارضات المحتملة**

---

## ⚠️ تحذيرات

1. **اختبر كل تغيير** على حدة
2. **احتفظ بنسخة احتياطية** من package.json
3. **تأكد من البناء** بعد كل تعديل
4. **راجع التبعيات المتداخلة**

---

**📝 ملاحظة**: هذا التحليل يهدف لتحسين بنية المشروع وليس حذف مكتبات مستخدمة.
`;

  return content;
}

/**
 * الدالة الرئيسية
 */
function main() {
  console.log('🔄 بدء تحليل المكتبات المكررة والتحسينات...\n');

  const analysis = analyzeDuplicates();
  const report = generateReport(analysis);

  const reportPath = path.join(__dirname, '..', 'docs', 'duplicate-dependencies-analysis.md');
  fs.writeFileSync(reportPath, report, 'utf8');

  console.log('📊 ملخص التحليل:');
  console.log(`   المكتبات المكررة: ${Object.keys(analysis.duplicates).length}`);
  console.log(`   اقتراحات التحسين: ${analysis.improvements.length}`);
  console.log(`\n✅ تم حفظ التقرير في: ${reportPath}`);
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = { analyzeDuplicates, generateReport };
