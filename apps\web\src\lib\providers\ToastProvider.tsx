import { ReactNode } from 'react';
import { SnackbarProvider } from 'notistack';

interface ToastProviderProps {
  children: ReactNode;
}

/**
 * مزود رسائل التنبيه (Toast)
 * يستخدم مكتبة notistack لعرض رسائل التنبيه بطريقة سهلة
 */
export const ToastProvider = ({ children }: ToastProviderProps) => {
  return (
    <SnackbarProvider
      maxSnack={3}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'left',
      }}
      autoHideDuration={3000}
    >
      {children}
    </SnackbarProvider>
  );
};

export default ToastProvider;
