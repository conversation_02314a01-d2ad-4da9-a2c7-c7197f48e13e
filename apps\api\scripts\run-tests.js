#!/usr/bin/env node

/**
 * Script لتشغيل الاختبارات مع تبديل schema تلقائي
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runCommand(command, args = []) {
  return new Promise((resolve, reject) => {
    console.log(`🔧 تشغيل: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      cwd: path.join(__dirname, '..')
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function runTests() {
  let testExitCode = 0;
  
  try {
    // 1. إعداد schema للاختبارات
    console.log('📋 إعداد schema للاختبارات...');
    await runCommand('node', ['scripts/setup-test-schema.js', 'setup']);
    
    // 2. إنشاء Prisma Client
    console.log('⚙️ إنشاء Prisma Client...');
    await runCommand('prisma', ['generate']);
    
    // 3. تشغيل الاختبارات
    console.log('🧪 تشغيل الاختبارات...');
    const jestArgs = ['--experimental-vm-modules', 'node_modules/jest/bin/jest.js'];
    
    // إضافة معاملات Jest من command line
    const additionalArgs = process.argv.slice(2);
    jestArgs.push(...additionalArgs);
    
    await runCommand('node', jestArgs);
    
  } catch (error) {
    console.error('❌ فشل في تشغيل الاختبارات:', error.message);
    testExitCode = 1;
  } finally {
    // 4. استعادة schema الأصلي (دائماً)
    try {
      console.log('🔄 استعادة schema الأصلي...');
      await runCommand('node', ['scripts/setup-test-schema.js', 'restore']);
    } catch (restoreError) {
      console.error('❌ فشل في استعادة schema:', restoreError.message);
      testExitCode = 1;
    }
  }
  
  process.exit(testExitCode);
}

runTests();
