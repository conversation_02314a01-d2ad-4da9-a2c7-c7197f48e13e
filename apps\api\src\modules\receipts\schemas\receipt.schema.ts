import { z } from 'zod';

// مخطط إنشاء استلام جديد
export const createReceiptSchema = z.object({
  body: z.object({
    declarationId: z.string({
      required_error: 'معرف البيان مطلوب',
    }).uuid(),
    receiptDate: z.string({
      required_error: 'تاريخ الاستلام مطلوب',
    }).transform(val => new Date(val)),
    receiptNumber: z.string({
      required_error: 'رقم الاستلام مطلوب',
    }),
    receiverName: z.string({
      required_error: 'اسم المستلم مطلوب',
    }),
    receiverPhone: z.string().optional(),
    receiverIdNumber: z.string().optional(),
    itemsDescription: z.string().optional(),
    notes: z.string().optional(),
  }),
});

// مخطط تحديث استلام
export const updateReceiptSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف الاستلام مطلوب',
    }),
  }),
  body: z.object({
    declarationId: z.string().uuid().optional(),
    receiptDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    receiptNumber: z.string().optional(),
    receiverName: z.string().optional(),
    receiverPhone: z.string().optional(),
    receiverIdNumber: z.string().optional(),
    itemsDescription: z.string().optional(),
    notes: z.string().optional(),
  }),
});

// مخطط الحصول على استلام محدد
export const getReceiptSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف الاستلام مطلوب',
    }),
  }),
});

// مخطط حذف استلام
export const deleteReceiptSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف الاستلام مطلوب',
    }),
  }),
});

// مخطط قائمة الاستلامات
export const listReceiptsSchema = z.object({
  query: z.object({
    page: z.string().optional().transform((val) => (val ? parseInt(val) : 1)),
    limit: z.string().optional().transform((val) => (val ? parseInt(val) : 10)),
    sort: z.string().optional().default('receiptDate'),
    order: z.enum(['asc', 'desc']).optional().default('desc'),
    search: z.string().optional(),
    declarationId: z.string().uuid().optional(),
    fromDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    toDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
  }),
});
