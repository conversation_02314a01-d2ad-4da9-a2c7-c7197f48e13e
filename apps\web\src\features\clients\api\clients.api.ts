import axios from 'axios';
import { Client } from '../types/client.types';

// واجهة طلب إنشاء العميل
export interface CreateClientRequest {
  taxNumber: string;
  name: string;
  companyName?: string;
  phone?: string;
  email?: string;
  address?: string;
  contactPerson?: string;
  contactPhone?: string;
  notes?: string;
}

// واجهة طلب تحديث العميل
export interface UpdateClientRequest {
  taxNumber?: string;
  name?: string;
  companyName?: string;
  phone?: string;
  email?: string;
  address?: string;
  contactPerson?: string;
  contactPhone?: string;
  notes?: string;
}

// واجهة معلمات البحث عن العملاء
export interface ClientSearchParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
  search?: string;
  taxNumber?: string;
}

// الحصول على قائمة العملاء
export const getClients = async (params: ClientSearchParams = {}) => {
  const { data } = await axios.get('/api/clients', { params });
  return data;
};

// الحصول على عميل محدد
export const getClient = async (id: string) => {
  const { data } = await axios.get(`/api/clients/${id}`);
  return data.data;
};

// إنشاء عميل جديد
export const createClient = async (clientData: CreateClientRequest) => {
  const { data } = await axios.post('/api/clients', clientData);
  return data.data;
};

// تحديث عميل
export const updateClient = async (id: string, clientData: UpdateClientRequest) => {
  const { data } = await axios.put(`/api/clients/${id}`, clientData);
  return data.data;
};

// حذف عميل
export const deleteClient = async (id: string) => {
  const { data } = await axios.delete(`/api/clients/${id}`);
  return data;
};
