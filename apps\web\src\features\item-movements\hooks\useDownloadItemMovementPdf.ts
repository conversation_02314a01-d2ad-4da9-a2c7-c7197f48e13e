import { useMutation } from '@tanstack/react-query';
import { api } from '../../../lib/api/api';

export const useDownloadItemMovementPdf = () => {
  return useMutation({
    mutationFn: async (id: string): Promise<Blob> => {
      const response = await api.downloadFile(`/api/item-movements/${id}/pdf`);
      return response;
    },
    onSuccess: (blob, id) => {
      // إنشاء رابط تحميل تلقائي
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `item-movement-${id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
  });
};
