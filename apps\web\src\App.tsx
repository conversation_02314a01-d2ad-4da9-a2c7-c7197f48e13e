import { Suspense } from 'react';
import { CircularProgress, Box } from '@mui/material';
import AppRouter from './app/router/AppRouter';

function App() {
  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <AppRouter />
    </Suspense>
  );
}

export default App;
