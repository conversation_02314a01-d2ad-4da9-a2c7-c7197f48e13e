import apiClient from './apiClient';
import { cacheApiResponse } from './apiCache';
import { AxiosRequestConfig } from 'axios';

/**
 * واجهة API موحدة للتطبيق
 * تستخدم apiClient للاتصال بالخادم وتدعم التخزين المؤقت
 */
const api = {
  /**
   * طلب GET
   * @param url مسار الطلب
   * @param config إعدادات الطلب
   * @param useCache استخدام التخزين المؤقت
   */
  get: async <T>(url: string, config?: AxiosRequestConfig, useCache: boolean = false): Promise<T> => {
    if (useCache) {
      return cacheApiResponse(() => apiClient.get<T>(url, config).then(data => ({ data })), url, config);
    }
    return apiClient.get<T>(url, config);
  },

  /**
   * طلب POST
   * @param url مسار الطلب
   * @param data البيانات المرسلة
   * @param config إعدادات الطلب
   */
  post: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return apiClient.post<T>(url, data, config);
  },

  /**
   * طلب PUT
   * @param url مسار الطلب
   * @param data البيانات المرسلة
   * @param config إعدادات الطلب
   */
  put: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return apiClient.put<T>(url, data, config);
  },

  /**
   * طلب PATCH
   * @param url مسار الطلب
   * @param data البيانات المرسلة
   * @param config إعدادات الطلب
   */
  patch: async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return apiClient.patch<T>(url, data, config);
  },

  /**
   * طلب DELETE
   * @param url مسار الطلب
   * @param config إعدادات الطلب
   */
  delete: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return apiClient.delete<T>(url, config);
  },

  /**
   * تحميل ملف
   * @param url مسار الطلب
   * @param data البيانات المرسلة
   * @param config إعدادات الطلب
   */
  uploadFile: async <T>(url: string, data: FormData, config?: AxiosRequestConfig): Promise<T> => {
    const uploadConfig = {
      ...config,
      headers: {
        ...config?.headers,
        'Content-Type': 'multipart/form-data',
      },
    };
    return apiClient.post<T>(url, data, uploadConfig);
  },

  /**
   * تنزيل ملف
   * @param url مسار الطلب
   * @param config إعدادات الطلب
   */
  downloadFile: async (url: string, config?: AxiosRequestConfig): Promise<Blob> => {
    const downloadConfig = {
      ...config,
      responseType: 'blob' as 'blob',
    };
    return apiClient.get<Blob>(url, downloadConfig);
  },

  /**
   * طلب عام مع خيارات مخصصة
   */
  request: async <T>(options: AxiosRequestConfig): Promise<T> => {
    const { method = 'GET', url = '', data, ...config } = options;

    switch (method.toUpperCase()) {
      case 'POST':
        return apiClient.post<T>(url, data, config);
      case 'PUT':
        return apiClient.put<T>(url, data, config);
      case 'PATCH':
        return apiClient.patch<T>(url, data, config);
      case 'DELETE':
        return apiClient.delete<T>(url, config);
      default:
        return apiClient.get<T>(url, config);
    }
  },
};

export { api };
export default api;
