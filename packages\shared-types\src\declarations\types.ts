import { BaseEntity, BaseSearchParams, Client } from '../common/types';
import { DeclarationType, GoodsType } from '../common/enums';

/**
 * نوع البيان
 */
export interface Declaration extends BaseEntity {
  declarationNumber: string;
  taxNumber: string;
  clientName: string;
  companyName?: string;
  policyNumber?: string;
  invoiceNumber?: string;
  gatewayEntryNumber?: string;
  declarationType: DeclarationType;
  declarationDate: Date;
  count?: number;
  weight?: number;
  goodsType?: GoodsType;
  itemsCount?: number;
  entryDate?: Date;
  exitDate?: Date;
  pdfFile?: string;
  client?: Client;
  drivers?: Driver[];
}

/**
 * نوع السائق
 */
export interface Driver extends BaseEntity {
  declarationId: string;
  driverName: string;
  truckNumber: string;
  trailerNumber?: string;
  driverPhone?: string;
  declaration?: Declaration;
}

/**
 * نوع معلمات البحث عن البيانات
 */
export interface DeclarationSearchParams extends BaseSearchParams {
  declarationNumber?: string;
  taxNumber?: string;
  clientName?: string;
  declarationType?: DeclarationType;
  goodsType?: GoodsType;
  clientId?: string;
}

/**
 * نوع إنشاء بيان جديد
 */
export interface CreateDeclarationDto {
  declarationNumber: string;
  taxNumber: string;
  clientName: string;
  companyName?: string;
  policyNumber?: string;
  invoiceNumber?: string;
  gatewayEntryNumber?: string;
  declarationType: DeclarationType;
  declarationDate: Date;
  count?: number;
  weight?: number;
  goodsType?: GoodsType;
  itemsCount?: number;
  entryDate?: Date;
  exitDate?: Date;
  clientId?: string;
  drivers?: CreateDriverDto[];
}

/**
 * نوع إنشاء بيان جديد مع ملف
 */
export interface CreateDeclarationWithFileDto extends CreateDeclarationDto {
  file?: File;
}

/**
 * نوع تحديث بيان
 */
export interface UpdateDeclarationDto {
  declarationNumber?: string;
  taxNumber?: string;
  clientName?: string;
  companyName?: string;
  policyNumber?: string;
  invoiceNumber?: string;
  gatewayEntryNumber?: string;
  declarationType?: DeclarationType;
  declarationDate?: Date;
  count?: number;
  weight?: number;
  goodsType?: GoodsType;
  itemsCount?: number;
  entryDate?: Date;
  exitDate?: Date;
  clientId?: string;
  drivers?: CreateDriverDto[];
}

/**
 * نوع تحديث بيان مع ملف
 */
export interface UpdateDeclarationWithFileDto extends UpdateDeclarationDto {
  file?: File;
}

/**
 * نوع إنشاء سائق جديد
 */
export interface CreateDriverDto {
  driverName: string;
  truckNumber: string;
  trailerNumber?: string;
  driverPhone?: string;
}

/**
 * نوع تحديث سائق
 */
export interface UpdateDriverDto {
  driverName?: string;
  truckNumber?: string;
  trailerNumber?: string;
  driverPhone?: string;
}
