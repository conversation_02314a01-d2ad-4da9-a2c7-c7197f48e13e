import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  TextField,
  MenuItem,
  Typography,
  Alert,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIndicatorIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useCustomForm } from '../hooks/useCustomForm';
import { useCreateCustomForm } from '../hooks/useCreateCustomForm';
import { useUpdateCustomForm } from '../hooks/useUpdateCustomForm';
import { PageHeader } from '@components/PageHeader';
import { LoadingScreen } from '@components/LoadingScreen';
import { ErrorScreen } from '@components/ErrorScreen';
import { FormFieldType, FormField } from '../types/custom-form.types';

/**
 * مخطط التحقق من صحة نموذج المخصص
 */
const customFormSchema = z.object({
  name: z.string().min(1, 'اسم النموذج مطلوب'),
  description: z.string().optional(),
  formType: z.string().min(1, 'نوع النموذج مطلوب'),
  isActive: z.boolean().optional(),
});

/**
 * نوع بيانات النموذج المخصص
 */
type CustomFormData = z.infer<typeof customFormSchema>;

/**
 * واجهة خصائص TabPanel
 */
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

/**
 * مكون TabPanel
 */
const TabPanel: React.FC<TabPanelProps> = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`form-builder-tabpanel-${index}`}
      aria-labelledby={`form-builder-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

/**
 * صفحة بناء النموذج المخصص
 */
const CustomFormBuilderPage: React.FC = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;
  const [tabValue, setTabValue] = useState(0);
  const [fields, setFields] = useState<FormField[]>([]);
  const [previewData, setPreviewData] = useState<Record<string, any>>({});

  // استخدام خطافات البيانات
  const { data: customForm, isLoading: isLoadingForm, error: formError } = useCustomForm(
    id || '',
    { enabled: isEditMode && !!id }
  );

  const createCustomFormMutation = useCreateCustomForm();
  const updateCustomFormMutation = useUpdateCustomForm();

  // إعداد نموذج النموذج المخصص
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<CustomFormData>({
    resolver: zodResolver(customFormSchema),
    defaultValues: {
      name: '',
      description: '',
      formType: 'GENERIC',
      isActive: true,
    },
  });

  // تحديث النموذج عند تحميل البيانات في وضع التعديل
  useEffect(() => {
    if (isEditMode && customForm) {
      reset({
        name: customForm.name,
        description: customForm.description || '',
        formType: customForm.formType,
        isActive: customForm.isActive,
      });

      if (customForm.fields) {
        setFields(customForm.fields.map(field => ({
          ...field,
          type: field.type as FormFieldType,
          required: field.required ?? false
        })));
      }
    }
  }, [isEditMode, customForm, reset]);

  // التعامل مع تغيير التبويب
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // معالجة تقديم النموذج
  const onSubmit = async (data: CustomFormData) => {
    try {
      const formData = {
        ...data,
        fields,
        isActive: data.isActive ?? true,
      };

      if (isEditMode) {
        // تحديث نموذج مخصص موجود
        await updateCustomFormMutation.mutateAsync({
          id: id!,
          data: formData,
        });
      } else {
        // إنشاء نموذج مخصص جديد
        await createCustomFormMutation.mutateAsync(formData);
      }

      // الانتقال إلى صفحة النماذج المخصصة
      navigate('/custom-forms');
    } catch (error) {
      console.error('Error saving custom form:', error);
    }
  };

  // إضافة حقل جديد
  const addField = () => {
    const newField: FormField = {
      id: `field_${Date.now()}`,
      name: '',
      label: '',
      type: FormFieldType.TEXT,
      required: false,
      placeholder: '',
      options: [],
      order: fields.length,
    };

    setFields([...fields, newField]);
  };

  // حذف حقل
  const removeField = (index: number) => {
    const newFields = [...fields];
    newFields.splice(index, 1);

    // تحديث ترتيب الحقول
    newFields.forEach((field, idx) => {
      field.order = idx;
    });

    setFields(newFields);
  };

  // تحديث حقل
  const updateField = (index: number, field: Partial<FormField>) => {
    const newFields = [...fields];
    newFields[index] = { ...newFields[index], ...field };
    setFields(newFields);
  };

  // إعداد أجهزة الاستشعار للسحب والإفلات
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // معالجة السحب والإفلات
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = fields.findIndex((field) => field.id === active.id);
      const newIndex = fields.findIndex((field) => field.id === over?.id);

      const newFields = arrayMove(fields, oldIndex, newIndex);

      // تحديث ترتيب الحقول
      newFields.forEach((field, idx) => {
        field.order = idx;
      });

      setFields(newFields);
    }
  };

  // مكون العنصر القابل للسحب
  const SortableFieldItem = ({ field, index }: { field: FormField; index: number }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
    } = useSortable({ id: field.id });

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    };

    return (
      <Card
        ref={setNodeRef}
        style={style}
        sx={{ mb: 2 }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box {...attributes} {...listeners} sx={{ mr: 1, cursor: 'grab' }}>
              <DragIndicatorIcon />
            </Box>
            <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
              {field.label || t('customForms.builder.fields.newField')}
            </Typography>
            <Tooltip title={t('common.delete')}>
              <IconButton
                size="small"
                color="error"
                onClick={() => removeField(index)}
              >
                <DeleteIcon />
              </IconButton>
            </Tooltip>
          </Box>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label={t('customForms.builder.fields.name')}
                fullWidth
                value={field.name}
                onChange={(e) => updateField(index, { name: e.target.value })}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label={t('customForms.builder.fields.label')}
                fullWidth
                value={field.label}
                onChange={(e) => updateField(index, { label: e.target.value })}
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                select
                label={t('customForms.builder.fields.type')}
                fullWidth
                value={field.type}
                onChange={(e) => updateField(index, { type: e.target.value as FormFieldType })}
                required
              >
                <MenuItem value={FormFieldType.TEXT}>
                  {t('customForms.builder.fields.types.TEXT')}
                </MenuItem>
                <MenuItem value={FormFieldType.TEXTAREA}>
                  {t('customForms.builder.fields.types.TEXTAREA')}
                </MenuItem>
                <MenuItem value={FormFieldType.NUMBER}>
                  {t('customForms.builder.fields.types.NUMBER')}
                </MenuItem>
                <MenuItem value={FormFieldType.DATE}>
                  {t('customForms.builder.fields.types.DATE')}
                </MenuItem>
                <MenuItem value={FormFieldType.SELECT}>
                  {t('customForms.builder.fields.types.SELECT')}
                </MenuItem>
                <MenuItem value={FormFieldType.CHECKBOX}>
                  {t('customForms.builder.fields.types.CHECKBOX')}
                </MenuItem>
              </TextField>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                select
                label={t('customForms.builder.fields.required')}
                fullWidth
                value={field.required ? 'true' : 'false'}
                onChange={(e) => updateField(index, { required: e.target.value === 'true' })}
              >
                <MenuItem value="true">
                  {t('common.yes')}
                </MenuItem>
                <MenuItem value="false">
                  {t('common.no')}
                </MenuItem>
              </TextField>
            </Grid>

            <Grid item xs={12}>
              <TextField
                label={t('customForms.builder.fields.placeholder')}
                fullWidth
                value={field.placeholder || ''}
                onChange={(e) => updateField(index, { placeholder: e.target.value })}
              />
            </Grid>

            {field.type === FormFieldType.SELECT && (
              <Grid item xs={12}>
                <TextField
                  label={t('customForms.builder.fields.options')}
                  fullWidth
                  multiline
                  rows={3}
                  value={field.options?.map(opt => typeof opt === 'string' ? opt : opt.label).join('\n') || ''}
                  onChange={(e) => updateField(index, {
                    options: e.target.value.split('\n').filter(Boolean).map(opt => ({ value: opt, label: opt }))
                  })}
                  helperText={t('customForms.builder.fields.optionsHelp')}
                />
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Card>
    );
  };

  // عرض شاشة التحميل
  if (isEditMode && isLoadingForm) {
    return <LoadingScreen />;
  }

  // عرض شاشة الخطأ
  if (isEditMode && (formError || !customForm)) {
    return (
      <ErrorScreen
        message={t('customForms.builder.errorLoading')}
        onRetry={() => window.location.reload()}
      />
    );
  }

  return (
    <Box>
      <PageHeader
        title={
          isEditMode
            ? t('customForms.builder.editTitle')
            : t('customForms.builder.addTitle')
        }
        subtitle={
          isEditMode
            ? customForm?.name
            : t('customForms.builder.subtitle')
        }
        backButton={
          <Button
            component={Link}
            to="/custom-forms"
            startIcon={<ArrowBackIcon />}
            variant="outlined"
          >
            {t('common.back')}
          </Button>
        }
      />

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab label={t('customForms.builder.tabs.settings')} />
          <Tab label={t('customForms.builder.tabs.fields')} />
          <Tab label={t('customForms.builder.tabs.preview')} />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('customForms.name')}
                      fullWidth
                      required
                      error={!!errors.name}
                      helperText={errors.name?.message}
                      disabled={isSubmitting}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label={t('customForms.description')}
                      fullWidth
                      multiline
                      rows={3}
                      error={!!errors.description}
                      helperText={errors.description?.message}
                      disabled={isSubmitting}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="formType"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      select
                      label={t('customForms.formType')}
                      fullWidth
                      required
                      error={!!errors.formType}
                      helperText={errors.formType?.message}
                      disabled={isSubmitting}
                    >
                      <MenuItem value="GENERIC">
                        {t('customForms.types.GENERIC')}
                      </MenuItem>
                      <MenuItem value="DECLARATION">
                        {t('customForms.types.DECLARATION')}
                      </MenuItem>
                      <MenuItem value="ITEM_MOVEMENT">
                        {t('customForms.types.ITEM_MOVEMENT')}
                      </MenuItem>
                      <MenuItem value="AUTHORIZATION">
                        {t('customForms.types.AUTHORIZATION')}
                      </MenuItem>
                    </TextField>
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="isActive"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      select
                      label={t('customForms.isActive')}
                      fullWidth
                      value={field.value ? 'true' : 'false'}
                      onChange={(e) => field.onChange(e.target.value === 'true')}
                      disabled={isSubmitting}
                    >
                      <MenuItem value="true">
                        {t('common.yes')}
                      </MenuItem>
                      <MenuItem value="false">
                        {t('common.no')}
                      </MenuItem>
                    </TextField>
                  )}
                />
              </Grid>

              <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  startIcon={<SaveIcon />}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <CircularProgress size={24} />
                  ) : (
                    t('common.save')
                  )}
                </Button>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              {t('customForms.builder.fields.title')}
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={addField}
            >
              {t('customForms.builder.fields.addField')}
            </Button>
          </Box>

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={fields.map(field => field.id)}
              strategy={verticalListSortingStrategy}
            >
              <Box>
                {fields.length === 0 ? (
                  <Alert severity="info">
                    {t('customForms.builder.fields.noFields')}
                  </Alert>
                ) : (
                  fields.map((field, index) => (
                    <SortableFieldItem
                      key={field.id}
                      field={field}
                      index={index}
                    />
                  ))
                )}
              </Box>
            </SortableContext>
          </DndContext>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={handleSubmit(onSubmit)}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <CircularProgress size={24} />
              ) : (
                t('common.save')
              )}
            </Button>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="h6">
              {t('customForms.builder.preview.title')}
            </Typography>
            <Button
              variant="outlined"
              startIcon={<VisibilityIcon />}
              onClick={() => setPreviewData({})}
            >
              {t('customForms.builder.preview.reset')}
            </Button>
          </Box>

          {fields.length === 0 ? (
            <Alert severity="info">
              {t('customForms.builder.preview.noFields')}
            </Alert>
          ) : (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {control._formValues.name || t('customForms.builder.preview.formTitle')}
                </Typography>
                {control._formValues.description && (
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {control._formValues.description}
                  </Typography>
                )}
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  {fields.map((field) => (
                    <Grid item xs={12} sm={6} key={field.id}>
                      {renderPreviewField(field)}
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          )}
        </TabPanel>
      </Paper>
    </Box>
  );

  // تقديم حقل في المعاينة
  function renderPreviewField(field: FormField) {
    const handleChange = (value: any) => {
      setPreviewData({
        ...previewData,
        [field.name]: value,
      });
    };

    switch (field.type) {
      case FormFieldType.TEXT:
        return (
          <TextField
            label={field.label}
            fullWidth
            placeholder={field.placeholder}
            required={field.required}
            value={previewData[field.name] || ''}
            onChange={(e) => handleChange(e.target.value)}
          />
        );
      case FormFieldType.TEXTAREA:
        return (
          <TextField
            label={field.label}
            fullWidth
            multiline
            rows={3}
            placeholder={field.placeholder}
            required={field.required}
            value={previewData[field.name] || ''}
            onChange={(e) => handleChange(e.target.value)}
          />
        );
      case FormFieldType.NUMBER:
        return (
          <TextField
            label={field.label}
            fullWidth
            type="number"
            placeholder={field.placeholder}
            required={field.required}
            value={previewData[field.name] || ''}
            onChange={(e) => handleChange(e.target.value)}
          />
        );
      case FormFieldType.DATE:
        return (
          <TextField
            label={field.label}
            fullWidth
            type="date"
            InputLabelProps={{ shrink: true }}
            required={field.required}
            value={previewData[field.name] || ''}
            onChange={(e) => handleChange(e.target.value)}
          />
        );
      case FormFieldType.SELECT:
        return (
          <TextField
            select
            label={field.label}
            fullWidth
            required={field.required}
            value={previewData[field.name] || ''}
            onChange={(e) => handleChange(e.target.value)}
          >
            {field.options?.map((option) => (
              <MenuItem
                key={typeof option === 'string' ? option : option.value}
                value={typeof option === 'string' ? option : option.value}
              >
                {typeof option === 'string' ? option : option.label}
              </MenuItem>
            ))}
          </TextField>
        );
      case FormFieldType.CHECKBOX:
        return (
          <TextField
            select
            label={field.label}
            fullWidth
            required={field.required}
            value={previewData[field.name] ? 'true' : 'false'}
            onChange={(e) => handleChange(e.target.value === 'true')}
          >
            <MenuItem value="true">
              {t('common.yes')}
            </MenuItem>
            <MenuItem value="false">
              {t('common.no')}
            </MenuItem>
          </TextField>
        );
      default:
        return null;
    }
  }
};

export default CustomFormBuilderPage;
