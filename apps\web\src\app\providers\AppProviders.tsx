import { ReactNode } from 'react';
import { Provider as ReduxProvider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { store } from '../store/store';
import ThemeProvider from './ThemeProvider';
import I18nProvider from './I18nProvider';
import QueryProvider from './QueryProvider';
import AuthProvider from './AuthProvider';
import ToastProvider from '@lib/providers/ToastProvider';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

interface AppProvidersProps {
  children: ReactNode;
}

export const AppProviders = ({ children }: AppProvidersProps) => {
  return (
    <ReduxProvider store={store}>
      <BrowserRouter>
        <I18nProvider>
          <ThemeProvider>
            <ToastProvider>
              <QueryProvider>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <AuthProvider>
                    {children}
                  </AuthProvider>
                </LocalizationProvider>
              </QueryProvider>
            </ToastProvider>
          </ThemeProvider>
        </I18nProvider>
      </BrowserRouter>
    </ReduxProvider>
  );
};

export default AppProviders;
