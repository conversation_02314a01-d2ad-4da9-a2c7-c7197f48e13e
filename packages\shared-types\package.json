{"name": "shared-types", "version": "0.0.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --dts --watch", "lint": "eslint \"src/**/*.ts\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "devDependencies": {"@types/node": "^20.11.20", "eslint": "^8.56.0", "tsup": "^8.0.2", "typescript": "^5.3.3"}}