import { Request, Response, NextFunction } from 'express';
import { clientService } from '../services/client.service.js';
import { successResponse, paginatedResponse } from '../../../core/utils/api/apiResponse.js';
import { HttpException } from '../../../core/middleware/error.middleware.js';

export const clientController = {
  /**
   * إنشاء عميل جديد
   */
  createClient: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على بيانات العميل من الطلب
      const clientData = req.body;

      // إنشاء العميل
      const client = await clientService.createClient(clientData);

      return res.status(201).json(successResponse(client, 'تم إنشاء العميل بنجاح', 201));
    } catch (error) {
      next(error);
    }
  },

  /**
   * تحديث عميل
   */
  updateClient: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف العميل من المعلمات
      const { id } = req.params;

      // الحصول على بيانات العميل من الطلب
      const clientData = req.body;

      // تحديث العميل
      const client = await clientService.updateClient(id, clientData);

      return res.status(200).json(successResponse(client, 'تم تحديث العميل بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على عميل محدد
   */
  getClient: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معرف العميل من المعلمات
      const { id } = req.params;

      // الحصول على العميل
      const client = await clientService.getClient(id);

      return res.status(200).json(successResponse(client));
    } catch (error) {
      next(error);
    }
  },

  /**
   * حذف عميل
   */
  deleteClient: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // التحقق من وجود المستخدم
      if (!req.user) {
        throw new HttpException(401, 'غير مصرح', 'Unauthorized');
      }

      // الحصول على معرف العميل من المعلمات
      const { id } = req.params;

      // حذف العميل
      await clientService.deleteClient(id);

      return res.status(200).json(successResponse(null, 'تم حذف العميل بنجاح'));
    } catch (error) {
      next(error);
    }
  },

  /**
   * الحصول على قائمة العملاء
   */
  listClients: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // الحصول على معلمات البحث
      const { page, limit, sort, order, search } = req.query as any;

      // الحصول على قائمة العملاء
      const result = await clientService.listClients({
        page: page ? parseInt(page) : undefined,
        limit: limit ? parseInt(limit) : undefined,
        sort,
        order,
        search,
      });

      return res.status(200).json(paginatedResponse(
        result.data,
        result.pagination.page,
        result.pagination.limit,
        result.pagination.total,
        'تم الحصول على قائمة العملاء بنجاح'
      ));
    } catch (error) {
      next(error);
    }
  },
};
