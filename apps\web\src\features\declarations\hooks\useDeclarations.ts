import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getDeclarations,
  getDeclaration,
  createDeclaration,
  updateDeclaration,
  deleteDeclaration,
  DeclarationSearchParams,
} from '../api/declarations.api';
import { CreateDeclarationRequest, UpdateDeclarationRequest } from '../api/declarations.api';

// خطاف للحصول على قائمة البيانات
export const useDeclarations = (params: DeclarationSearchParams = {}) => {
  return useQuery({
    queryKey: ['declarations', params],
    queryFn: () => getDeclarations(params),
  });
};

// خطاف للحصول على بيان محدد
export const useDeclaration = (id: string) => {
  return useQuery({
    queryKey: ['declaration', id],
    queryFn: () => getDeclaration(id),
    enabled: !!id,
  });
};

// خطاف لإنشاء بيان جديد
export const useCreateDeclaration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ data, file }: { data: CreateDeclarationRequest; file?: File }) =>
      createDeclaration(data, file),
    onSuccess: () => {
      // إعادة تحميل قائمة البيانات بعد الإنشاء
      queryClient.invalidateQueries({ queryKey: ['declarations'] });
    },
  });
};

// خطاف لتحديث بيان
export const useUpdateDeclaration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
      file,
    }: {
      id: string;
      data: UpdateDeclarationRequest;
      file?: File;
    }) => updateDeclaration(id, data, file),
    onSuccess: (_, variables) => {
      // إعادة تحميل البيان وقائمة البيانات بعد التحديث
      queryClient.invalidateQueries({ queryKey: ['declaration', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['declarations'] });
    },
  });
};

// خطاف لحذف بيان
export const useDeleteDeclaration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteDeclaration(id),
    onSuccess: () => {
      // إعادة تحميل قائمة البيانات بعد الحذف
      queryClient.invalidateQueries({ queryKey: ['declarations'] });
    },
  });
};
