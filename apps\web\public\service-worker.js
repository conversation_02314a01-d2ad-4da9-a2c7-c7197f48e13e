// اسم التخزين المؤقت
const CACHE_NAME = 'alnoor-cache-v1';

// الموارد التي سيتم تخزينها مؤقتًا
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/assets/styles/fonts.css',
  '/assets/fonts/Tajawal-Regular.ttf',
  '/assets/fonts/Tajawal-Medium.ttf',
  '/assets/fonts/Tajawal-Bold.ttf',
  '/assets/fonts/Cairo-Regular.ttf',
  '/assets/fonts/Cairo-SemiBold.ttf',
  '/assets/fonts/Cairo-Bold.ttf',
  '/assets/fonts/Amiri-Regular.ttf',
  '/assets/fonts/Amiri-Bold.ttf',
  '/assets/images/logos/logo.png',
  '/assets/images/logos/favicon.svg',
];

// تثبيت Service Worker
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log('تم فتح التخزين المؤقت');
      return cache.addAll(STATIC_ASSETS);
    })
  );
});

// تنشيط Service Worker
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames
          .filter((cacheName) => {
            // حذف التخزين المؤقت القديم
            return cacheName !== CACHE_NAME;
          })
          .map((cacheName) => {
            return caches.delete(cacheName);
          })
      );
    })
  );
});

// استراتيجية التخزين المؤقت: الشبكة أولاً، ثم التخزين المؤقت
self.addEventListener('fetch', (event) => {
  // تجاهل طلبات API
  if (event.request.url.includes('/api/')) {
    return;
  }

  // تجاهل طلبات التحليلات
  if (event.request.url.includes('analytics') || event.request.url.includes('googletagmanager')) {
    return;
  }

  event.respondWith(
    fetch(event.request)
      .then((response) => {
        // نسخة من الاستجابة للتخزين المؤقت
        const responseClone = response.clone();
        
        caches.open(CACHE_NAME).then((cache) => {
          // تخزين الاستجابة في التخزين المؤقت
          cache.put(event.request, responseClone);
        });
        
        return response;
      })
      .catch(() => {
        // إذا فشل الطلب، استخدم التخزين المؤقت
        return caches.match(event.request).then((response) => {
          return response || caches.match('/index.html');
        });
      })
  );
});

// التعامل مع الرسائل من التطبيق
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// تخزين الصور مؤقتًا
self.addEventListener('fetch', (event) => {
  // التحقق مما إذا كان الطلب للصور
  if (event.request.destination === 'image') {
    event.respondWith(
      caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((cachedResponse) => {
          // إذا كانت الصورة موجودة في التخزين المؤقت، استخدمها
          if (cachedResponse) {
            return cachedResponse;
          }
          
          // إذا لم تكن موجودة، قم بتنزيلها وتخزينها
          return fetch(event.request).then((networkResponse) => {
            cache.put(event.request, networkResponse.clone());
            return networkResponse;
          });
        });
      })
    );
  }
});

// تخزين الخطوط مؤقتًا
self.addEventListener('fetch', (event) => {
  // التحقق مما إذا كان الطلب للخطوط
  if (
    event.request.url.includes('.ttf') ||
    event.request.url.includes('.woff') ||
    event.request.url.includes('.woff2')
  ) {
    event.respondWith(
      caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((cachedResponse) => {
          // إذا كان الخط موجودًا في التخزين المؤقت، استخدمه
          if (cachedResponse) {
            return cachedResponse;
          }
          
          // إذا لم يكن موجودًا، قم بتنزيله وتخزينه
          return fetch(event.request).then((networkResponse) => {
            cache.put(event.request, networkResponse.clone());
            return networkResponse;
          });
        });
      })
    );
  }
});
