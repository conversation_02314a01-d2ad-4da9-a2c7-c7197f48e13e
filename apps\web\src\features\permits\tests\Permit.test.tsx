import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { createStore } from '../../../app/store/store';
import PermitsPage from '../pages/PermitsPage';
import PermitFormPage from '../pages/PermitFormPage';
import PermitDetailsPage from '../pages/PermitDetailsPage';
import { PermitType } from '../types/permit.types';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

// Mock React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

// Mock Redux Store
const store = createStore();

// Mock React Router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ id: '123' }),
    useNavigate: () => vi.fn(),
  };
});

// Mock React Query Hooks
vi.mock('../hooks/usePermits', () => ({
  usePermits: () => ({
    data: {
      data: [
        {
          id: '123',
          permitNumber: 1001,
          permitType: PermitType.ENTRY,
          issueDate: new Date().toISOString(),
          expiryDate: new Date().toISOString(),
          notes: 'Test notes',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: { name: 'Test User' },
        },
      ],
      pagination: {
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      },
    },
    isLoading: false,
    isError: false,
  }),
  usePermit: () => ({
    data: {
      id: '123',
      permitNumber: 1001,
      permitType: PermitType.ENTRY,
      issueDate: new Date().toISOString(),
      expiryDate: new Date().toISOString(),
      notes: 'Test notes',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: { name: 'Test User' },
    },
    isLoading: false,
    isError: false,
  }),
  useCreatePermit: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useUpdatePermit: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useDeletePermit: () => ({
    mutateAsync: vi.fn().mockResolvedValue({}),
    isLoading: false,
  }),
  useDownloadPermitPdf: () => ({
    mutate: vi.fn(),
    mutateAsync: vi.fn().mockResolvedValue(new Blob()),
    isLoading: false,
  }),
}));

// Mock i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: vi.fn(),
    },
  }),
}));

// Mock date-fns
vi.mock('date-fns', () => ({
  format: vi.fn().mockReturnValue('2023-01-01'),
  arSA: {},
}));

// Mock dayjs
vi.mock('dayjs', () => {
  const dayjs = () => ({
    format: () => '2023-01-01',
    isBefore: () => false,
    isAfter: () => false,
    isSame: () => true,
    locale: () => {},
  });
  dayjs.extend = vi.fn();
  dayjs.locale = vi.fn();
  return dayjs;
});

// Wrapper Component
const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <BrowserRouter>{children}</BrowserRouter>
      </LocalizationProvider>
    </QueryClientProvider>
  </Provider>
);

describe('Permits Feature', () => {
  describe('PermitsPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <PermitsPage />
        </Wrapper>
      );
    });

    it('renders the permits page title', () => {
      expect(screen.getByText('permits.title')).toBeInTheDocument();
    });

    it('renders the permits table', () => {
      expect(screen.getByText('permits.number')).toBeInTheDocument();
      expect(screen.getByText('permits.type')).toBeInTheDocument();
      expect(screen.getByText('permits.issueDate')).toBeInTheDocument();
      expect(screen.getByText('permits.expiryDate')).toBeInTheDocument();
    });

    it('renders the create permit button', () => {
      expect(screen.getByText('permits.create')).toBeInTheDocument();
    });

    it('shows filters when filter button is clicked', async () => {
      const filterButton = screen.getByText('common.filters');
      fireEvent.click(filterButton);

      await waitFor(() => {
        expect(screen.getByText('common.resetFilters')).toBeInTheDocument();
      });
    });
  });

  describe('PermitFormPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <PermitFormPage />
        </Wrapper>
      );
    });

    it('renders the permit form', () => {
      expect(screen.getByText('permits.type')).toBeInTheDocument();
      expect(screen.getByText('permits.issueDate')).toBeInTheDocument();
      expect(screen.getByText('permits.expiryDate')).toBeInTheDocument();
      expect(screen.getByText('permits.notes')).toBeInTheDocument();
    });

    it('renders the save button', () => {
      expect(screen.getByText('common.save')).toBeInTheDocument();
    });

    it('renders the cancel button', () => {
      expect(screen.getByText('common.cancel')).toBeInTheDocument();
    });
  });

  describe('PermitDetailsPage', () => {
    beforeEach(() => {
      render(
        <Wrapper>
          <PermitDetailsPage />
        </Wrapper>
      );
    });

    it('renders the permit details', () => {
      expect(screen.getByText('permits.details')).toBeInTheDocument();
      expect(screen.getByText('permits.number')).toBeInTheDocument();
      expect(screen.getByText('permits.type')).toBeInTheDocument();
      expect(screen.getByText('permits.issueDate')).toBeInTheDocument();
      expect(screen.getByText('permits.expiryDate')).toBeInTheDocument();
    });

    it('renders the edit button', () => {
      expect(screen.getByText('common.edit')).toBeInTheDocument();
    });

    it('renders the delete button', () => {
      expect(screen.getByText('common.delete')).toBeInTheDocument();
    });

    it('renders the back button', () => {
      expect(screen.getByText('common.back')).toBeInTheDocument();
    });
  });
});
