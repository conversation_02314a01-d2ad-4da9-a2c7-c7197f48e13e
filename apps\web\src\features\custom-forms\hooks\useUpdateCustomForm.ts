import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api/api';
import { CustomForm } from '@/types/global';

interface UpdateCustomFormRequest {
  id: string;
  data: {
    title?: string;
    description?: string;
    fields?: any[];
  };
}

export const useUpdateCustomForm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: UpdateCustomFormRequest): Promise<CustomForm> => {
      const response = await api.put(`/api/custom-forms/${id}`, data);
      return response as CustomForm;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['customForms'] });
      queryClient.invalidateQueries({ queryKey: ['customForm', id] });
    },
  });
};
