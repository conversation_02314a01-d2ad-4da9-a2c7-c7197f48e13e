import { z } from 'zod';

// مخطط إنشاء تصريح جديد
export const createPermitSchema = z.object({
  body: z.object({
    declarationId: z.string({
      required_error: 'معرف البيان مطلوب',
    }).uuid(),
    permitDate: z.string({
      required_error: 'تاريخ التصريح مطلوب',
    }).transform(val => new Date(val)),
    permitNumber: z.string({
      required_error: 'رقم التصريح مطلوب',
    }),
    expiryDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    notes: z.string().optional(),
  }),
});

// مخطط تحديث تصريح
export const updatePermitSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف التصريح مطلوب',
    }),
  }),
  body: z.object({
    declarationId: z.string().uuid().optional(),
    permitDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    permitNumber: z.string().optional(),
    expiryDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
    notes: z.string().optional(),
  }),
});

// مخطط الحصول على تصريح محدد
export const getPermitSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف التصريح مطلوب',
    }),
  }),
});

// مخطط حذف تصريح
export const deletePermitSchema = z.object({
  params: z.object({
    id: z.string({
      required_error: 'معرف التصريح مطلوب',
    }),
  }),
});

// مخطط قائمة التصاريح
export const listPermitsSchema = z.object({
  query: z.object({
    page: z.string().optional().transform((val) => (val ? parseInt(val) : 1)),
    limit: z.string().optional().transform((val) => (val ? parseInt(val) : 10)),
    sort: z.string().optional().default('createdAt'),
    order: z.enum(['asc', 'desc']).optional().default('desc'),
    search: z.string().optional(),
    declarationId: z.string().uuid().optional(),
    fromDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
    toDate: z.string().optional().transform((val) => (val ? new Date(val) : undefined)),
  }),
});
