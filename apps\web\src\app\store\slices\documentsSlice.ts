import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Document } from '@features/documents/types/document.types';

export interface DocumentsState {
  selectedDocument: Document | null;
  filters: {
    title?: string;
    documentType?: string;
    startDate?: string;
    endDate?: string;
  };
  pagination: {
    page: number;
    limit: number;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: DocumentsState = {
  selectedDocument: null,
  filters: {},
  pagination: {
    page: 0,
    limit: 10,
  },
  isLoading: false,
  error: null,
};

export const documentsSlice = createSlice({
  name: 'documents',
  initialState,
  reducers: {
    setSelectedDocument: (state, action: PayloadAction<Document | null>) => {
      state.selectedDocument = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<DocumentsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = {};
    },
    setPagination: (state, action: PayloadAction<Partial<DocumentsState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setSelectedDocument,
  setFilters,
  resetFilters,
  setPagination,
  setLoading,
  setError,
} = documentsSlice.actions;

export default documentsSlice.reducer;
