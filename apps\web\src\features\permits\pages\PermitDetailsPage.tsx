import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  Typography,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as ArrowBackIcon,
  PictureAsPdf as PdfIcon,
} from '@mui/icons-material';
import { usePermit, useDeletePermit, useDownloadPermitPdf } from '../hooks/usePermits';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const PermitDetailsPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // استخدام خطافات التصريح
  const { data: permit, isLoading, isError } = usePermit(id || '');
  const deleteMutation = useDeletePermit();
  const downloadPdfMutation = useDownloadPermitPdf();

  // التعامل مع تعديل التصريح
  const handleEdit = () => {
    navigate(`/permits/${id}/edit`);
  };

  // التعامل مع حذف التصريح
  const handleDelete = async () => {
    if (window.confirm(t('permits.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(id || '');
        navigate('/permits');
      } catch (error) {
        console.error('Error deleting permit:', error);
      }
    }
  };

  // التعامل مع العودة إلى قائمة التصاريح
  const handleBack = () => {
    navigate('/permits');
  };

  // التعامل مع عرض ملف PDF
  const handleViewPdf = () => {
    if (permit?.pdfFile) {
      downloadPdfMutation.mutate(id || '');
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (isError || !permit) {
    return (
      <Container maxWidth="md">
        <Box textAlign="center" my={4}>
          <Typography variant="h5" color="error" gutterBottom>
            {t('common.errorLoading')}
          </Typography>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleBack}
          >
            {t('common.backToList')}
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('permits.details')}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {t('permits.detailsDescription')}
        </Typography>
      </Box>

      <Box mb={3} display="flex" justifyContent="flex-end">
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={handleBack}
          sx={{ mr: 1 }}
        >
          {t('common.back')}
        </Button>
        <Button
          variant="outlined"
          startIcon={<EditIcon />}
          onClick={handleEdit}
          sx={{ mr: 1 }}
        >
          {t('common.edit')}
        </Button>
        <Button
          variant="outlined"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={handleDelete}
        >
          {t('common.delete')}
        </Button>
      </Box>

      <Paper>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('permits.number')}
              </Typography>
              <Typography variant="body1">
                {permit.permitNumber || '-'}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('permits.type')}
              </Typography>
              <Chip
                label={t(`permits.types.${permit.permitType}`)}
                color={
                  permit.permitType === 'ENTRY'
                    ? 'primary'
                    : permit.permitType === 'EXIT'
                    ? 'secondary'
                    : 'default'
                }
                size="small"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('permits.permitDate')}
              </Typography>
              <Typography variant="body1">
                {format(new Date(permit.permitDate), 'yyyy-MM-dd', { locale: arSA })}
              </Typography>
            </Grid>

            {permit.issueDate && (
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">
                  {t('permits.issueDate')}
                </Typography>
                <Typography variant="body1">
                  {format(new Date(permit.issueDate), 'yyyy-MM-dd', { locale: arSA })}
                </Typography>
              </Grid>
            )}

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('permits.expiryDate')}
              </Typography>
              <Typography variant="body1">
                {format(new Date(permit.expiryDate), 'yyyy-MM-dd', { locale: arSA })}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('permits.declaration')}
              </Typography>
              <Typography variant="body1">
                {permit.declaration?.declarationNumber || '-'}
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('permits.client')}
              </Typography>
              <Typography variant="body1">
                {permit.declaration?.clientName || '-'}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('permits.notes')}
              </Typography>
              <Typography variant="body1">
                {permit.notes || '-'}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle2" color="textSecondary">
                {t('common.createdBy')}
              </Typography>
              <Typography variant="body1">
                {permit.createdBy?.name || '-'}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle2" color="textSecondary">
                {t('common.createdAt')}
              </Typography>
              <Typography variant="body1">
                {format(new Date(permit.createdAt), 'yyyy-MM-dd HH:mm', { locale: arSA })}
              </Typography>
            </Grid>

            {permit.pdfFile && (
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Box display="flex" alignItems="center">
                  <Button
                    variant="outlined"
                    startIcon={<PdfIcon />}
                    onClick={handleViewPdf}
                  >
                    {t('common.viewPdf')}
                  </Button>
                </Box>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Paper>
    </Container>
  );
};

export default PermitDetailsPage;
