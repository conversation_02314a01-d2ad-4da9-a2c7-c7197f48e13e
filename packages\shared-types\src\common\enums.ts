/**
 * تعداد نوع البيان
 */
export enum DeclarationType {
  IMPORT = 'IMPORT',
  EXPORT = 'EXPORT',
}

/**
 * تعداد نوع البضاعة
 */
export enum GoodsType {
  HUMAN_MEDICINE = 'HUMAN_MEDICINE',
  LABORATORY_SOLUTIONS = 'LABORATORY_SOLUTIONS',
  MEDICAL_SUPPLIES = 'MEDICAL_SUPPLIES',
  SUGAR_STRIPS = 'SUGAR_STRIPS',
  MEDICAL_DEVICES = 'MEDICAL_DEVICES',
  MISCELLANEOUS = 'MISCELLANEOUS',
}

/**
 * تعداد نوع العبوة
 */
export enum PackageType {
  PALLET = 'PALLET',
  CARTON = 'CARTON',
  BARREL = 'BARREL',
}

/**
 * تعداد العملة
 */
export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  SAR = 'SAR',
}

/**
 * تعداد نوع التفويض
 */
export enum AuthorizationType {
  FOLLOW_UP = 'FOLLOW_UP',
  CLEARANCE = 'CLEARANCE',
  RECEIPT = 'RECEIPT',
  FULL = 'FULL',
}

/**
 * تعداد حالة الضمان
 */
export enum GuaranteeStatus {
  ACTIVE = 'ACTIVE',
  RETURNED = 'RETURNED',
  EXPIRED = 'EXPIRED',
}

/**
 * تعداد نوع التقرير
 */
export enum ReportType {
  DECLARATIONS = 'DECLARATIONS',
  ITEM_MOVEMENTS = 'ITEM_MOVEMENTS',
  AUTHORIZATIONS = 'AUTHORIZATIONS',
  RELEASES = 'RELEASES',
  PERMITS = 'PERMITS',
  GUARANTEES = 'GUARANTEES',
  RECEIPTS = 'RECEIPTS',
  CLIENTS = 'CLIENTS',
  DOCUMENTS = 'DOCUMENTS',
}

/**
 * تعداد صيغة التقرير
 */
export enum ReportFormat {
  PDF = 'PDF',
  EXCEL = 'EXCEL',
  CSV = 'CSV',
}
