import { useTranslation } from 'react-i18next';
import { Box, Grid, Paper, Typography, CircularProgress } from '@mui/material';
import {
  Description as DescriptionIcon,
  SwapHoriz as SwapHorizIcon,
  Assignment as AssignmentIcon,
  Gavel as GavelIcon,
  FactCheck as FactCheckIcon,
  Security as SecurityIcon,
  Receipt as ReceiptIcon,
  People as PeopleIcon,
} from '@mui/icons-material';
import StatCard from '../components/StatCard';
import RecentDeclarations from '../components/RecentDeclarations';
import DeclarationsByTypeChart from '../components/DeclarationsByTypeChart';
import { useDashboardStats } from '../hooks/useDeclarations';

const DashboardPage = () => {
  const { t } = useTranslation();
  const { data: statsData, isLoading: statsLoading } = useDashboardStats();

  // إنشاء مصفوفة الإحصائيات من البيانات المجلبة
  const stats = statsData ? [
    {
      title: t('navigation.declarations'),
      value: statsData.totalDeclarations,
      icon: <DescriptionIcon fontSize="large" />,
      color: '#1976d2',
    },
    {
      title: t('navigation.itemsMovement'),
      value: statsData.totalItemMovements,
      icon: <SwapHorizIcon fontSize="large" />,
      color: '#2196f3',
    },
    {
      title: t('navigation.authorizations'),
      value: statsData.totalAuthorizations,
      icon: <AssignmentIcon fontSize="large" />,
      color: '#03a9f4',
    },
    {
      title: t('navigation.releases'),
      value: statsData.totalReleases,
      icon: <GavelIcon fontSize="large" />,
      color: '#00bcd4',
    },
    {
      title: t('navigation.permits'),
      value: statsData.totalPermits,
      icon: <FactCheckIcon fontSize="large" />,
      color: '#009688',
    },
    {
      title: t('navigation.guarantees'),
      value: statsData.totalGuarantees,
      icon: <SecurityIcon fontSize="large" />,
      color: '#4caf50',
    },
    {
      title: t('navigation.receipts'),
      value: statsData.totalReceipts,
      icon: <ReceiptIcon fontSize="large" />,
      color: '#8bc34a',
    },
    {
      title: t('navigation.clients'),
      value: statsData.totalClients,
      icon: <PeopleIcon fontSize="large" />,
      color: '#cddc39',
    },
  ] : [];

  if (statsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          جاري تحميل لوحة التحكم...
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        {t('navigation.dashboard')}
      </Typography>

      <Grid container spacing={3}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatCard
              title={stat.title}
              value={stat.value}
              icon={stat.icon}
              color={stat.color}
            />
          </Grid>
        ))}

        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              {t('dashboard.recentDeclarations')}
            </Typography>
            <RecentDeclarations />
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>
              {t('dashboard.declarationsByType')}
            </Typography>
            <DeclarationsByTypeChart />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
